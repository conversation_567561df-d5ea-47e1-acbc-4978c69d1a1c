import pandas as pd
import numpy as np
from typing import List, Tuple, Dict
from abc import ABC, abstractmethod


class Strategy(ABC):
    def __init__(self, symbol: str):
        self.symbol = symbol
        self.position = 0

    @abstractmethod
    def on_bar(self, bar: Dict) -> int:
        pass


class TrendFollowing(Strategy):
    def __init__(self, symbol: str, short_window: int = 10, long_window: int = 30):
        super().__init__(symbol)
        self.short_window = short_window
        self.long_window = long_window
        self.short_ma = []
        self.long_ma = []

    def on_bar(self, bar: Dict) -> int:
        self.short_ma.append(bar['close'])
        self.long_ma.append(bar['close'])

        if len(self.short_ma) > self.short_window:
            self.short_ma.pop(0)
        if len(self.long_ma) > self.long_window:
            self.long_ma.pop(0)

        if len(self.long_ma) < self.long_window:
            return 0

        short_ma_value = sum(self.short_ma) / len(self.short_ma)
        long_ma_value = sum(self.long_ma) / len(self.long_ma)

        if short_ma_value > long_ma_value and self.position <= 0:
            return 1  # 买入信号
        elif short_ma_value < long_ma_value and self.position >= 0:
            return -1  # 卖出信号
        return 0


class MeanReversion(Strategy):
    def __init__(self, symbol: str, window: int = 20, std_dev: float = 2):
        super().__init__(symbol)
        self.window = window
        self.std_dev = std_dev
        self.prices = []

    def on_bar(self, bar: Dict) -> int:
        self.prices.append(bar['close'])
        if len(self.prices) > self.window:
            self.prices.pop(0)

        if len(self.prices) < self.window:
            return 0

        mean = sum(self.prices) / len(self.prices)
        std = np.std(self.prices)

        if bar['close'] < mean - self.std_dev * std and self.position <= 0:
            return 1  # 买入信号
        elif bar['close'] > mean + self.std_dev * std and self.position >= 0:
            return -1  # 卖出信号
        return 0


class FuturesTrading:
    def __init__(self, data_path: str):
        self.data = self.load_data(data_path)
        self.strategies: Dict[str, Strategy] = {}
        self.position = 0
        self.cash = 100000.0  # 初始资金
        self.equity = [self.cash]
        self.trades = []

    @staticmethod
    def load_data(file_path: str) -> pd.DataFrame:
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'].astype(float), unit='ns')
        df.set_index('datetime', inplace=True)
        return df

    def add_strategy(self, name: str, strategy: Strategy):
        self.strategies[name] = strategy

    def open_long(self, bar: Dict, amount: float):
        price = bar['close']
        quantity = amount / price
        self.position += quantity
        self.cash -= amount
        self.trades.append(('LONG', bar['datetime'], price, quantity))

    def close_long(self, bar: Dict):
        price = bar['close']
        amount = self.position * price
        self.cash += amount
        self.trades.append(('CLOSE_LONG', bar['datetime'], price, self.position))
        self.position = 0

    def open_short(self, bar: Dict, amount: float):
        price = bar['close']
        quantity = amount / price
        self.position -= quantity
        self.cash += amount
        self.trades.append(('SHORT', bar['datetime'], price, quantity))

    def close_short(self, bar: Dict):
        price = bar['close']
        amount = abs(self.position) * price
        self.cash -= amount
        self.trades.append(('CLOSE_SHORT', bar['datetime'], price, abs(self.position)))
        self.position = 0

    def on_bar(self, bar: Dict):
        for strategy in self.strategies.values():
            signal = strategy.on_bar(bar)
            if signal == 1 and self.position <= 0:
                if self.position < 0:
                    self.close_short(bar)
                self.open_long(bar, self.cash * 0.1)  # 使用10%的资金开仓
            elif signal == -1 and self.position >= 0:
                if self.position > 0:
                    self.close_long(bar)
                self.open_short(bar, self.cash * 0.1)  # 使用10%的资金开仓

        # 更新账户权益
        self.equity.append(self.cash + self.position * bar['close'])

    def run_backtest(self):
        for _, row in self.data.iterrows():
            bar = row.to_dict()
            bar['datetime'] = row.name
            self.on_bar(bar)

        # 计算回测结果
        total_return = (self.equity[-1] - self.equity[0]) / self.equity[0]
        sharpe_ratio = np.sqrt(252) * np.mean(np.diff(self.equity)) / np.std(np.diff(self.equity))

        print(f"Total Return: {total_return:.2%}")
        print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
        print(f"Number of Trades: {len(self.trades)}")


def main():
    trader = FuturesTrading('data.csv')
    trader.add_strategy('TrendFollowing', TrendFollowing('CZCE.OI501'))
    trader.add_strategy('MeanReversion', MeanReversion('CZCE.OI501'))
    trader.run_backtest()


if __name__ == "__main__":
    main()