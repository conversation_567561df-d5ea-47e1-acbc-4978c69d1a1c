import tkinter as tk
from tkinter import ttk
import json
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
import platform

# 设置中文字体支持
def setup_chinese_font():
    """设置matplotlib中文字体支持"""
    system = platform.system()

    if system == "Windows":
        # Windows系统字体
        fonts = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong']
    elif system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Heiti TC', 'PingFang SC']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']

    # 尝试设置字体
    for font in fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            # 测试字体是否可用
            fig, ax = plt.subplots()
            ax.text(0.5, 0.5, '测试中文', fontsize=12)
            plt.close(fig)
            print(f"成功设置字体: {font}")
            return True
        except:
            continue

    print("警告: 无法设置中文字体，中文可能显示为方块")
    return False

# 初始化中文字体
setup_chinese_font()

# 读取数据
def load_data():
    """加载模拟盘数据"""
    try:
        with open('simulatedaysummary_new.json', 'r', encoding='utf-8') as file:
            data = json.load(file)

        dates = list(data.keys())
        account_balances = {}

        for date in dates:
            for user in data[date]:
                user_name = user["user_name"]
                if user_name not in account_balances:
                    account_balances[user_name] = []
                account_balances[user_name].append(user["balance"])

        accounts = list(account_balances.keys())

        if not accounts:
            raise ValueError("没有找到账户数据")

        print(f"成功加载数据: {len(accounts)}个账户, {len(dates)}个交易日")
        return dates, account_balances, accounts

    except FileNotFoundError:
        print("错误: 找不到文件 'simulatedaysummary_new.json'")
        print("请确保文件存在于当前目录中")
        return None, None, None
    except json.JSONDecodeError:
        print("错误: JSON文件格式错误")
        return None, None, None
    except Exception as e:
        print(f"错误: 加载数据失败 - {e}")
        return None, None, None

# 加载数据
dates, account_balances, accounts = load_data()

if not accounts:
    print("无法加载数据，程序退出")
    exit(1)

# 创建主窗口
root = tk.Tk()
root.title("模拟盘资金曲线分析器")
root.geometry("1200x800")

# 设置窗口居中
root.update_idletasks()
width = root.winfo_width()
height = root.winfo_height()
x = (root.winfo_screenwidth() // 2) - (width // 2)
y = (root.winfo_screenheight() // 2) - (height // 2)
root.geometry(f"{width}x{height}+{x}+{y}")

# 下拉菜单选择账号
selected_account = tk.StringVar(value=accounts[0])

def plot_balance(account):
    """绘制账户资金曲线"""
    fig, ax = plt.subplots(figsize=(10, 6))

    # 绘制资金曲线
    balances = account_balances[account]
    ax.plot(dates, balances, marker='o', linewidth=2, markersize=4, color='#1f77b4')

    # 设置标题和标签（确保中文显示）
    ax.set_title(f"{account} 资金曲线", fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel("交易日", fontsize=12)
    ax.set_ylabel("资金（元）", fontsize=12)

    # 格式化Y轴显示
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))

    # 设置网格
    ax.grid(True, alpha=0.3)

    # 旋转X轴标签
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

    # 添加统计信息
    if balances:
        start_balance = balances[0]
        end_balance = balances[-1]
        max_balance = max(balances)
        min_balance = min(balances)

        # 计算收益率
        if start_balance != 0:
            return_rate = (end_balance - start_balance) / start_balance * 100
        else:
            return_rate = 0

        # 在图上添加统计信息
        stats_text = f"起始资金: {start_balance:,.0f}元\n"
        stats_text += f"最终资金: {end_balance:,.0f}元\n"
        stats_text += f"最高资金: {max_balance:,.0f}元\n"
        stats_text += f"最低资金: {min_balance:,.0f}元\n"
        stats_text += f"收益率: {return_rate:+.2f}%"

        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round',
                facecolor='wheat', alpha=0.8), fontsize=10)

    fig.tight_layout()
    return fig

def update_plot(*args):
    """更新图表显示"""
    try:
        # 清除旧图表
        plt.close('all')

        # 创建新图表
        new_fig = plot_balance(selected_account.get())

        # 更新canvas
        canvas.figure = new_fig
        canvas.draw()

        # 更新状态栏信息
        update_status_bar()

    except Exception as e:
        print(f"更新图表失败: {e}")

def update_status_bar():
    """更新状态栏信息"""
    account = selected_account.get()
    if account in account_balances:
        balances = account_balances[account]
        if balances:
            start_balance = balances[0]
            end_balance = balances[-1]
            return_rate = (end_balance - start_balance) / start_balance * 100 if start_balance != 0 else 0

            status_text = f"账户: {account} | 起始: {start_balance:,.0f}元 | 最终: {end_balance:,.0f}元 | 收益率: {return_rate:+.2f}%"
            status_label.config(text=status_text)

def export_chart():
    """导出图表"""
    try:
        from tkinter import filedialog, messagebox

        filename = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("JPG files", "*.jpg"), ("All files", "*.*")],
            title="保存图表",
            initialname=f"{selected_account.get()}_资金曲线"
        )

        if filename:
            fig = plot_balance(selected_account.get())
            fig.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)
            messagebox.showinfo("成功", f"图表已保存到:\n{filename}")

    except Exception as e:
        from tkinter import messagebox
        messagebox.showerror("错误", f"保存失败: {e}")

# 创建主框架
main_frame = ttk.Frame(root)
main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

# 控制面板
control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding=10)
control_frame.pack(fill=tk.X, pady=(0, 10))

# 账户选择
ttk.Label(control_frame, text="选择账户:").pack(side=tk.LEFT, padx=(0, 5))
account_combo = ttk.Combobox(control_frame, textvariable=selected_account,
                            values=accounts, state="readonly", width=25)
account_combo.pack(side=tk.LEFT, padx=(0, 10))
account_combo.bind('<<ComboboxSelected>>', update_plot)

# 按钮
ttk.Button(control_frame, text="刷新图表", command=update_plot).pack(side=tk.LEFT, padx=(0, 5))
ttk.Button(control_frame, text="导出图片", command=export_chart).pack(side=tk.LEFT, padx=(0, 5))

# 账户数量信息
info_text = f"共 {len(accounts)} 个账户，{len(dates)} 个交易日"
ttk.Label(control_frame, text=info_text, foreground="gray").pack(side=tk.RIGHT)

# 图表框架
chart_frame = ttk.LabelFrame(main_frame, text="资金曲线图", padding=5)
chart_frame.pack(fill=tk.BOTH, expand=True)

# 初始绘图
fig = plot_balance(accounts[0])
canvas = FigureCanvasTkAgg(fig, master=chart_frame)
canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

# 状态栏
status_frame = ttk.Frame(main_frame)
status_frame.pack(fill=tk.X, pady=(5, 0))

status_label = ttk.Label(status_frame, text="", foreground="blue", font=("Arial", 9))
status_label.pack(side=tk.LEFT)

# 初始化状态栏
update_status_bar()

# 运行主循环
try:
    root.mainloop()
except KeyboardInterrupt:
    print("\n程序被用户中断")
except Exception as e:
    print(f"程序运行错误: {e}")
finally:
    plt.close('all')  # 确保关闭所有图表