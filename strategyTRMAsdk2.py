import copy
from myfunction import *
import pandas as pd
from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma
from tradefuncs import *
from strategies.alive_orders_save_and_replay import save_alive_orders, replay_alive_orders
import time
from utils.utils import tradingTime, is_contract_trading_time, get_time_period, is_trading_time, get_contract_trading_times

test = False

try:
    from speaktextng import speak_text
except:
    from speaktext import speak_text

from loguru import logger as mylog

from time import sleep
import time
import sys


def disp_day_info(daymean, last3mean, hlmax, homean, olmean):
    print('最大价差:', hlmax, '平均价差:', daymean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:', int(olmean))


def disp_0Day_info(quote):
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    gapcover = False

    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    print(quote.datetime, quote.instrument_id, '缺口：', gap, '回补:', gapcover, '现价:', quote.last_price, 'BB:', BB, '今日价差：',
          dayvib,
          '上价差:', upvib, '下价差:', dnvib)


def disp_bars_info(bars):
    bars = bars.dropna()
    symbol = bars.iloc[0].symbol
    interval = bars.iloc[0].duration

    hl = bars.high - bars.low
    ho = bars.high - bars.open
    ol = bars.open - bars.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())
    print('合约:', symbol, '周期:', interval, '最大价差:', hlmax, '平均价差:', daymean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:',
          int(olmean))


def trmastrategy(api, symbol, interval, single_volume, bklimit, sklimit, period=13):
    # todo: 解锁模式, 开盘检查如果bkvol == bklimit, skvol==sklimit, 则当天进入解锁模式,不开仓.

    strategyname = 'trma'
    acct = api.get_account()
    try:
        userid = acct.user_id.split('-')[0]
    except:
        userid = acct._user_key

    logfilename = '_'.join([userid, symbol, strategyname])
    mylog.add(logfilename + '.log', encoding='utf-8')
    savebars = False
    quote = api.get_quote(symbol)
    highLimit = quote.upper_limit
    lowlimit = quote.lower_limit

    if len(symbol) < 3:
        symbol = api.query_cont_quotes(product_id=symbol)

    if '@' in symbol:
        SYMBOL = quote.underlying_symbol
    else:
        SYMBOL = symbol

    trading_symbol = SYMBOL.split('.')[1]

    ticks = api.get_tick_serial(SYMBOL)
    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines

    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()
    hl = daybar.high - daybar.low
    ho = daybar.high - daybar.open
    ol = daybar.open - daybar.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    N = 23
    O = klines1.open
    H = klines1.high
    L = klines1.low
    C = klines1.close

    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    KNUM = len(klines1)
    gapcover = False

    position = api.get_position(SYMBOL)
    acc = api.get_account()
    bkvol = position.pos_long
    bkvol_yd = position.pos_long_his
    bkvol_td = position.pos_long_today

    skvol = position.pos_short
    skvol_yd = position.pos_short_his
    skvol_td = position.pos_short_today

    skflag = True
    bkflag = True
    spflag = True
    bpflag = True
    speakyn = True
    dayopensp = True
    dayopenbp = True

    fundavailable = acc.available
    disp_day_info(daymean, last3mean, hlmax, homean, olmean)
    replay_orders = False
    last_order_price = 0
    while True:

        dthour = time.localtime().tm_hour
        dtmin = time.localtime().tm_min
        # 使用通用的合约交易时间检查系统
        is_trading = is_contract_trading_time(symbol)

        if not is_trading:
            # 获取合约的交易时间信息用于显示
            trading_periods = get_contract_trading_times(symbol)
            periods_str = ', '.join([f"{p[0]}-{p[1]}" for p in trading_periods])
            print(f'非交易时间,合约:{symbol},交易时间:{periods_str},当前时间:{time.asctime()}')
            time.sleep(1)
            continue

            # api.close()
            # return
        else:
            if replay_orders:
                replay_alive_orders(api)
                replay_orders = False

        api.wait_update()

        tradingMode = True

        if bkvol == 0 or skvol == 0:
            tradingMode = True
        if bkvol >= bklimit and skvol >= sklimit:
            tradingMode = False

        if api.is_changing(daybars.iloc[-1], "datetime"):
            savebars = True
            dayopensp = True
            dayopenbp = True

            hl = daybar.high - daybar.low
            ho = daybar.high - daybar.open
            ol = daybar.open - daybar.low
            daymean = int(hl.mean())
            homean = ho.mean()
            olmean = ol.mean()
            hlmax = max(hl)
            last3mean = int(hl.iloc[-4:-1].mean())
            dayopen = quote.open
            dayhigh = quote.highest
            daylow = quote.lowest
            preclose = quote.pre_close
            dayvib = dayhigh - daylow
            gap = dayopen - preclose
            gapcover = False
            print('日线数据更新完成。。。')
            mylog.info('日线数据更新完成。。。')

        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            # 恢复下单标志, 确保k线周期内如果有多个信号的话,执行一次.
            spflag = True
            disp_day_info(daymean, last3mean, hlmax, homean, olmean)
            disp_0Day_info(quote)

            speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                newk = newk.to_frame()
                newk = newk.T
                klines1 = pd.concat([klines1, newk], ignore_index=True)

            disp_bars_info(klines1)

            O = klines1.open
            H = klines1.high
            L = klines1.low
            C = klines1.close

            hl = ema(H - L, 23).iloc[-1]
            cover_gap = max(int(hl), 5)

            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today
            # skfreeze = position.volume_short_frozen

            trmac = trma(C, period)

            ups = crossup(C, trmac)
            dns = crossdown(C, trmac)
            bkdist = be_apart_from(ups.tolist())
            skdist = be_apart_from(dns.tolist())
            if bkdist > skdist:
                signalnow = 'SK'
                distnow = skdist
            else:
                signalnow = 'BK'
                distnow = bkdist

            signalprice = C.iloc[-distnow]
            sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
            print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)
            uplist = ups.tolist()
            average_signal_distance = int(len(uplist) / (uplist.count(1)) / 2)
            print('平均信号距离：', average_signal_distance)
            print('upsignal:  ', ups.iloc[-40:].tolist())
            print('downsignal:', dns.iloc[-40:].tolist())

            print('bklimit:', bklimit, 'bkvol:', bkvol, 'sklimit:', sklimit, 'skvol:', skvol)

            acct = api.get_account()
            yuee = acct.available

            # 交易部分
            basevalue = average_signal_distance
            order_volume = (basevalue - distnow) * single_volume if distnow < average_signal_distance else single_volume
            # order_volume = single_volume

            if C.iloc[-1] > trmac.iloc[-1]:
                print('下多单信号区间。。。', '开盘净值：', acc.pre_balance, '当前净值：', acc.balance, '可用保证金：', acc.available, 'bkvol:', bkvol, 'bklimit:', bklimit)
                if bkvol < bklimit:  # and hlmax < daymean
                    print('持仓小于持仓限额，下多单。。。')

                    bkprice = quote.last_price + 1

                    # 涨跌停价格限制
                    if bkprice > highLimit:
                        bkprice = highLimit
                    if bkprice < lowlimit:
                        bkprice = lowlimit

                    if abs(bkprice-last_order_price)>=0:
                        BK(api, symbol=SYMBOL, order_price=bkprice, volume=order_volume)
                        last_order_price = bkprice
                        mylog.info(['bk', SYMBOL, bkprice, order_volume, cover_gap])

                        spprice = max(H.iloc[-2], bkprice + cover_gap)

                        SP(api, symbol=SYMBOL, order_price=spprice, volume=order_volume, today_prefer=True)
                        spflag = False
                        mylog.info(['sp', SYMBOL, spprice, order_volume, spprice - bkprice])
                        bkflag = False

                    else:
                        print('和上次下单价格差距太小.不下单')


                elif position.float_profit_long < 0 and yuee > quote.last_price * 10 * 1000 and bkvol < 630000:
                    skprice = quote.last_price
                    order_volume = 1
                    if abs(skprice - last_order_price) >=0:
                        SK(api, symbol=SYMBOL, order_price=skprice, volume=order_volume)
                        last_order_price = skprice
                        mylog.info(['bk', SYMBOL, skprice, order_volume, cover_gap])

                        bprice = min(L.iloc[-2], skprice - cover_gap)

                        BP(api, symbol=SYMBOL, order_price=bprice, volume=order_volume, today_prefer=True)
                        # spflag = False
                        # mylog.info(['sp', SYMBOL, spprice, order_volume, spprice - bkprice])
                        # bkflag = False

                    else:
                        print('和上次下单价格差距太小.不下单')
                    #
                    BK(api, symbol=SYMBOL, order_price=bkprice, volume=order_volume)
                    mylog.info(['bk', SYMBOL, bkprice, order_volume])

                    # spprice = max(H.iloc[-2], int(position.open_price_long) + 10)
                    spprice = bkprice + 10
                    SP(api, symbol=SYMBOL, order_price=spprice, volume=order_volume, today_prefer=True)
                    mylog.info(['sp', SYMBOL, spprice, order_volume])


                else:

                    print('持仓限额:', bklimit, '多单持仓:', bkvol, '持仓超过持仓限额，不再下单。。。')

            if C.iloc[-1] < trmac.iloc[-1]:
                print('下空单信号区间。。。', '开盘净值：', acc.pre_balance, '当前净值：', acc.balance, '可用保证金：',
                      acc.available, 'skvol:', skvol, 'sklimit:', sklimit)


                if skvol < sklimit and yuee > 10000:  # and hlmax < daymean:
                    print('持仓小于持仓限额， 下空单。。。')
                    skprice = quote.last_price

                    # 涨跌停价格限制
                    if skprice > highLimit:
                        skprice = highLimit
                    if skprice < lowlimit:
                        skprice = lowlimit

                    SK(api, symbol=SYMBOL, order_price=quote.last_price - 1, volume=order_volume)

                    bpprice = min(L.iloc[-2], skprice - cover_gap - 1)
                    # 涨跌停价格限制
                    if bpprice > highLimit:
                        bpprice = highLimit
                    if bpprice < lowlimit:
                        bpprice = lowlimit
                    #
                    # if position.float_profit_short < 0:
                    #     bpprice = min(L.iloc[-2], int(position.open_price_short) - 50)
                    # else:
                    # bpprice = max(L.iloc[-2], skprice - cover_gap)
                    bpprice = skprice -5
                    BP(api, symbol=SYMBOL, order_price=bpprice, volume=order_volume, today_prefer=True)
                    mylog.info(['sk', SYMBOL, skprice, order_volume, cover_gap])
                    mylog.info(['bp', SYMBOL, bpprice, order_volume, skprice - bpprice])

                elif position.float_profit_short < 0 and yuee > quote.last_price * 10 * 1000 and skvol < sklimit:
                    skprice = quote.last_price
                    order_volume = 10
                    SK(api, symbol=SYMBOL, order_price=skprice, volume=order_volume)
                    mylog.info(['sk', SYMBOL, skprice, order_volume])

                    # bpprice = min(L.iloc[-2], - 10)
                    bpprice = min(L.iloc[-2], skprice - cover_gap)
                    BP(api, symbol=SYMBOL, order_price=bpprice, volume=order_volume, today_prefer=True)
                    mylog.info(['bp', SYMBOL, bpprice, order_volume])

                else:
                    print('持仓限额：', sklimit, '持仓：', skvol, '持仓超过持仓限额,或者余额不足， 不再下单。。。。', acct.available)

        if api.is_changing(quote):
            if gap > 0:
                if quote.lowest < quote.pre_close:
                    gapcover = True
            else:
                if quote.highest > quote.pre_close:
                    gapcover = True

        # if api.is_changing(position):
        #     bkvol = position.pos_long
        #     bkvol_yd = position.pos_long_his
        #     bkvol_td = position.pos_long_today
        #
        #     skvol = position.pos_short
        #     skvol_yd = position.pos_short_his
        #     skvol_td = position.pos_short_today

        dthour = time.localtime().tm_hour
        dtmin = time.localtime().tm_min

        if (dthour == 15 and (0 < dtmin < 5)) or (dthour == 2 and (30 < dtmin < 35)):
            if save_orders:
                save_alive_orders(api)
                save_orders = False
                replay_alive_orders = True

            klines1.to_csv(SYMBOL + '.csv')
            # api.close()
            mylog.info('非交易时间.')
            # sys.exit(0)


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    # symbol = 'SHFE.ag2205'
    # symbol = 'CZCE.SR101'
    # symbol = 'DCE.pp2105'
    # symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 3000000
    sklimit = 3000
    single_volume = 500

    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123", disable_print=True, debug=False)
    symbol = api.query_cont_quotes(product_id='OI')[0]

    trmastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
