import copy
import sys
import pandas as pd
from strategies.load_self_build_packages import *
from tqsdk import TqApi, TqKq
from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma

from loguru import logger as mylog
from time import sleep
import time


def disp_day_info(daymean, last3mean, hlmax, homean, olmean):
    print('最大价差:', hlmax, '平均价差:', daymean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:',
          int(olmean))


def disp_0Day_info(quote):
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    gapcover = False

    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    print(quote.datetime, quote.instrument_id, '缺口：', gap, '回补:', gapcover, '现价:', quote.last_price, 'BB:', BB,
          '今日价差：',
          dayvib,
          '上价差:', upvib, '下价差:', dnvib)


def MaCrossCaculate(bars, period, quote):
    C = bars.close
    SYMBOL = bars.symbol.iloc[0]
    interval = bars.duration.iloc[0]

    trmac = ma(C, period)
    ups = crossup(C, trmac)
    dns = crossdown(C, trmac)

    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)
    if bkdist > skdist:
        signalnow = 'SK'
        distnow = skdist
    else:
        signalnow = 'BK'
        distnow = bkdist
    updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    signalprice = C.iloc[-distnow]
    sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
    print('时间:', updatetime, SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice,
          '现价:',
          C.iloc[-1], '信号盈亏:', sigfloatprofit)
    uplist = ups.tolist()
    average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
    print('平均信号距离：', average_signal_distance)
    print('upsignal:  ', ups.iloc[-40:].tolist())
    print('downsignal:', dns.iloc[-40:].tolist())

    updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    signal = {'合约': SYMBOL, '周期': interval, '时间': updatetime, '当前信号': signalnow,
              '持续周期': distnow, '信号价格': signalprice, '现价': C.iloc[-1],
              '信号盈亏': sigfloatprofit}

    return signal


def get_Signals_Ma(bars, period=13):
    CLOSE = bars.close.values
    N = period

    ups = list(CROSS(CLOSE, MA(CLOSE, N)))
    dns = list(CROSS(MA(CLOSE, N), CLOSE))

    # dnsn = [-1 if x == 1 else x for x in dns]
    signals = []

    for i in range(len(ups)):
        if ups[i] == 0 and dns[i] == 0:
            signals.append(0)

        else:
            if ups[i] == 1:
                signals.append(1)
            if dns[i] == 1:
                signals.append(-1)

    # bsigs = ups.count(True)
    # ssigs = dns.count(True)
    # avgdistance = len(ups) / (bsigs + ssigs)
    return signals


def disp_account(acct):
    acct_info = dict(acct)
    print(acct_info)


def update_bars(bars, newbar):
    # bars: k线序列, newbar, 更新的k线序列
    newk = newbar[-2]
    bdt = bars.datetime.tolist()
    tt = newk.datetime

    if tt in bdt:
        mylog.info('发现重复数据, 跳过...', time_to_str(tt))
    else:
        newk = newk.to_frame()
        newk = newk.T
        bars = pd.concat([bars, newk], ignore_index=True)

    return bars


def check_bk_trade_prices(skpricelist, bkprice):
    found = False
    index = False
    for i in range(len(skpricelist)):
        if skpricelist[i] >= bkprice:
            index = i
            print('多单信号:', bkprice, '上一个座空价格:', skpricelist[i])
            found = True
            break
    return found, index


def check_sk_trade_prices(bkpricelist, skprice):
    found = False
    index = False
    for i in range(len(bkpricelist)):
        if bkpricelist[i] <= skprice:
            index = i
            print('空单信号:', skprice, '上一个多单价格:', bkpricelist[i])
            found = True
            break
    return found, index


class TimeRose:
    def __init__(self, account, symbol, interval, single_volume, bklimit, sklimit, period=13):
        print('系统开始初始化。')
        self.acct = account
        self.symbol = symbol
        self.singlevolume = single_volume
        self.bklimit = bklimit
        self.sklimit = sklimit
        self.interval = interval
        self.period = period

        self.api = TqApi(TqKq(), auth=self.acct, disable_print=True)
        # self.api = TqApi(TqSim(), backtest=TqBacktest(start_dt=date(2022, 10, 1), end_dt=date(2022, 12, 1)), auth=TqAuth("follower","ftp123"))
        self.account = self.api.get_account()
        self.position = self.api.get_position(self.symbol)
        self.quote = self.api.get_quote(self.symbol)
        self.klines1 = self.api.get_kline_serial(self.symbol, duration_seconds=self.interval, data_length=8964).dropna()

        self.klines_tmp = self.api.get_kline_serial(self.symbol, interval, 10)
        self.bars = copy.deepcopy(self.klines1)
        del self.klines1

        # 获得日线数据的相关信息
        self.daybars = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 60 * 24, data_length=365)
        daybar = self.daybars.dropna()
        hl = daybar.high - daybar.low
        ho = daybar.high - daybar.open
        ol = daybar.open - daybar.low
        self.hlmax = max(hl)
        self.daymean = int(hl.mean())
        self.homean = ho.mean()
        self.olmean = ol.mean()
        self.last3mean = int(hl.iloc[-4:-1].mean())

        self.bkpricelist = []
        self.skpricelist = []
        self.daybkpricelist = []
        self.dayskpricelist = []

        self.orderlist = []

    def onsignal(self, signal):

        if signal == 1:
            print(self.position)
            if self.position.pos_long < self.bklimit:
                orderid = self.api.insert_order(self.symbol, direction='BUY', offset='OPEN', volume=1,
                                                limit_price=self.quote.last_price)
                self.orderlist.append(orderid)
                mylog.info(['发出做多信号，下多单。', 'price:', self.quote.last_price, 'volume:', 1])
            else:
                mylog.info('bklimit reached...')
        if signal == -1:
            print(self.position)
            if self.position.pos_short < self.sklimit:
                orderid = self.api.insert_order(self.symbol, direction='SELL', offset='OPEN', volume=1,
                                                limit_price=self.quote.last_price)
                self.orderlist.append(orderid)
                mylog.info(['发出做空信号， 下空单', 'price:', self.quote.last_price, 'volume:', 1])
            else:
                mylog.info('sklimit reached....')

    def onbar(self, bars):
        symbol = bars.iloc[-1].symbol
        duration = bars.iloc[-1].duration
        symbolname = symbol.split('.')[1]
        interval = int(duration / 60)

        signals = get_Signals_Ma(bars)

        print(symbol, duration, signals[-40:])

        if signals[-1] == 1:
            self.onsignal(signals[-1])
            mylog.info(symbol, duration, 'signal', signals[-1], self.quote.last_price)
            speak_text(symbolname + str(interval) + '分钟' + '发出做多信号。')

        if signals[-1] == -1:
            self.onsignal(signals[-1])
            mylog.info(symbol, duration, 'signal', signals[-1], self.quote.last_price)
            speak_text(symbolname + str(interval) + '分钟' + '发出做空信号。')

    def update_bars(self, bars, bar):
        barsduration = bars.iloc[0].duration
        tmpbarduration = bar.duration
        if barsduration == tmpbarduration:
            tt = bar.datetime
            print(time_to_str(bar.datetime), bar.symbol, bar.duration)
            # print(type(newk.duration))
            bdt = bars.datetime.tolist()
            if tt not in bdt:
                newktmp = bar.to_frame()
                newktmp = newktmp.T
                bars = pd.concat([bars, newktmp], ignore_index=True)
                print(bar.duration, self.barupdateinfo)
                # speak_text(str(newktmp.duration)+'updated.')
            else:
                print('发现重复数据，跳过。。。')

        else:
            print('k线周期不一致，跳过， 请检查程序和数据。')

        return bars

    def run(self):

        while True:

            self.api.wait_update()

            if self.api.is_changing(self.daybars.iloc[-1], "datetime"):
                savebars = True
                dayopensp = True
                dayopenbp = True

                daybkpricelist = []
                dayskpricelist = []

                daybar = copy.deepcopy(self.daybars)
                hl = daybar.high - daybar.low
                ho = daybar.high - daybar.open
                ol = daybar.open - daybar.low

                self.daymean = int(hl.mean())
                self.homean = ho.mean()
                self.olmean = ol.mean()
                self.hlmax = max(hl)
                self.last3mean = int(hl.iloc[-4:-1].mean())

                quote = copy.deepcopy(self.quote)
                dayopen = quote.open
                dayhigh = quote.highest
                daylow = quote.lowest
                preclose = quote.pre_close
                dayvib = dayhigh - daylow
                gap = dayopen - preclose
                gapcover = False
                print('日线数据更新完成。。。')
                mylog.info('日线数据更新完成。。。')

            if self.api.is_changing(self.klines_tmp.iloc[-1], "datetime"):
                disp_day_info(self.daymean, self.last3mean, self.hlmax, self.homean, self.olmean)
                print('balance:', self.account.balance, 'poslong:', self.position.pos_long, 'posshort:',
                      self.position.pos_short,
                      'poslong float profit:', self.position.float_profit_long, 'posshort float profit:',
                      self.position.float_profit_short)

                speakyn = True

                newk = self.klines_tmp.iloc[-2]
                self.bars = self.update_bars(self.bars, newk)
                self.onbar(self.bars)

            if self.api.is_changing(self.quote):
                disp_0Day_info(self.quote)

            if self.api.is_changing(self.position):
                bkvol = self.position.pos_long
                bkvol_yd = self.position.pos_long_his
                bkvol_td = self.position.pos_long_today

                skvol = self.position.pos_short
                skvol_yd = self.position.pos_short_his
                skvol_td = self.position.pos_short_today

            hr = time.localtime().tm_hour
            mi = time.localtime().tm_min
            if ((time.localtime().tm_hour == 15 and time.localtime().tm_min > 15) or (
                    time.localtime().tm_hour == 23 and time.localtime().tm_min > 30)):
                self.klines1.to_csv(self.symbol + '.csv')
                self.api.close()
                mylog.info('no trading time, quit.')
                sys.exit(0)


#
if __name__ == "__main__":
    strategy = TimeRose('walkquant,ftp123', 'CZCE.OI501', interval=60, single_volume=1, bklimit=60, sklimit=60)
    strategy.run()
