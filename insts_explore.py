#合约信息探索
import time

from instrumentinfo import disp_bars_info, disp_0Day_info
from tqsdk import TqApi, TqKq

if __name__=='__main__':

    tradeinstid = 'OI'
    interval = 15
    bklimit = 500
    sklimit = 500
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="wolfquant,ftp123", disable_print=True, debug=False)
    symbols = api.query_cont_quotes()
    for symbol in symbols:
        syminfo = api.query_symbol_info(symbol)
        quote = api.get_quote(symbol)

        try:
            bars = api.get_kline_serial(symbol, duration_seconds=60 * 60 * 24, data_length=365)
        except:
            continue

        print('-'*50)
        disp_bars_info(bars)
        disp_0Day_info(quote)

        time.sleep(1)

    api.close()



