import copy
import os
from tqsdk import TqApi, TqAccount, TqKq
from tqsdk.tafunc import time_to_str
# from tradefuncs import *
from loguru import logger as mylog
import pandas as pd

from strategies.weiswave_claude_dataframe import calculate_signals, wave_signal_trading

mylog.add('weiswave' + '.log', encoding='utf-8')


def get_data_filename(symbol, interval):
    """生成数据文件名"""
    # 清理合约代码中的特殊字符
    clean_symbol = symbol.replace('.', '_').replace('@', '_').replace(':', '_')
    return f"{clean_symbol}_{interval}.csv"


def load_historical_data(filename):
    """从文件加载历史数据"""
    if os.path.exists(filename):
        try:
            df = pd.read_csv(filename)
            # 确保datetime列是正确的类型
            if 'datetime' in df.columns:
                df['datetime'] = pd.to_datetime(df['datetime'])
                # 删除datetime为NaN的行
                df = df.dropna(subset=['datetime'])
            mylog.info(f"成功加载历史数据: {filename}, 数据量: {len(df)}")
            return df
        except Exception as e:
            mylog.error(f"加载历史数据失败: {filename}, 错误: {e}")
            return None
    else:
        mylog.info(f"历史数据文件不存在: {filename}")
        return None


def save_data_to_file(df, filename):
    """保存数据到文件"""
    try:
        df.to_csv(filename, index=False)
        mylog.info(f"数据已保存到: {filename}, 数据量: {len(df)}")
    except Exception as e:
        mylog.error(f"保存数据失败: {filename}, 错误: {e}")



def merge_historical_and_new_data(historical_df, new_df):
    """合并历史数据和新数据，去重并排序"""
    if historical_df is None or len(historical_df) == 0:
        mylog.info("无历史数据，使用新获取的数据")
        return new_df.copy()

    if new_df is None or len(new_df) == 0:
        mylog.info("无新数据，使用历史数据")
        return historical_df.copy()

    try:
        # 确保两个DataFrame的datetime列都是正确的类型
        if 'datetime' in historical_df.columns:
            historical_df['datetime'] = pd.to_datetime(historical_df['datetime'])
        if 'datetime' in new_df.columns:
            new_df['datetime'] = pd.to_datetime(new_df['datetime'])

        # 检查并处理NaN值
        historical_df = historical_df.dropna(subset=['datetime'])
        new_df = new_df.dropna(subset=['datetime'])

        # 合并数据
        combined_df = pd.concat([historical_df, new_df], ignore_index=True)

        # 按datetime去重，保留最后出现的数据
        combined_df = combined_df.drop_duplicates(subset=['datetime'], keep='last')

        # 按时间排序
        combined_df = combined_df.sort_values('datetime').reset_index(drop=True)

        new_data_count = len(combined_df) - len(historical_df)
        mylog.info(f"数据合并完成: 历史数据{len(historical_df)}条, 新增数据{new_data_count}条, 总计{len(combined_df)}条")

        return combined_df

    except Exception as e:
        mylog.error(f"数据合并失败: {e}")
        # 如果合并失败，返回新数据
        mylog.info("使用新获取的数据作为备选方案")
        return new_df.copy()

if __name__ == "__main__":

    product = 'OI'
    symbol = product
    interval = 60
    long_limit = 1000
    short_limit = 1000
    single_volume = 1
    close_today_first = False  # 是否优先平今仓（True=优先平今，False=优先平昨）

    # from accounts_zjy import hsqhzjy as acct

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    api = TqApi(TqKq(), auth="bigwolf,ftp123")
    symbol = api.query_cont_quotes(product_id=product)[0]
    print(symbol)

    # 生成数据文件名
    data_filename = get_data_filename(symbol, interval)
    mylog.info(f"数据文件名: {data_filename}")

    # 1. 加载历史数据
    historical_data = load_historical_data(data_filename)

    # 2. 获取初始化数据
    mylog.info("获取初始化K线数据...")
    barsinit = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964)
    bartmp = api.get_kline_serial(symbol, duration_seconds=interval, data_length=10)

    # 3. 合并历史数据和初始化数据
    bars = merge_historical_and_new_data(historical_data, barsinit)

    # 4. 保存合并后的数据
    save_data_to_file(bars, data_filename)

    # 清理临时数据
    del barsinit

    # 计算信号并初始化交易
    df = calculate_signals(bars)
    position_mgr = wave_signal_trading(df, long_limit, short_limit, single_volume, api, close_today_first)
    print(df.signal.iloc[-1], df.G.iloc[-1])

    while True:
        api.wait_update()
        if api.is_changing(bartmp.iloc[-1], "datetime"):

            position = api.get_position(symbol)
            acc = api.get_account()
            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today

            newk = bartmp.iloc[-2]
            bdt = bars.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                # 添加新K线数据
                newk = newk.to_frame()
                newk = newk.T
                bars = pd.concat([bars, newk], ignore_index=True)

                # 每次更新后保存数据到文件
                save_data_to_file(bars, data_filename)
                mylog.info(f"新增K线数据: {time_to_str(tt)}, 总数据量: {len(bars)}")

            # 重新计算信号并执行交易
            wave_data = calculate_signals(bars)
            position_mgr = wave_signal_trading(wave_data, long_limit, short_limit, single_volume, api, close_today_first)