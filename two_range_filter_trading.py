import tqsdk


# 回测配置类
# class BackTestConfig(Enum):
#     # 交易品种
#     INSTRUMENT = 'SHFE.cu'
#     # 回测模式（False代表实盘）
#     BACKTEST_MODE = False
#     # 回测数据获取周期
#     DATA_FROM = tqsdk.KLineDataType_1Min
#     # 回测数据获取范围
#     DATA_TO = tqsdk.KLineDataType_10Years
#

# 交易策略类
class TwinRangeFilter:
    def __init__(self, api):
        self.api = api
        self.data = api.get_kline_data(BackTestConfig.INSTRUMENT,
                                        BackTestConfig.DATA_FROM,
                                        BackTestConfig.DATA_TO)
        # 设置K线数据回调函数
        self.api.on_risk_level_changed(self.on_risk_level_changed)
        self.api.on_quote_changed(self.on_quote_changed)
        self.api.on_position_change(self.on_position_change)

    def on_risk_level_changed(self, risk_level):
        print(f'Risk level: {risk_level}')

    def on_quote_changed(self, quote):
        print(f'Quote changed to: {quote}')

    def on_position_change(self, position):
        print(f'Position changed to: {position}')

    # 当前价格计算函数
    def calculate_current_price(self):
        return self.data.get_price()

    # 快速均线计算函数
    def get_fast_sma(self):
        fast_period = 27
        return self.data.get_sma(fast_period) * 1.6

    # 慢速均线计算函数
    def get_slow_sma(self):
        slow_period = 55
        return self.data.get_sma(slow_period) * 2.0

    # 策略实现函数
    def strategy_implementation(self, current_price):
        fast_sma = self.get_fast_sma()
        slow_sma = self.get_slow_sma()
        if not self.api.is_long_position():
            if current_price > (fast_sma + slow_sma) / 2 and \
               ((current_price > self.data.pre_close) or (self.api.get_risk_level() == 'high')):
                return True
        else:
            if self.api.is_short_position():
                if current_price < (fast_sma + slow_sma) / 2 and \
                   ((current_price < self.data.pre_close) or (self.api.get_risk_level() == 'low')):
                    return True

    # 订单执行函数
    def order_execution(self, api):
        position = api.get_position()
        if not api.is_long_position():
            if self.strategy_implementation(api.get_quote().price):
                api.place_order(BackTestConfig.INSTRUMENT,
                                tqsdk.OrderAction.BUY,
                                1.0)
        else:
            if self.api.is_short_position():
                if self.strategy_implementation(api.get_quote().price):
                    api.place_order(BackTestConfig.INSTRUMENT,
                                    tqsdk.OrderAction.SELL,
                                    -1.0)


# 主函数
def main():
    # 初始化TQSDK环境
    tq = tqsdk.TqApi()
    # 实例化交易策略类
    strategy = TwinRangeFilter(tq)
    # 开启实盘模式
    print('Real trading mode enabled')
    # 订单执行函数
    strategy.order_execution(tq)


if __name__ == '__main__':
    main()

