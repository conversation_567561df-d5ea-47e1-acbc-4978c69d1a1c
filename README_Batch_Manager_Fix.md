# 批量管理器修复说明

## 🎯 问题分析

您遇到的批量启动程序问题主要有以下几个原因：

### 1. 进程状态不持久化
**问题**：每次运行脚本都会创建新的 `StrategyManager` 实例，之前启动的进程信息丢失。

**现象**：
```bash
# 启动策略
python start_scheduled_strategies.py start cu
✓ 策略 cu 启动成功 (PID: 1199)

# 停止策略
python start_scheduled_strategies.py stop cu
策略 cu 未在运行  # ← 找不到之前启动的进程
```

### 2. 命令参数错误
**问题**：批量管理脚本使用了错误的命令参数。

**错误代码**：
```python
cmd = [
    sys.executable,
    "TimeRoseMA_cross_ag_MultiTimeFrames.py",
    product,
    mode,  # ← 这里应该是 "schedule" 而不是 mode 变量
    auth
]
```

### 3. 进程检测逻辑问题
**问题**：进程状态检测和管理逻辑不完善。

## ✅ 解决方案

### 1. 添加状态持久化
```python
class StrategyManager:
    def __init__(self):
        self.state_file = "strategy_manager_state.json"
        self.load_state()  # 启动时加载之前的状态
    
    def save_state(self):
        """保存策略状态到文件"""
        state_data = {}
        for product, info in self.processes.items():
            state_data[product] = {
                'pid': info['process'].pid,
                'start_time': info['start_time'].isoformat(),
                'log_file': info['log_file']
            }
        
        with open(self.state_file, 'w') as f:
            json.dump(state_data, f)
    
    def load_state(self):
        """从文件加载策略状态"""
        if os.path.exists(self.state_file):
            with open(self.state_file, 'r') as f:
                state_data = json.load(f)
            
            # 验证进程是否仍在运行
            for product, info in state_data.items():
                pid = info['pid']
                if self.is_process_running(pid):
                    # 重建进程对象
                    process = psutil.Process(pid)
                    self.processes[product] = {
                        'process': process,
                        'start_time': datetime.fromisoformat(info['start_time']),
                        'log_file': info['log_file'],
                        'pid': pid
                    }
```

### 2. 修复命令参数
```python
def start_strategy(self, product: str, auth: str = "quant_ggh,Qiai1301"):
    # 修复后的命令构建
    cmd = [
        sys.executable,
        "TimeRoseMA_cross_ag_MultiTimeFrames.py",
        product,
        auth  # 直接使用默认的共享API模式
    ]
```

### 3. 改进进程管理
```python
def is_process_running(self, pid):
    """使用 psutil 检查进程是否在运行"""
    try:
        process = psutil.Process(pid)
        return process.is_running()
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        return False

def stop_strategy(self, product: str):
    """改进的停止策略逻辑"""
    process = self.processes[product]['process']
    
    if hasattr(process, 'terminate'):
        # subprocess.Popen 对象
        process.terminate()
        process.wait(timeout=10)
    else:
        # psutil.Process 对象
        process.terminate()
        process.wait(timeout=10)
    
    # 更新状态文件
    del self.processes[product]
    self.save_state()
```

## 🚀 修复版本使用

### 1. 使用修复版批量管理器
```bash
# 使用修复版脚本
python start_scheduled_strategies_fixed.py start ag

# 输出示例
============================================================
定时策略批量管理器 (修复版)
============================================================
启动策略: ag
启动命令: python TimeRoseMA_cross_ag_MultiTimeFrames.py ag quant_ggh,Qiai1301
启动 ag 策略...
状态已保存到 strategy_manager_state.json
✓ 策略 ag 启动成功 (PID: 7444)
  日志文件: logs\ag_strategy.log

成功启动 1/1 个策略
```

### 2. 查看运行状态
```bash
python start_scheduled_strategies_fixed.py list

# 输出示例
加载状态文件，找到 1 个策略记录
  恢复策略: ag (PID: 7444)
============================================================
定时策略批量管理器 (修复版)
============================================================
运行中的策略:
  ag: PID 7444, 运行中, 运行时间: 0:02:15
```

### 3. 停止策略
```bash
python start_scheduled_strategies_fixed.py stop ag

# 输出示例
停止策略 ag...
✓ 策略 ag 已停止 (运行时间: 0:05:32)
状态已保存到 strategy_manager_state.json
```

## 📊 功能对比

| 功能 | 原版本 | 修复版本 |
|------|--------|----------|
| **状态持久化** | ✗ 无 | ✅ JSON文件持久化 |
| **进程恢复** | ✗ 无法恢复 | ✅ 自动恢复进程状态 |
| **命令参数** | ✗ 错误参数 | ✅ 正确的命令构建 |
| **进程检测** | ✗ 简单检测 | ✅ psutil精确检测 |
| **错误处理** | ✗ 基础处理 | ✅ 完善的异常处理 |
| **调试信息** | ✗ 信息不足 | ✅ 详细的调试输出 |
| **状态清理** | ✗ 无清理功能 | ✅ 支持状态清理 |

## 🔧 新增功能

### 1. 状态文件管理
```bash
# 清理状态文件
python start_scheduled_strategies_fixed.py cleanup

# 状态文件内容示例
{
  "ag": {
    "pid": 7444,
    "start_time": "2025-06-20T10:30:15.123456",
    "log_file": "logs/ag_strategy.log"
  }
}
```

### 2. 改进的调试信息
```bash
# 启动时的详细信息
加载状态文件，找到 1 个策略记录
  恢复策略: ag (PID: 7444)
启动命令: python TimeRoseMA_cross_ag_MultiTimeFrames.py ag quant_ggh,Qiai1301
状态已保存到 strategy_manager_state.json
```

### 3. 更好的错误处理
```python
# 启动失败时的处理
if process.poll() is not None:
    stdout, stderr = process.communicate()
    print(f"✗ 策略 {product} 启动失败 (返回码: {process.returncode})")
    print("输出:")
    print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
    return False
```

## ⚠️ 注意事项

### 1. 依赖库
修复版本需要 `psutil` 库：
```bash
pip install psutil
```

### 2. 状态文件
- 状态文件：`strategy_manager_state.json`
- 自动创建和维护
- 可手动删除重置状态

### 3. 进程生命周期
- 策略进程可能因为网络问题等原因快速退出
- 修复版本会检测并报告进程状态
- 建议检查日志文件了解退出原因

### 4. 使用建议
```bash
# 推荐的使用流程
python start_scheduled_strategies_fixed.py cleanup  # 清理旧状态
python start_scheduled_strategies_fixed.py start ag # 启动策略
python start_scheduled_strategies_fixed.py list     # 检查状态
python start_scheduled_strategies_fixed.py stop ag  # 停止策略
```

## 🎯 问题排查

### 1. 策略快速退出
如果策略启动后立即退出，检查：
- 网络连接是否正常
- TqSDK认证是否有效
- 查看日志文件了解具体错误

### 2. 进程状态不一致
如果出现状态不一致：
```bash
# 强制清理状态
python start_scheduled_strategies_fixed.py cleanup

# 重新启动
python start_scheduled_strategies_fixed.py start ag
```

### 3. 权限问题
在某些系统上可能需要管理员权限来检测进程状态。

## 🎉 总结

修复版批量管理器解决了原版本的主要问题：

✅ **状态持久化**：进程信息不会丢失  
✅ **正确的命令参数**：使用正确的策略启动命令  
✅ **精确的进程管理**：基于psutil的进程检测  
✅ **完善的错误处理**：详细的错误信息和调试输出  
✅ **状态恢复**：重启脚本后自动恢复进程状态  

现在批量管理器可以正常工作，支持启动、停止、列出和检查策略状态！

---

**修复文件**: `start_scheduled_strategies_fixed.py`  
**调试文件**: `debug_batch_manager.py`  
**状态文件**: `strategy_manager_state.json`
