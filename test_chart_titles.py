"""
测试图表标题修复
验证不同策略的图表标题是否正确显示
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from loguru import logger
from llt_multi_contract_analyzer import (
    ContractConfig, ContractAnalysisResult, 
    plot_equity_curves_from_results
)

def create_mock_results(strategy_type="LLT"):
    """创建模拟分析结果"""
    contracts = [
        ContractConfig("CZCE.OI509", "菜籽油主力", "CZCE"),
        ContractConfig("SHFE.cu2510", "沪铜主力", "SHFE"),
        ContractConfig("DCE.m2509", "豆粕主力", "DCE"),
        ContractConfig("SHFE.au2510", "沪金主力", "SHFE"),
        ContractConfig("DCE.i2509", "铁矿石主力", "DCE"),
    ]
    
    results = []
    
    # 根据策略类型创建不同的模拟数据
    if strategy_type == "LLT":
        mock_data = [
            (11.73, 100.0, 40, 14, 0.133333),
            (8.45, 85.2, 156, 35, 0.055556),
            (6.78, 68.9, 142, 28, 0.066667),
            (5.92, 65.4, 138, 42, 0.045455),
            (4.44, 26.7, 576, 63, 0.03125),
        ]
    elif strategy_type == "MA":
        mock_data = [
            (9.23, 75.5, 85, 13, 0.0),
            (7.15, 68.2, 92, 15, 0.0),
            (5.89, 62.1, 78, 18, 0.0),
            (4.67, 58.9, 65, 21, 0.0),
            (3.45, 55.2, 58, 25, 0.0),
        ]
    elif strategy_type == "DualMA":
        mock_data = [
            (8.76, 72.3, 45, 5, 13),
            (6.54, 65.8, 52, 7, 15),
            (5.23, 61.4, 38, 8, 18),
            (4.12, 58.7, 42, 6, 20),
            (3.01, 54.9, 35, 9, 22),
        ]
    else:
        mock_data = [(0, 0, 0, 0, 0)] * 5
    
    for i, (return_rate, win_rate, total_trades, param1, param2) in enumerate(mock_data):
        if i < len(contracts):
            contract = contracts[i]
            
            # 创建模拟的权益曲线数据
            equity_curve = [0.0]
            for j in range(1, 51):
                # 模拟权益变化，最终达到目标收益
                change = (return_rate * 100 / 50) + np.random.randn() * 2
                equity_curve.append(equity_curve[-1] + change)
            
            equity_dates = [datetime.now() + timedelta(minutes=5*j) for j in range(51)]
            
            result = ContractAnalysisResult(
                contract=contract,
                best_d_value=int(param1),
                best_alpha=param2,
                total_trades=total_trades,
                total_pnl=return_rate * 100,
                win_rate=win_rate,
                return_rate=return_rate,
                winning_trades=int(total_trades * win_rate / 100),
                losing_trades=int(total_trades * (100 - win_rate) / 100),
                avg_win=20.0,
                avg_loss=-10.0,
                profit_factor=2.0,
                max_drawdown=50.0,
                sharpe_ratio=1.2,
                analysis_period_days=365,
                data_quality_score=95.0,
                equity_curve=equity_curve,
                equity_dates=equity_dates
            )
            
            results.append(result)
    
    return results


def test_llt_chart_title():
    """测试LLT策略图表标题"""
    print("=" * 60)
    print("测试LLT策略图表标题")
    print("=" * 60)
    
    try:
        # 创建LLT策略模拟结果
        results = create_mock_results("LLT")
        print(f"创建了 {len(results)} 个LLT策略模拟结果")
        
        # 绘制图表
        print(f"\n📈 绘制LLT策略资金曲线...")
        plot_equity_curves_from_results(
            results, 
            "test_llt_charts", 
            top_n=5, 
            strategy_name="LLT策略"
        )
        
        print(f"✅ LLT策略图表绘制完成")
        print(f"📁 图片保存在: test_llt_charts/top5_equity_curves.png")
        print(f"📊 标题应显示: LLT策略 - 前5名合约资金曲线")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ma_chart_title():
    """测试MA策略图表标题"""
    print("=" * 60)
    print("测试MA策略图表标题")
    print("=" * 60)
    
    try:
        # 创建MA策略模拟结果
        results = create_mock_results("MA")
        print(f"创建了 {len(results)} 个MA策略模拟结果")
        
        # 绘制图表
        print(f"\n📈 绘制MA策略资金曲线...")
        plot_equity_curves_from_results(
            results, 
            "test_ma_charts", 
            top_n=5, 
            strategy_name="MA策略"
        )
        
        print(f"✅ MA策略图表绘制完成")
        print(f"📁 图片保存在: test_ma_charts/top5_equity_curves.png")
        print(f"📊 标题应显示: MA策略 - 前5名合约资金曲线")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dual_ma_chart_title():
    """测试双均线策略图表标题"""
    print("=" * 60)
    print("测试双均线策略图表标题")
    print("=" * 60)
    
    try:
        # 创建双均线策略模拟结果
        results = create_mock_results("DualMA")
        print(f"创建了 {len(results)} 个双均线策略模拟结果")
        
        # 绘制图表
        print(f"\n📈 绘制双均线策略资金曲线...")
        plot_equity_curves_from_results(
            results, 
            "test_dual_ma_charts", 
            top_n=5, 
            strategy_name="DualMA策略"
        )
        
        print(f"✅ 双均线策略图表绘制完成")
        print(f"📁 图片保存在: test_dual_ma_charts/top5_equity_curves.png")
        print(f"📊 标题应显示: DualMA策略 - 前5名合约资金曲线")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_chart_content():
    """测试图表内容"""
    print("=" * 60)
    print("测试图表内容")
    print("=" * 60)
    
    try:
        import matplotlib.pyplot as plt
        
        # 测试matplotlib是否正常工作
        print("✅ matplotlib导入成功")
        
        # 测试中文字体设置
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        print("✅ 中文字体设置成功")
        
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试不同策略的标题
        strategies = ["LLT策略", "MA策略", "DualMA策略"]
        
        for i, strategy in enumerate(strategies):
            ax.clear()
            ax.set_title(f'{strategy} - 前5名合约资金曲线', fontsize=14, fontweight='bold')
            ax.text(0.5, 0.5, f'这是{strategy}的测试图表', ha='center', va='center', transform=ax.transAxes)
            
            # 保存测试图片
            plt.savefig(f'test_{strategy.replace("策略", "")}_title.png', dpi=150, bbox_inches='tight')
            print(f"✅ {strategy}标题测试图片已保存")
        
        plt.close()
        
        print("✅ 图表内容测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("图表标题修复测试")
    print("=" * 80)
    
    tests = [
        ("图表内容", test_chart_content),
        ("LLT策略图表标题", test_llt_chart_title),
        ("MA策略图表标题", test_ma_chart_title),
        ("双均线策略图表标题", test_dual_ma_chart_title),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 图表标题修复测试成功！")
        print("\n📋 现在图表标题会正确显示:")
        print("• LLT策略 → 'LLT策略 - 前5名合约资金曲线'")
        print("• MA策略 → 'MA策略 - 前5名合约资金曲线'")
        print("• DualMA策略 → 'DualMA策略 - 前5名合约资金曲线'")
        print("\n📋 使用方法:")
        print("python llt_multi_contract_analyzer.py --strategy MA --mode quick --plot-curves")
        print("python llt_multi_contract_analyzer.py --strategy DualMA --mode quick --plot-curves")
    else:
        print("⚠️  部分测试失败，图表标题可能还有问题")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
