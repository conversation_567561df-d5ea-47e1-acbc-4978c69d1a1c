"""
************多时间周期策略历史数据回测系统
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

try:
    from tqsdk import TqApi, TqKq
    from multi_timeframe_strategy_tqsdk import (
        StrategyConfig, SignalType, PositionDirection, 
        MarketSignal, Position, MultiTimeFrameStrategy
    )
    TQSDK_AVAILABLE = True
except ImportError:
    print("警告: 部分模块未找到，将使用简化版本")
    TQSDK_AVAILABLE = False


@dataclass
class BacktestResult:
    """回测结果"""
    total_return: float
    return_rate: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    profit_factor: float
    equity_curve: List[float]
    trade_log: List[Dict]


class HistoricalDataManager:
    """历史数据管理器"""
    
    def __init__(self, symbol: str = "<EMAIL>", auth: str = None):
        self.symbol = symbol
        self.auth = auth
        self.api = None
        self.historical_data: Dict[int, pd.DataFrame] = {}
        
    def fetch_historical_data(self, timeframes: List[int], data_length: int = 10000):
        """获取历史数据"""
        print("=" * 60)
        print("获取历史数据用于回测...")
        print("=" * 60)
        
        try:
            if self.auth:
                self.api = TqApi(TqKq(), auth=self.auth, disable_print=True)
            else:
                self.api = TqApi(TqKq(), disable_print=True)
            
            for timeframe in timeframes:
                print(f"获取 {timeframe}秒 周期数据...")
                
                # 计算数据量
                if timeframe == 60:
                    length = data_length
                else:
                    ratio = timeframe / 60
                    length = max(200, int(data_length / ratio))
                
                # 获取数据
                klines = self.api.get_kline_serial(self.symbol, timeframe, length)
                df = klines.copy()
                
                # 添加技术指标
                df = self._add_technical_indicators(df)
                
                self.historical_data[timeframe] = df
                
                if len(df) > 0:
                    start_time = pd.to_datetime(df.datetime.iloc[0])
                    end_time = pd.to_datetime(df.datetime.iloc[-1])
                    print(f"  ✓ {len(df)} 条数据，时间范围: {start_time} 到 {end_time}")
                else:
                    print(f"  ✗ 无数据")
            
            self.api.close()
            print("历史数据获取完成")
            
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            if self.api:
                self.api.close()
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加技术指标"""
        df = df.copy()
        
        # 添加移动平均线
        for period in [5, 10, 20, 30]:
            df[f'ma_{period}'] = df['close'].rolling(period).mean()
        
        # 添加价格变化
        df['price_change'] = df['close'].pct_change()
        df['is_up'] = df['close'] > df['open']
        
        return df
    
    def get_data(self, timeframe: int) -> pd.DataFrame:
        """获取指定时间周期的数据"""
        return self.historical_data.get(timeframe, pd.DataFrame())


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, data_manager: HistoricalDataManager, config: StrategyConfig):
        self.data_manager = data_manager
        self.config = config
        self.initial_capital = 100000
        self.current_capital = self.initial_capital
        
        # 持仓管理
        self.positions: Dict[int, Position] = {}
        for tf in config.timeframes:
            self.positions[tf] = Position(timeframe=tf)
        
        # 回测记录
        self.equity_curve = [self.initial_capital]
        self.trade_log = []
        self.daily_returns = []
        
    def calculate_ma_signals(self, df: pd.DataFrame, timeframe: int, index: int) -> MarketSignal:
        """计算均线信号（回测版本）"""
        if index < max(self.config.short_ma_period, self.config.long_ma_period):
            return MarketSignal(timeframe, SignalType.NONE, 0, datetime.now(), 0, 0, 0)
        
        # 获取当前和前一个数据点
        current_data = df.iloc[:index+1].copy()
        
        # 计算均线
        current_data['ma_short'] = current_data['close'].rolling(self.config.short_ma_period).mean()
        current_data['ma_long'] = current_data['close'].rolling(self.config.long_ma_period).mean()
        current_data['ma_diff'] = current_data['ma_short'] - current_data['ma_long']
        
        current = current_data.iloc[-1]
        previous = current_data.iloc[-2] if len(current_data) > 1 else current
        
        # 判断信号类型
        signal_type = SignalType.NONE
        
        # 金叉死叉信号
        if (previous['ma_short'] <= previous['ma_long'] and 
            current['ma_short'] > current['ma_long']):
            signal_type = SignalType.GOLDEN_CROSS
        elif (previous['ma_short'] >= previous['ma_long'] and 
              current['ma_short'] < current['ma_long']):
            signal_type = SignalType.DEATH_CROSS
        else:
            # 回归信号
            if len(current_data) >= 5:
                recent_diffs = current_data['ma_diff'].tail(5).values
                
                if (current['ma_diff'] < 0 and len(recent_diffs) >= 3 and
                    recent_diffs[-1] > recent_diffs[-2] > recent_diffs[-3]):
                    signal_type = SignalType.UPWARD_REGRESSION
                elif (current['ma_diff'] > 0 and len(recent_diffs) >= 3 and
                      recent_diffs[-1] < recent_diffs[-2] < recent_diffs[-3]):
                    signal_type = SignalType.DOWNWARD_REGRESSION
        
        return MarketSignal(
            timeframe=timeframe,
            signal_type=signal_type,
            price=current['close'],
            timestamp=pd.to_datetime(current['datetime']),
            ma_short=current['ma_short'],
            ma_long=current['ma_long'],
            ma_diff=current['ma_diff']
        )
    
    def get_higher_timeframe_signal(self, current_timeframe: int, index: int) -> Optional[MarketSignal]:
        """获取大周期信号"""
        try:
            current_index = self.config.timeframes.index(current_timeframe)
            if current_index >= len(self.config.timeframes) - 1:
                return None
            
            higher_timeframe = self.config.timeframes[current_index + 1]
            higher_df = self.data_manager.get_data(higher_timeframe)
            
            if len(higher_df) == 0:
                return None
            
            # 找到对应的时间点
            current_df = self.data_manager.get_data(current_timeframe)
            current_time = pd.to_datetime(current_df.iloc[index]['datetime'])
            
            # 在大周期中找到最接近的时间点
            higher_df['datetime_pd'] = pd.to_datetime(higher_df['datetime'])
            valid_indices = higher_df[higher_df['datetime_pd'] <= current_time].index
            
            if len(valid_indices) == 0:
                return None
            
            higher_index = valid_indices[-1]
            return self.calculate_ma_signals(higher_df, higher_timeframe, higher_index)
            
        except Exception as e:
            return None
    
    def should_open_position(self, current_signal: MarketSignal, higher_signal: Optional[MarketSignal], 
                           current_kline: pd.Series) -> Tuple[bool, PositionDirection]:
        """判断是否开仓"""
        if not higher_signal:
            return False, PositionDirection.NONE
        
        # 多仓条件
        if (current_signal.signal_type == SignalType.GOLDEN_CROSS and
            current_kline['close'] > current_kline['open'] and
            higher_signal.signal_type == SignalType.UPWARD_REGRESSION):
            return True, PositionDirection.LONG
        
        # 空仓条件
        elif (current_signal.signal_type == SignalType.DEATH_CROSS and
              current_kline['close'] < current_kline['open'] and
              higher_signal.signal_type == SignalType.DOWNWARD_REGRESSION):
            return True, PositionDirection.SHORT
        
        return False, PositionDirection.NONE
    
    def calculate_stop_loss(self, df: pd.DataFrame, direction: PositionDirection, index: int) -> float:
        """计算止损价格"""
        start_index = max(0, index - self.config.stop_loss_periods + 1)
        recent_data = df.iloc[start_index:index+1]
        
        if direction == PositionDirection.LONG:
            return recent_data['low'].min()
        else:
            return recent_data['high'].max()
    
    def open_position(self, timeframe: int, direction: PositionDirection, 
                     price: float, stop_loss_price: float, timestamp: datetime):
        """开仓"""
        position = self.positions[timeframe]
        if position.direction != PositionDirection.NONE:
            return False
        
        # 计算开仓数量
        available_capital = self.current_capital * self.config.max_position_ratio / len(self.config.timeframes)
        leveraged_capital = available_capital * self.config.max_leverage
        quantity = max(1, int(leveraged_capital / price))
        
        # 更新持仓
        position.direction = direction
        position.entry_price = price
        position.quantity = quantity
        position.stop_loss_price = stop_loss_price
        position.entry_time = timestamp
        
        # 记录交易
        self.trade_log.append({
            'timestamp': timestamp,
            'timeframe': timeframe,
            'action': 'OPEN',
            'direction': direction.value,
            'price': price,
            'quantity': quantity,
            'stop_loss': stop_loss_price
        })
        
        return True
    
    def close_position(self, timeframe: int, price: float, timestamp: datetime, reason: str = ""):
        """平仓"""
        position = self.positions[timeframe]
        if position.direction == PositionDirection.NONE:
            return False
        
        # 计算盈亏
        if position.direction == PositionDirection.LONG:
            pnl = (price - position.entry_price) * position.quantity
        else:
            pnl = (position.entry_price - price) * position.quantity
        
        # 扣除手续费
        commission = position.entry_price * position.quantity * self.config.commission_rate * 2
        net_pnl = pnl - commission
        
        # 更新资金
        self.current_capital += net_pnl
        
        # 记录交易
        self.trade_log.append({
            'timestamp': timestamp,
            'timeframe': timeframe,
            'action': 'CLOSE',
            'direction': position.direction.value,
            'price': price,
            'quantity': position.quantity,
            'pnl': net_pnl,
            'reason': reason,
            'hold_time': timestamp - position.entry_time
        })
        
        # 重置持仓
        position.direction = PositionDirection.NONE
        position.entry_price = 0.0
        position.quantity = 0
        position.stop_loss_price = 0.0
        
        return True

    def check_stop_loss(self, timeframe: int, current_price: float, timestamp: datetime) -> bool:
        """检查止损"""
        position = self.positions[timeframe]
        if position.direction == PositionDirection.NONE:
            return False

        if position.direction == PositionDirection.LONG:
            if current_price <= position.stop_loss_price:
                self.close_position(timeframe, current_price, timestamp, "止损")
                return True
        else:
            if current_price >= position.stop_loss_price:
                self.close_position(timeframe, current_price, timestamp, "止损")
                return True

        return False

    def run_backtest(self) -> BacktestResult:
        """运行回测"""
        print("=" * 60)
        print("开始历史数据回测...")
        print("=" * 60)

        # 获取1分钟数据作为主时间轴
        main_df = self.data_manager.get_data(60)
        if len(main_df) == 0:
            raise ValueError("没有1分钟数据，无法进行回测")

        print(f"回测数据: {len(main_df)} 根1分钟K线")
        print(f"回测时间: {pd.to_datetime(main_df.iloc[0]['datetime'])} 到 {pd.to_datetime(main_df.iloc[-1]['datetime'])}")

        # 按时间顺序处理每根K线
        for i in range(len(main_df)):
            current_time = pd.to_datetime(main_df.iloc[i]['datetime'])
            current_price = main_df.iloc[i]['close']

            # 更新权益曲线
            self.equity_curve.append(self.current_capital)

            # 检查所有持仓的止损
            for timeframe in self.config.timeframes:
                self.check_stop_loss(timeframe, current_price, current_time)

            # 处理各个时间周期的信号（每N根K线处理一次）
            for timeframe in self.config.timeframes:
                if self._should_process_timeframe(i, timeframe):
                    self._process_timeframe_signals(timeframe, i, current_time)

            # 每天记录收益
            if i > 0 and i % 1440 == 0:  # 每1440分钟（1天）
                daily_return = (self.current_capital - self.equity_curve[-1440]) / self.equity_curve[-1440]
                self.daily_returns.append(daily_return)

        # 平掉所有剩余持仓
        final_price = main_df.iloc[-1]['close']
        final_time = pd.to_datetime(main_df.iloc[-1]['datetime'])
        for timeframe in self.config.timeframes:
            if self.positions[timeframe].direction != PositionDirection.NONE:
                self.close_position(timeframe, final_price, final_time, "回测结束")

        # 计算回测结果
        return self._calculate_results()

    def _should_process_timeframe(self, current_index: int, timeframe: int) -> bool:
        """判断是否应该处理该时间周期"""
        if timeframe == 60:  # 1分钟，每次都处理
            return True
        elif timeframe == 900:  # 15分钟，每15分钟处理一次
            return current_index % 15 == 0
        elif timeframe == 14400:  # 4小时，每240分钟处理一次
            return current_index % 240 == 0
        elif timeframe == 86400:  # 日线，每1440分钟处理一次
            return current_index % 1440 == 0
        elif timeframe == 604800:  # 周线，每10080分钟处理一次
            return current_index % 10080 == 0
        return False

    def _process_timeframe_signals(self, timeframe: int, current_index: int, current_time: datetime):
        """处理时间周期信号"""
        df = self.data_manager.get_data(timeframe)
        if len(df) == 0:
            return

        # 找到对应的数据索引
        df_index = self._find_corresponding_index(df, current_time)
        if df_index < max(self.config.short_ma_period, self.config.long_ma_period):
            return

        # 计算信号
        current_signal = self.calculate_ma_signals(df, timeframe, df_index)
        higher_signal = self.get_higher_timeframe_signal(timeframe, current_index)
        current_kline = df.iloc[df_index]

        # 检查开仓条件
        should_open, direction = self.should_open_position(current_signal, higher_signal, current_kline)

        if should_open and self.positions[timeframe].direction == PositionDirection.NONE:
            # 计算止损
            stop_loss_price = self.calculate_stop_loss(df, direction, df_index)

            # 开仓
            self.open_position(timeframe, direction, current_signal.price, stop_loss_price, current_time)

    def _find_corresponding_index(self, df: pd.DataFrame, target_time: datetime) -> int:
        """找到对应的数据索引"""
        df['datetime_pd'] = pd.to_datetime(df['datetime'])
        valid_indices = df[df['datetime_pd'] <= target_time].index
        return valid_indices[-1] if len(valid_indices) > 0 else 0

    def _calculate_results(self) -> BacktestResult:
        """计算回测结果"""
        # 基本统计
        total_return = self.current_capital - self.initial_capital
        return_rate = total_return / self.initial_capital * 100

        # 最大回撤
        equity_series = pd.Series(self.equity_curve)
        rolling_max = equity_series.expanding().max()
        drawdown = (equity_series - rolling_max) / rolling_max
        max_drawdown = drawdown.min() * 100

        # 夏普比率
        if len(self.daily_returns) > 1:
            sharpe_ratio = np.mean(self.daily_returns) / np.std(self.daily_returns) * np.sqrt(252)
        else:
            sharpe_ratio = 0

        # 交易统计
        closed_trades = [trade for trade in self.trade_log if trade['action'] == 'CLOSE']
        total_trades = len(closed_trades)

        if total_trades > 0:
            winning_trades = len([t for t in closed_trades if t['pnl'] > 0])
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades * 100

            wins = [t['pnl'] for t in closed_trades if t['pnl'] > 0]
            losses = [t['pnl'] for t in closed_trades if t['pnl'] <= 0]

            avg_win = np.mean(wins) if wins else 0
            avg_loss = abs(np.mean(losses)) if losses else 0

            profit_factor = sum(wins) / abs(sum(losses)) if losses else float('inf')
        else:
            winning_trades = losing_trades = 0
            win_rate = avg_win = avg_loss = profit_factor = 0

        return BacktestResult(
            total_return=total_return,
            return_rate=return_rate,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            equity_curve=self.equity_curve,
            trade_log=self.trade_log
        )


def print_backtest_results(result: BacktestResult):
    """打印回测结果"""
    print("\n" + "=" * 60)
    print("回测结果报告")
    print("=" * 60)

    print(f"总收益: {result.total_return:,.2f}")
    print(f"收益率: {result.return_rate:.2f}%")
    print(f"最大回撤: {result.max_drawdown:.2f}%")
    print(f"夏普比率: {result.sharpe_ratio:.2f}")

    print(f"\n交易统计:")
    print(f"总交易次数: {result.total_trades}")
    print(f"盈利交易: {result.winning_trades}")
    print(f"亏损交易: {result.losing_trades}")
    print(f"胜率: {result.win_rate:.2f}%")
    print(f"平均盈利: {result.avg_win:.2f}")
    print(f"平均亏损: {result.avg_loss:.2f}")
    print(f"盈亏比: {result.profit_factor:.2f}")


def plot_results(result: BacktestResult):
    """绘制回测结果图表"""
    try:
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 权益曲线
        ax1.plot(result.equity_curve)
        ax1.set_title('权益曲线')
        ax1.set_ylabel('资金')
        ax1.grid(True)

        # 回撤曲线
        equity_series = pd.Series(result.equity_curve)
        rolling_max = equity_series.expanding().max()
        drawdown = (equity_series - rolling_max) / rolling_max * 100

        ax2.fill_between(range(len(drawdown)), drawdown, 0, alpha=0.3, color='red')
        ax2.set_title('回撤曲线')
        ax2.set_ylabel('回撤 (%)')
        ax2.set_xlabel('时间')
        ax2.grid(True)

        plt.tight_layout()
        plt.savefig('backtest_results.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("图表已保存为 backtest_results.png")

    except Exception as e:
        print(f"绘图失败: {e}")


def run_full_backtest(auth: str = "smartmanp,ftp123"):
    """运行完整回测"""
    try:
        # 创建配置
        config = StrategyConfig(
            timeframes=[60, 900, 14400, 86400, 604800],
            base_data_length=10000,
            short_ma_period=5,
            long_ma_period=20,
            stop_loss_periods=10,
            max_position_ratio=0.1,
            max_leverage=3.0,
            commission_rate=0.0003,
            trailing_stop_enabled=False,  # 回测中暂时关闭移动止损
            profit_threshold_ratio=2.0
        )

        # 获取历史数据
        data_manager = HistoricalDataManager("<EMAIL>", auth)
        data_manager.fetch_historical_data(config.timeframes, config.base_data_length)

        # 创建回测引擎
        backtest_engine = BacktestEngine(data_manager, config)

        # 运行回测
        result = backtest_engine.run_backtest()

        # 显示结果
        print_backtest_results(result)

        # 绘制图表
        plot_results(result)

        # 保存详细交易记录
        if result.trade_log:
            trade_df = pd.DataFrame(result.trade_log)
            trade_df.to_csv('trade_log.csv', index=False, encoding='utf-8')
            print(f"\n交易记录已保存为 trade_log.csv")

        return result

    except Exception as e:
        print(f"回测失败: {e}")
        return None


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        auth = sys.argv[1]
    else:
        auth = "smartmanp,ftp123"

    print("************多时间周期策略历史回测")
    print("=" * 60)

    result = run_full_backtest(auth)

    if result:
        print("\n回测完成！")
        print("查看生成的文件:")
        print("- backtest_results.png: 权益曲线和回撤图")
        print("- trade_log.csv: 详细交易记录")
    else:
        print("回测失败，请检查网络连接和认证信息")
