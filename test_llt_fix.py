"""
测试LLT策略修复效果
验证pandas Series布尔值歧义问题是否已解决
"""

import pandas as pd
import numpy as np
from loguru import logger
from llt_strategy_refactored import LLTIndicator, ParameterOptimizer, TradingConfig, setup_logging


def test_llt_calculation():
    """测试LLT计算功能"""
    print("=" * 60)
    print("测试LLT指标计算")
    print("=" * 60)
    
    # 创建测试数据
    np.random.seed(42)
    prices = np.random.randn(100).cumsum() + 100
    price_series = pd.Series(prices)
    
    print(f"测试数据: {len(price_series)}个价格点")
    print(f"价格范围: {price_series.min():.2f} - {price_series.max():.2f}")
    
    try:
        # 测试LLT计算
        alpha = 0.1
        llt_values = LLTIndicator.calculate(price_series, alpha)
        
        print(f"✅ LLT计算成功")
        print(f"   输入长度: {len(price_series)}")
        print(f"   输出长度: {len(llt_values)}")
        print(f"   Alpha值: {alpha}")
        print(f"   LLT范围: {min(llt_values):.2f} - {max(llt_values):.2f}")
        
        # 测试信号生成
        signals = LLTIndicator.generate_signals(llt_values)
        
        print(f"✅ 信号生成成功")
        print(f"   信号长度: {len(signals)}")
        print(f"   买入信号: {signals.count(1)}个")
        print(f"   卖出信号: {signals.count(-1)}个")
        print(f"   无信号: {signals.count(0)}个")
        
        return True
        
    except Exception as e:
        print(f"❌ LLT计算失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False


def test_backtest_function():
    """测试回测功能"""
    print("=" * 60)
    print("测试回测功能")
    print("=" * 60)
    
    try:
        # 创建测试数据
        np.random.seed(42)
        n_points = 1000
        
        # 生成模拟K线数据
        prices = np.random.randn(n_points).cumsum() + 100
        volumes = np.random.randint(100, 1000, n_points)
        
        klines_data = pd.DataFrame({
            'close': prices,
            'open': prices + np.random.randn(n_points) * 0.1,
            'high': prices + np.abs(np.random.randn(n_points)) * 0.5,
            'low': prices - np.abs(np.random.randn(n_points)) * 0.5,
            'volume': volumes
        })
        
        print(f"测试数据: {len(klines_data)}条K线")
        print(f"价格范围: {klines_data.close.min():.2f} - {klines_data.close.max():.2f}")
        
        # 创建参数优化器
        optimizer = ParameterOptimizer(klines_data)
        
        # 测试单个参数的回测
        test_alphas = [0.05, 0.1, 0.2]
        
        for alpha in test_alphas:
            print(f"\n测试 Alpha = {alpha}")
            
            result = optimizer._run_backtest(alpha)
            
            print(f"✅ 回测成功")
            print(f"   交易次数: {len(result['trades'])}")
            print(f"   累计盈亏: {result['total_pnl']:.2f}")
            print(f"   胜率: {result['win_rate']:.1f}%")
            print(f"   收益率: {result['return_rate']:.2f}%")
            print(f"   盈利交易: {result['winning_trades']}")
            print(f"   亏损交易: {result['losing_trades']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False


def test_parameter_optimization():
    """测试参数优化功能"""
    print("=" * 60)
    print("测试参数优化功能")
    print("=" * 60)
    
    try:
        # 创建测试数据
        np.random.seed(42)
        n_points = 500  # 减少数据量以加快测试
        
        # 生成模拟K线数据
        prices = np.random.randn(n_points).cumsum() + 100
        volumes = np.random.randint(100, 1000, n_points)
        
        klines_data = pd.DataFrame({
            'close': prices,
            'open': prices + np.random.randn(n_points) * 0.1,
            'high': prices + np.abs(np.random.randn(n_points)) * 0.5,
            'low': prices - np.abs(np.random.randn(n_points)) * 0.5,
            'volume': volumes
        })
        
        print(f"测试数据: {len(klines_data)}条K线")
        
        # 创建参数优化器
        optimizer = ParameterOptimizer(klines_data)
        
        # 测试小范围参数优化
        test_range = range(10, 21)  # 只测试10个参数
        print(f"测试参数范围: {test_range.start} - {test_range.stop-1}")
        
        results = optimizer.optimize_d_value(test_range)
        
        print(f"✅ 参数优化成功")
        print(f"   测试参数: {len(test_range)}个")
        print(f"   有效结果: {len(results)}个")
        
        if results:
            best_result = results[0]
            print(f"   最优参数: D_VALUE={best_result['D_VALUE']}")
            print(f"   最优收益率: {best_result['return_rate']:.2f}%")
            print(f"   最优胜率: {best_result['win_rate']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数优化失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False


def test_data_types():
    """测试数据类型处理"""
    print("=" * 60)
    print("测试数据类型处理")
    print("=" * 60)
    
    try:
        # 测试不同类型的输入数据
        test_cases = [
            ("Python列表", [100.0, 101.0, 102.0, 101.5, 103.0]),
            ("Numpy数组", np.array([100.0, 101.0, 102.0, 101.5, 103.0])),
            ("Pandas Series", pd.Series([100.0, 101.0, 102.0, 101.5, 103.0])),
            ("整数数据", pd.Series([100, 101, 102, 101, 103])),
        ]
        
        alpha = 0.1
        
        for case_name, data in test_cases:
            print(f"\n测试 {case_name}:")
            print(f"   数据类型: {type(data)}")
            print(f"   数据长度: {len(data)}")
            
            try:
                if isinstance(data, list):
                    data = pd.Series(data)
                
                llt_values = LLTIndicator.calculate(data, alpha)
                signals = LLTIndicator.generate_signals(llt_values)
                
                print(f"   ✅ 处理成功")
                print(f"   LLT长度: {len(llt_values)}")
                print(f"   信号长度: {len(signals)}")
                
            except Exception as e:
                print(f"   ❌ 处理失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据类型测试失败: {e}")
        return False


def main():
    """主测试函数"""
    # 设置日志
    setup_logging("INFO")
    
    print("LLT策略修复效果测试")
    print("=" * 80)
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("LLT指标计算", test_llt_calculation),
        ("数据类型处理", test_data_types),
        ("回测功能", test_backtest_function),
        ("参数优化", test_parameter_optimization),
    ]
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！pandas Series布尔值歧义问题已修复")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
