import pandas as pd
import numpy as np
import mplfinance as mpf

def preprocess_data(df):
    # 将 datetime 列转换为 datetime 类型并设置为索引
    df['datetime'] = pd.to_datetime(df['datetime'].astype(float), unit='ns')
    df.set_index('datetime', inplace=True)
    
    # 确保所有价格和成交量列为数值类型
    for col in ['open', 'high', 'low', 'close', 'volume']:
        df[col] = pd.to_numeric(df[col])
    
    return df

def wh8_indicator(df):
    HIGH = df['high'].values
    LOW = df['low'].values
    CLOSE = df['close'].values

    def REF(arr, n):
        return np.roll(arr, n)

    def HHV(arr, n):
        return pd.Series(arr).rolling(n).max().values

    def LLV(arr, n):
        return pd.Series(arr).rolling(n).min().values

    def BARSLAST(condition):
        return np.maximum.accumulate(np.where(condition, 0, np.arange(len(condition))))

    def VALUEWHEN(condition, value):
        result = np.full_like(value, np.nan)
        mask = condition.astype(bool)
        result[mask] = value[mask]
        return result

    def CROSS(a, b):
        return (a > b) & (REF(a, 1) <= REF(b, 1))

    X_34 = np.where(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
    X_35 = np.where(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)
    X_36 = HHV(X_34, 3)
    X_37 = LLV(X_35, 3)
    X_38 = BARSLAST(X_37 < REF(X_37, 1))
    X_39 = BARSLAST(X_36 > REF(X_36, 1))
    X_40 = np.where(HHV(X_34, X_39 + 1) == X_34, 1, 0)
    X_41 = np.where(LLV(X_35, X_38 + 1) == X_35, 1, 0)
    X_42 = BARSLAST(X_40)
    X_43 = REF(LLV(X_35, 3), X_42)
    X_44 = BARSLAST(X_41)
    X_45 = REF(HHV(X_34, 3), X_44)
    X_46 = VALUEWHEN(X_45 > 0, X_45)
    X_47 = VALUEWHEN(X_43 > 0, X_43)
    X_48 = np.where(CLOSE > X_46, -1, np.where(CLOSE < X_47, 1, 0))
    X_49 = VALUEWHEN(X_48 != 0, X_48)
    X_50 = BARSLAST(CROSS(0, X_49))
    X_51 = BARSLAST(CROSS(X_49, 0))
    X_52 = np.where(X_49 == 1, np.where(LLV(X_46, X_51 + 1) == X_46, X_46, LLV(X_46, X_51 + 1)), X_46)
    X_53 = np.where(X_49 == -1, np.where(HHV(X_47, X_50 + 1) == X_47, X_47, HHV(X_47, X_50 + 1)), X_47)
    X_54 = np.where(CLOSE > X_52, -1, np.where(CLOSE < X_53, 1, 0))
    X_55 = VALUEWHEN(X_54 != 0, X_54)
    X_56 = BARSLAST(CROSS(0, X_54))
    X_57 = BARSLAST(CROSS(X_54, 0))
    X_58 = np.where(X_55 == 1, 
                    np.where(LLV(X_52, X_57 + 1) == X_52, X_52, LLV(X_52, X_57 + 1)),
                    np.where(HHV(X_53, X_56 + 1) == X_53, X_53, HHV(X_53, X_56 + 1)))

    long_stop_loss = np.where(X_55 < 0, X_58, np.nan)
    short_stop_loss = np.where(X_55 > 0, X_58, np.nan)
    G = X_58
    W1 = X_55 < 0
    W2 = X_55 > 0

    return pd.DataFrame({
        'long_stop_loss': long_stop_loss,
        'short_stop_loss': short_stop_loss,
        'G': G,
        'W1': W1,
        'W2': W2
    }, index=df.index)

def plot_wh8_indicator(df, indicator_df):
    # 准备绘图数据
    df_plot = df.copy()
    df_plot['long_stop_loss'] = indicator_df['long_stop_loss']
    df_plot['short_stop_loss'] = indicator_df['short_stop_loss']

    # 创建附加图表
    apds = [
        mpf.make_addplot(df_plot['long_stop_loss'], color='red', width=1.5),
        mpf.make_addplot(df_plot['short_stop_loss'], color='green', width=1.5)
    ]

    # 绘制K线图和指标
    mpf.plot(df_plot, type='candle', style='charles',
             title=f'WH8 Technical Indicator - {df_plot.index[0].date()} to {df_plot.index[-1].date()}',
             ylabel='Price',
             volume=True,
             addplot=apds,
             figsize=(12, 8))

# 主程序
if __name__ == "__main__":
    # 读取数据
    df = pd.read_csv('900.csv')  # 替换为您的实际文件名
    
    # 预处理数据
    df = preprocess_data(df)
    
    # 计算WH8指标
    indicator_df = wh8_indicator(df)
    
    # 绘制图表
    plot_wh8_indicator(df, indicator_df)
