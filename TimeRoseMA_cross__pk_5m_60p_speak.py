from strategies.TimeRoseMA_cross_speak import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date
    from accounts_zjy import cyzjy as acct


    product = 'CZCE.PK301'
    symbol=product
    interval = 60*5
    bklimit = 6
    sklimit = 6
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    api = TqApi(TqKq(), auth="quant_tyc,Qiai1301")

    ma_cross(api, product, interval, single_volume, bklimit, sklimit,period=60)


runstrategy()
