import pandas as pd
import numpy as np
from typing import Tuple, List, Dict
from dataclasses import dataclass


@dataclass
class TradeStats:
    total_trades: int
    profitable_trades: int
    losing_trades: int
    win_rate: float
    profit_loss_ratio: float
    max_profit: float
    max_loss: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    total_returns: float
    sharpe_ratio: float
    max_drawdown: float
    avg_profit_per_trade: float
    avg_loss_per_trade: float


class TradingStrategy:
    def __init__(self, lookback_period: int = 20):
        self.lookback_period = lookback_period
        self.positions = []
        self.trades = []

    def calculate_macd(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        exp12 = data['close'].ewm(span=12, adjust=False).mean()
        exp26 = data['close'].ewm(span=26, adjust=False).mean()

        data['diff'] = exp12 - exp26
        data['dea'] = data['diff'].ewm(span=9, adjust=False).mean()
        data['macd'] = 2 * (data['diff'] - data['dea'])

        return data

    def calculate_mean_reversion(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算均值回归信号"""
        data['ma'] = data['close'].rolling(window=self.lookback_period).mean()
        data['std'] = data['close'].rolling(window=self.lookback_period).std()

        data['upper_band'] = data['ma'] + 2 * data['std']
        data['lower_band'] = data['ma'] - 2 * data['std']

        data['mean_reversion_long'] = (data['close'] < data['lower_band']).astype(int)
        data['mean_reversion_short'] = (data['close'] > data['upper_band']).astype(int)

        return data

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        data = self.calculate_macd(data)
        data = self.calculate_mean_reversion(data)

        data['signal'] = 0

        data['macd_cross_up'] = (data['macd'] > 0) & (data['macd'].shift(1) <= 0)
        data['macd_cross_down'] = (data['macd'] < 0) & (data['macd'].shift(1) >= 0)

        data['diff_cross_up'] = (data['diff'] > data['dea']) & (data['diff'].shift(1) <= data['dea'].shift(1))
        data['diff_cross_down'] = (data['diff'] < data['dea']) & (data['diff'].shift(1) >= data['dea'].shift(1))

        for i in range(1, len(data)):
            if data.iloc[i]['macd_cross_up']:
                data.iloc[i, data.columns.get_loc('signal')] = 1
            elif data.iloc[i]['macd_cross_down']:
                data.iloc[i, data.columns.get_loc('signal')] = -1
            elif (data.iloc[i]['mean_reversion_short'] or data.iloc[i]['diff_cross_down']) and \
                    data.iloc[i - 1]['signal'] == 1:
                data.iloc[i, data.columns.get_loc('signal')] = 0
            elif (data.iloc[i]['mean_reversion_long'] or data.iloc[i]['diff_cross_up']) and \
                    data.iloc[i - 1]['signal'] == -1:
                data.iloc[i, data.columns.get_loc('signal')] = 0
            else:
                data.iloc[i, data.columns.get_loc('signal')] = data.iloc[i - 1]['signal']

        return data

    def analyze_trades(self, trades: List[Dict]) -> TradeStats:
        """分析交易统计指标"""
        profits = []
        consecutive_wins = 0
        consecutive_losses = 0
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        current_streak = 0

        # 计算每笔交易的盈亏
        for trade in trades:
            if 'profit' in trade:
                profits.append(trade['profit'])

                # 更新连续盈亏计数
                if trade['profit'] > 0:
                    if current_streak >= 0:
                        current_streak += 1
                    else:
                        current_streak = 1
                    max_consecutive_wins = max(max_consecutive_wins, current_streak)
                elif trade['profit'] < 0:
                    if current_streak <= 0:
                        current_streak -= 1
                    else:
                        current_streak = -1
                    max_consecutive_losses = max(max_consecutive_losses, abs(current_streak))

        # 统计盈亏交易
        profitable_trades = len([p for p in profits if p > 0])
        losing_trades = len([p for p in profits if p < 0])
        total_trades = len(profits)

        # 计算盈亏统计
        if total_trades > 0:
            win_rate = profitable_trades / total_trades

            winning_profits = [p for p in profits if p > 0]
            losing_profits = [abs(p) for p in profits if p < 0]

            avg_profit = np.mean(winning_profits) if winning_profits else 0
            avg_loss = np.mean(losing_profits) if losing_profits else 0

            profit_loss_ratio = avg_profit / avg_loss if avg_loss != 0 else float('inf')
        else:
            win_rate = 0
            profit_loss_ratio = 0
            avg_profit = 0
            avg_loss = 0

        return TradeStats(
            total_trades=total_trades,
            profitable_trades=profitable_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            profit_loss_ratio=profit_loss_ratio,
            max_profit=max(profits) if profits else 0,
            max_loss=min(profits) if profits else 0,
            max_consecutive_wins=max_consecutive_wins,
            max_consecutive_losses=max_consecutive_losses,
            total_returns=sum(profits),
            sharpe_ratio=np.sqrt(252) * np.mean(profits) / np.std(profits) if profits else 0,
            max_drawdown=min(np.minimum.accumulate(np.cumsum(profits)) - np.cumsum(profits)) if profits else 0,
            avg_profit_per_trade=avg_profit,
            avg_loss_per_trade=avg_loss
        )

    def backtest(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, List, TradeStats]:
        """执行回测"""
        data = self.generate_signals(data)

        data['returns'] = data['close'].pct_change()
        data['strategy_returns'] = data['signal'].shift(1) * data['returns']

        trades = []
        position = 0
        entry_price = 0

        for i in range(1, len(data)):
            current_signal = data.iloc[i]['signal']
            prev_signal = data.iloc[i - 1]['signal']

            if current_signal != prev_signal:
                if position != 0:
                    exit_price = data.iloc[i]['close']
                    profit = (exit_price - entry_price) * position
                    trades.append({
                        'exit_time': data.iloc[i]['datetime'],
                        'exit_price': exit_price,
                        'profit': profit,
                        'holding_period': i - entry_index
                    })

                if current_signal != 0:
                    position = current_signal
                    entry_price = data.iloc[i]['close']
                    entry_index = i
                    trades.append({
                        'entry_time': data.iloc[i]['datetime'],
                        'entry_price': entry_price,
                        'position': position
                    })
                else:
                    position = 0
                    entry_price = 0

        trade_stats = self.analyze_trades(trades)
        return data, trades, trade_stats


def format_number(num: float) -> str:
    """格式化数字输出"""
    if abs(num) >= 1000000:
        return f"{num / 1000000:.2f}M"
    elif abs(num) >= 1000:
        return f"{num / 1000:.2f}K"
    else:
        return f"{num:.2f}"


def run_strategy():
    """运行策略并输出结果"""
    # 读取数据
    data = pd.read_csv('data.csv')
    data['datetime'] = pd.to_datetime(data['datetime'].astype(float), unit='ns')

    # 初始化策略并执行回测
    strategy = TradingStrategy(lookback_period=20)
    results, trades, stats = strategy.backtest(data)

    # 打印统计结果
    print("\n=== 交易统计 ===")
    print(f"总交易次数: {stats.total_trades}")
    print(f"盈利交易: {stats.profitable_trades}")
    print(f"亏损交易: {stats.losing_trades}")
    print(f"胜率: {stats.win_rate:.2%}")
    print(f"盈亏比: {stats.profit_loss_ratio:.2f}")
    print(f"最大单次盈利: {format_number(stats.max_profit)}")
    print(f"最大单次亏损: {format_number(stats.max_loss)}")
    print(f"最大连续盈利次数: {stats.max_consecutive_wins}")
    print(f"最大连续亏损次数: {stats.max_consecutive_losses}")

    print("\n=== 绩效指标 ===")
    print(f"总收益: {format_number(stats.total_returns)}")
    print(f"夏普比率: {stats.sharpe_ratio:.2f}")
    print(f"最大回撤: {stats.max_drawdown:.2%}")
    print(f"平均盈利: {format_number(stats.avg_profit_per_trade)}")
    print(f"平均亏损: {format_number(stats.avg_loss_per_trade)}")

    return results, trades, stats


if __name__ == "__main__":
    results, trades, stats = run_strategy()