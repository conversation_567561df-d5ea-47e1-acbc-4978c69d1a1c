"""
测试通用合约交易时间系统
验证各种合约的交易时间判断是否正确
"""

import sys
from datetime import datetime, time as dt_time
from unittest.mock import patch

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_contract_trading_times():
    """测试各种合约的交易时间配置"""
    print("=" * 60)
    print("测试各种合约的交易时间配置")
    print("=" * 60)
    
    try:
        from utils.utils import get_contract_trading_times
        
        # 测试不同类型的合约
        test_contracts = [
            # 贵金属（有夜盘到02:30）
            ('SHFE.ag2512', '白银'),
            ('SHFE.au2512', '黄金'),
            
            # 有色金属（有夜盘到01:00）
            ('SHFE.cu2512', '沪铜'),
            ('SHFE.al2512', '沪铝'),
            ('SHFE.zn2512', '沪锌'),
            
            # 黑色金属（有夜盘到23:00）
            ('SHFE.rb2512', '螺纹钢'),
            ('SHFE.hc2512', '热卷'),
            ('DCE.i2512', '铁矿石'),
            ('DCE.j2512', '焦炭'),
            ('DCE.jm2512', '焦煤'),
            
            # 能源化工（有夜盘到23:00）
            ('INE.sc2512', '原油'),
            ('SHFE.fu2512', '燃料油'),
            ('SHFE.ru2512', '橡胶'),
            ('DCE.l2512', '塑料'),
            ('DCE.v2512', 'PVC'),
            
            # 农产品（有夜盘到23:00）
            ('DCE.y2512', '豆油'),
            ('DCE.m2512', '豆粕'),
            ('DCE.a2512', '豆一'),
            ('DCE.c2512', '玉米'),
            
            # 农产品（无夜盘）
            ('CZCE.CF512', '棉花'),
            ('CZCE.SR512', '白糖'),
            ('CZCE.OI512', '菜籽油'),
            ('CZCE.RM512', '菜粕'),
            
            # 股指期货
            ('CFFEX.IF2512', '沪深300'),
            ('CFFEX.IC2512', '中证500'),
            ('CFFEX.IH2512', '上证50'),
            
            # 国债期货
            ('CFFEX.T2512', '10年期国债'),
            ('CFFEX.TF2512', '5年期国债'),
        ]
        
        print(f"{'合约代码':<15} {'品种':<8} {'交易时间段'}")
        print("-" * 70)
        
        for symbol, name in test_contracts:
            trading_times = get_contract_trading_times(symbol)
            periods_str = ', '.join([f"{p[0]}-{p[1]}" for p in trading_times])
            print(f"{symbol:<15} {name:<8} {periods_str}")
        
        print("\n✓ 合约交易时间配置测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_trading_time_judgment():
    """测试交易时间判断"""
    print("=" * 60)
    print("测试交易时间判断")
    print("=" * 60)
    
    try:
        from utils.utils import is_contract_trading_time, tradingTime
        
        # 测试不同合约在不同时间的判断
        test_cases = [
            # 时间, 合约, 预期结果, 说明
            (10, 0, 'SHFE.ag2512', True, '白银日盘'),
            (14, 0, 'SHFE.ag2512', True, '白银日盘'),
            (22, 0, 'SHFE.ag2512', True, '白银夜盘'),
            (1, 0, 'SHFE.ag2512', True, '白银夜盘跨日'),
            (16, 0, 'SHFE.ag2512', False, '白银非交易时间'),
            
            (10, 0, 'SHFE.cu2512', True, '沪铜日盘'),
            (22, 0, 'SHFE.cu2512', True, '沪铜夜盘'),
            (2, 0, 'SHFE.cu2512', False, '沪铜夜盘结束后'),
            
            (10, 0, 'SHFE.rb2512', True, '螺纹钢日盘'),
            (22, 0, 'SHFE.rb2512', True, '螺纹钢夜盘'),
            (0, 30, 'SHFE.rb2512', False, '螺纹钢夜盘结束后'),
            
            (10, 0, 'CZCE.CF512', True, '棉花日盘'),
            (22, 0, 'CZCE.CF512', False, '棉花无夜盘'),
            
            (10, 0, 'CFFEX.IF2512', True, '股指期货交易时间'),
            (22, 0, 'CFFEX.IF2512', False, '股指期货无夜盘'),
        ]
        
        print(f"{'时间':<8} {'合约':<15} {'预期':<6} {'实际':<6} {'结果':<6} {'说明'}")
        print("-" * 70)
        
        all_passed = True
        
        for hour, minute, symbol, expected, description in test_cases:
            # 模拟指定时间
            test_datetime = datetime.now().replace(hour=hour, minute=minute, second=0)
            
            with patch('utils.utils.datetime') as mock_datetime:
                mock_datetime.now.return_value = test_datetime
                mock_datetime.strptime = datetime.strptime
                
                # 测试合约特定函数
                actual = is_contract_trading_time(symbol)
                
                time_str = f"{hour:02d}:{minute:02d}"
                expected_str = "是" if expected else "否"
                actual_str = "是" if actual else "否"
                result_str = "✓" if actual == expected else "✗"
                
                print(f"{time_str:<8} {symbol:<15} {expected_str:<6} {actual_str:<6} {result_str:<6} {description}")
                
                if actual != expected:
                    all_passed = False
        
        if all_passed:
            print("\n✓ 交易时间判断测试通过")
            return True
        else:
            print("\n✗ 交易时间判断测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_strategy_integration():
    """测试策略集成"""
    print("=" * 60)
    print("测试策略集成")
    print("=" * 60)
    
    try:
        # 检查strategyTRMAsdk2.py的修改
        with open('strategyTRMAsdk2.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查导入语句
        if 'is_contract_trading_time' in content:
            print("✓ 找到is_contract_trading_time导入")
        else:
            print("✗ 未找到is_contract_trading_time导入")
            return False
        
        # 检查通用交易时间检查
        if 'is_contract_trading_time(symbol)' in content:
            print("✓ 找到通用交易时间检查调用")
        else:
            print("✗ 未找到通用交易时间检查调用")
            return False
        
        # 检查交易时间信息显示
        if 'get_contract_trading_times(symbol)' in content:
            print("✓ 找到交易时间信息获取")
        else:
            print("✗ 未找到交易时间信息获取")
            return False
        
        print("✓ 策略集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 策略集成测试失败: {e}")
        return False


def show_trading_time_summary():
    """显示交易时间总结"""
    print("=" * 60)
    print("期货合约交易时间总结")
    print("=" * 60)
    
    categories = [
        {
            "类别": "贵金属",
            "合约": "ag(白银), au(黄金)",
            "日盘": "09:00-11:30, 13:30-15:00",
            "夜盘": "21:00-02:30+1"
        },
        {
            "类别": "有色金属",
            "合约": "cu(铜), al(铝), zn(锌), pb(铅), ni(镍), sn(锡)",
            "日盘": "09:00-11:30, 13:30-15:00",
            "夜盘": "21:00-01:00+1"
        },
        {
            "类别": "黑色金属",
            "合约": "rb(螺纹钢), hc(热卷), i(铁矿石), j(焦炭), jm(焦煤)",
            "日盘": "09:00-11:30, 13:30-15:00",
            "夜盘": "21:00-23:00"
        },
        {
            "类别": "能源化工",
            "合约": "sc(原油), fu(燃料油), ru(橡胶), l(塑料), v(PVC)等",
            "日盘": "09:00-11:30, 13:30-15:00",
            "夜盘": "21:00-23:00"
        },
        {
            "类别": "农产品(有夜盘)",
            "合约": "y(豆油), m(豆粕), a(豆一), c(玉米)等",
            "日盘": "09:00-11:30, 13:30-15:00",
            "夜盘": "21:00-23:00"
        },
        {
            "类别": "农产品(无夜盘)",
            "合约": "CF(棉花), SR(白糖), OI(菜籽油)等",
            "日盘": "09:00-11:30, 13:30-15:00",
            "夜盘": "无"
        },
        {
            "类别": "股指期货",
            "合约": "IF, IC, IH, IM",
            "日盘": "09:30-11:30, 13:00-15:00",
            "夜盘": "无"
        },
        {
            "类别": "国债期货",
            "合约": "T, TF, TS",
            "日盘": "09:15-11:30, 13:00-15:15",
            "夜盘": "无"
        }
    ]
    
    for cat in categories:
        print(f"\n{cat['类别']}:")
        print(f"  合约: {cat['合约']}")
        print(f"  日盘: {cat['日盘']}")
        print(f"  夜盘: {cat['夜盘']}")


def main():
    """主测试函数"""
    print("通用合约交易时间系统测试")
    print("=" * 80)
    
    # 显示交易时间总结
    show_trading_time_summary()
    
    tests = [
        ("合约交易时间配置", test_contract_trading_times),
        ("交易时间判断", test_trading_time_judgment),
        ("策略集成", test_strategy_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 通用合约交易时间系统实现成功！")
        print("\n系统特点:")
        print("1. ✓ 支持所有主要期货合约类型")
        print("2. ✓ 自动识别合约并应用对应交易时间")
        print("3. ✓ 正确处理夜盘和跨日交易时间")
        print("4. ✓ 提供详细的交易时间信息显示")
        print("5. ✓ 向后兼容原有代码")
        print("\n使用方法:")
        print("- 程序会根据合约代码自动选择正确的交易时间")
        print("- 支持贵金属、有色金属、黑色金属、能源化工、农产品、股指、国债等")
        print("- 在非交易时间会显示该合约的具体交易时间段")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
