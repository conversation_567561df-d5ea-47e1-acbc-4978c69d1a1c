import pandas as pd
import numpy as np
from loguru import logger as mylog
from tqsdk import TqApi, TqAuth, TqKq
from tqsdk.tafunc import time_to_str
import copy
import time


# 假设我们已经有了包含开高低收(OHLC)数据的DataFrame,命名为'df'
# df = pd.DataFrame({'open': [...], 'high': [...], 'low': [...], 'close': [...]})

def MA(series, n):
    return series.rolling(window=n).mean()


def VALUEWHEN(condition, value):
    return value[condition].reindex(value.index).ffill()


def indicator(df):
    # 计算指标
    df['MA1'] = MA(df['close'], 5)

    df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
                         df['high'].shift(2), 0)
    df['HH2'] = VALUEWHEN(df['HH1'] > 0, df['HH1'])

    df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)),df['low'].shift(2),0)
    df['LL2'] = VALUEWHEN(df['LL1'] > 0, df['LL1'])

    df['K1'] = np.where(df['close'] > df['HH2'], 1, np.where(df['close'] < df['LL2'], -1, 0))
    df['K2'] = VALUEWHEN(df['K1'] != 0, df['K1'])

    df['G'] = np.where(df['K2'] == -1, df['HH2'], df['LL2'])
    df['G1'] = df['G'].iloc[-1]

    df['W1'] = df['K2']
    df['W2'] = df['open'] - df['close']
    df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
    df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])

    return df


def on_bar(klines):
    df = indicator(klines)
    global current_signal, signal_price
    if df['W1'].iloc[-2] == 1 and df['W1'].iloc[-3] == -1:
        print(df['G1'].iloc[-1])
        print(df['W1'].tolist()[-20:])
        print('买入信号')
        current_signal = "做多"
        signal_price = klines.iloc[-1]['close']

    elif df['W1'].iloc[-2] == -1 and df['W1'].iloc[-3] == 1:
        print(df['G1'].iloc[-1])
        print(df['W1'].tolist()[-20:])
        print('卖出信号')
        current_signal = "做空"
        signal_price = klines.iloc[-1]['close']
    else:
        print('止损价:', df['G1'].iloc[-1], '当前信号:', current_signal, '信号价格:', signal_price, '当前价格:',
              klines.iloc[-1]['close'])
        print(df['W1'].tolist()[-20:])


def on_signal(klines):
    pass


api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
symbol = api.query_cont_quotes(product_id='ag')[0]
# symbol = 'CZCE.OI501'
interval = 60
klines_history = api.get_kline_serial(symbol, duration_seconds=interval, data_length=3333)
klines_history = klines_history.set_index('datetime')

klines = copy.deepcopy(klines_history)
del klines_history
df = indicator(klines)
print(df['G1'].iloc[-1])
print(np.array(df['W1']).astype(int).tolist()[-20:])
print(df['W1'].tolist()[-20:])
current_signal = "做多" if df['W1'].iloc[-1] == 1 else "做空"
signal_price = df.close.iloc[-1]

klines_tmp = api.get_kline_serial(symbol, duration_seconds=interval, data_length=3)

while True:
    api.wait_update()
    if api.is_changing(klines_tmp.iloc[-1], 'datetime'):
        newk = klines_tmp.iloc[:-1].set_index('datetime')

        for index, row in newk.iterrows():
            if index not in klines.index:
                klines.loc[index] = row
                print(time_to_str(index))
                print('当前数据共有:', len(klines))
                print(f"最新K线时间：", time_to_str(newk.index[-1]), '数据已经更新.更新时间:', time.asctime())
                df = indicator(klines)
                print(df['G1'].iloc[-1])
                print(np.array(df['W1']).astype(int).tolist()[-20:])
                current_signal = "做多" if df['W1'].iloc[-1] == 1 else "做空"
                signal_price = df.close.iloc[-1]

                # signals = calculate_signals(klines, last_signal)
                # if signals:
                #     self.last_signal = signals[-1]
                # try:
                #     await self.loop.run_in_executor(self.executor, self.plot_signals, klines, signals)
                # except Exception as e:
                #     print(e)
        else:
            print(f"最新K线时间：{time_to_str(newk.index[-1])}", '数据没有更新.当前时间:', time.asctime())


