import pandas as pd
import numpy as np

# 读取CSV文件
df = pd.read_csv('900.csv', parse_dates=['datetime'])

# 计算 amount
df['amount'] = df['volume'] * df['close']

# 常量
RB = 3
HC = 2
BS = 0
DV = 1
IK = 6

# 辅助函数
def REF(series, n):
    if isinstance(n, pd.Series):
        return series.groupby(n).shift(1)
    else:
        return series.shift(n)

def TROUGHBARS(series, n, k):
    return (series[::-1].rolling(window=n, center=False).apply(lambda x: np.argmin(x[-n:]) if len(x) >= n else np.nan)[::-1].fillna(0).astype(int))

def PEAKBARS(series, n, k):
    return (series[::-1].rolling(window=n, center=False).apply(lambda x: np.argmax(x[-n:]) if len(x) >= n else np.nan)[::-1].fillna(0).astype(int))

def BARSLAST(condition):
    return pd.Series(np.where(condition, 0, 1)).cumsum().where(condition).ffill().fillna(0).astype(int)


def HHV(series, n):
    if n is None:
        return pd.Series([np.nan] * len(series))

    if isinstance(n, (int, float)):
        if n == 0:
            return series.expanding().max()
        return series.rolling(window=n, min_periods=1).max()

    if len(n) != len(series):
        raise ValueError("Length of n must match length of series")

    result = pd.Series(index=series.index, dtype=float)
    for i in range(len(series)):
        if pd.isna(n[i]):
            result[i] = np.nan
        elif n[i] == 0:
            result[i] = series[:i + 1].max()
        else:
            start = max(0, i - int(n[i]) + 1)
            result[i] = series[start:i + 1].max()
    return result


def LLV(series, n):
    if n is None:
        return pd.Series([np.nan] * len(series))

    if isinstance(n, (int, float)):
        if n == 0:
            return series.expanding().min()
        return series.rolling(window=n, min_periods=1).min()

    if len(n) != len(series):
        raise ValueError("Length of n must match length of series")

    result = pd.Series(index=series.index, dtype=float)
    for i in range(len(series)):
        if pd.isna(n[i]):
            result[i] = np.nan
        elif n[i] == 0:
            result[i] = series[:i + 1].min()
        else:
            start = max(0, i - int(n[i]) + 1)
            result[i] = series[start:i + 1].min()
    return result

def CROSS(series1, series2):
    return (series1 > series2) & (series1.shift(1) <= series2.shift(1))

def SMA(series, n, m):
    return series.ewm(alpha=m/n, adjust=False).mean()

# 主要计算
df['X_1'] = np.where((HC == 1) | (HC == 3), df['high'], df['close'])
df['X_2'] = np.where((HC == 1) | (HC == 3), df['low'], df['close'])
df['X_3'] = ((IK == 1) | (IK == 5) | (IK == 6))
df['X_4'] = ((IK == 2) | (IK == 5) | (IK == 6))
df['X_5'] = ((IK == 3) | (IK == 6))
df['X_6'] = ((IK == 4) | (IK == 6))
df['X_7'] = 1

for col in ['X_3', 'X_4', 'X_5', 'X_6']:
    df[col] = df[col].astype(bool)

df['X_7'] = df['X_7'].astype(int)

df['X_8'] = np.where(df['X_7'] == 1, REF(df['X_7'], 1) - 1, np.where(df['X_7'] == -1, REF(df['X_7'], 1) + 1, df['X_7']))
df['X_9'] = np.where(df['X_8'] < 0, df['X_8'] + 1, df['X_8'] - 1)

df['X_10'] = TROUGHBARS(df['close'], RB, 1)
df['X_11'] = PEAKBARS(df['close'], RB, 1)
df['X_12'] = np.where(df['X_10'] == 0, -1, np.where(df['X_11'] == 0, 1, 0))
df['X_13'] = np.where(df['X_10'] == 0, df['X_11'],
                      np.where(df['X_11'] == 0, -df['X_10'],
                               np.where(df['X_10'] > df['X_11'], df['X_11'], -df['X_10'])))

df['X_14'] = 1
df['X_14'] = df['X_14'].astype(int)

df['X_15'] = BARSLAST(df['X_14'] < 0)
df['X_16'] = BARSLAST(df['X_14'] > 0)
df['X_17'] = np.where(df['X_15'] == 0, -1, np.where(df['X_16'] == 0, 1, 0))
df['X_18'] = np.where(df['X_15'] == 0, df['X_16'],
                      np.where(df['X_16'] == 0, -df['X_15'],
                               np.where(df['X_15'] > df['X_16'], df['X_16'], -df['X_15'])))

df['X_19'] = np.where(HC == 2, df['X_12'], np.where(HC == 3, df['X_17'], df['X_7']))
df['X_20'] = np.where(HC == 2, df['X_13'], np.where(HC == 3, df['X_18'], df['X_9']))
df['X_21'] = np.abs(df['X_20'])

df['周期'] = df['X_21']

df['X_22'] = np.where(df['X_20'] > 0,
                      100 * (df['X_2'] / REF(df['X_1'], df['X_21'].astype(int)) - 1),
                      100 * (df['X_1'] / REF(df['X_2'], df['X_21'].astype(int)) - 1))

df['涨跌幅'] = df['X_22']

# 假设 X_23 为 True（需要根据实际情况调整）
df['X_23'] = True

df['X_24'] = np.where(BS == 0, df['X_21'], np.minimum(df['X_21'], BS))
df['X_25'] = np.where(df['X_20'] > 0,
                      100 * (df['X_2'] / REF(df['X_1'], df['X_24'].astype(int)) - 1),
                      100 * (df['X_1'] / REF(df['X_2'], df['X_24'].astype(int)) - 1))

df['X_26'] = np.where(df['X_23'], df['amount'] / 100000000, df['volume'] / 100)
df['X_27'] = df.apply(lambda row: df['X_26'].iloc[max(0, row.name-int(row['X_24'])+1):row.name+1].sum(), axis=1)
df['X_28'] = df['X_27'] / df['X_25'] / DV

df['速度指数'] = df['X_28']

df['X_29'] = BARSLAST(df['X_19'] == 1)
df['X_30'] = BARSLAST(df['X_19'] == -1)

df['X_31'] = BARSLAST(df['X_19'] == 1)
df['X_32'] = BARSLAST(df['X_19'] == -1)
df['X_33'] = np.where((df['X_31'] == 0) | (df['X_32'] == 0), np.nan,
                      (REF(df['X_1'], df['X_31'].astype(int)) + REF(df['X_2'], df['X_32'].astype(int))) / 2)

df['R50%'] = np.where(df['X_5'], df['X_33'], np.nan)

df['X_34'] = np.where(df['high'] < REF(df['low'], 1), REF(df['low'], 1), df['high'])
df['X_35'] = np.where(df['low'] > REF(df['high'], 1), REF(df['high'], 1), df['low'])
df['X_36'] = HHV(df['X_34'], 3)
df['X_37'] = LLV(df['X_35'], 3)
df['X_38'] = BARSLAST(df['X_37'] < REF(df['X_37'], 1))
df['X_39'] = BARSLAST(df['X_36'] > REF(df['X_36'], 1))
df['X_40'] = np.where(HHV(df['X_34'], df['X_39'] + 1) == df['X_34'], 1, 0)
df['X_41'] = np.where(LLV(df['X_35'], df['X_38'] + 1) == df['X_35'], 1, 0)
df['X_42'] = BARSLAST(df['X_40'])
df['X_43'] = REF(LLV(df['X_35'], 3), df['X_42'].astype(int))
df['X_44'] = BARSLAST(df['X_41'])
df['X_45'] = REF(HHV(df['X_34'], 3), df['X_44'].astype(int))
df['X_46'] = df['X_45'].where(df['X_45'] > 0).ffill()
df['X_47'] = df['X_43'].where(df['X_43'] > 0).ffill()
df['X_48'] = np.where(df['close'] > df['X_46'], -1, np.where(df['close'] < df['X_47'], 1, 0))
df['X_49'] = df['X_48'].where(df['X_48'] != 0).ffill()
df['X_50'] = BARSLAST(CROSS(0, df['X_49']))
df['X_51'] = BARSLAST(CROSS(df['X_49'], 0))
df['X_52'] = np.where(df['X_49'] == 1,
                      np.where(LLV(df['X_46'], df['X_51'] + 1) == df['X_46'], df['X_46'], LLV(df['X_46'], df['X_51'] + 1)),
                      df['X_46'])
df['X_53'] = np.where(df['X_49'] == -1,
                      np.where(HHV(df['X_47'], df['X_50'] + 1) == df['X_47'], df['X_47'], HHV(df['X_47'], df['X_50'] + 1)),
                      df['X_47'])
df['X_54'] = np.where(df['close'] > df['X_52'], -1, np.where(df['close'] < df['X_53'], 1, 0))
df['X_55'] = df['X_54'].where(df['X_54'] != 0).ffill()
df['X_56'] = BARSLAST(CROSS(0, df['X_54']))
df['X_57'] = BARSLAST(CROSS(df['X_54'], 0))

df['GOODGUPIAO'] = SMA(np.maximum(df['close'] - df['close'], 0), 7, 1) / SMA(np.abs(df['close'] - df['close']), 7, 1) * 100

df['X_58'] = np.where(df['X_55'] == 1,
                      np.where(LLV(df['X_52'], df['X_57'] + 1) == df['X_52'], df['X_52'], LLV(df['X_52'], df['X_57'] + 1)),
                      np.where(HHV(df['X_53'], df['X_56'] + 1) == df['X_53'], df['X_53'], HHV(df['X_53'], df['X_56'] + 1)))

df['多头止损'] = np.where((df['X_6']) & (df['X_55'] < 0), df['X_58'], np.nan)
df['空头止损'] = np.where((df['X_6']) & (df['X_55'] > 0), df['X_58'], np.nan)

# 结果存储在DataFrame 'df' 中
print(df.head())  # 显示前几行结果