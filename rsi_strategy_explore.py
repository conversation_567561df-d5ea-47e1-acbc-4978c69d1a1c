from strategies.rsi_strategy import rsi_info_display
from tqsdk import TqApi, TqKq
import numpy as np
import pandas_ta as ta

import pandas as pd
import numpy as np


def SMA(series, n, weight):
    """
    计算加权移动平均
    series: 输入数据
    n: 周期
    weight: 权重
    """
    result = pd.Series(index=series.index, dtype=float)
    result.iloc[0] = series.iloc[0]  # 第一个值直接使用

    for i in range(1, len(series)):
        result.iloc[i] = (series.iloc[i] * weight +
                          result.iloc[i - 1] * (n - weight)) / n

    return result


def calculate_RSI(close, n1=14):
    """
    计算RSI指标
    close: 收盘价序列
    n1: RSI周期数，默认14
    """
    # 计算前一日收盘价
    prev_close = close.shift(1)

    # 计算价格变化
    price_diff = close - prev_close

    # 计算上涨和下跌的绝对值
    gains = pd.Series([max(diff, 0) for diff in price_diff[1:]], index=price_diff.index[1:])
    abs_diff = price_diff[1:].abs()

    # 使用SMA计算RSI
    avg_gains = SMA(gains, n1, 1)
    avg_abs_diff = SMA(abs_diff, n1, 1)

    # 计算RSI
    rsi = avg_gains / avg_abs_diff * 100

    return rsi


# 示例使用
if __name__ == "__main__":
    # 创建示例数据
    df = pd.DataFrame({
        'CLOSE': [10, 10.5, 10.2, 10.8, 10.9, 10.7, 10.3, 10.6, 10.4, 10.9]
    })

    # 计算RSI
    df['RSI'] = calculate_RSI(df['CLOSE'], n1=6)

    print(df)


api = TqApi(TqKq(), auth=("bigwolf,ftp123"), disable_print=True)

productid = 'OI'
interval = 900

symbol = api.query_cont_quotes(product_id=productid)[0]

klines = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964).dropna()
rsi = ta.rsi(klines.close, 6).tolist()
quote=api.get_quote(symbol)
print(quote.last_price)
print(rsi[-40:])
while True:
    api.wait_update()
    if api.is_changing(klines.iloc[-1], "close"):
        rsi = ta.rsi(klines.close, 6).tolist()
        rsi_ar = np.around(rsi, decimals=2)
        print(rsi_ar[-10:], klines.iloc[-1].close)
        print(quote.last_price)

        # result = rsi_info_display(klines)

print('done')

api.close()
