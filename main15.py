from tqsdk import TqApi, TqAuth,TqKq
from tqsdk.ta import MA
'''
由于tqsdk中k线周期的基础单位是秒, 我们把程序改一下,周期参数的数值直接用秒, 程序中做相应的修改,即,传入的时间周期为[60,180,800,900]等等

非常好的建议。我们可以将交易周期设置为一个可变参数，并且直接使用秒作为单位。这样可以让程序更加灵活和通用。下面是根据您的要求修改后的程序：
'''
class MultiTimeframeStrategy:
    def __init__(self, api, symbol, timeframes, max_positions):
        self.api = api
        self.symbol = symbol
        self.timeframes = timeframes  # 以秒为单位的时间周期列表
        self.max_positions = max_positions  # 每个周期的多空单独限制
        self.positions = {tf: {'long': 0, 'short': 0} for tf in self.timeframes}
        self.klines = {}

        for tf in self.timeframes:
            self.klines[tf] = self.api.get_kline_serial(self.symbol, tf)

    def check_crossover(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] > ma.iloc[-1] and close.iloc[-2] <= ma.iloc[-2]

    def check_crossunder(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] < ma.iloc[-1] and close.iloc[-2] >= ma.iloc[-2]

    def get_position_profit(self, position_type):
        position = self.api.get_position(self.symbol)
        if position_type == 'long':
            return position.float_profit_long
        else:
            return position.float_profit_short

    def execute_trade(self, timeframe, action, position_type):
        if action == 'open':
            if self.positions[timeframe][position_type] < self.max_positions[timeframe][position_type]:
                self.positions[timeframe][position_type] += 1
                volume = 1 if position_type == 'long' else -1
                self.api.insert_order(symbol=self.symbol, direction="BUY" if position_type == 'long' else "SELL", offset="OPEN", volume=abs(volume))
                print(f"{timeframe}秒周期: 开{position_type}单")
        elif action == 'close':
            if self.positions[timeframe][position_type] > 0:
                profit = self.get_position_profit(position_type)
                if profit >= 0:
                    self.positions[timeframe][position_type] -= 1
                    volume = 1 if position_type == 'long' else -1
                    self.api.insert_order(symbol=self.symbol, direction="SELL" if position_type == 'long' else "BUY", offset="CLOSE", volume=abs(volume))
                    print(f"{timeframe}秒周期: 平{position_type}单")
                else:
                    print(f"{timeframe}秒周期: {position_type}单亏损,不平仓")

    def run(self, timeframe):
        kline = self.klines[timeframe]
        ma13 = kline.close.rolling(13).mean()

        if self.check_crossover(kline.close, ma13):
            print(f"{timeframe}秒周期: 收盘价上穿13日均线, 开多仓")
            self.execute_trade(timeframe, 'open', 'long')
            self.execute_trade(timeframe, 'close', 'short')
        elif self.check_crossunder(kline.close, ma13):
            print(f"{timeframe}秒周期: 收盘价下穿13日均线, 开空仓")
            self.execute_trade(timeframe, 'open', 'short')
            self.execute_trade(timeframe, 'close', 'long')

    def update(self):
        for timeframe in self.timeframes:
            self.run(timeframe)

# 主程序
api = TqApi(auth=TqAuth("wolfquant", "ftp123"))
# api = TqApi(TqKq(), auth="wolfquant,ftp123", disable_print=True, debug=None)
try:
    product_id = 'OI'
    symbol = api.query_cont_quotes(product_id=product_id)[0]
    print(f"交易的合约是: {symbol}")

    # 定义交易周期（以秒为单位）和每个周期的持仓限制
    timeframes = [60, 180, 800, 900]  # 1分钟, 3分钟, 13分20秒, 15分钟
    max_positions = {
        60: {'long': 1, 'short': 1},
        180: {'long': 2, 'short': 1},
        800: {'long': 1, 'short': 2},
        900: {'long': 2, 'short': 2}
    }

    strategy = MultiTimeframeStrategy(api, symbol, timeframes, max_positions)

    while True:
        api.wait_update()
        strategy.update()

except KeyboardInterrupt:
    print("策略已停止")
finally:
    api.close()