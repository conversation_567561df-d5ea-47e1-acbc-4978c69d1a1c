import pandas as pd
import numpy as np
from loguru import logger as mylog
from tqsdk import TqApi, TqAuth, TqKq
from tqsdk.tafunc import time_to_str
import copy
import time
from speaktext import speak_text

from myfunction import count_same_from_right


def MA(series, n):
    return series.rolling(window=n).mean()


def VALUEWHEN(condition, value):
    return value[condition].reindex(value.index).ffill()


def indicator(df):
    # 计算指标
    df['MA1'] = MA(df['close'], 5)

    df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)), df['high'].shift(2), 0)
    df['HH2'] = VALUEWHEN(df['HH1'] > 0, df['HH1'])

    df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)), df['low'].shift(2), 0)
    df['LL2'] = VALUEWHEN(df['LL1'] > 0, df['LL1'])

    df['K1'] = np.where(df['close'] > df['HH2'], 1, np.where(df['close'] < df['LL2'], -1, 0))
    df['K2'] = VALUEWHEN(df['K1'] != 0, df['K1'])

    df['G'] = np.where(df['K2'] == -1, df['HH2'], df['LL2'])
    df['G1'] = df['G'].iloc[-1]

    df['W1'] = df['K2']
    df['W2'] = df['open'] - df['close']
    df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
    df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])

    return df


def on_bar(klines):
    df = indicator(klines)
    global current_signal, signal_price
    if df['W1'].iloc[-2] == 1 and df['W1'].iloc[-3] == -1:
        print(df['G1'].iloc[-1])
        print(df['W1'].tolist()[-20:])
        print('买入信号')
        current_signal = "做多"
        signal_price = klines.iloc[-1]['close']

    elif df['W1'].iloc[-2] == -1 and df['W1'].iloc[-3] == 1:
        print(df['G1'].iloc[-1])
        print(df['W1'].tolist()[-20:])
        print('卖出信号')
        current_signal = "做空"
        signal_price = klines.iloc[-1]['close']
    else:
        print('止损价:', df['G1'].iloc[-1], '当前信号:', current_signal, '信号价格:', signal_price, '当前价格:',
              klines.iloc[-1]['close'])
        print(df['W1'].tolist()[-20:])


def on_signal(klines):
    pass


class WaveTrading:
    def __init__(self, account, symbol, interval, bklimit, sklimit, singlevolume):
        self.symbol = symbol
        self.interval = interval
        self.account = account
        self.api = TqApi(TqKq(), auth=self.account, disable_print=True, debug=None)
        self.position = self.api.get_position(self.symbol)
        self.bklimit = bklimit
        self.sklimit = sklimit
        self.singlevolume = singlevolume

        self.klines_history = self.api.get_kline_serial(symbol, duration_seconds=interval, data_length=3333)
        self.klines_tmp = self.api.get_kline_serial(symbol, duration_seconds=interval, data_length=3)

        klines_history = self.api.get_kline_serial(symbol, duration_seconds=interval, data_length=3333).dropna().set_index('datetime')
        self.klines = copy.deepcopy(klines_history)
        del klines_history
        self.df = indicator(self.klines)

        self.display_signal_info()

    def display_signal_info(self):
        current_signal = "多" if self.df['W1'].iloc[-1] == 1 else "空"
        cont_interval = count_same_from_right(self.df['W1'].tolist())
        signal_price = self.df.close.iloc[-cont_interval]
        signal_profit=signal_price - self.df.close.iloc[-1] if current_signal == "空" else self.df.close.iloc[-1] - signal_price
        print(self.account, self.symbol, '当前信号:', current_signal, '信号持续周期', cont_interval, '信号价格:', signal_price, '当前价格:', self.df.close.iloc[-1], '止损价格:', self.df['G1'].iloc[-1], '盈亏:',signal_profit)

    def run(self):
        print("开始运行")
        while True:
            self.api.wait_update()
            if self.api.is_changing(self.klines_tmp.iloc[-1], 'datetime'):
                newk = self.klines_tmp.iloc[0:-1].set_index('datetime')

                for index, row in newk.iterrows():
                    if index not in self.klines.index:
                        self.klines.loc[index] = row
                        # print(time_to_str(index))
                        # print('当前数据共有:', len(self.klines))
                        # print(f"最新K线时间：", time_to_str(newk.index[-1]), '数据已经更新.更新时间:', time.asctime())
                        df = indicator(self.klines)
                        # print(df['G1'].iloc[-1])
                        # print(np.array(df['W1']).astype(int).tolist()[-20:])
                        current_signal = "做多" if df['W1'].iloc[-1] == 1 else "做空"

                        if df['W1'].iloc[-1] == 1 and df['W1'].iloc[-2] == -1:
                            print(self.symbol, '买入信号')
                            speak_text('买入信号')
                            current_signal = "做多"
                            signal_price = df.close.iloc[-1]
                            print(self.account, self.symbol, self.position.pos_long, self.bklimit)
                            if self.position.pos_long < self.bklimit:
                                self.api.insert_order(symbol=self.symbol, direction='BUY', offset='OPEN', limit_price=signal_price, volume=self.singlevolume)
                                mylog.info(''.join([self.account, self.symbol, '下多单...', 'price:', str(signal_price), 'volume:', str(self.singlevolume)]))
                            else:
                                mylog.info(''.join([self.account, self.symbol, '多单数量已达上限...', 'price:', str(signal_price), 'volume:', str(self.singlevolume)]))
                            print(self.account, self.symbol, self.position.pos_short, self.sklimit, self.position.position_profit_short*10, self.position.float_profit_short)
                            if self.position.position_profit_short > self.position.pos_short * 10 and self.position.float_profit_short > 0:
                                self.api.insert_order(symbol=self.symbol, direction='BUY', offset='CLOSE', limit_price=signal_price, volume=self.singlevolume)
                                mylog.info(''.join([self.account, self.symbol, '平空单...', 'price:', str(signal_price), 'volume:', str(self.singlevolume)]))
                        if df['W1'].iloc[-1] == -1 and df['W1'].iloc[-2] == 1:
                            print(self.account, self.symbol, '卖出信号')
                            speak_text('卖出信号')
                            current_signal = "做空"
                            signal_price = df.close.iloc[-1]
                            print(self.account, self.symbol, self.position.pos_short, self.sklimit)
                            if self.position.pos_short < self.sklimit:
                                self.api.insert_order(symbol=self.symbol, direction='SELL', offset='OPEN', limit_price=signal_price, volume=self.singlevolume)
                                mylog.info(''.join([self.account, self.symbol, '下空单...', 'price:', str(signal_price), 'volume:', str(self.singlevolume)]))
                            else:
                                mylog.info(''.join([self.account, self.symbol, '空单数量已达上限...', 'price:', str(signal_price), 'volume:', str(self.singlevolume)]))
                            if self.position.position_profit_long > self.position.pos_long * 10 and self.position.float_profit_long > 0:
                                self.api.insert_order(symbol=self.symbol, direction='SELL', offset='CLOSE', limit_price=signal_price, volume=self.singlevolume)
                                mylog.info(''.join([self.account, self.symbol, '平多单...', 'price:', str(signal_price), 'volume:', str(self.singlevolume)]))
                    else:
                        print(f"最新K线时间：{time_to_str(newk.index[-1])}", '数据没有更新.当前时间:', time.asctime(), self.account, self.symbol)
                        print('')

                self.display_signal_info()


if __name__ == '__main__':
    account = 'ningyukun1,258369'
    symbol = "CZCE.OI411"
    interval = 15
    bklimit = 25
    sklimit = 25
    singlevolume = 1
    bot = WaveTrading(account, symbol, interval, bklimit, sklimit, singlevolume)
    bot.run()


