
import tkinter as tk
from tkinter import ttk
import json
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 读取数据
with open('simulatedaysummary_new.json', 'r', encoding='utf-8') as file:
    data = json.load(file)

dates = list(data.keys())
accounts_set = set()
for users in data.values():
    for user in users:
        accounts_set.add(user["user_name"])
accounts = list(accounts_set)

# 初始化每个账户的资金曲线
account_balances = {acc: [] for acc in accounts}
last_balance = {acc: 0 for acc in accounts}

for date in dates:
    users_today = {user["user_name"]: user["balance"] for user in data[date]}
    for acc in accounts:
        if acc in users_today:
            last_balance[acc] = users_today[acc]
        # 补齐：如果当天没有数据，延用前一天余额
        account_balances[acc].append(last_balance[acc])

root = tk.Tk()
root.title("账号资金曲线查看器")
default_account = 'suiyz' if 'suiyz' in accounts else accounts[0]
selected_account = tk.StringVar(value=default_account)

def plot_balance(account):
    fig, ax = plt.subplots(figsize=(8, 4))
    ax.plot(dates, account_balances[account], marker='o')
    ax.set_title(f"{account} 资金曲线")
    ax.set_xlabel("交易日")
    ax.set_ylabel("资金")
    ax.tick_params(axis='x', rotation=45)
    fig.tight_layout()
    return fig

def update_plot(*args):
    fig = plot_balance(selected_account.get())
    canvas.figure = fig
    canvas.draw()

account_menu = ttk.OptionMenu(root, selected_account, default_account, *accounts)
account_menu.pack(pady=10)

refresh_btn = ttk.Button(root, text="刷新", command=update_plot)
refresh_btn.pack(pady=5)

fig = plot_balance(default_account)
canvas = FigureCanvasTkAgg(fig, master=root)
canvas.get_tk_widget().pack()

root.mainloop()