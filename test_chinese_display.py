"""
测试中文显示修复效果
创建模拟数据并测试图表显示
"""

import json
import os
import matplotlib.pyplot as plt
import matplotlib
import platform
from datetime import datetime, timedelta

def setup_chinese_font():
    """设置matplotlib中文字体支持"""
    system = platform.system()
    
    if system == "Windows":
        fonts = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong']
    elif system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Heiti TC', 'PingFang SC']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']
    
    for font in fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            # 测试字体
            fig, ax = plt.subplots()
            ax.text(0.5, 0.5, '测试中文显示', fontsize=12)
            plt.close(fig)
            print(f"✓ 成功设置字体: {font}")
            return True
        except:
            continue
    
    print("⚠️  警告: 无法设置中文字体")
    return False

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 生成日期
    start_date = datetime(2024, 1, 1)
    dates = []
    for i in range(30):  # 30个交易日
        date = start_date + timedelta(days=i)
        if date.weekday() < 5:  # 只包含工作日
            dates.append(date.strftime('%Y-%m-%d'))
    
    # 生成账户数据
    accounts = ['张三的账户', '李四投资', '王五基金', '赵六理财', 'Alpha策略']
    
    data = {}
    
    for date in dates:
        data[date] = []
        
        for i, account in enumerate(accounts):
            # 模拟资金变化
            base_balance = 100000 + i * 50000  # 基础资金
            days_passed = dates.index(date)
            
            # 添加一些随机波动
            import random
            random.seed(hash(account + date) % 1000)
            
            # 模拟不同的收益模式
            if i == 0:  # 稳定增长
                balance = base_balance * (1 + 0.001 * days_passed + random.uniform(-0.002, 0.002))
            elif i == 1:  # 波动较大
                balance = base_balance * (1 + 0.002 * days_passed + random.uniform(-0.01, 0.01))
            elif i == 2:  # 下跌趋势
                balance = base_balance * (1 - 0.001 * days_passed + random.uniform(-0.005, 0.005))
            elif i == 3:  # 震荡
                balance = base_balance * (1 + 0.01 * (random.random() - 0.5))
            else:  # 高收益高风险
                balance = base_balance * (1 + 0.005 * days_passed + random.uniform(-0.02, 0.02))
            
            data[date].append({
                "user_name": account,
                "balance": max(balance, base_balance * 0.5)  # 最低不低于50%
            })
    
    # 保存测试数据
    with open('simulatedaysummary_new.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"✓ 测试数据已创建: {len(accounts)}个账户, {len(dates)}个交易日")
    return True

def test_chinese_display():
    """测试中文显示"""
    print("=" * 60)
    print("测试中文显示效果")
    print("=" * 60)
    
    # 设置中文字体
    font_ok = setup_chinese_font()
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 测试数据
    x = range(10)
    y = [100000 + i * 5000 for i in x]
    
    # 绘制图表
    ax.plot(x, y, marker='o', linewidth=2, color='#1f77b4')
    
    # 设置中文标题和标签
    ax.set_title("张三的账户 资金曲线", fontsize=16, fontweight='bold')
    ax.set_xlabel("交易日", fontsize=12)
    ax.set_ylabel("资金（元）", fontsize=12)
    
    # 格式化Y轴
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = "起始资金: 100,000元\n"
    stats_text += "最终资金: 145,000元\n"
    stats_text += "收益率: +45.00%"
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
            verticalalignment='top', bbox=dict(boxstyle='round',
            facecolor='wheat', alpha=0.8), fontsize=10)
    
    plt.tight_layout()
    
    # 保存测试图片
    test_filename = "中文显示测试.png"
    try:
        fig.savefig(test_filename, dpi=150, bbox_inches='tight', facecolor='white')
        print(f"✓ 测试图片已保存: {test_filename}")
        
        # 检查文件是否存在
        if os.path.exists(test_filename):
            file_size = os.path.getsize(test_filename)
            print(f"✓ 文件大小: {file_size:,} 字节")
        
    except Exception as e:
        print(f"✗ 保存测试图片失败: {e}")
        return False
    
    plt.close(fig)
    
    return font_ok

def test_program_import():
    """测试程序导入"""
    print("=" * 60)
    print("测试程序模块导入")
    print("=" * 60)
    
    try:
        # 测试必要的模块
        import tkinter as tk
        print("✓ tkinter 导入成功")
        
        from tkinter import ttk
        print("✓ ttk 导入成功")
        
        import matplotlib.pyplot as plt
        print("✓ matplotlib 导入成功")
        
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        print("✓ FigureCanvasTkAgg 导入成功")
        
        import json
        print("✓ json 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def check_data_file():
    """检查数据文件"""
    print("=" * 60)
    print("检查数据文件")
    print("=" * 60)
    
    filename = 'simulatedaysummary_new.json'
    
    if not os.path.exists(filename):
        print(f"⚠️  数据文件不存在: {filename}")
        print("正在创建测试数据...")
        create_test_data()
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        dates = list(data.keys())
        accounts = set()
        
        for date in dates:
            for user in data[date]:
                accounts.add(user["user_name"])
        
        print(f"✓ 数据文件加载成功")
        print(f"✓ 交易日数量: {len(dates)}")
        print(f"✓ 账户数量: {len(accounts)}")
        print(f"✓ 账户列表: {list(accounts)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据文件检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("模拟盘资金曲线中文显示修复测试")
    print("=" * 80)
    
    tests = [
        ("程序模块导入", test_program_import),
        ("数据文件检查", check_data_file),
        ("中文显示测试", test_chinese_display),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:15}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 中文显示修复成功！")
        print("\n修复内容:")
        print("1. ✓ 添加了中文字体自动检测和设置")
        print("2. ✓ 改进了图表布局和样式")
        print("3. ✓ 增强了GUI界面功能")
        print("4. ✓ 添加了导出功能")
        print("5. ✓ 改进了错误处理")
        print("\n现在可以运行:")
        print("python 模拟盘资金曲线_分帐户.py")
    else:
        print("\n⚠️  部分测试失败，可能需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
