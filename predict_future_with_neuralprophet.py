import pandas as pd
import matplotlib.pyplot as plt
from neuralprophet import NeuralProphet
from tqsdk import Tq<PERSON>pi, TqAccount, TqKq

api = TqApi(TqKq(), auth="walkquant,ftp123")
product = 'OI'
product_id = 'OI'
# product_id = 'rb'
product_index_prefix = 'KQ.i'
product_main_prifix = 'KQ.m'
symbol = api.query_cont_quotes(product_id=product_id)[0]
exchang_id = symbol.split('.')[0]
symbol_index = product_index_prefix + '@' + exchang_id + '.' + product_id
symbol_main = product_main_prifix + '@' + exchang_id + '.' + product_id
symbol = symbol_index

stocks=api.get_kline_serial(symbol, duration_seconds=60*60, data_length=8964).dropna()
api.close()


# filename = 'sz000034.pkl'
# stocks = pd.read_pickle(filename)
# print(stocks.head())
stocks['datetime'] = pd.to_datetime(stocks['datetime'])
stocks = stocks[['datetime', 'close']]
# print(stocks)

stocks.columns = ['ds', 'y']

# print(stocks.head())
plt.plot(stocks['ds'], stocks['y'], label='actual', c='g')
plt.show()

model = NeuralProphet()
model.fit(stocks, freq='1H')

future= model.make_future_dataframe(stocks, periods=300)

forecast = model.predict(future)
actual_prediction = model.predict(stocks)

plt.plot(actual_prediction['ds'], actual_prediction['yhat1'], label="predict_actual", c='r')
plt.plot(forecast['ds'], forecast['yhat1'], label= 'future_prediction', c = 'b')
plt.plot(stocks['ds'], stocks['y'], label='actual', c='g')
plt.legend()
plt.show()
