"""
测试缠论策略参数提示功能
"""

import subprocess
import sys
import time
from loguru import logger

def test_help_display():
    """测试帮助信息显示"""
    print("=" * 60)
    print("测试帮助信息显示")
    print("=" * 60)
    
    try:
        # 测试 --help 参数
        result = subprocess.run([
            sys.executable, "chanlun_strategy.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            help_text = result.stdout
            
            # 检查帮助信息内容
            checks = [
                ("缠论策略回测程序", "程序标题"),
                ("--tq-user", "TQ用户名参数"),
                ("--tq-pwd", "TQ密码参数"),
                ("--init-money", "初始资金参数"),
                ("--start-date", "开始日期参数"),
                ("--end-date", "结束日期参数"),
                ("使用示例", "使用示例"),
                ("默认配置", "默认配置说明"),
            ]
            
            print("✅ 帮助信息获取成功")
            print("\n📋 内容检查:")
            
            all_found = True
            for content, desc in checks:
                if content in help_text:
                    print(f"  ✅ {desc}: 找到")
                else:
                    print(f"  ❌ {desc}: 未找到")
                    all_found = False
            
            if all_found:
                print("\n✅ 所有帮助内容都已正确添加")
                return True
            else:
                print("\n❌ 部分帮助内容缺失")
                return False
        else:
            print(f"❌ 获取帮助信息失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_parameter_parsing():
    """测试参数解析功能"""
    print("=" * 60)
    print("测试参数解析功能")
    print("=" * 60)
    
    try:
        # 测试参数解析（不实际运行策略）
        from chanlun_strategy import create_argument_parser
        
        parser = create_argument_parser()
        
        # 测试默认参数
        print("📊 测试默认参数:")
        args = parser.parse_args([])
        
        print(f"  TQ用户名: {args.tq_user}")
        print(f"  TQ密码: {args.tq_pwd}")
        print(f"  初始资金: {args.init_money:,} 元")
        print(f"  开始日期: {args.start_date}")
        print(f"  结束日期: {args.end_date}")
        
        # 验证默认值
        expected_defaults = {
            'tq_user': 'smartmanp',
            'tq_pwd': 'ftp123',
            'init_money': 1000000,
            'start_date': '20180101',
            'end_date': '20250728'
        }
        
        all_correct = True
        for param, expected in expected_defaults.items():
            actual = getattr(args, param)
            if actual == expected:
                print(f"  ✅ {param}: {actual}")
            else:
                print(f"  ❌ {param}: 期望{expected}, 实际{actual}")
                all_correct = False
        
        if not all_correct:
            return False
        
        # 测试自定义参数
        print(f"\n📊 测试自定义参数:")
        custom_args = parser.parse_args([
            '--tq-user', 'testuser',
            '--tq-pwd', 'testpwd',
            '--init-money', '2000000',
            '--start-date', '20200101',
            '--end-date', '20241231'
        ])
        
        expected_custom = {
            'tq_user': 'testuser',
            'tq_pwd': 'testpwd',
            'init_money': 2000000,
            'start_date': '20200101',
            'end_date': '20241231'
        }
        
        for param, expected in expected_custom.items():
            actual = getattr(custom_args, param)
            if actual == expected:
                print(f"  ✅ {param}: {actual}")
            else:
                print(f"  ❌ {param}: 期望{expected}, 实际{actual}")
                all_correct = False
        
        if all_correct:
            print("\n✅ 参数解析功能正常")
            return True
        else:
            print("\n❌ 参数解析有问题")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_function_imports():
    """测试函数导入"""
    print("=" * 60)
    print("测试函数导入")
    print("=" * 60)
    
    try:
        from chanlun_strategy import (
            show_usage_help, interactive_parameter_input,
            create_argument_parser, run_multi_main_symbol
        )
        
        print("✅ 函数导入成功:")
        print(f"  - show_usage_help: {show_usage_help}")
        print(f"  - interactive_parameter_input: {interactive_parameter_input}")
        print(f"  - create_argument_parser: {create_argument_parser}")
        print(f"  - run_multi_main_symbol: {run_multi_main_symbol}")
        
        # 测试参数解析器创建
        parser = create_argument_parser()
        print(f"  - 参数解析器创建成功: {type(parser)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 函数导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_no_args_behavior():
    """测试无参数运行行为"""
    print("=" * 60)
    print("测试无参数运行行为")
    print("=" * 60)
    
    try:
        # 创建一个测试脚本来模拟无参数运行
        test_script = """
import sys
sys.path.insert(0, '.')

# 模拟无参数运行
sys.argv = ['chanlun_strategy.py']

try:
    from chanlun_strategy import show_usage_help
    print("测试显示帮助信息:")
    show_usage_help()
    print("\\n✅ 帮助信息显示成功")
except Exception as e:
    print(f"❌ 帮助信息显示失败: {e}")
"""
        
        # 写入临时测试文件
        with open('temp_test_chanlun.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        # 运行测试
        result = subprocess.run([
            sys.executable, 'temp_test_chanlun.py'
        ], capture_output=True, text=True, timeout=30)
        
        # 清理临时文件
        import os
        if os.path.exists('temp_test_chanlun.py'):
            os.remove('temp_test_chanlun.py')
        
        if result.returncode == 0:
            output = result.stdout
            if "缠论策略回测程序" in output and "使用示例" in output:
                print("✅ 无参数运行显示帮助信息成功")
                return True
            else:
                print("❌ 帮助信息内容不完整")
                print(f"输出: {output[:500]}...")
                return False
        else:
            print(f"❌ 测试脚本运行失败: {result.stderr}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def show_usage_examples():
    """显示使用示例"""
    print("=" * 60)
    print("缠论策略使用示例")
    print("=" * 60)
    
    examples = [
        {
            "title": "无参数运行（交互式）",
            "command": "python chanlun_strategy.py",
            "description": "显示帮助信息和交互式参数配置"
        },
        {
            "title": "查看帮助信息",
            "command": "python chanlun_strategy.py --help",
            "description": "显示完整的命令行帮助信息"
        },
        {
            "title": "使用默认配置",
            "command": "python chanlun_strategy.py --tq-user smartmanp --tq-pwd ftp123",
            "description": "使用默认配置运行缠论策略"
        },
        {
            "title": "自定义时间范围",
            "command": "python chanlun_strategy.py --start-date 20200101 --end-date 20241231",
            "description": "自定义回测时间范围"
        },
        {
            "title": "自定义初始资金",
            "command": "python chanlun_strategy.py --init-money 2000000",
            "description": "设置初始资金为200万元"
        },
        {
            "title": "完整自定义配置",
            "command": "python chanlun_strategy.py --tq-user myuser --tq-pwd mypwd --init-money 500000 --start-date 20220101 --end-date 20231231",
            "description": "完全自定义所有参数"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")
    
    print(f"\n📋 参数说明:")
    print(f"  --tq-user: TQ用户名")
    print(f"  --tq-pwd: TQ密码")
    print(f"  --init-money: 初始资金（元）")
    print(f"  --start-date: 回测开始日期（YYYYMMDD格式）")
    print(f"  --end-date: 回测结束日期（YYYYMMDD格式）")


def main():
    """主测试函数"""
    print("缠论策略参数提示功能测试")
    print("=" * 80)
    
    tests = [
        ("函数导入", test_function_imports),
        ("参数解析功能", test_parameter_parsing),
        ("帮助信息显示", test_help_display),
        ("无参数运行行为", test_no_args_behavior),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 缠论策略参数提示功能测试成功！")
        show_usage_examples()
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
