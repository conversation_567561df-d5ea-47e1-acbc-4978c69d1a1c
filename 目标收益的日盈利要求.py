def calculate_required_daily_return():
    # 获取用户输入
    principal = float(input("请输入本金（元）: "))
    target_annual_return = float(input("请输入期望年化收益率（%）: ")) / 100.0
    position_ratio = float(input("请输入持仓比例（%）: ")) / 100.0

    # 验证输入有效性
    if principal <= 0 or target_annual_return <= 0 or position_ratio <= 0 or position_ratio > 1:
        print("输入数据无效：本金和收益率必须为正数，持仓比例在0-100%之间")
        return

    # 设置交易天数（2025年中国期货交易日）
    trading_days = 242

    # 计算最终目标资产
    target_assets = principal * (1 + target_annual_return)

    # 计算每日账户必须达到的收益率（复利计算）
    daily_account_return = (target_assets / principal) ** (1 / trading_days) - 1

    # 计算每日持仓部分必须达到的收益率
    daily_position_return = daily_account_return / position_ratio

    print("\n计算结果：")
    print(f"全年交易日数: {trading_days}天")
    print(f"初始本金: {principal:.2f}元")
    print(f"目标年化收益率: {target_annual_return * 100:.2f}%")
    print(f"期末目标资产: {target_assets:.2f}元")
    print(f"每日账户必须收益率: {daily_account_return * 100:.6f}%")
    print(f"每日持仓部分必须收益率: {daily_position_return * 100:.6f}%\n")

    # 计算每日必须达到的盈利
    print("=" * 85)
    print(f"{'交易日':<8} {'当日开始资产(元)':<18} {'持仓资金(元)':<18} {'持仓必须盈利(元)':<18} {'持仓必须收益率':<15} {'累计需完成比例':<15}")
    print("-" * 85)

    current_assets = principal
    cumulative_return = 0

    for day in range(1, trading_days + 1):
        # 计算当日持仓资金
        position_value = current_assets * position_ratio

        # 计算当日持仓必须盈利
        required_position_profit = position_value * daily_position_return

        # 更新账户资产（复利增长）
        current_assets = current_assets * (1 + daily_account_return)
        cumulative_return = (current_assets / principal - 1) * 100

        # 输出前5天、最后5天和每10天的数据
        if day <= 5 or day >= trading_days - 4 or day % 3 == 0:
            print(f"{day:<8} {current_assets / (1 + daily_account_return):<18.2f} {position_value:<18.2f} "
                  f"{required_position_profit:<18.2f} {daily_position_return * 100:.6f}% {cumulative_return:<15.6f}%")

    print("=" * 85)
    print(f"最终目标资产: {target_assets:.2f}元 | 累计收益: {target_assets - principal:.2f}元 | "
          f"累计收益率: {(target_assets / principal - 1) * 100:.2f}%")
    print(f"平均每日持仓必须收益率: {daily_position_return * 100:.6f}%")


# 运行程序
if __name__ == "__main__":
    calculate_required_daily_return()