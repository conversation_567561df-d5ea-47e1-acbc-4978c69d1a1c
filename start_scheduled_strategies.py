"""
批量启动定时策略脚本
支持多品种同时运行定时策略
"""

import subprocess
import sys
import time
import os
import json
import psutil
from datetime import datetime


class StrategyManager:
    """策略管理器"""

    def __init__(self):
        self.processes = {}
        self.log_dir = "logs"
        self.state_file = "strategy_manager_state.json"

        # 创建日志目录
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        # 加载之前的状态
        self.load_state()

    def load_state(self):
        """加载策略状态"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                # 验证进程是否仍在运行
                for product, info in state_data.items():
                    pid = info.get('pid')
                    if pid and self.is_process_running(pid):
                        # 重建进程对象
                        try:
                            process = psutil.Process(pid)
                            self.processes[product] = {
                                'process': process,
                                'start_time': datetime.fromisoformat(info['start_time']),
                                'log_file': info['log_file'],
                                'pid': pid
                            }
                            print(f"恢复策略状态: {product} (PID: {pid})")
                        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                            print(f"进程 {pid} 不存在或无权限访问: {e}")
                    else:
                        print(f"进程 {pid} 未运行，跳过 {product}")
        except Exception as e:
            print(f"加载状态失败: {e}")

    def save_state(self):
        """保存策略状态"""
        try:
            state_data = {}
            for product, info in self.processes.items():
                if hasattr(info['process'], 'pid'):
                    pid = info['process'].pid
                else:
                    pid = info.get('pid')

                state_data[product] = {
                    'pid': pid,
                    'start_time': info['start_time'].isoformat(),
                    'log_file': info['log_file']
                }

            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存状态失败: {e}")

    def is_process_running(self, pid):
        """检查进程是否在运行"""
        try:
            process = psutil.Process(pid)
            return process.is_running()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False

    def start_strategy(self, product: str, auth: str = "quant_ggh,Qiai1301", mode: str = "schedule"):
        """启动单个策略"""
        if product in self.processes:
            print(f"策略 {product} 已在运行中")
            return False
        
        try:
            # 构建命令
            cmd = [
                sys.executable,
                "TimeRoseMA_cross_ag_MultiTimeFrames.py",
                product,
                mode,
                auth
            ]
            
            # 日志文件
            log_file = os.path.join(self.log_dir, f"{product}_schedule.log")
            
            # 启动进程
            with open(log_file, 'w', encoding='utf-8') as f:
                process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    text=True
                )
            
            self.processes[product] = {
                'process': process,
                'start_time': datetime.now(),
                'log_file': log_file,
                'pid': process.pid
            }

            # 保存状态
            self.save_state()

            print(f"✓ 策略 {product} 启动成功 (PID: {process.pid})")
            print(f"  日志文件: {log_file}")
            return True
            
        except Exception as e:
            print(f"✗ 策略 {product} 启动失败: {e}")
            return False
    
    def stop_strategy(self, product: str):
        """停止单个策略"""
        if product not in self.processes:
            print(f"策略 {product} 未在运行")
            return False
        
        try:
            process_info = self.processes[product]
            process = process_info['process']

            # 终止进程
            if hasattr(process, 'terminate'):
                # subprocess.Popen 对象
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            else:
                # psutil.Process 对象
                process.terminate()
                try:
                    process.wait(timeout=10)
                except psutil.TimeoutExpired:
                    process.kill()

            # 计算运行时间
            run_time = datetime.now() - process_info['start_time']

            print(f"✓ 策略 {product} 已停止 (运行时间: {run_time})")

            del self.processes[product]

            # 保存状态
            self.save_state()

            return True
            
        except Exception as e:
            print(f"✗ 停止策略 {product} 失败: {e}")
            return False
    
    def stop_all_strategies(self):
        """停止所有策略"""
        print("停止所有策略...")
        products = list(self.processes.keys())
        
        for product in products:
            self.stop_strategy(product)
    
    def list_strategies(self):
        """列出所有运行中的策略"""
        if not self.processes:
            print("当前没有运行中的策略")
            return
        
        print("运行中的策略:")
        for product, info in self.processes.items():
            process = info['process']
            start_time = info['start_time']
            run_time = datetime.now() - start_time

            # 检查进程状态
            try:
                if hasattr(process, 'poll'):
                    # subprocess.Popen 对象
                    if process.poll() is None:
                        status = "运行中"
                        pid = process.pid
                    else:
                        status = f"已退出 (返回码: {process.returncode})"
                        pid = info.get('pid', 'N/A')
                else:
                    # psutil.Process 对象
                    if process.is_running():
                        status = "运行中"
                        pid = process.pid
                    else:
                        status = "已退出"
                        pid = info.get('pid', 'N/A')
            except Exception as e:
                status = f"状态未知 ({e})"
                pid = info.get('pid', 'N/A')

            print(f"  {product}: PID {pid}, {status}, 运行时间: {run_time}")
    
    def check_strategies(self):
        """检查策略状态"""
        dead_processes = []
        
        for product, info in self.processes.items():
            process = info['process']
            if process.poll() is not None:
                print(f"⚠️  策略 {product} 已退出 (返回码: {process.returncode})")
                dead_processes.append(product)
        
        # 清理已退出的进程
        for product in dead_processes:
            del self.processes[product]
        
        # 保存状态
        if dead_processes:
            self.save_state()

        return len(dead_processes) == 0

    def cleanup_state(self):
        """清理状态文件"""
        try:
            if os.path.exists(self.state_file):
                os.remove(self.state_file)
                print("状态文件已清理")
        except Exception as e:
            print(f"清理状态文件失败: {e}")

    def force_cleanup(self):
        """强制清理所有策略和状态"""
        print("强制清理所有策略...")

        # 尝试从状态文件加载所有进程
        self.load_state()

        # 停止所有已知进程
        products = list(self.processes.keys())
        for product in products:
            try:
                self.stop_strategy(product)
            except Exception as e:
                print(f"停止策略 {product} 失败: {e}")

        # 清理状态文件
        self.cleanup_state()

        print("强制清理完成")
    
    def show_logs(self, product: str, lines: int = 20):
        """显示策略日志"""
        if product not in self.processes:
            print(f"策略 {product} 未在运行")
            return
        
        log_file = self.processes[product]['log_file']
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                print(f"策略 {product} 最近 {len(recent_lines)} 行日志:")
                print("-" * 50)
                for line in recent_lines:
                    print(line.rstrip())
                print("-" * 50)
                
        except Exception as e:
            print(f"读取日志失败: {e}")


def main():
    """主函数"""
    manager = StrategyManager()
    
    # 默认策略配置
    default_strategies = [
        "ag",  # 白银
        "rb",  # 螺纹钢
        "cu",  # 铜
    ]
    
    print("=" * 60)
    print("定时策略批量管理器")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "start":
            # 启动指定策略或所有默认策略
            if len(sys.argv) > 2:
                products = sys.argv[2:]
            else:
                products = default_strategies
            
            print(f"启动策略: {', '.join(products)}")
            
            for product in products:
                manager.start_strategy(product)
                time.sleep(2)  # 间隔启动
            
            print(f"\n已启动 {len(products)} 个策略")
            
        elif command == "stop":
            # 停止指定策略或所有策略
            if len(sys.argv) > 2:
                products = sys.argv[2:]
                for product in products:
                    manager.stop_strategy(product)
            else:
                manager.stop_all_strategies()
                
        elif command == "list":
            # 列出运行中的策略
            manager.list_strategies()
            
        elif command == "check":
            # 检查策略状态
            if manager.check_strategies():
                print("所有策略运行正常")
            else:
                print("发现异常退出的策略")
                
        elif command == "logs":
            # 显示日志
            if len(sys.argv) > 2:
                product = sys.argv[2]
                lines = int(sys.argv[3]) if len(sys.argv) > 3 else 20
                manager.show_logs(product, lines)
            else:
                print("用法: python start_scheduled_strategies.py logs <product> [lines]")
                
        elif command == "monitor":
            # 监控模式
            print("进入监控模式 (按 Ctrl+C 退出)")
            try:
                while True:
                    print(f"\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 策略状态检查")
                    manager.list_strategies()
                    manager.check_strategies()
                    time.sleep(60)  # 每分钟检查一次
            except KeyboardInterrupt:
                print("\n退出监控模式")

        elif command == "cleanup":
            # 清理状态
            manager.cleanup_state()

        elif command == "force-stop":
            # 强制清理
            manager.force_cleanup()

        else:
            print_usage()
    else:
        print_usage()


def print_usage():
    """打印使用说明"""
    print("用法:")
    print("  python start_scheduled_strategies.py <command> [args]")
    print("")
    print("命令:")
    print("  start [products...]    启动策略 (默认: ag rb cu)")
    print("  stop [products...]     停止策略 (无参数则停止所有)")
    print("  list                   列出运行中的策略")
    print("  check                  检查策略状态")
    print("  logs <product> [lines] 显示策略日志")
    print("  monitor                监控模式")
    print("  cleanup                清理状态文件")
    print("  force-stop             强制停止所有策略并清理状态")
    print("")
    print("示例:")
    print("  python start_scheduled_strategies.py start           # 启动默认策略")
    print("  python start_scheduled_strategies.py start ag rb     # 启动指定策略")
    print("  python start_scheduled_strategies.py stop            # 停止所有策略")
    print("  python start_scheduled_strategies.py stop ag         # 停止ag策略")
    print("  python start_scheduled_strategies.py list            # 列出策略")
    print("  python start_scheduled_strategies.py logs ag 50      # 显示ag日志")
    print("  python start_scheduled_strategies.py monitor         # 监控模式")
    print("  python start_scheduled_strategies.py cleanup         # 清理状态文件")
    print("  python start_scheduled_strategies.py force-stop      # 强制清理")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        # 这里可以添加清理逻辑
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)
