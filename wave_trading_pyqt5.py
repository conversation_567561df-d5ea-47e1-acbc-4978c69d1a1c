import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer
from tqsdk import TqApi, TqAuth, TqKq

class TradingApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.initTradingSystem()

    def initUI(self):
        self.setWindowTitle('Futures Trading System')
        self.setGeometry(100, 100, 1200, 800)

        # 创建主窗口部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 创建 matplotlib 图形和画布
        self.figure, self.ax = plt.subplots(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # 设置定时器以更新数据和图表
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_data_and_plot)
        self.timer.start(15000)  # 每15秒更新一次，与K线周期相匹配

    def initTradingSystem(self):
        self.api = TqApi(TqKq(), auth=TqAuth("walkquant", "ftp123"))
        self.symbol = self.api.query_cont_quotes(product_id='OI')[0]
        self.symbol = 'CZCE.OI501'

    def MA(self, series, n):
        return series.rolling(window=n).mean()

    def VALUEWHEN(self, condition, value):
        return value[condition].reindex(value.index).ffill()

    def REFX1(self, series, n):
        return series.shift(n)

    def indicator(self, df):
        # 计算指标（与原代码相同）

        # 计算指标
        df['MA1'] = MA(df['close'], 5)

        df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
                             df['high'].shift(2), 0)
        df['HH2'] = VALUEWHEN(df['HH1'] > 0, df['HH1'])

        df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)),
                             df['low'].shift(2),
                             0)
        df['LL2'] = VALUEWHEN(df['LL1'] > 0, df['LL1'])

        df['K1'] = np.where(df['close'] > df['HH2'], -3, np.where(df['close'] < df['LL2'], 1, 0))
        df['K2'] = VALUEWHEN(df['K1'] != 0, df['K1'])

        df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
        df['G1'] = df['G'].iloc[-1]

        df['W1'] = df['K2']
        df['W2'] = df['open'] - df['close']
        df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
        df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])

        df['LLL'] = np.minimum(df['open'].shift(1), df['close'].shift(1))
        df['HHH'] = np.maximum(df['open'].shift(1), df['close'].shift(1))

        df['CCCC'] = REFX1(df['close'], 1000)
        df['LLLL'] = REFX1(df['LLL'], 1000)
        df['HHHH'] = REFX1(df['HHH'], 1000)

        # ...

    def plot_candlestick(self, df):
        self.ax.clear()
        width = 0.6
        width2 = 0.05

        up = df[df.close > df.open]
        down = df[df.close < df.open]
        equal = df[df.close == df.open]

        self.ax.bar(up.index, up.close - up.open, width, bottom=up.open, color='red')
        self.ax.vlines(up.index, up.low, up.high, color='red', linewidth=1)

        self.ax.bar(down.index, down.close - down.open, width, bottom=down.open, color='green')
        self.ax.vlines(down.index, down.low, down.high, color='green', linewidth=1)

        self.ax.bar(equal.index, width2, width, bottom=equal.open - width2 / 2, color='cyan')
        self.ax.vlines(equal.index, equal.low, equal.high, color='cyan', linewidth=1)

    def plot_others(self, df):
        # 绘制其他指标（与原代码相同）
        # ...
        pass

    def update_data_and_plot(self):
        # 获取最新数据
        df = self.api.get_kline_serial(self.symbol, duration_seconds=15, data_length=200)
        df = self.indicator(df)

        # 更新图表
        self.plot_candlestick(df)
        self.plot_others(df)

        # 刷新画布
        self.canvas.draw()

    def closeEvent(self, event):
        self.api.close()
        event.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = TradingApp()
    ex.show()
    sys.exit(app.exec_())