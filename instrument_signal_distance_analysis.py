import sys
import copy
import os.path
import pickle
import gc
import time

from tqsdk import TqApi, TqKq

api = TqApi(TqKq(), auth="follower,ftp123", disable_print=True, debug=False)
deadproductid = ['bb', 'PM', 'JR', 'RI', 'LR', 'RS', 'wr']
from signal_distance_analysis import MaCrossCaculate, zjtj


def get_product_ids(api):
    productidlist = []

    symbols = api.query_cont_quotes()
    for s in symbols:
        exchangeid = s.split('.')[0]
        symbolid = ''.join(filter(str.isalpha, s.split('.')[1]))
        indexsymbolid = ''.join(['KQ.i@', exchangeid, '.', symbolid])
        productidlist.append(indexsymbolid)

    with open('productids.pkl', 'wb') as f:
        pickle.dump(productidlist, f)

    return productidlist


def get_symbol_bars(api, symbol, interval, datalength=8964):
    # from tqsdk import TqApi, TqKq
    # api = TqApi(TqKq(), auth="smartmanp,ftp123", disable_print=True, debug=False)
    bars = api.get_kline_serial(symbol, duration_seconds=interval, data_length=datalength).dropna()

    # api.close()
    return bars


if __name__ == '__main__':
    productidlist = get_product_ids(api)

    while True:
        productid = input('请输入合约代码(Q退出):')

        if productid.upper() == 'Q':
            api.close()
            sys.exit(0)

        else:
            productid = '.' + productid
            instid = ''.join(list(filter(lambda f: str(f).endswith(productid), productidlist)))
            if instid == '.':
                print('合约不存在，请重新输入．')
                continue

        interval = input('请输入ｋ线周期,分钟为单位:')
        try:
            interval = 60 * int(interval)
        except:
            interval = 60*3

        bars = get_symbol_bars(api, instid, interval, datalength=8964)
        avgdistance = MaCrossCaculate(bars, period=13)
        print(avgdistance)

    else:
        api.close()
