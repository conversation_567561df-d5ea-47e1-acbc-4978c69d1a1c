# 开发环境依赖
# 包含所有生产环境依赖 + 开发工具

-r requirements.txt

# 代码质量和格式化
black>=22.0.0
flake8>=4.0.0
isort>=5.10.0
mypy>=0.950

# 测试工具
pytest>=7.0.0
pytest-cov>=3.0.0
pytest-mock>=3.7.0

# 文档生成
sphinx>=4.5.0
sphinx-rtd-theme>=1.0.0

# 开发工具
ipython>=8.0.0
jupyter>=1.0.0
notebook>=6.4.0

# 调试工具
pdb++>=0.10.0
ipdb>=0.13.0

# 性能分析
memory-profiler>=0.60.0
line-profiler>=3.5.0

# 构建工具
setuptools>=60.0.0
wheel>=0.37.0
build>=0.8.0

# 版本管理
bump2version>=1.0.0

# 安全检查
safety>=2.0.0
bandit>=1.7.0
