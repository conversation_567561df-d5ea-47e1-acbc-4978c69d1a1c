import pandas as pd
import numpy as np


class TradingStrategy:
    def __init__(self):
        self.position_180s_long = 0
        self.position_180s_short = 0
        self.position_300s_long = 0
        self.position_300s_short = 0
        self.position_900s_long = 0
        self.position_900s_short = 0

        self.entry_price_180s_long = 0
        self.entry_price_180s_short = 0
        self.entry_price_300s_long = 0
        self.entry_price_300s_short = 0
        self.entry_price_900s_long = 0
        self.entry_price_900s_short = 0

        self.pnl_180s_long = 0
        self.pnl_180s_short = 0
        self.pnl_300s_long = 0
        self.pnl_300s_short = 0
        self.pnl_900s_long = 0
        self.pnl_900s_short = 0

        self.signals = pd.DataFrame(columns=['signal_180s', 'signal_300s', 'signal_900s'])

    def process_df(self, df):
        """
        处理单个DataFrame

        参数:
        df (pd.DataFrame): 包含K线数据的DataFrame

        返回:
        signals (pd.DataFrame): 包含交易信号的DataFrame
        """
        # 计算13周期均线
        df['ma13'] = df['close'].rolling(13).mean()

        # 根据周期长度判断处理逻辑
        if df['duration'][0] == 180:
            # 3分钟策略
            if df['close'][0] > df['ma13'][0] and df['close'][0] > df['ma13'][0]:
                self.signals['signal_180s'][0] = 1  # 多头信号
                if self.position_180s_long == 0:
                    self.position_180s_long = 1
                    self.entry_price_180s_long = df['open'][0]
            elif df['close'][0] < df['ma13'][0] and df['close'][0] < df['ma13'][0]:
                self.signals['signal_180s'][0] = -1  # 空头信号
                if self.position_180s_short == 0:
                    self.position_180s_short = 1
                    self.entry_price_180s_short = df['open'][0]
            else:
                self.signals['signal_180s'][0] = 0
                if self.position_180s_long == 1:
                    self.pnl_180s_long += df['close'][0] - self.entry_price_180s_long
                    self.position_180s_long = 0
                    self.entry_price_180s_long = 0
                if self.position_180s_short == 1:
                    self.pnl_180s_short += self.entry_price_180s_short - df['close'][0]
                    self.position_180s_short = 0
                    self.entry_price_180s_short = 0

        # 其他周期的策略逻辑与3分钟类似...

        # 如果当前持仓亏损,则不平仓
        if (self.pnl_180s_long + self.pnl_180s_short) < 0 and self.signals['signal_180s'][0] != 0:
            self.signals['signal_180s'][0] = 0
        # 其他周期的亏损判断逻辑与3分钟类似...

        return self.signals