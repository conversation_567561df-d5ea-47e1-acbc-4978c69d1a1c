"""
多合约分析配置文件
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Tuple, Optional
from llt_multi_contract_analyzer import ContractConfig, AnalysisConfig
import json
from tqsdk import TqApi, TqAuth, TqKq
from loguru import logger


class MainContractFetcher:
    """主力合约获取器"""

    def __init__(self, auth_username: str = "bigwolf", auth_password: str = "ftp123"):
        self.auth_username = auth_username
        self.auth_password = auth_password
        self.api = None
        self._main_contracts_cache = {}
        self._all_main_contracts = None  # 缓存所有主力合约

    def __enter__(self):
        """进入上下文管理器"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        self.disconnect()

    def connect(self):
        """连接API"""
        try:
            logger.info("🔌 连接TqSDK API获取主力合约信息...")
            auth = TqAuth(self.auth_username, self.auth_password)
            self.api = TqApi(TqKq(), auth=auth, disable_print=True)
            logger.success("✅ API连接成功")
        except Exception as e:
            logger.error(f"❌ API连接失败: {e}")
            raise

    def disconnect(self):
        """断开API连接"""
        if self.api:
            self.api.close()
            logger.info("🔌 API连接已关闭")

    def _fetch_all_main_contracts(self):
        """获取所有主力合约列表"""
        if self._all_main_contracts is not None:
            return self._all_main_contracts

        try:
            logger.info("📊 获取所有主力合约列表...")
            # 直接调用 query_cont_quotes() 不传参数
            all_quotes = self.api.query_cont_quotes()
            logger.debug(f"📊 API返回数据类型: {type(all_quotes)}")
            logger.debug(f"📊 API返回数据长度: {len(all_quotes) if all_quotes else 0}")

            if all_quotes:
                # 解析主力合约数据
                main_contracts = {}

                for quote in all_quotes:
                    try:
                        # 尝试不同的字段名来获取合约代码
                        symbol = None

                        if hasattr(quote, 'instrument_id'):
                            symbol = quote.instrument_id
                        elif hasattr(quote, 'symbol'):
                            symbol = quote.symbol
                        elif isinstance(quote, dict):
                            symbol = quote.get('instrument_id') or quote.get('symbol')
                        elif isinstance(quote, str):
                            symbol = quote

                        if symbol and '.' in symbol:
                            # 解析交易所和品种代码
                            parts = symbol.split('.')
                            if len(parts) >= 2:
                                exchange = parts[0]
                                contract_code = parts[1]

                                # 提取品种代码（去掉月份）
                                if exchange == "SHFE":
                                    # SHFE格式: cu2510 -> cu
                                    product_id = ''.join([c for c in contract_code if c.isalpha()])
                                elif exchange == "DCE":
                                    # DCE格式: m2501 -> m
                                    product_id = ''.join([c for c in contract_code if c.isalpha()])
                                elif exchange == "CZCE":
                                    # CZCE格式: OI2601 -> OI
                                    product_id = ''.join([c for c in contract_code if c.isalpha()])
                                elif exchange == "INE":
                                    # INE格式: lu2506 -> lu
                                    product_id = ''.join([c for c in contract_code if c.isalpha()])
                                else:
                                    continue

                                key = f"{exchange}.{product_id}"
                                main_contracts[key] = symbol
                                logger.debug(f"📊 解析主力合约: {key} -> {symbol}")

                    except Exception as e:
                        logger.debug(f"📊 解析合约数据失败: {quote}, 错误: {e}")
                        continue

                self._all_main_contracts = main_contracts
                logger.success(f"✅ 成功解析 {len(main_contracts)} 个主力合约")

                # 显示部分结果用于调试
                if main_contracts:
                    logger.debug("📊 部分主力合约示例:")
                    for i, (key, symbol) in enumerate(list(main_contracts.items())[:5]):
                        logger.debug(f"   {key}: {symbol}")
                    if len(main_contracts) > 5:
                        logger.debug(f"   ... 还有 {len(main_contracts) - 5} 个")

                return main_contracts
            else:
                logger.warning("⚠️  API返回空的主力合约列表")
                return {}

        except Exception as e:
            logger.error(f"❌ 获取所有主力合约失败: {e}")
            import traceback
            logger.debug(f"详细错误:\n{traceback.format_exc()}")
            return {}

    def get_main_contract(self, exchange: str, product_id: str) -> Optional[str]:
        """获取指定品种的主力合约"""
        try:
            cache_key = f"{exchange}.{product_id}"

            # 检查缓存
            if cache_key in self._main_contracts_cache:
                return self._main_contracts_cache[cache_key]

            # 获取所有主力合约
            all_main_contracts = self._fetch_all_main_contracts()

            if cache_key in all_main_contracts:
                main_contract = all_main_contracts[cache_key]
                self._main_contracts_cache[cache_key] = main_contract
                logger.info(f"✅ {product_id} API查询主力合约: {main_contract}")
                return main_contract
            else:
                logger.warning(f"⚠️  {product_id} 在主力合约列表中未找到")

                # 回退到推断模式
                logger.info(f"🔄 {product_id} API查询失败，使用推断模式")
                main_contract = self._infer_main_contract(exchange, product_id)
                if main_contract:
                    self._main_contracts_cache[cache_key] = main_contract
                    logger.warning(f"⚠️  {product_id} 推断主力合约: {main_contract}")
                    return main_contract

            logger.warning(f"⚠️  无法获取 {product_id} 主力合约")
            return None

        except Exception as e:
            logger.error(f"❌ 获取 {product_id} 主力合约失败: {e}")
            return None

    def _generate_possible_main_contracts(self, exchange: str, product_id: str) -> List[str]:
        """生成可能的主力合约列表"""
        from datetime import datetime, timedelta

        current_date = datetime.now()
        possible_contracts = []

        try:
            if exchange == "SHFE":
                # 上期所：通常当月、下月、下下月
                for months_ahead in [0, 1, 2, 3]:
                    target_date = current_date + timedelta(days=30 * months_ahead)
                    month_str = target_date.strftime('%y%m')
                    possible_contracts.append(f"SHFE.{product_id}{month_str}")

            elif exchange == "DCE":
                # 大商所：通常1月、5月、9月
                year = current_date.year % 100
                month = current_date.month

                # 当前年的可能月份
                for target_month in [1, 5, 9]:
                    if target_month >= month:
                        possible_contracts.append(f"DCE.{product_id}{year:02d}{target_month:02d}")

                # 下一年的可能月份
                next_year = (year + 1) % 100
                for target_month in [1, 5, 9]:
                    possible_contracts.append(f"DCE.{product_id}{next_year:02d}{target_month:02d}")

            elif exchange == "CZCE":
                # 郑商所：通常1月、5月、9月
                year = current_date.year % 100
                month = current_date.month

                # 当前年的可能月份
                for target_month in [1, 5, 9]:
                    if target_month >= month:
                        possible_contracts.append(f"CZCE.{product_id}{year:02d}{target_month:02d}")

                # 下一年的可能月份
                next_year = (year + 1) % 100
                for target_month in [1, 5, 9]:
                    possible_contracts.append(f"CZCE.{product_id}{next_year:02d}{target_month:02d}")

            logger.debug(f"📊 {product_id} 生成可能合约: {possible_contracts}")
            return possible_contracts

        except Exception as e:
            logger.error(f"❌ 生成 {product_id} 可能合约失败: {e}")
            return []

    def _infer_main_contract(self, exchange: str, product_id: str) -> Optional[str]:
        """根据交易所规则推断主力合约"""
        from datetime import datetime, timedelta

        current_date = datetime.now()

        try:
            if exchange == "SHFE":
                # 上期所：通常下个月或下下个月
                if product_id in ["au", "ag"]:  # 贵金属
                    next_month = current_date + timedelta(days=60)
                    return f"SHFE.{product_id}{next_month.strftime('%y%m')}"
                else:  # 其他金属
                    next_month = current_date + timedelta(days=90)
                    return f"SHFE.{product_id}{next_month.strftime('%y%m')}"

            elif exchange == "DCE":
                # 大商所：通常1月、5月、9月
                year = current_date.year % 100
                month = current_date.month

                if month <= 1:
                    main_month = f"{year:02d}05"
                elif month <= 5:
                    main_month = f"{year:02d}09"
                elif month <= 9:
                    main_month = f"{year:02d}01"
                    year = (year + 1) % 100
                    main_month = f"{year:02d}01"
                else:
                    year = (year + 1) % 100
                    main_month = f"{year:02d}05"

                return f"DCE.{product_id}{main_month}"

            elif exchange == "CZCE":
                # 郑商所：通常1月、5月、9月
                year = current_date.year % 100
                month = current_date.month

                if month <= 1:
                    main_month = f"{year:02d}05"
                elif month <= 5:
                    main_month = f"{year:02d}09"
                elif month <= 9:
                    year = (year + 1) % 100
                    main_month = f"{year:02d}01"
                else:
                    year = (year + 1) % 100
                    main_month = f"{year:02d}05"

                return f"CZCE.{product_id}{main_month}"

        except Exception as e:
            logger.error(f"❌ 推断 {product_id} 主力合约失败: {e}")

        return None

    def get_multiple_main_contracts(self, contract_specs: List[Tuple[str, str]]) -> Dict[str, str]:
        """批量获取多个品种的主力合约"""
        results = {}

        logger.info(f"📊 批量获取 {len(contract_specs)} 个品种的主力合约...")

        for exchange, product_id in contract_specs:
            main_contract = self.get_main_contract(exchange, product_id)
            if main_contract:
                results[f"{exchange}.{product_id}"] = main_contract
            else:
                logger.warning(f"⚠️  {exchange}.{product_id} 主力合约获取失败")

        logger.success(f"✅ 成功获取 {len(results)} 个主力合约")
        return results


# 品种基础信息配置
PRODUCT_INFO = {
    # 农产品
    'agricultural': [
        ("CZCE", "OI", "菜籽油", 10, 2, 0.05),
        ("CZCE", "RM", "菜籽粕", 10, 1, 0.05),
        ("CZCE", "CF", "棉花", 5, 5, 0.05),
        ("CZCE", "SR", "白糖", 10, 1, 0.05),
        ("CZCE", "TA", "PTA", 5, 2, 0.05),
        ("DCE", "m", "豆粕", 10, 1, 0.05),
        ("DCE", "y", "豆油", 10, 2, 0.05),
        ("DCE", "a", "豆一", 10, 1, 0.05),
        ("DCE", "c", "玉米", 10, 1, 0.05),
        ("DCE", "cs", "玉米淀粉", 10, 1, 0.05),
    ],

    # 金属
    'metals': [
        ("SHFE", "cu", "沪铜", 5, 10, 0.05),
        ("SHFE", "al", "沪铝", 5, 5, 0.05),
        ("SHFE", "zn", "沪锌", 5, 5, 0.05),
        ("SHFE", "pb", "沪铅", 5, 5, 0.05),
        ("SHFE", "ni", "沪镍", 1, 10, 0.05),
        ("SHFE", "sn", "沪锡", 1, 10, 0.05),
        ("SHFE", "au", "沪金", 1000, 0.02, 0.04),
        ("SHFE", "ag", "沪银", 15, 1, 0.05),
    ],

    # 化工
    'chemicals': [
        ("DCE", "pp", "聚丙烯", 5, 1, 0.05),
        ("DCE", "l", "聚乙烯", 5, 5, 0.05),
        ("DCE", "v", "PVC", 5, 5, 0.05),
        ("DCE", "eg", "乙二醇", 10, 1, 0.05),
        ("CZCE", "MA", "甲醇", 10, 1, 0.05),
        ("SHFE", "ru", "橡胶", 10, 5, 0.05),
        ("SHFE", "bu", "沥青", 10, 2, 0.05),
    ],

    # 黑色系
    'ferrous': [
        ("DCE", "i", "铁矿石", 100, 0.5, 0.05),
        ("DCE", "j", "焦炭", 100, 0.5, 0.05),
        ("DCE", "jm", "焦煤", 60, 0.5, 0.05),
        ("SHFE", "rb", "螺纹钢", 10, 1, 0.05),
        ("SHFE", "hc", "热卷", 10, 1, 0.05),
    ],

    # 能源
    'energy': [
        ("SHFE", "sc", "原油", 1000, 0.1, 0.05),
        ("SHFE", "fu", "燃料油", 10, 1, 0.05),
        ("INE", "lu", "低硫燃料油", 10, 1, 0.05),
    ]
}


def create_main_contracts_config(product_groups: List[str] = None,
                               auth_username: str = "bigwolf",
                               auth_password: str = "ftp123") -> Dict[str, List[ContractConfig]]:
    """创建基于主力合约的配置"""

    if product_groups is None:
        product_groups = list(PRODUCT_INFO.keys())

    # 收集需要查询的品种
    contract_specs = []
    group_products = {}

    for group in product_groups:
        if group in PRODUCT_INFO:
            group_products[group] = []
            for exchange, product_id, name, multiplier, min_price_change, margin_rate in PRODUCT_INFO[group]:
                contract_specs.append((exchange, product_id))
                group_products[group].append((exchange, product_id, name, multiplier, min_price_change, margin_rate))

    # 获取主力合约
    with MainContractFetcher(auth_username, auth_password) as fetcher:
        main_contracts = fetcher.get_multiple_main_contracts(contract_specs)

    # 构建配置
    result = {}

    for group in product_groups:
        if group not in group_products:
            continue

        contracts = []
        for exchange, product_id, name, multiplier, min_price_change, margin_rate in group_products[group]:
            key = f"{exchange}.{product_id}"
            if key in main_contracts:
                main_symbol = main_contracts[key]
                contracts.append(ContractConfig(
                    symbol=main_symbol,
                    name=f"{name}主力",
                    exchange=exchange,
                    product_id=product_id,
                    multiplier=multiplier,
                    min_price_change=min_price_change,
                    margin_rate=margin_rate
                ))
                logger.info(f"✅ {name}: {main_symbol}")
            else:
                logger.warning(f"⚠️  {name} 主力合约获取失败，跳过")

        result[group] = contracts
        logger.info(f"📊 {group} 组: {len(contracts)} 个主力合约")

    return result


# 静态合约配置（备用）
EXTENDED_CONTRACTS = {
    # 农产品 - 更全面的配置
    'agricultural_full': [
        ContractConfig("CZCE.OI601", "菜籽油主力", "CZCE", "OI", 10, 2, 0.05),
        ContractConfig("CZCE.OI605", "菜籽油05", "CZCE", "OI", 10, 2, 0.05),
        ContractConfig("CZCE.RM601", "菜籽粕主力", "CZCE", "RM", 10, 1, 0.05),
        ContractConfig("CZCE.RM605", "菜籽粕05", "CZCE", "RM", 10, 1, 0.05),
        ContractConfig("CZCE.CF601", "棉花主力", "CZCE", "CF", 5, 5, 0.05),
        ContractConfig("CZCE.CF605", "棉花05", "CZCE", "CF", 5, 5, 0.05),
        ContractConfig("CZCE.SR601", "白糖主力", "CZCE", "SR", 10, 1, 0.05),
        ContractConfig("CZCE.SR605", "白糖05", "CZCE", "SR", 10, 1, 0.05),
        ContractConfig("CZCE.TA601", "PTA主力", "CZCE", "TA", 5, 2, 0.05),
        ContractConfig("CZCE.TA605", "PTA05", "CZCE", "TA", 5, 2, 0.05),
        ContractConfig("DCE.m2501", "豆粕主力", "DCE", "m", 10, 1, 0.05),
        ContractConfig("DCE.m2505", "豆粕05", "DCE", "m", 10, 1, 0.05),
        ContractConfig("DCE.y2501", "豆油主力", "DCE", "y", 10, 2, 0.05),
        ContractConfig("DCE.y2505", "豆油05", "DCE", "y", 10, 2, 0.05),
        ContractConfig("DCE.a2501", "豆一主力", "DCE", "a", 10, 1, 0.05),
        ContractConfig("DCE.a2505", "豆一05", "DCE", "a", 10, 1, 0.05),
        ContractConfig("DCE.c2501", "玉米主力", "DCE", "c", 10, 1, 0.05),
        ContractConfig("DCE.c2505", "玉米05", "DCE", "c", 10, 1, 0.05),
        ContractConfig("DCE.cs2501", "玉米淀粉主力", "DCE", "cs", 10, 1, 0.05),
        ContractConfig("DCE.cs2505", "玉米淀粉05", "DCE", "cs", 10, 1, 0.05),
    ],
    
    # 金属 - 包含更多月份
    'metals_full': [
        ContractConfig("SHFE.cu2507", "沪铜07", "SHFE", "cu", 5, 10, 0.05),
        ContractConfig("SHFE.cu2508", "沪铜08", "SHFE", "cu", 5, 10, 0.05),
        ContractConfig("SHFE.cu2509", "沪铜09", "SHFE", "cu", 5, 10, 0.05),
        ContractConfig("SHFE.al2507", "沪铝07", "SHFE", "al", 5, 5, 0.05),
        ContractConfig("SHFE.al2508", "沪铝08", "SHFE", "al", 5, 5, 0.05),
        ContractConfig("SHFE.al2509", "沪铝09", "SHFE", "al", 5, 5, 0.05),
        ContractConfig("SHFE.zn2507", "沪锌07", "SHFE", "zn", 5, 5, 0.05),
        ContractConfig("SHFE.zn2508", "沪锌08", "SHFE", "zn", 5, 5, 0.05),
        ContractConfig("SHFE.pb2507", "沪铅07", "SHFE", "pb", 5, 5, 0.05),
        ContractConfig("SHFE.pb2508", "沪铅08", "SHFE", "pb", 5, 5, 0.05),
        ContractConfig("SHFE.ni2507", "沪镍07", "SHFE", "ni", 1, 10, 0.05),
        ContractConfig("SHFE.ni2508", "沪镍08", "SHFE", "ni", 1, 10, 0.05),
        ContractConfig("SHFE.sn2507", "沪锡07", "SHFE", "sn", 1, 10, 0.05),
        ContractConfig("SHFE.sn2508", "沪锡08", "SHFE", "sn", 1, 10, 0.05),
        ContractConfig("SHFE.au2506", "沪金06", "SHFE", "au", 1000, 0.02, 0.04),
        ContractConfig("SHFE.au2508", "沪金08", "SHFE", "au", 1000, 0.02, 0.04),
        ContractConfig("SHFE.ag2506", "沪银06", "SHFE", "ag", 15, 1, 0.05),
        ContractConfig("SHFE.ag2508", "沪银08", "SHFE", "ag", 15, 1, 0.05),
    ],
    
    # 高流动性合约
    'high_liquidity': [
        ContractConfig("SHFE.cu2507", "沪铜主力", "SHFE", "cu", 5, 10, 0.05),
        ContractConfig("SHFE.al2507", "沪铝主力", "SHFE", "al", 5, 5, 0.05),
        ContractConfig("SHFE.rb2505", "螺纹钢主力", "SHFE", "rb", 10, 1, 0.05),
        ContractConfig("DCE.i2501", "铁矿石主力", "DCE", "i", 100, 0.5, 0.05),
        ContractConfig("DCE.j2501", "焦炭主力", "DCE", "j", 100, 0.5, 0.05),
        ContractConfig("DCE.m2501", "豆粕主力", "DCE", "m", 10, 1, 0.05),
        ContractConfig("CZCE.OI601", "菜籽油主力", "CZCE", "OI", 10, 2, 0.05),
        ContractConfig("CZCE.TA601", "PTA主力", "CZCE", "TA", 5, 2, 0.05),
        ContractConfig("SHFE.au2506", "沪金主力", "SHFE", "au", 1000, 0.02, 0.04),
        ContractConfig("SHFE.ag2506", "沪银主力", "SHFE", "ag", 15, 1, 0.05),
    ],
    
    # 测试合约（少量合约用于快速测试）
    'test': [
        ContractConfig("SHFE.cu2507", "沪铜测试", "SHFE", "cu", 5, 10, 0.05),
        ContractConfig("CZCE.OI601", "菜籽油测试", "CZCE", "OI", 10, 2, 0.05),
        ContractConfig("DCE.m2501", "豆粕测试", "DCE", "m", 10, 1, 0.05),
    ]
}


@dataclass
class PresetAnalysisConfig:
    """预设分析配置"""
    name: str
    description: str
    contract_groups: List[str]
    d_value_range: Tuple[int, int]
    kline_period_seconds: int
    kline_data_length: int
    output_dir: str
    save_detailed_results: bool = True
    use_main_contracts: bool = True  # 是否使用主力合约
    auth_username: str = "bigwolf"
    auth_password: str = "ftp123"

    def to_analysis_config(self) -> AnalysisConfig:
        """转换为AnalysisConfig"""
        contracts = []

        if self.use_main_contracts:
            # 使用动态主力合约
            try:
                logger.info(f"🔄 获取主力合约配置: {self.contract_groups}")
                main_contracts_config = create_main_contracts_config(
                    product_groups=self.contract_groups,
                    auth_username=self.auth_username,
                    auth_password=self.auth_password
                )

                for group in self.contract_groups:
                    if group in main_contracts_config:
                        contracts.extend(main_contracts_config[group])

                logger.success(f"✅ 主力合约配置完成，共 {len(contracts)} 个合约")

            except Exception as e:
                logger.error(f"❌ 获取主力合约失败: {e}")
                logger.warning("⚠️  回退到静态合约配置")
                # 回退到静态配置
                for group in self.contract_groups:
                    if group in EXTENDED_CONTRACTS:
                        contracts.extend(EXTENDED_CONTRACTS[group])
        else:
            # 使用静态合约配置
            for group in self.contract_groups:
                if group in EXTENDED_CONTRACTS:
                    contracts.extend(EXTENDED_CONTRACTS[group])

        return AnalysisConfig(
            contracts=contracts,
            kline_period_seconds=self.kline_period_seconds,
            kline_data_length=self.kline_data_length,
            d_value_range=self.d_value_range,
            output_dir=self.output_dir,
            save_detailed_results=self.save_detailed_results
        )


# 预设配置
PRESET_CONFIGS = {
    'quick_test': PresetAnalysisConfig(
        name="快速测试",
        description="使用少量主力合约进行快速测试",
        contract_groups=['metals'],  # 改为使用金属主力合约
        d_value_range=(20, 51),
        kline_period_seconds=300,
        kline_data_length=1000,
        output_dir="quick_test_results",
        use_main_contracts=True
    ),

    'main_contracts_analysis': PresetAnalysisConfig(
        name="主力合约分析",
        description="分析当前主力合约的盈利能力",
        contract_groups=['agricultural', 'metals'],
        d_value_range=(10, 101),
        kline_period_seconds=300,
        kline_data_length=5000,
        output_dir="main_contracts_results",
        use_main_contracts=True
    ),

    'agricultural_main': PresetAnalysisConfig(
        name="农产品主力分析",
        description="农产品主力合约深度分析",
        contract_groups=['agricultural'],
        d_value_range=(10, 101),
        kline_period_seconds=300,
        kline_data_length=8964,
        output_dir="agricultural_main_results",
        use_main_contracts=True
    ),

    'metals_main': PresetAnalysisConfig(
        name="金属主力分析",
        description="金属主力合约综合分析",
        contract_groups=['metals'],
        d_value_range=(10, 101),
        kline_period_seconds=300,
        kline_data_length=8964,
        output_dir="metals_main_results",
        use_main_contracts=True
    ),

    'full_main_market_scan': PresetAnalysisConfig(
        name="全市场主力扫描",
        description="扫描所有品种的主力合约",
        contract_groups=['agricultural', 'metals', 'chemicals', 'ferrous', 'energy'],
        d_value_range=(10, 101),
        kline_period_seconds=300,
        kline_data_length=8964,
        output_dir="full_main_market_results",
        use_main_contracts=True
    ),

    'intraday_main_analysis': PresetAnalysisConfig(
        name="主力合约日内分析",
        description="使用1分钟K线分析主力合约",
        contract_groups=['agricultural', 'metals'],
        d_value_range=(5, 51),
        kline_period_seconds=60,  # 1分钟
        kline_data_length=2000,
        output_dir="intraday_main_results",
        use_main_contracts=True
    ),

    'swing_main_analysis': PresetAnalysisConfig(
        name="主力合约波段分析",
        description="使用15分钟K线分析主力合约",
        contract_groups=['agricultural', 'metals'],
        d_value_range=(20, 101),
        kline_period_seconds=900,  # 15分钟
        kline_data_length=3000,
        output_dir="swing_main_results",
        use_main_contracts=True
    ),

    # 保留静态配置选项
    'static_contracts': PresetAnalysisConfig(
        name="静态合约分析",
        description="使用预定义的静态合约配置",
        contract_groups=['agricultural_full', 'metals_full'],
        d_value_range=(10, 101),
        kline_period_seconds=300,
        kline_data_length=8964,
        output_dir="static_contracts_results",
        use_main_contracts=False  # 使用静态配置
    )
}


def get_preset_config(preset_name: str) -> AnalysisConfig:
    """获取预设配置"""
    if preset_name not in PRESET_CONFIGS:
        available = list(PRESET_CONFIGS.keys())
        raise ValueError(f"未知预设配置: {preset_name}. 可用配置: {available}")

    return PRESET_CONFIGS[preset_name].to_analysis_config()


def get_current_main_contracts(product_groups: List[str] = None,
                             auth_username: str = "bigwolf",
                             auth_password: str = "ftp123") -> Dict[str, str]:
    """获取当前主力合约列表"""
    if product_groups is None:
        product_groups = list(PRODUCT_INFO.keys())

    # 收集需要查询的品种
    contract_specs = []
    for group in product_groups:
        if group in PRODUCT_INFO:
            for exchange, product_id, name, _, _, _ in PRODUCT_INFO[group]:
                contract_specs.append((exchange, product_id))

    # 获取主力合约
    with MainContractFetcher(auth_username, auth_password) as fetcher:
        main_contracts = fetcher.get_multiple_main_contracts(contract_specs)

    return main_contracts


def create_main_contracts_analysis_config(product_groups: List[str] = None,
                                        d_value_range: Tuple[int, int] = (10, 101),
                                        kline_period_seconds: int = 300,
                                        kline_data_length: int = 8964,
                                        output_dir: str = "main_contracts_analysis",
                                        auth_username: str = "bigwolf",
                                        auth_password: str = "ftp123") -> AnalysisConfig:
    """创建基于主力合约的分析配置"""

    if product_groups is None:
        product_groups = ['agricultural', 'metals']

    logger.info(f"🔄 创建主力合约分析配置: {product_groups}")

    # 获取主力合约配置
    main_contracts_config = create_main_contracts_config(
        product_groups=product_groups,
        auth_username=auth_username,
        auth_password=auth_password
    )

    # 合并所有合约
    contracts = []
    for group in product_groups:
        if group in main_contracts_config:
            contracts.extend(main_contracts_config[group])

    logger.success(f"✅ 主力合约配置创建完成，共 {len(contracts)} 个合约")

    return AnalysisConfig(
        contracts=contracts,
        kline_period_seconds=kline_period_seconds,
        kline_data_length=kline_data_length,
        d_value_range=d_value_range,
        output_dir=output_dir,
        save_detailed_results=True
    )


def list_preset_configs():
    """列出所有预设配置"""
    print("可用的预设配置:")
    print("=" * 60)
    for name, config in PRESET_CONFIGS.items():
        print(f"名称: {name}")
        print(f"描述: {config.description}")
        print(f"合约组: {config.contract_groups}")
        print(f"参数范围: D_VALUE {config.d_value_range[0]}-{config.d_value_range[1]-1}")
        print(f"K线周期: {config.kline_period_seconds}秒")
        print(f"数据长度: {config.kline_data_length}条")
        print(f"输出目录: {config.output_dir}")
        print("-" * 40)


def save_custom_config(config: AnalysisConfig, filename: str):
    """保存自定义配置"""
    config_dict = {
        'contracts': [
            {
                'symbol': c.symbol,
                'name': c.name,
                'exchange': c.exchange,
                'product_id': c.product_id,
                'multiplier': c.multiplier,
                'min_price_change': c.min_price_change,
                'margin_rate': c.margin_rate
            }
            for c in config.contracts
        ],
        'kline_period_seconds': config.kline_period_seconds,
        'kline_data_length': config.kline_data_length,
        'd_value_range': config.d_value_range,
        'output_dir': config.output_dir,
        'save_detailed_results': config.save_detailed_results,
        'top_results_count': config.top_results_count,
        'min_trades_required': config.min_trades_required
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    print(f"配置已保存到: {filename}")


def load_custom_config(filename: str) -> AnalysisConfig:
    """加载自定义配置"""
    with open(filename, 'r', encoding='utf-8') as f:
        config_dict = json.load(f)
    
    # 重建合约配置
    contracts = []
    for c in config_dict['contracts']:
        contracts.append(ContractConfig(
            symbol=c['symbol'],
            name=c['name'],
            exchange=c['exchange'],
            product_id=c['product_id'],
            multiplier=c['multiplier'],
            min_price_change=c['min_price_change'],
            margin_rate=c['margin_rate']
        ))
    
    return AnalysisConfig(
        contracts=contracts,
        kline_period_seconds=config_dict['kline_period_seconds'],
        kline_data_length=config_dict['kline_data_length'],
        d_value_range=tuple(config_dict['d_value_range']),
        output_dir=config_dict['output_dir'],
        save_detailed_results=config_dict['save_detailed_results'],
        top_results_count=config_dict['top_results_count'],
        min_trades_required=config_dict['min_trades_required']
    )


def create_custom_contract_list(symbols: List[str]) -> List[ContractConfig]:
    """根据合约代码列表创建合约配置"""
    contracts = []
    
    for symbol in symbols:
        # 简单的合约信息推断
        if symbol.startswith("SHFE."):
            exchange = "SHFE"
            product_id = symbol.split(".")[1][:2]
        elif symbol.startswith("DCE."):
            exchange = "DCE"
            product_id = symbol.split(".")[1][0]
        elif symbol.startswith("CZCE."):
            exchange = "CZCE"
            product_id = symbol.split(".")[1][:2]
        elif symbol.startswith("INE."):
            exchange = "INE"
            product_id = symbol.split(".")[1][:2]
        else:
            exchange = "UNKNOWN"
            product_id = "UNKNOWN"
        
        contracts.append(ContractConfig(
            symbol=symbol,
            name=f"{product_id}合约",
            exchange=exchange,
            product_id=product_id,
            multiplier=10,  # 默认值
            min_price_change=1.0,  # 默认值
            margin_rate=0.05  # 默认值
        ))
    
    return contracts


def test_main_contracts_fetcher():
    """测试主力合约获取功能"""
    import argparse

    parser = argparse.ArgumentParser(description="测试主力合约获取")
    parser.add_argument('--groups', nargs='+',
                       choices=list(PRODUCT_INFO.keys()) + ['all'],
                       default=['metals'],
                       help='要测试的品种组')
    parser.add_argument('--auth-user', default='bigwolf', help='认证用户名')
    parser.add_argument('--auth-pass', default='ftp123', help='认证密码')

    args = parser.parse_args()

    groups = args.groups if 'all' not in args.groups else list(PRODUCT_INFO.keys())

    print("=" * 60)
    print("主力合约获取测试")
    print("=" * 60)

    try:
        # 测试获取主力合约
        main_contracts = get_current_main_contracts(
            product_groups=groups,
            auth_username=args.auth_user,
            auth_password=args.auth_pass
        )

        print(f"\n✅ 成功获取 {len(main_contracts)} 个主力合约:")
        print("-" * 40)

        for key, symbol in main_contracts.items():
            exchange, product_id = key.split('.')
            # 查找品种名称
            name = "未知"
            for group_info in PRODUCT_INFO.values():
                for ex, pid, pname, _, _, _ in group_info:
                    if ex == exchange and pid == product_id:
                        name = pname
                        break

            print(f"{name:8} ({key:12}): {symbol}")

        print(f"\n📊 按交易所分布:")
        exchanges = {}
        for symbol in main_contracts.values():
            ex = symbol.split('.')[0]
            exchanges[ex] = exchanges.get(ex, 0) + 1

        for ex, count in exchanges.items():
            print(f"  {ex}: {count}个")

        # 测试创建分析配置
        print(f"\n🔄 测试创建分析配置...")
        config = create_main_contracts_analysis_config(
            product_groups=groups,
            d_value_range=(20, 31),
            kline_data_length=100,
            output_dir="test_main_contracts",
            auth_username=args.auth_user,
            auth_password=args.auth_pass
        )

        print(f"✅ 分析配置创建成功，包含 {len(config.contracts)} 个合约")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        # 运行测试
        sys.argv.pop(1)  # 移除 'test' 参数
        test_main_contracts_fetcher()
    else:
        # 显示所有预设配置
        list_preset_configs()

        print("\n" + "=" * 60)
        print("主力合约功能说明")
        print("=" * 60)
        print("1. 预设配置现在默认使用主力合约")
        print("2. 运行 'python multi_contract_config.py test' 测试主力合约获取")
        print("3. 使用 get_current_main_contracts() 获取当前主力合约")
        print("4. 使用 create_main_contracts_analysis_config() 创建主力合约分析配置")

        print("\n示例用法:")
        print("# 获取当前主力合约")
        print("main_contracts = get_current_main_contracts(['metals', 'agricultural'])")
        print("")
        print("# 创建主力合约分析配置")
        print("config = create_main_contracts_analysis_config(['metals'])")
        print("")
        print("# 使用预设配置（自动获取主力合约）")
        print("config = get_preset_config('main_contracts_analysis')")
