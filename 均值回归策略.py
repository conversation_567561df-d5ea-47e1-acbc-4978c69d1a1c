#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
均值回归交易策略
包含风险管理和仓位管理
基于天勤量化TQSDK开发
不使用TargetPosTask，直接通过下单API控制
"""

from tqsdk import TqApi, TqAuth
from tqsdk.ta import MA
import time
import numpy as np
import json
import datetime


class MeanReversionStrategy:
    def __init__(self, symbol, auth_info=None,
                 ma_period=20, entry_std_dev=2.0, exit_std_dev=0.5,
                 max_pos=5, risk_per_trade=0.02, stop_loss_pct=0.03,
                 max_daily_loss=0.05, time_stop_minutes=120):
        """
        初始化均值回归交易策略

        参数:
        symbol (str): 交易合约代码，例如 "SHFE.au2106"
        auth_info (tuple): 天勤账户认证信息 (username, password)
        ma_period (int): 计算移动平均的周期
        entry_std_dev (float): 入场标准差倍数
        exit_std_dev (float): 出场标准差倍数
        max_pos (int): 最大持仓数量
        risk_per_trade (float): 每笔交易风险占账户比例
        stop_loss_pct (float): 单笔交易止损百分比
        max_daily_loss (float): 每日最大亏损百分比
        time_stop_minutes (int): 持仓时间限制（分钟）
        """
        # 策略参数
        self.symbol = symbol
        self.ma_period = ma_period
        self.entry_std_dev = entry_std_dev
        self.exit_std_dev = exit_std_dev

        # 风险与仓位管理参数
        self.max_pos = max_pos
        self.risk_per_trade = risk_per_trade
        self.stop_loss_pct = stop_loss_pct
        self.max_daily_loss = max_daily_loss
        self.time_stop_minutes = time_stop_minutes

        # 初始化API
        if auth_info:
            self.api = TqApi(auth=TqAuth(*auth_info))
        else:
            self.api = TqApi()

        # 获取合约信息
        self.quote = self.api.get_quote(self.symbol)

        # 设置K线数据，使用15分钟K线
        self.klines = self.api.get_kline_serial(self.symbol, 15 * 60, self.ma_period * 3)

        # 交易状态
        self.position = 0  # 当前持仓状态：正数表示多头手数，负数表示空头手数，0无持仓
        self.entry_price = 0  # 入场价格
        self.entry_time = None  # 入场时间
        self.daily_pnl = 0  # 每日盈亏
        self.daily_trades = []  # 每日交易记录
        self.start_equity = self.get_account_equity()  # 初始账户权益
        self.orders = {}  # 记录订单信息

        # 记录日志
        self.log_info(f"策略初始化完成，交易品种：{self.symbol}，均线周期：{self.ma_period}")

    def get_account_equity(self):
        """获取账户权益"""
        account = self.api.get_account()
        return account.balance

    def get_current_position(self):
        """获取当前合约的持仓"""
        position = self.api.get_position(self.symbol)
        long_pos = position.pos_long
        short_pos = position.pos_short
        net_pos = long_pos - short_pos
        return net_pos

    def calculate_position_size(self, price):
        """
        计算仓位大小
        基于账户风险比例计算合适的仓位大小

        参数:
        price (float): 当前价格

        返回:
        int: 计算出的仓位大小（手数）
        """
        account = self.api.get_account()
        equity = account.balance

        # 计算每手价值和风险价值
        contract_value = self.quote.volume_multiple * price
        risk_amount = equity * self.risk_per_trade
        stop_loss_amount = price * self.stop_loss_pct

        # 计算仓位大小
        position_size = risk_amount / (stop_loss_amount * self.quote.volume_multiple)
        position_size = min(int(position_size), self.max_pos)
        position_size = max(position_size, 1)  # 至少开1手

        return position_size

    def place_order(self, direction, volume, price_type="BEST", price=None):
        """
        下单函数

        参数:
        direction (str): 买卖方向，"BUY"表示买入，"SELL"表示卖出
        volume (int): 下单手数
        price_type (str): 价格类型，"LIMIT"表示限价单，"BEST"表示市价单
        price (float): 限价单价格，市价单可忽略

        返回:
        str: 订单ID
        """
        if price_type == "BEST":
            order = self.api.insert_order(symbol=self.symbol, direction=direction, offset="OPEN", volume=volume, limit_price=price)
        else:
            order = self.api.insert_order(symbol=self.symbol, direction=direction, offset="OPEN", volume=volume, limit_price=price)

        self.orders[order.order_id] = {
            "direction": direction,
            "volume": volume,
            "status": "SUBMITTED"
        }

        self.log_info(f"下单：{direction} {volume}手 {self.symbol}")
        return order.order_id

    def close_position(self, direction, volume, price_type="BEST", price=None):
        """
        平仓函数

        参数:
        direction (str): 平仓方向，"BUY"表示买入平空，"SELL"表示卖出平多
        volume (int): 平仓手数
        price_type (str): 价格类型，"LIMIT"表示限价单，"BEST"表示市价单
        price (float): 限价单价格，市价单可忽略

        返回:
        str: 订单ID
        """
        if price_type == "BEST":
            order = self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE", volume=volume, limit_price=price)
        else:
            order = self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE", volume=volume, limit_price=price)

        self.orders[order.order_id] = {
            "direction": direction,
            "volume": volume,
            "status": "SUBMITTED",
            "is_close": True
        }

        self.log_info(f"平仓：{direction} {volume}手 {self.symbol}")
        return order.order_id

    def cancel_all_orders(self):
        """取消所有未完成订单"""
        for order_id, order_info in list(self.orders.items()):
            if order_info["status"] in ["SUBMITTED", "ACCEPTED"]:
                self.api.cancel_order(order_id)
                self.log_info(f"取消订单: {order_id}")

    def check_time_stop(self):
        """检查是否触发时间止损"""
        if self.position != 0 and self.entry_time is not None:
            current_time = datetime.datetime.now()
            elapsed_minutes = (current_time - self.entry_time).total_seconds() / 60

            if elapsed_minutes >= self.time_stop_minutes:
                self.log_info(f"触发时间止损，已持仓{elapsed_minutes:.1f}分钟")
                return True
        return False

    def check_daily_risk(self):
        """检查是否超过每日最大亏损限制"""
        daily_loss_pct = self.daily_pnl / self.start_equity
        if daily_loss_pct <= -self.max_daily_loss:
            self.log_info(f"触发每日风险限制，当日亏损已达{-daily_loss_pct:.2%}")
            return True
        return False

    def check_stop_loss(self, current_price):
        """检查是否触发止损"""
        if self.position == 0 or self.entry_price == 0:
            return False

        if self.position > 0:  # 多头
            loss_pct = (current_price - self.entry_price) / self.entry_price
            if loss_pct <= -self.stop_loss_pct:
                self.log_info(f"多头触发止损，入场价：{self.entry_price}，当前价：{current_price}，亏损：{loss_pct:.2%}")
                return True
        elif self.position < 0:  # 空头
            loss_pct = (self.entry_price - current_price) / self.entry_price
            if loss_pct <= -self.stop_loss_pct:
                self.log_info(f"空头触发止损，入场价：{self.entry_price}，当前价：{current_price}，亏损：{loss_pct:.2%}")
                return True

        return False

    def update_pnl(self, exit_price):
        """更新盈亏"""
        if self.position == 0 or self.entry_price == 0:
            return

        position_size = abs(self.position)
        contract_value = self.quote.volume_multiple

        if self.position > 0:  # 多头
            trade_pnl = (exit_price - self.entry_price) * position_size * contract_value
        else:  # 空头
            trade_pnl = (self.entry_price - exit_price) * position_size * contract_value

        self.daily_pnl += trade_pnl

        # 记录交易
        trade_record = {
            "entry_time": self.entry_time.strftime("%Y-%m-%d %H:%M:%S") if self.entry_time else "",
            "exit_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "direction": "多" if self.position > 0 else "空",
            "size": position_size,
            "entry_price": self.entry_price,
            "exit_price": exit_price,
            "pnl": trade_pnl,
            "pnl_pct": (trade_pnl / (self.entry_price * position_size * contract_value)) if self.entry_price else 0
        }

        self.daily_trades.append(trade_record)
        self.log_info(f"平仓完成，{trade_record['direction']}单，盈亏：{trade_pnl:.2f}，收益率：{trade_record['pnl_pct']:.2%}")

    def log_info(self, msg):
        """记录日志信息"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] {msg}")

    def reset_daily_stats(self):
        """重置每日统计数据"""
        current_day = datetime.datetime.now().day
        if hasattr(self, 'last_reset_day') and self.last_reset_day == current_day:
            return

        self.log_info("重置每日统计数据")
        self.daily_pnl = 0
        self.daily_trades = []
        self.start_equity = self.get_account_equity()
        self.last_reset_day = current_day

    def process_order_status(self):
        """处理订单状态变化"""
        # 检查所有已提交的订单状态
        for order_id in list(self.orders.keys()):
            order = self.api.get_order(order_id)
            if order.status == "FINISHED":
                if order_id in self.orders:
                    old_status = self.orders[order_id]["status"]
                    if old_status != "FINISHED":
                        self.log_info(f"订单 {order_id} 已完成: {order.direction} {order.volume_left}/{order.volume_orign}手")
                        self.orders[order_id]["status"] = "FINISHED"

                        # 如果是开仓订单，更新持仓信息
                        if not self.orders[order_id].get("is_close", False):
                            volume = order.volume_left
                            if order.direction == "BUY":
                                # 买入开仓
                                self.position += volume
                                self.entry_price = order.trade_price
                                self.entry_time = datetime.datetime.now()
                            else:
                                # 卖出开仓
                                self.position -= volume
                                self.entry_price = order.trade_price
                                self.entry_time = datetime.datetime.now()

            elif order.status == "REJECTED":
                if order_id in self.orders:
                    old_status = self.orders[order_id]["status"]
                    if old_status != "REJECTED":
                        self.log_info(f"订单 {order_id} 被拒绝")
                        self.orders[order_id]["status"] = "REJECTED"

    def run(self):
        """运行策略"""
        self.log_info("策略开始运行...")

        try:
            while True:
                # 重置每日统计
                self.reset_daily_stats()

                # 更新行情数据
                self.api.wait_update()

                # 处理订单状态变化
                self.process_order_status()

                # 确保有足够的K线数据
                if len(self.klines.close) <= self.ma_period:
                    continue

                # 计算指标
                ma = MA(self.klines, self.ma_period)
                close = self.klines.close.iloc[-1]

                # 计算价格偏离度
                # 使用过去N个周期数据计算标准差
                historical_prices = self.klines.close[-(self.ma_period * 2):-1]
                std_dev = np.std(historical_prices)
                ma_value = ma.iloc[-1]
                deviation = close - ma_value
                z_score = deviation / std_dev if std_dev > 0 else 0

                # 获取当前价格和持仓信息
                current_price = self.quote.last_price
                current_pos = self.get_current_position()

                # 检查风险条件
                # 如果有持仓，检查是否需要止损或时间止损
                if current_pos != 0:
                    if self.check_stop_loss(current_price) or self.check_time_stop() or self.check_daily_risk():
                        # 平掉所有仓位
                        if current_pos > 0:  # 多头持仓
                            self.cancel_all_orders()  # 取消所有挂单
                            self.close_position("SELL", current_pos)  # 卖出平多
                            self.update_pnl(current_price)
                            self.position = 0
                            self.entry_price = 0
                            self.entry_time = None
                            self.log_info("触发风险控制，平多仓")
                        elif current_pos < 0:  # 空头持仓
                            self.cancel_all_orders()  # 取消所有挂单
                            self.close_position("BUY", abs(current_pos))  # 买入平空
                            self.update_pnl(current_price)
                            self.position = 0
                            self.entry_price = 0
                            self.entry_time = None
                            self.log_info("触发风险控制，平空仓")
                        continue

                # 交易信号判断
                # 1. 均值回归策略逻辑
                if self.position == 0:  # 无持仓状态
                    # 价格明显偏离均线，产生交易信号
                    if z_score >= self.entry_std_dev:  # 价格偏高，做空信号
                        pos_size = self.calculate_position_size(current_price)
                        self.place_order("SELL", pos_size)  # 卖出开仓
                        self.log_info(f"价格偏高 (z-score: {z_score:.2f})，做空信号，开仓 {pos_size} 手")

                    elif z_score <= -self.entry_std_dev:  # 价格偏低，做多信号
                        pos_size = self.calculate_position_size(current_price)
                        self.place_order("BUY", pos_size)  # 买入开仓
                        self.log_info(f"价格偏低 (z-score: {z_score:.2f})，做多信号，开仓 {pos_size} 手")

                else:  # 已有持仓
                    # 判断是否达到平仓条件（价格回归均值附近）
                    if (self.position > 0 and z_score >= -self.exit_std_dev) or \
                            (self.position < 0 and z_score <= self.exit_std_dev):
                        # 平仓
                        if self.position > 0:  # 多头持仓
                            self.close_position("SELL", self.position)  # 卖出平多
                            self.log_info(f"价格回归 (z-score: {z_score:.2f})，平多仓信号")
                        else:  # 空头持仓
                            self.close_position("BUY", abs(self.position))  # 买入平空
                            self.log_info(f"价格回归 (z-score: {z_score:.2f})，平空仓信号")

                        self.update_pnl(current_price)
                        self.position = 0
                        self.entry_price = 0
                        self.entry_time = None

                # 输出当前状态
                if self.klines.iloc[-1].datetime != self.klines.iloc[-2].datetime:  # 每新K线打印一次
                    status = {
                        "时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "合约": self.symbol,
                        "价格": current_price,
                        "均线": ma_value,
                        "Z分数": z_score,
                        "持仓": current_pos,
                        "持仓方向": "多" if self.position > 0 else "空" if self.position < 0 else "无",
                        "入场价": self.entry_price,
                        "持仓时间(分钟)": (datetime.datetime.now() - self.entry_time).total_seconds() / 60 if self.entry_time else 0,
                        "当日盈亏": self.daily_pnl
                    }
                    self.log_info(f"策略状态: {json.dumps(status, ensure_ascii=False)}")

        except KeyboardInterrupt:
            self.log_info("策略手动停止")
        finally:
            # 关闭API连接
            self.api.close()
            self.log_info("策略运行结束")


# 策略使用示例
if __name__ == "__main__":
    # 注意：实际使用时请替换为您的账户信息和交易品种
    auth_info = ("YOUR_USERNAME", "YOUR_PASSWORD")  # 天勤账户信息
    symbol = "SHFE.au2106"  # 以黄金期货为例

    # 策略参数设置
    strategy = MeanReversionStrategy(
        symbol=symbol,
        auth_info=auth_info,
        ma_period=20,  # 20周期均线
        entry_std_dev=2.0,  # 入场标准差阈值
        exit_std_dev=0.5,  # 出场标准差阈值
        max_pos=3,  # 最大持仓3手
        risk_per_trade=0.02,  # 每笔交易风险2%
        stop_loss_pct=0.03,  # 止损比例3%
        max_daily_loss=0.05,  # 每日最大亏损5%
        time_stop_minutes=120  # 最大持仓时间2小时
    )

    # 运行策略
    strategy.run()