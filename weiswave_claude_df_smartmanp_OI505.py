# from pine_test import calculate_indicators
# from strategies.weiswave_claude import calculate_signals
from strategies.weiswave_claude_dataframe import calculate_signals, wave_signal
from strategies import be_apart_from

import copy
from addict import Dict
# from .MyTT import *
# from .MyTT_plus import HHV, LLV, REF_plus

from tqsdk import TqApi, TqAccount, TqKq
from tqsdk.tafunc import time_to_str
from tradefuncs import *
from loguru import logger as mylog
import pandas as pd

mylog.add('weiswave' + '.log', encoding='utf-8')

if __name__ == "__main__":

    product = 'OI'
    symbol = product
    interval = 60
    bklimit = 1
    sklimit = 1
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    # api = TqApi(TqKq(), auth="wolfquant,ftp123")
    api = TqApi(TqKq(), auth="smartmanp,ftp123")
    # symbol = api.query_cont_quotes(product_id=product)[0]
    print(symbol)
    symbol = 'CZCE.OI505'
    barsinit = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964)
    bartmp = api.get_kline_serial(symbol, duration_seconds=interval, data_length=10)
    # api.close()
    bars = copy.deepcopy(barsinit)
    del barsinit

    # bars.to_pickle(symbol + str(interval) + '.pkl')
    df = calculate_signals(bars)
    wave_signal(df)
    print(df.signal.iloc[-1], df.G.iloc[-1])

    #
    #
    # if buydistance < selldistance:
    #     lastsignal = signalsBuy[-buydistance]
    # else:
    #     lastsignal = signalsSell[-selldistance]
    #
    # print(lastsignal, '持续周期：', min(buydistance, selldistance), '当前价格：', bars.close.iloc[-1])
    # current_sig_pos = Dict()
    # current_sig_profit = 0

    while True:
        api.wait_update()
        if api.is_changing(bartmp.iloc[-1], "datetime"):

            position = api.get_position(symbol)
            acc = api.get_account()
            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today

            newk = bartmp.iloc[-2]
            bdt = bars.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                newk = newk.to_frame()
                newk = newk.T
                bars = pd.concat([bars, newk], ignore_index=True)

            df = calculate_signals(bars)
            wave_signal(df)
            # print(df.long.iloc[-1])
            # print(df.signal.iloc[-1], df.G.iloc[-1])
            # signalsBuy, signalsSell = gen_WeisWave_signals(ups, dns, bars)
            # buydistance = be_apart_from(signalsBuy)
            # selldistance = be_apart_from(signalsSell)
            #
            # if buydistance < selldistance:
            #     lastsignal = signalsBuy[-buydistance]
            # else:
            #     lastsignal = signalsSell[-selldistance]
            #
            # if isinstance(signalsBuy[-1], list):
            #     print(signalsBuy[-1][0])
            #     orderprice = bars.close.iloc[-1]
            #     if bkvol < bklimit:
            #         BK(api, symbol=symbol, order_price=bars.close.iloc[-1], volume=1)
            #         mylog.info('下多单。')
            #     if current_sig_pos.sigtype == 'sell' and current_sig_pos.sigprice - bars.close.iloc[-1] > 3:
            #         BP(api, symbol, order_price=bars.close.iloc[-1], volume=1)
            #         mylog.info('平空单。')
            #     else:
            #         mylog.info(['持仓盈利不足，不平仓。', '信号：', current_sig_pos.sigtype, current_sig_pos.sigprice,
            #                     'now price', orderprice])
            #
            #     current_sig_pos.sigtype = 'buy'
            #     current_sig_pos.sigprice = bars.close.iloc[-1]
            #     current_sig_pos.volume = 1
            #
            #     print('buy signal')
            #
            # if isinstance(signalsSell[-1], list):
            #     print(signalsSell[-1][0])
            #     orderprice = bars.close.iloc[-1]
            #     if skvol < sklimit:
            #         SK(api, symbol=symbol, order_price=bars.close.iloc[-1], volume=1)
            #         mylog.info('下空单。')
            #     if current_sig_pos.sigtype == 'buy' and bars.close.iloc[-1] - current_sig_pos.sigprice > 3:
            #         SP(api, symbol, order_price=bars.close.iloc[-1], volume=1)
            #         mylog.info('平多单。')
            #
            #     else:
            #         mylog.info(['持仓盈利不足，不平仓。', '信号：', current_sig_pos.sigtype, current_sig_pos.sigprice,
            #                     'now price', orderprice])
            #
            #     current_sig_pos.sigtype = 'sell'
            #     current_sig_pos.sigprice = bars.close.iloc[-1]
            #     current_sig_pos.volume = 1
            #
            # if lastsignal[0] == 'buy':
            #     current_sig_profit = bars.close.iloc[-1] - lastsignal[1]
            # else:
            #     current_sig_profit = lastsignal[1] - bars.close.iloc[-1]
            #
            # print(lastsignal, '持续周期：', min(buydistance, selldistance), '当前价格：', bars.close.iloc[-1],
            #       '信号盈亏：', current_sig_profit)
