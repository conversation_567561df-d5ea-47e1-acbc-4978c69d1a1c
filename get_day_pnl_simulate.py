import json

# Load the JSON file
with open('simulatedaysummary_new.json', 'r', encoding='utf-8') as file:
    data = json.load(file)

# Extract trading date and user day_pnl
result = []
for date, users in data.items():
    for user in users:
        result.append({
            "trading_date": date,
            "user_name": user["user_name"],
            "day_pnl": user["day_pnl"]
        })

# Print the extracted data
for entry in result:
    print(entry)


# Calculate the sum of day_pnl
total_day_pnl = 0
for date, users in data.items():
    for user in users:
        total_day_pnl += user["day_pnl"]

# Print the total day_pnl
print(f"Total day_pnl: {total_day_pnl}")