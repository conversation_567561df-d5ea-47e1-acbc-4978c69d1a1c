import pytdx
import sys
# 创建一个 pytdx 客户端对象
client = pytdx.client()

# 连接到 pytdx 服务器
if not client.connect('119.147.212.81', 7709):
    print('无法连接到 pytdx 服务器')
    sys.exit(1)

# 获取指定股票的行情数据
data = client.get_security_quotes([(0, '000001')])
if not data:
    print('无法获取股票行情数据')
    sys.exit(1)

# 打印股票行情数据
stock = data[0]
print('股票代码: %s' % stock.code)
print('股票名称: %s' % stock.name)
print('开盘价: %.2f' % stock.open)
print('收盘价: %.2f' % stock.close)
print('最高价: %.2f' % stock.high)
print('最低价: %.2f' % stock.low)
print('成交量: %d' % stock.volume)

# 断开与 pytdx 服务器的连接
client.disconnect()
