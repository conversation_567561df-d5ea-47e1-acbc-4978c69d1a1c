from tqsdk import TqApi, TqKq

product = 'ag'
# symbol = 'CZCE.SR101'
# symbol = 'DCE.pp2105'
# symname = symbol.rsplit('.', 1)[1]
interval = 15
bklimit = 100
sklimit = 100
single_volume = 1

# 交易账号设置
api = TqApi(TqKq(), auth="follower,ftp123")
bars=api.get_kline_serial('SSE.000016', duration_seconds=60, data_length=100)
while True:
    api.wait_update()
    if api.is_changing(bars):
        print(bars.iloc[-1].close)

