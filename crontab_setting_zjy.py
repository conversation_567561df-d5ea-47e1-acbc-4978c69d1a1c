import os
import getpass
from crontab import CronTab
import copy

username = getpass.getuser()
basedir = os.path.dirname(__file__)

cron = CronTab(user=username)
crons = copy.deepcopy(cron.crons)

programs = [
    # 'TimeRoseMA_cross_zjy_cy_oi_1m.py',
    # 'TimeRoseMA_cross_zjy_cy_oi_3m.py',
    # 'TimeRoseMA_cross_zjy_cy_oi_5m.py',
    # 'TimeRoseMA_cross_zjy_cy_oi_15m.py',
    # 'TimeRoseMA_cross_zjy_tgjy_oi_5m.py',
    'TimeRoseMA_cross_zjy_df_rb_3m.py',
    'TimeRoseMA_cross_zjy_df_rb_5m.py',
    'TimeRoseMA_cross_zjy_tgjy_rb_3m.py',
    'TimeRoseMA_cross_zjy_tgjy_rb_5m.py',
]

for p in programs:
    prejob = "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = prejob + '; python ' + os.path.join(basedir, p)
    job = cron.new(command=jobstr)

    job.hour.on(20)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    if job in crons:
        continue
    else:
        cron.write()

for p in programs:
    prejob = "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = prejob + '; python ' + os.path.join(basedir, p)
    job = cron.new(command=jobstr)

    job.hour.on(8)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')

    if job in crons:
        continue
    else:
        cron.write()

for p in programs:
    prejob = "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = 'python ' + os.path.join(basedir, p)
    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.every_reboot()
    if not job in crons:
        print('not done, write the job.')
        cron.write()
    else:
        print('already done, pass.')

# deleter cron item test
# cron = CronTab(user=getpass.getuser())
# itercron=cron.find_command('tgjysss')
# for job in itercron:
#     print(job)
#     cron.remove(job)
# cron.write()
