import tqsdk


# 交易策略类
class TwinRangeFilter:
    def __init__(self, api):
        self.api = api

    # 快速均线计算函数
    def get_fast_sma(self):
        fast_period = 27
        return self.api.get_kline_data('SHFE.cu', '1Min', '10Years').get_sma(fast_period) * 1.6

    # 慢速均线计算函数
    def get_slow_sma(self):
        slow_period = 55
        return self.api.get_kline_data('SHFE.cu', '1Min', '10Years').get_sma(slow_period) * 2.0

    # 策略实现函数
    def strategy_implementation(self, current_price):
        fast_sma = self.get_fast_sma()
        slow_sma = self.get_slow_sma()
        if current_price > (fast_sma + slow_sma) / 2 and \
           ((current_price > self.api.get_kline_data('SHFE.cu', '1Min', '10Years').pre_close) or (self.api.get_risk_level() == 'high')):
            return True
        else:
            if self.api.is_short_position():
                if current_price < (fast_sma + slow_sma) / 2 and \
                   ((current_price < self.api.get_kline_data('SHFE.cu', '1Min', '10Years').pre_close) or (self.api.get_risk_level() == 'low')):
                    return True

    # 订单执行函数
    def order_execution(self, api):
        position = api.get_position()
        if not api.is_long_position():
            if self.strategy_implementation(api.get_quote().price):
                api.place_order('SHFE.cu', tqsdk.OrderAction.BUY, 1.0)
        else:
            if self.api.is_short_position():
                if self.strategy_implementation(api.get_quote().price):
                    api.place_order('SHFE.cu', tqsdk.OrderAction.SELL, -1.0)


# 主函数
def main():
    # 初始化TQSDK环境
    tq = tqsdk.TqApi()
    # 实例化交易策略类
    strategy = TwinRangeFilter(tq)
    # 开启实盘模式
    print('Real trading mode enabled')
    # 订单执行函数
    strategy.order_execution(tq)


if __name__ == '__main__':
    main()

