"""
数据管理器组件
负责处理K线数据的加载、保存、合并等操作
"""

import os
import pandas as pd
from loguru import logger as mylog


class DataManager:
    """数据管理器类"""
    
    def __init__(self, symbol, interval):
        """
        初始化数据管理器
        
        参数:
        symbol: 交易品种代码
        interval: K线周期（秒）
        """
        self.symbol = symbol
        self.interval = interval
        self.data_filename = self._get_data_filename()
        
    def _get_data_filename(self):
        """生成数据文件名"""
        # 清理合约代码中的特殊字符
        clean_symbol = self.symbol.replace('.', '_').replace('@', '_').replace(':', '_')
        return f"{clean_symbol}_{self.interval}.csv"
    
    def _convert_datetime(self, dt_value):
        """
        转换datetime值，支持科学计数法格式的时间戳
        
        参数:
        dt_value: datetime值（可能是字符串、科学计数法或datetime对象）
        
        返回:
        转换后的datetime对象或pd.NaT
        """
        try:
            # 如果是字符串且包含科学计数法
            if isinstance(dt_value, str) and ('e+' in dt_value or 'E+' in dt_value):
                # 转换为浮点数（纳秒时间戳），然后转换为datetime
                timestamp_ns = float(dt_value)
                return pd.to_datetime(timestamp_ns, unit='ns')
            else:
                # 正常的datetime字符串或已经是datetime对象
                return pd.to_datetime(dt_value)
        except Exception as e:
            mylog.warning(f"转换datetime失败: {dt_value}, 错误: {e}")
            return pd.NaT
    
    def _safe_convert_datetime_series(self, dt_series):
        """
        安全转换datetime列
        
        参数:
        dt_series: pandas Series，包含datetime数据
        
        返回:
        转换后的datetime Series
        """
        return dt_series.apply(self._convert_datetime)
    
    def load_historical_data(self):
        """
        从文件加载历史数据
        
        返回:
        DataFrame或None（如果加载失败）
        """
        if os.path.exists(self.data_filename):
            try:
                # 尝试多种方式加载CSV文件
                df = None
                
                # 方法1: 正常加载
                try:
                    df = pd.read_csv(self.data_filename)
                except Exception as e1:
                    mylog.warning(f"正常加载失败: {e1}")
                    
                    # 方法2: 跳过错误行（使用新版pandas参数）
                    try:
                        mylog.info("尝试跳过错误行重新加载...")
                        df = pd.read_csv(self.data_filename, on_bad_lines='skip')
                    except Exception as e2:
                        mylog.warning(f"跳过错误行加载失败: {e2}")
                        
                        # 方法3: 使用engine='python'，更宽松的解析
                        try:
                            mylog.info("尝试使用Python引擎加载...")
                            df = pd.read_csv(self.data_filename, engine='python', on_bad_lines='skip')
                        except Exception as e3:
                            mylog.warning(f"Python引擎加载失败: {e3}")
                            
                            # 方法4: 尝试修复文件
                            mylog.info("尝试修复损坏的数据文件...")
                            if self.repair_data_file():
                                # 修复成功，重新尝试加载
                                try:
                                    df = pd.read_csv(self.data_filename)
                                    mylog.info("文件修复后加载成功")
                                except Exception as e4:
                                    mylog.error(f"修复后仍无法加载: {e4}")
                                    return None
                            else:
                                mylog.error("文件修复失败，将重新开始")
                                return None
                
                if df is not None:
                    # 确保datetime列是正确的类型
                    if 'datetime' in df.columns:
                        df['datetime'] = self._safe_convert_datetime_series(df['datetime'])
                        # 删除datetime为NaN的行
                        df = df.dropna(subset=['datetime'])
                    
                    mylog.info(f"成功加载历史数据: {self.data_filename}, 数据量: {len(df)}")
                    return df
                else:
                    mylog.error(f"所有加载方法都失败了: {self.data_filename}")
                    return None
                    
            except Exception as e:
                mylog.error(f"加载历史数据失败: {self.data_filename}, 错误: {e}")
                return None
        else:
            mylog.info(f"历史数据文件不存在: {self.data_filename}")
            return None
    
    def _extract_kline_data(self, df):
        """
        提取纯K线数据，过滤掉计算指标列
        
        参数:
        df: 包含所有数据的DataFrame
        
        返回:
        只包含K线数据的DataFrame
        """
        # 定义K线数据的基本列
        kline_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
        
        # 添加可能存在的其他基本列
        optional_columns = ['symbol', 'duration', 'open_oi', 'close_oi']
        
        # 找出实际存在的列
        available_columns = []
        for col in kline_columns + optional_columns:
            if col in df.columns:
                available_columns.append(col)
        
        # 返回只包含K线数据的DataFrame
        return df[available_columns].copy()

    def save_data(self, df):
        """
        保存数据到文件（只保存纯K线数据）
        
        参数:
        df: 要保存的DataFrame（可能包含计算指标）
        """
        try:
            # 数据验证
            if df is None or len(df) == 0:
                mylog.warning("尝试保存空数据，跳过保存操作")
                return
            
            # 提取纯K线数据
            kline_df = self._extract_kline_data(df)
            
            # 检查必要的列
            required_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in kline_df.columns]
            if missing_columns:
                mylog.warning(f"数据缺少必要列: {missing_columns}")
                return
            
            mylog.info(f"保存纯K线数据，原始列数: {len(df.columns)}, K线列数: {len(kline_df.columns)}")
            
            # 先保存到临时文件，确保写入成功后再替换原文件
            temp_filename = f"{self.data_filename}.tmp"
            kline_df.to_csv(temp_filename, index=False)
            
            # 验证临时文件是否正确
            try:
                test_df = pd.read_csv(temp_filename, nrows=5)  # 只读取前5行进行验证
                if len(test_df) > 0:
                    # 验证成功，替换原文件
                    if os.path.exists(self.data_filename):
                        os.remove(self.data_filename)
                    os.rename(temp_filename, self.data_filename)
                    mylog.info(f"纯K线数据已保存到: {self.data_filename}, 数据量: {len(kline_df)}")
                else:
                    mylog.error("保存的文件验证失败：文件为空")
                    if os.path.exists(temp_filename):
                        os.remove(temp_filename)
            except Exception as ve:
                mylog.error(f"保存的文件验证失败: {ve}")
                if os.path.exists(temp_filename):
                    os.remove(temp_filename)
                    
        except Exception as e:
            mylog.error(f"保存数据失败: {self.data_filename}, 错误: {e}")
            # 清理临时文件
            temp_filename = f"{self.data_filename}.tmp"
            if os.path.exists(temp_filename):
                os.remove(temp_filename)
    
    def merge_data(self, historical_df, new_df):
        """
        合并历史数据和新数据，去重并排序
        
        参数:
        historical_df: 历史数据DataFrame
        new_df: 新数据DataFrame
        
        返回:
        合并后的DataFrame
        """
        if historical_df is None or len(historical_df) == 0:
            mylog.info("无历史数据，使用新获取的数据")
            return new_df.copy()

        if new_df is None or len(new_df) == 0:
            mylog.info("无新数据，使用历史数据")
            return historical_df.copy()

        try:
            # 确保两个DataFrame的datetime列都是正确的类型
            if 'datetime' in historical_df.columns:
                historical_df['datetime'] = self._safe_convert_datetime_series(historical_df['datetime'])
            if 'datetime' in new_df.columns:
                new_df['datetime'] = self._safe_convert_datetime_series(new_df['datetime'])

            # 检查并处理NaN值
            historical_df = historical_df.dropna(subset=['datetime'])
            new_df = new_df.dropna(subset=['datetime'])

            # 合并数据
            combined_df = pd.concat([historical_df, new_df], ignore_index=True)

            # 按datetime去重，保留最后出现的数据
            combined_df = combined_df.drop_duplicates(subset=['datetime'], keep='last')

            # 按时间排序
            combined_df = combined_df.sort_values('datetime').reset_index(drop=True)

            new_data_count = len(combined_df) - len(historical_df)
            mylog.info(f"数据合并完成: 历史数据{len(historical_df)}条, 新增数据{new_data_count}条, 总计{len(combined_df)}条")

            return combined_df

        except Exception as e:
            mylog.error(f"数据合并失败: {e}")
            # 如果合并失败，返回新数据
            mylog.info("使用新获取的数据作为备选方案")
            return new_df.copy()
    
    def initialize_data(self, api, data_length=8964):
        """
        初始化数据：加载历史数据并与API获取的数据合并
        
        参数:
        api: TqApi实例
        data_length: 获取的K线数据长度
        
        返回:
        初始化后的DataFrame
        """
        mylog.info(f"初始化数据管理器: {self.symbol}, 周期: {self.interval}秒")
        
        # 1. 加载历史数据
        historical_data = self.load_historical_data()
        
        # 2. 获取初始化数据
        mylog.info("获取初始化K线数据...")
        barsinit = api.get_kline_serial(self.symbol, duration_seconds=self.interval, data_length=data_length)
        
        # 3. 合并历史数据和初始化数据
        bars = self.merge_data(historical_data, barsinit)
        
        # 4. 保存合并后的数据
        self.save_data(bars)
        
        return bars
    
    def add_new_kline(self, bars, new_kline):
        """
        添加新的K线数据
        
        参数:
        bars: 当前的K线数据DataFrame
        new_kline: 新的K线数据
        
        返回:
        更新后的DataFrame和是否添加了新数据的标志
        """
        bdt = bars.datetime.tolist()
        tt = new_kline.datetime
        
        if tt in bdt:
            mylog.info(f'发现重复数据, 跳过... {tt}')
            return bars, False
        else:
            # 添加新K线数据
            newk = new_kline.to_frame().T
            bars = pd.concat([bars, newk], ignore_index=True)
            
            # 保存更新后的数据
            self.save_data(bars)
            mylog.info(f"新增K线数据: {tt}, 总数据量: {len(bars)}")
            
            return bars, True
    
    def get_filename(self):
        """获取数据文件名"""
        return self.data_filename
    
    def get_symbol(self):
        """获取交易品种代码"""
        return self.symbol
    
    def get_interval(self):
        """获取K线周期"""
        return self.interval
    
    def repair_data_file(self):
        """
        修复损坏的数据文件
        
        返回:
        修复是否成功
        """
        if not os.path.exists(self.data_filename):
            mylog.info("数据文件不存在，无需修复")
            return True
        
        try:
            mylog.info(f"开始修复数据文件: {self.data_filename}")
            
            # 逐行读取文件，过滤掉损坏的行
            good_lines = []
            header = None
            
            with open(self.data_filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
                if len(lines) == 0:
                    mylog.warning("文件为空")
                    return False
                
                # 获取表头
                header = lines[0].strip()
                expected_fields = len(header.split(','))
                good_lines.append(header)
                
                mylog.info(f"期望字段数: {expected_fields}")
                
                # 检查每一行
                for i, line in enumerate(lines[1:], start=2):
                    line = line.strip()
                    if not line:  # 跳过空行
                        continue
                        
                    fields = line.split(',')
                    if len(fields) == expected_fields:
                        good_lines.append(line)
                    else:
                        mylog.warning(f"跳过损坏行 {i}: 期望{expected_fields}字段，实际{len(fields)}字段")
            
            # 如果有有效数据，重写文件
            if len(good_lines) > 1:  # 至少有表头和一行数据
                backup_filename = f"{self.data_filename}.damaged"
                os.rename(self.data_filename, backup_filename)
                mylog.info(f"原文件已备份为: {backup_filename}")
                
                with open(self.data_filename, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(good_lines) + '\n')
                
                mylog.info(f"文件修复完成，保留了 {len(good_lines)-1} 行有效数据")
                return True
            else:
                mylog.error("没有找到有效数据行")
                return False
                
        except Exception as e:
            mylog.error(f"修复数据文件失败: {e}")
            return False


if __name__ == "__main__":
    # 测试数据管理器
    print("数据管理器组件测试")
    
    # 创建数据管理器实例
    dm = DataManager("SHFE.rb2510", 180)
    print(f"数据文件名: {dm.get_filename()}")
    
    # 测试历史数据加载
    historical_data = dm.load_historical_data()
    if historical_data is not None:
        print(f"历史数据量: {len(historical_data)}")
    else:
        print("无历史数据")