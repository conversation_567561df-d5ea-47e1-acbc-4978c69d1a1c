class pos_interval_info:

    def __init__(self, poslong_limit=1, posshort_limit=1, poslong=0, posshort=0, singlevolume=1):
        self.poslong_limit = poslong_limit
        self.posshort_limit = posshort_limit
        self.poslong = poslong
        self.posshort = posshort
        self.singlevolume = singlevolume


# 非常好，根据您提供的类定义，我可以给出一些具体的例子来说明如何动态调用这个类的变量。假设我们已经创建了四个实例，分别对应不同的时间周期：
s60 = pos_interval_info()
s180 = pos_interval_info()
s300 = pos_interval_info()
s900 = pos_interval_info()

class_instances = [s60, s180, s300, s900]


# 现在，我们可以用以下几种方式来动态调用类的变量：
#
# 使用 getattr() 函数：
#
# for instance in class_instances:
#     poslong = getattr(instance, 'poslong')
#     posshort = getattr(instance, 'posshort')
#     print(f"Long: {poslong}, Short: {posshort}")
#
#
# # 使用字典映射
# instance_map = {
#     60: s60,
#     180: s180,
#     300: s300,
#     900: s900
# }
#
# for duration in [60, 180, 300, 900]:
#     instance = instance_map[duration]
#     print(f"Duration {duration}s - Long: {instance.poslong}, Short: {instance.posshort}")
#
#
# variables = ['poslong_limit', 'posshort_limit','poslong', 'posshort', 'singlevolume']
#
# for instance in class_instances:
#     values = [getattr(instance, var) for var in variables]
#     print(f"Values: {values}")
#
# def update_position(instance, long_change, short_change):
#     instance.poslong += long_change
#     instance.posshort += short_change
#     return instance.poslong, instance.posshort
#
# for instance in class_instances:
#     new_long, new_short = update_position(instance, 1, -1)
#     print(f"New Long: {new_long}, New Short: {new_short}")
#
# def check_position_limits(instance):
#     long_limit_reached = instance.poslong >= instance.poslong_limit
#     short_limit_reached = instance.posshort >= instance.posshort_limit
#     return long_limit_reached, short_limit_reached
#
# for instance in class_instances:
#     long_limit, short_limit = check_position_limits(instance)
#     print(f"Long limit reached: {long_limit}, Short limit reached: {short_limit}")


def process_position(duration):
    # 创建实例映射字典
    instance_map = {
        60: s60,
        180: s180,
        300: s300,
        900: s900
    }

    # 1. 根据传入的参数,获取相应的实例
    if duration not in instance_map:
        print(f"Error: Invalid duration {duration}")
        return

    instance = instance_map[duration]

    # 2. 读取并打印实例中的相关属性
    print(f"Duration: {duration}s")
    print(f"Long position limit: {instance.poslong_limit}")
    print(f"Short position limit: {instance.posshort_limit}")
    print(f"Current long position: {instance.poslong}")
    print(f"Current short position: {instance.posshort}")

    # 3. 对实例中的poslong和posshort各加上常数3
    instance.poslong += 3
    instance.posshort += 3

    print("\nAfter adjustment:")
    print(f"New long position: {instance.poslong}")
    print(f"New short position: {instance.posshort}")

if __name__ == "__main__":
    # 使用示例
    import time
    while True:
        process_position(60)
        process_position(180)
        process_position(300)
        process_position(900)
        process_position(1200)

        time.sleep(1)