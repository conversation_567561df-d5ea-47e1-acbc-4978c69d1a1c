import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *


class StockWidget(QWidget):
    def init(self):
        super().init()
        self.stock_code = 'stock_code'
        self.initUI()


    def initUI(self):
        self.setWindowTitle('股票行情 - %s' % self.stock_code)
        self.resize(600, 400)
        self.setMinimumSize(400, 300)

        # 创建布局
        layout = QVBoxLayout()
        self.setLayout(layout)

        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(['指标', '值'])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.table)

        # 创建按钮
        self.refresh_button = QPushButton('刷新')
        self.refresh_button.clicked.connect(self.refresh)
        layout.addWidget(self.refresh_button)


    def refresh(self):
        # 获取股票行情数据，并更新表格
        data = {
            '开盘价': '100.00',
            '收盘价': '101.00',
            '最高价': '102.00',
            '最低价': '99.00',
            '成交量': '10000'
        }

        self.table.setRowCount(len(data))
        row = 0
        for k, v in data.items():
            self.table.setItem(row, 0, QTableWidgetItem(k))
            self.table.setItem(row, 1, QTableWidgetItem(v))
            row += 1


# if name == '__main__':
app = QApplication(sys.argv)
widget = StockWidget()
widget.show()
sys.exit(app.exec_())
