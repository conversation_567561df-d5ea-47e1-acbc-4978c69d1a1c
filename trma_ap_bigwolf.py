import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog

from time import sleep
import time
from strategyTRMA import trmastrategy


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    symbol = 'CZCE.AP110'
    symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 130000
    sklimit = 130000
    single_volume = 10

    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123")

    trmastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
