
from strategies.TimeRoseMA_cross_speak import ma_cross
from tqsdk import TqApi, TqKq

def run_single_timeframe():
    product = "sh"
    interval = 900
    bklimit = 1
    sklimit = 1
    single_volume = 1

    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")
    if len(product) <= 3:
        symbol = api.query_cont_quotes(product_id=product)[0]
    else:
        symbol = product

    print(f"启动 15m 时间周期策略，合约: {symbol}")
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)

if __name__ == "__main__":
    run_single_timeframe()
