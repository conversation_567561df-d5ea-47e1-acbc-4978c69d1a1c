from strategies.TimeRoseMA_cross_speak_explore import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date


    product = 'OI'
    symbol=product
    interval = 60
    bklimit = 60
    sklimit = 60
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    api = TqApi(TqKq(), auth="ningyukun1,258369", disable_print=True, debug=None)
    symbol = api.query_cont_quotes(product_id=product)[0]
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit, period=13)


runstrategy()
