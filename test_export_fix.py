"""
测试导出功能修复
验证filedialog参数修复是否正确
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import matplotlib.pyplot as plt
import os
import sys

def test_filedialog_parameters():
    """测试文件对话框参数"""
    print("=" * 60)
    print("测试文件对话框参数")
    print("=" * 60)
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 测试正确的参数
        print("测试正确的filedialog参数...")
        
        # 模拟导出功能的参数
        test_params = {
            "title": "导出图表",
            "defaultextension": ".png",
            "initialfile": "测试图表",  # 正确的参数名
            "filetypes": [("PNG files", "*.png"), ("JPG files", "*.jpg"), 
                         ("PDF files", "*.pdf"), ("SVG files", "*.svg")]
        }
        
        print("✓ 参数配置正确:")
        for key, value in test_params.items():
            print(f"  {key}: {value}")
        
        # 验证参数名称
        valid_params = [
            'confirmoverwrite', 'defaultextension', 'filetypes', 
            'initialdir', 'initialfile', 'parent', 'title', 'typevariable'
        ]
        
        print(f"\n✓ 有效的filedialog参数:")
        for param in valid_params:
            print(f"  - {param}")
        
        # 检查我们使用的参数是否都有效
        all_valid = True
        for param in test_params.keys():
            if param in valid_params:
                print(f"✓ {param} 是有效参数")
            else:
                print(f"✗ {param} 是无效参数")
                all_valid = False
        
        root.destroy()
        return all_valid
        
    except Exception as e:
        print(f"✗ 文件对话框参数测试失败: {e}")
        return False

def test_export_function_simulation():
    """模拟测试导出功能"""
    print("=" * 60)
    print("模拟测试导出功能")
    print("=" * 60)
    
    try:
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 绘制测试数据
        x = range(10)
        y = [i**2 for i in x]
        
        ax.plot(x, y, marker='o', linewidth=2, color='blue')
        ax.set_title('测试图表', fontsize=14, fontweight='bold')
        ax.set_xlabel('X轴')
        ax.set_ylabel('Y轴')
        ax.grid(True, alpha=0.3)
        
        print("✓ 测试图表创建成功")
        
        # 模拟导出功能
        def mock_export_chart(mode="总体分析", account=None):
            """模拟导出图表功能"""
            # 根据模式设置默认文件名
            if mode == "分账户分析" and account:
                default_name = f"{account}_资金曲线"
            else:
                default_name = "总体资金曲线分析"
            
            print(f"模拟导出: 模式={mode}, 默认文件名={default_name}")
            
            # 模拟文件对话框参数（不实际显示对话框）
            file_params = {
                "title": "导出图表",
                "defaultextension": ".png",
                "initialfile": default_name,  # 修复后的参数名
                "filetypes": [("PNG files", "*.png"), ("JPG files", "*.jpg"), 
                             ("PDF files", "*.pdf"), ("SVG files", "*.svg")]
            }
            
            print("✓ 文件对话框参数:")
            for key, value in file_params.items():
                print(f"  {key}: {value}")
            
            # 模拟保存文件
            test_filename = f"{default_name}.png"
            try:
                fig.savefig(test_filename, dpi=300, bbox_inches='tight', facecolor='white')
                print(f"✓ 模拟导出成功: {test_filename}")
                
                # 检查文件是否创建
                if os.path.exists(test_filename):
                    file_size = os.path.getsize(test_filename)
                    print(f"✓ 文件大小: {file_size:,} 字节")
                    
                    # 清理测试文件
                    os.remove(test_filename)
                    print("✓ 测试文件已清理")
                
                return True
            except Exception as e:
                print(f"✗ 模拟导出失败: {e}")
                return False
        
        # 测试不同模式的导出
        test_cases = [
            ("总体分析", None),
            ("分账户分析", "张三的账户"),
            ("分账户分析", "李四投资")
        ]
        
        all_passed = True
        for mode, account in test_cases:
            print(f"\n测试导出: {mode}" + (f" - {account}" if account else ""))
            if not mock_export_chart(mode, account):
                all_passed = False
        
        plt.close(fig)
        return all_passed
        
    except Exception as e:
        print(f"✗ 导出功能模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_program_fix():
    """测试程序修复"""
    print("=" * 60)
    print("测试程序修复")
    print("=" * 60)
    
    try:
        # 检查程序文件
        program_file = "模拟盘资金曲线分析器.py"
        if not os.path.exists(program_file):
            print(f"✗ 程序文件不存在: {program_file}")
            return False
        
        # 读取程序内容
        with open(program_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已修复
        if 'initialname=' in content:
            print("✗ 程序中仍然包含错误的 'initialname' 参数")
            return False
        
        if 'initialfile=' in content:
            print("✓ 程序中包含正确的 'initialfile' 参数")
        else:
            print("✗ 程序中没有找到 'initialfile' 参数")
            return False
        
        # 检查导出函数
        if 'def export_chart(self):' in content:
            print("✓ 导出函数存在")
        else:
            print("✗ 导出函数不存在")
            return False
        
        # 检查文件对话框调用
        if 'filedialog.asksaveasfilename(' in content:
            print("✓ 文件对话框调用存在")
        else:
            print("✗ 文件对话框调用不存在")
            return False
        
        print("✓ 程序修复验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 程序修复测试失败: {e}")
        return False

def show_fix_summary():
    """显示修复总结"""
    print("=" * 60)
    print("导出功能修复总结")
    print("=" * 60)
    
    print("问题描述:")
    print("  tkinter.filedialog.asksaveasfilename() 函数报错")
    print("  错误信息: bad option \"-initialname\"")
    print()
    
    print("问题原因:")
    print("  使用了错误的参数名 'initialname'")
    print("  正确的参数名应该是 'initialfile'")
    print()
    
    print("修复方案:")
    print("  将 initialname=default_name 改为 initialfile=default_name")
    print()
    
    print("有效的 filedialog 参数:")
    valid_params = [
        'confirmoverwrite', 'defaultextension', 'filetypes', 
        'initialdir', 'initialfile', 'parent', 'title', 'typevariable'
    ]
    for param in valid_params:
        print(f"  • {param}")
    
    print()
    print("修复后的代码:")
    print("  file_path = filedialog.asksaveasfilename(")
    print("      title=\"导出图表\",")
    print("      defaultextension=\".png\",")
    print("      initialfile=default_name,  # 修复: initialname -> initialfile")
    print("      filetypes=[...]")
    print("  )")

def main():
    """主测试函数"""
    print("导出功能修复测试")
    print("=" * 80)
    
    tests = [
        ("文件对话框参数", test_filedialog_parameters),
        ("程序修复验证", test_program_fix),
        ("导出功能模拟", test_export_function_simulation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示修复总结
    show_fix_summary()
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:15}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 导出功能修复成功！")
        print("\n修复内容:")
        print("✓ 修正了 filedialog 参数名错误")
        print("✓ initialname -> initialfile")
        print("✓ 导出功能现在可以正常工作")
        print("\n现在可以正常使用导出功能:")
        print("1. 运行程序: python 模拟盘资金曲线分析器.py")
        print("2. 加载数据并生成图表")
        print("3. 点击'💾 导出图表'按钮")
        print("4. 选择保存位置和文件格式")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
