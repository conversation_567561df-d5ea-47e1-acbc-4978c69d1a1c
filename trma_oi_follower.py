import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog

from time import sleep
import time
from strategyTRMA_fixed_volume import trmastrategy


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    symbol = 'SHFE.ag2105'
    # symbol = 'CZCE.SR101'
    # symbol = 'DCE.pp2105'
    symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 200
    sklimit = 200
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="follower,ftp123")
    symbol = api.query_cont_quotes(product_id='OI')[0]
    trmastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
