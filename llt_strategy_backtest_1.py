import pandas as pd
from tqsdk import TqApi, TqAuth, TqBacktest, TqAccount
import matplotlib.pyplot as plt
import matplotlib.dates as mdates


# 1. 核心LLT计算函数 (与之前相同)
def cal_LLT(price: pd.Series, alpha: float):
    """
    计算低延迟趋势线 (LLT)
    """
    LLT = []
    price_value = price.values
    if len(price_value) < 2:
        return [float('nan')] * len(price_value)

    LLT.append(price_value[0])
    LLT.append(price_value[1])

    for i, e in enumerate(price_value):
        if i > 1:
            v = (alpha - alpha ** 2 / 4) * e + (alpha ** 2 / 2) * price_value[i - 1] - \
                (alpha - 3 * (alpha ** 2) / 4) * price_value[i - 2] + 2 * \
                (1 - alpha) * LLT[i - 1] - (1 - alpha) ** 2 * LLT[i - 2]
            LLT.append(v)

    return LLT


def run_llt_backtest(symbol, kline_period, start_dt, end_dt):
    """
    执行LLT策略的回测并绘制PnL曲线。
    """
    # 策略参数
    D_VALUE = 60
    ALPHA = 2 / (D_VALUE + 1)
    VOLUME = 1

    # 初始化 TqApi 进行回测
    api = TqApi(account=TqAccount(init_balance=********),
                backtest=TqBacktest(start_dt=start_dt, end_dt=end_dt),
                auth=TqAuth("快期账户", "账户密码"))

    print(f"开始回测: 合约={symbol}, 周期={kline_period}秒, 时间={start_dt} to {end_dt}")

    # 获取指定时间范围内的K线数据，留足计算余量
    klines = api.get_kline_serial(symbol, duration=kline_period, data_length=8000)

    # +++ 新增功能：计算并输出所有历史信号 +++
    print("\n" + "=" * 25 + " 历史信号输出 " + "=" * 25)
    initial_close_prices = pd.Series(klines.close)
    initial_llt_series = cal_LLT(initial_close_prices, ALPHA)

    # 从第三根K线开始，因为LLT需要两个前置值
    for i in range(2, len(klines)):
        # 确定信号
        if initial_llt_series[i] > initial_llt_series[i - 1]:
            signal = 1  # 看多
        elif initial_llt_series[i] < initial_llt_series[i - 1]:
            signal = -1  # 看空
        else:
            signal = 0  # 观望

        # 格式化输出
        dt = pd.to_datetime(klines.iloc[i]["datetime"])
        close_price = klines.iloc[i]["close"]
        llt_val = initial_llt_series[i]

        print(f"日期: {dt.strftime('%Y-%m-%d %H:%M')}, 收盘价: {close_price:.2f}, LLT: {llt_val:.2f}, 信号: {signal}")

    print("=" * 25 + " 历史信号输出结束 " + "=" * 25 + "\n")
    print("开始进入实时回测循环...\n")
    # +++ 功能结束 +++

    # 用于存储每个时间点的账户权益
    pnl_records = []

    # 主循环，等待API更新
    while True:
        api.wait_update()

        if api.is_changing(klines.iloc[-1], "datetime"):
            # 记录当前账户权益
            account = api.get_account()
            current_time = pd.to_datetime(klines.iloc[-1]["datetime"])
            pnl_records.append({
                "datetime": current_time,
                "pnl": account.static_balance
            })

            # --- 交易逻辑 ---
            close_prices = pd.Series(klines.close)
            llt_series = cal_LLT(close_prices, ALPHA)

            if len(llt_series) < 2:
                continue

            if llt_series[-1] > llt_series[-2]:
                signal = 1
            elif llt_series[-1] < llt_series[-2]:
                signal = -1
            else:
                signal = 0

            position = api.get_position(symbol)

            if signal == 1:
                if position.pos_short > 0:
                    api.close_position(symbol, offset="CLOSE", volume=position.pos_short)
                if position.pos_long == 0:
                    api.insert_order(symbol=symbol, direction="BUY", offset="OPEN", volume=VOLUME)
            elif signal == -1:
                if position.pos_long > 0:
                    api.close_position(symbol, offset="CLOSE", volume=position.pos_long)
                if position.pos_short == 0:
                    api.insert_order(symbol=symbol, direction="SELL", offset="OPEN", volume=VOLUME)

        if api.is_backtest_finished():
            break

    api.close()
    print("回测完成！")

    # 绘制PnL曲线
    if not pnl_records:
        print("没有生成PnL数据，无法绘制曲线。")
        return

    pnl_df = pd.DataFrame(pnl_records)

    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    plt.figure(figsize=(15, 7))
    plt.plot(pnl_df['datetime'], pnl_df['pnl'], label='账户静态权益')
    plt.title(f'"{symbol}" PnL 曲线 ({start_dt} to {end_dt})')
    plt.xlabel("日期")
    plt.ylabel("账户静态权益 (元)")

    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    plt.gca().xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.gcf().autofmt_xdate()

    plt.grid(True)
    plt.legend()

    pnl_chart_path = "pnl_curve.png"
    plt.savefig(pnl_chart_path)
    print(f"PnL曲线图已保存至: {pnl_chart_path}")


# --- 回测参数设置 ---
if __name__ == "__main__":
    SYMBOL = "SHFE.rb2509"
    KLINE_PERIOD = 24 * 60 * 60
    START_DATE = "2023-01-01"
    END_DATE = "2024-07-01"

    run_llt_backtest(
        symbol=SYMBOL,
        kline_period=KLINE_PERIOD,
        start_dt=START_DATE,
        end_dt=END_DATE
    )