"""
演示缠论策略的交互式功能
"""

import subprocess
import sys

def demo_interactive_help():
    """演示交互式帮助"""
    print("=" * 60)
    print("演示缠论策略交互式功能")
    print("=" * 60)
    
    print("当您运行以下命令时:")
    print("python chanlun_strategy.py")
    print()
    print("程序会显示:")
    print("-" * 40)
    
    # 模拟显示帮助信息
    from chanlun_strategy import show_usage_help
    show_usage_help()
    
    print("-" * 40)
    print()
    print("然后程序会提示您选择操作:")
    print("选择操作:")
    print("  1. 交互式配置参数")
    print("  2. 查看完整帮助")
    print("  3. 使用默认配置运行")
    print("  4. 退出")
    print("请输入选择 (1-4):")
    print()
    print("如果选择1，程序会引导您配置:")
    print("- TQ用户名和密码")
    print("- 初始资金")
    print("- 回测时间范围")
    print()
    print("配置完成后确认运行即可开始回测。")


def demo_command_line_usage():
    """演示命令行使用方法"""
    print("=" * 60)
    print("命令行使用方法")
    print("=" * 60)
    
    examples = [
        {
            "title": "查看帮助",
            "command": "python chanlun_strategy.py --help",
            "description": "显示完整的参数说明和使用示例"
        },
        {
            "title": "使用默认配置",
            "command": "python chanlun_strategy.py --tq-user smartmanp --tq-pwd ftp123",
            "description": "使用默认的时间范围和资金配置"
        },
        {
            "title": "自定义时间范围",
            "command": "python chanlun_strategy.py --start-date 20220101 --end-date 20231231",
            "description": "回测2022年到2023年的数据"
        },
        {
            "title": "自定义初始资金",
            "command": "python chanlun_strategy.py --init-money 2000000",
            "description": "设置初始资金为200万元"
        },
        {
            "title": "完整自定义",
            "command": "python chanlun_strategy.py --tq-user myuser --tq-pwd mypwd --init-money 500000 --start-date 20220101",
            "description": "自定义所有参数"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['title']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")
        print()


def demo_parameter_explanation():
    """演示参数说明"""
    print("=" * 60)
    print("参数详细说明")
    print("=" * 60)
    
    params = [
        {
            "param": "--tq-user",
            "type": "字符串",
            "default": "smartmanp",
            "description": "天勤量化平台的用户名"
        },
        {
            "param": "--tq-pwd",
            "type": "字符串", 
            "default": "ftp123",
            "description": "天勤量化平台的密码"
        },
        {
            "param": "--init-money",
            "type": "整数",
            "default": "1000000",
            "description": "回测初始资金，单位：元"
        },
        {
            "param": "--start-date",
            "type": "字符串",
            "default": "20180101",
            "description": "回测开始日期，格式：YYYYMMDD"
        },
        {
            "param": "--end-date",
            "type": "字符串",
            "default": "20250728",
            "description": "回测结束日期，格式：YYYYMMDD"
        }
    ]
    
    print(f"{'参数':<15} {'类型':<8} {'默认值':<12} {'说明'}")
    print("-" * 70)
    
    for param in params:
        print(f"{param['param']:<15} {param['type']:<8} {param['default']:<12} {param['description']}")


def main():
    """主演示函数"""
    print("缠论策略参数提示功能演示")
    print("=" * 80)
    
    demos = [
        ("交互式功能演示", demo_interactive_help),
        ("命令行使用方法", demo_command_line_usage),
        ("参数详细说明", demo_parameter_explanation),
    ]
    
    for demo_name, demo_func in demos:
        print(f"\n{demo_name}")
        demo_func()
    
    print("\n" + "=" * 80)
    print("总结")
    print("=" * 80)
    print("缠论策略现在支持两种使用方式:")
    print()
    print("1. 交互式使用（推荐新手）:")
    print("   - 直接运行: python chanlun_strategy.py")
    print("   - 程序会引导您配置所有参数")
    print("   - 适合不熟悉命令行的用户")
    print()
    print("2. 命令行使用（推荐高级用户）:")
    print("   - 直接指定参数运行")
    print("   - 适合脚本化和批量处理")
    print("   - 可以快速重复运行不同配置")
    print()
    print("无论选择哪种方式，都可以:")
    print("- 自定义TQ账户信息")
    print("- 设置回测时间范围")
    print("- 调整初始资金")
    print("- 查看详细帮助信息")
    print()
    print("现在就可以开始使用缠论策略进行回测了！")


if __name__ == "__main__":
    main()
