"""
测试多时间周期策略的命令行参数功能
"""

import subprocess
import sys
import time


def test_help():
    """测试帮助信息"""
    print("=" * 50)
    print("测试帮助信息")
    print("=" * 50)
    
    try:
        # 不提供参数，应该显示错误和帮助
        result = subprocess.run([
            sys.executable, 
            "TimeRoseMA_cross_ag_MultiTimeFrames.py"
        ], capture_output=True, text=True, timeout=10)
        
        print("返回码:", result.returncode)
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("命令超时")
    except Exception as e:
        print(f"测试失败: {e}")


def test_single_timeframe():
    """测试单时间周期模式"""
    print("=" * 50)
    print("测试单时间周期模式")
    print("=" * 50)
    
    test_cases = [
        ["ag", "1m"],
        ["rb", "5m"],
        ["cu", "15m"]
    ]
    
    for product, timeframe in test_cases:
        print(f"\n测试: {product} {timeframe}")
        try:
            # 启动进程但立即终止（只测试参数解析）
            process = subprocess.Popen([
                sys.executable,
                "TimeRoseMA_cross_ag_MultiTimeFrames.py",
                product,
                timeframe
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 等待一小段时间让程序启动
            time.sleep(2)
            
            # 终止进程
            process.terminate()
            
            # 获取输出
            stdout, stderr = process.communicate(timeout=5)
            
            print(f"启动成功，输出:")
            print(stdout[:200] + "..." if len(stdout) > 200 else stdout)
            
        except subprocess.TimeoutExpired:
            print("进程启动成功（超时终止）")
            process.kill()
        except Exception as e:
            print(f"测试失败: {e}")


def test_multi_timeframe():
    """测试多时间周期模式"""
    print("=" * 50)
    print("测试多时间周期模式")
    print("=" * 50)
    
    test_cases = [
        ["ag"],  # 默认模式
        ["rb", "independent"],
        ["cu", "threaded"]
    ]
    
    for case in test_cases:
        print(f"\n测试: {' '.join(case)}")
        try:
            # 启动进程但立即终止（只测试参数解析）
            process = subprocess.Popen([
                sys.executable,
                "TimeRoseMA_cross_ag_MultiTimeFrames.py"
            ] + case, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 等待一小段时间让程序启动
            time.sleep(2)
            
            # 终止进程
            process.terminate()
            
            # 获取输出
            stdout, stderr = process.communicate(timeout=5)
            
            print(f"启动成功，输出:")
            print(stdout[:200] + "..." if len(stdout) > 200 else stdout)
            
        except subprocess.TimeoutExpired:
            print("进程启动成功（超时终止）")
            process.kill()
        except Exception as e:
            print(f"测试失败: {e}")


def test_invalid_args():
    """测试无效参数"""
    print("=" * 50)
    print("测试无效参数")
    print("=" * 50)
    
    test_cases = [
        ["ag", "invalid_timeframe"],
        ["rb", "invalid_mode"],
    ]
    
    for case in test_cases:
        print(f"\n测试无效参数: {' '.join(case)}")
        try:
            result = subprocess.run([
                sys.executable,
                "TimeRoseMA_cross_ag_MultiTimeFrames.py"
            ] + case, capture_output=True, text=True, timeout=10)
            
            print("返回码:", result.returncode)
            print("输出:")
            print(result.stdout)
            if result.stderr:
                print("错误:")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("命令超时")
        except Exception as e:
            print(f"测试失败: {e}")


def show_usage_examples():
    """显示使用示例"""
    print("=" * 50)
    print("使用示例")
    print("=" * 50)
    
    examples = [
        "# 运行ag多时间周期策略（默认模式）",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py ag",
        "",
        "# 运行rb独立API模式多时间周期策略",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py rb independent",
        "",
        "# 运行cu 5分钟单时间周期策略",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py cu 5m",
        "",
        "# 运行au 1分钟策略，使用自定义认证",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py au 1m \"user,pass\"",
        "",
        "# 运行ni多线程模式多时间周期策略",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py ni threaded",
    ]
    
    for example in examples:
        print(example)


def main():
    """主测试函数"""
    print("多时间周期策略命令行参数测试")
    print("=" * 60)
    
    # 显示使用示例
    show_usage_examples()
    
    # 测试帮助信息
    test_help()
    
    # 测试无效参数
    test_invalid_args()
    
    print("\n" + "=" * 60)
    print("基本参数解析测试完成")
    print("=" * 60)
    
    # 询问是否进行实际运行测试
    choice = input("\n是否进行实际运行测试？(需要TqSDK认证) [y/N]: ").strip().lower()
    
    if choice in ['y', 'yes']:
        print("\n注意: 以下测试会实际启动策略程序，请确保有有效的TqSDK认证")
        input("按回车键继续...")
        
        # 测试单时间周期
        test_single_timeframe()
        
        # 测试多时间周期
        test_multi_timeframe()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
