# 连续交易双边市场多时间周期策略

## 概述

这是一个完整的多时间周期双边市场交易策略实现，支持5个K线周期（1分钟、15分钟、4小时、日K线、周K线），实现了金叉死叉信号、回归信号、移动止损等高级功能。

## 🚀 核心特性

### 1. 多时间周期支持
- **5个标准周期**：1分钟、15分钟、4小时、日K线、周K线
- **自定义周期**：支持任意时间周期组合
- **等权重资金分配**：每个周期平均分配资金和杠杆

### 2. 双均线信号系统
- **金叉信号**：短均线上穿长均线
- **死叉信号**：短均线下穿长均线
- **向上回归**：短长均线差值从负最小值逐渐增大
- **向下回归**：短长均线差值从正最大值逐渐减小

### 3. 精确入场机制
- **1分钟级别确认**：所有时间周期的入场点都以1分钟K线交叉点确认
- **多重条件验证**：当前周期信号 + K线方向 + 大周期回归信号

### 4. 智能风险管理
- **动态止损**：基于最近N个周期的最高/最低点
- **移动止损**：盈利超过阈值后启用跟踪止损
- **持仓限制**：每个周期每个方向只开一个仓
- **资金控制**：总持仓不超过资金的N%，杠杆不超过M倍

## 📁 文件结构

```
├── multi_timeframe_dual_market_strategy.py  # 主策略文件
├── strategy_config.py                       # 配置管理
├── strategy_examples.py                     # 使用示例
└── README_MultiTimeFrame_Strategy.md        # 本文档
```

## 🔧 策略参数

### 核心参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `timeframes` | [60,900,14400,86400,604800] | 时间周期（秒） |
| `short_ma_period` | 5 | 短均线周期 |
| `long_ma_period` | 20 | 长均线周期 |
| `stop_loss_periods` | 10 | 止损计算周期数 |
| `max_position_ratio` | 0.1 | 最大持仓比例（10%） |
| `max_leverage` | 3.0 | 最大杠杆倍数 |
| `commission_rate` | 0.0003 | 双边手续费率 |
| `trailing_stop_enabled` | True | 是否启用移动止损 |
| `profit_threshold_ratio` | 2.0 | 盈利阈值（手续费倍数） |

### 预设配置方案
- **保守型**：较低风险，较长均线周期
- **激进型**：较高风险，较短均线周期
- **平衡型**：中等风险，标准参数
- **剥头皮**：快进快出，短周期
- **波段型**：持仓时间较长，大周期

## 🎯 交易逻辑

### 开仓条件

#### 多仓开仓
1. 当前K线周期出现"金叉"信号
2. 当前K线为阳线（收盘价 > 开盘价）
3. 上一级大周期出现"向上回归"信号
4. 1分钟级别确认金叉信号
5. 该周期当前无持仓

#### 空仓开仓
1. 当前K线周期出现"死叉"信号
2. 当前K线为阴线（收盘价 < 开盘价）
3. 上一级大周期出现"向下回归"信号
4. 1分钟级别确认死叉信号
5. 该周期当前无持仓

### 止损机制

#### 初始止损
- **多仓**：最近N个周期的最低点
- **空仓**：最近N个周期的最高点

#### 移动止损
- 盈利超过双边手续费的2倍时启用
- 止损点设为当前价格与长周期均线的中间点
- 只能向有利方向移动

## 📊 使用方法

### 1. 基本使用

```python
from multi_timeframe_dual_market_strategy import MultiTimeFrameStrategy, StrategyConfig

# 创建策略配置
config = StrategyConfig()

# 创建策略实例
strategy = MultiTimeFrameStrategy(
    config=config,
    symbol='rb',  # 螺纹钢
    initial_capital=100000
)

# 运行策略
strategy.run_strategy(iterations=50)
```

### 2. 自定义参数

```python
config = StrategyConfig(
    timeframes=[60, 900, 3600],  # 1分钟、15分钟、1小时
    short_ma_period=3,           # 短均线3周期
    long_ma_period=15,           # 长均线15周期
    max_position_ratio=0.08,     # 最大持仓8%
    max_leverage=2.5,            # 杠杆2.5倍
    stop_loss_periods=8,         # 止损8个周期
    commission_rate=0.0002,      # 手续费0.02%
)
```

### 3. 使用预设配置

```python
from strategy_config import load_config_from_preset, load_config_for_symbol

# 加载保守型配置
config = load_config_from_preset("conservative")

# 为特定品种优化配置
config = load_config_for_symbol("ag", config)  # 白银
```

### 4. 命令行使用

```bash
# 运行示例
python multi_timeframe_dual_market_strategy.py example

# 自定义运行
python multi_timeframe_dual_market_strategy.py custom rb 200000

# 回测模式
python multi_timeframe_dual_market_strategy.py backtest ag 2024-01-01 2024-12-31
```

### 5. 配置管理

```bash
# 交互式创建配置
python strategy_config.py interactive

# 查看预设配置
python strategy_config.py preset balanced

# 查看品种配置
python strategy_config.py symbol rb
```

### 6. 运行示例

```bash
# 运行所有示例
python strategy_examples.py all

# 运行特定示例
python strategy_examples.py 1  # 基本使用
python strategy_examples.py 4  # 多品种对比
python strategy_examples.py 7  # 均线参数优化
```

## 🔍 策略优势

### 1. 多维度信号确认
- 当前周期信号
- K线方向确认
- 大周期趋势确认
- 1分钟精确入场

### 2. 完善的风险控制
- 多层次止损机制
- 动态资金管理
- 持仓数量限制
- 杠杆风险控制

### 3. 高度可配置
- 灵活的时间周期组合
- 可调整的均线参数
- 多种预设配置方案
- 品种特定优化

### 4. 实战导向
- 考虑手续费成本
- 移动止损优化
- 等权重资金分配
- 1分钟精确执行

## ⚠️ 注意事项

### 1. 风险提示
- 本策略仅供学习和研究使用
- 实盘交易前请充分测试
- 注意控制风险和资金管理
- 不同市场环境下表现可能不同

### 2. 参数调优
- 根据不同品种调整参数
- 考虑市场波动性特征
- 定期回测和优化
- 监控策略表现

### 3. 技术要求
- 稳定的网络连接
- 有效的TqSDK认证
- 足够的计算资源
- 实时数据源

## 🛠️ 扩展功能

### 1. 可添加的功能
- 更多技术指标
- 机器学习信号
- 情绪指标
- 基本面数据

### 2. 优化方向
- 参数自动优化
- 动态仓位管理
- 多品种组合
- 风险平价

### 3. 集成可能
- 回测框架
- 实时监控
- 报告生成
- 风险管理系统

## 📈 性能监控

策略提供详细的执行报告：
- 总收益和收益率
- 各时间周期信号统计
- 持仓状态监控
- 实时盈亏计算

## 🤝 贡献

欢迎提交问题和改进建议：
1. 参数优化建议
2. 新功能需求
3. Bug报告
4. 性能改进

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
