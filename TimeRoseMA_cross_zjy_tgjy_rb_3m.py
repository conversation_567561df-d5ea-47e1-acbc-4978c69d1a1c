from strategies.TimeRoseMA_cross import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount
    from datetime import date
    from accounts_zjy import tgjyzjy as acct


    productid = 'rb'
    # symbol=product
    interval = 60*3
    bklimit = 3
    sklimit = 2
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    symbol = api.query_cont_quotes(product_id=productid)[0]

    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


runstrategy()
