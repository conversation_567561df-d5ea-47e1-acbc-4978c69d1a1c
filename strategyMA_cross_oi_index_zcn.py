import copy
import sys

from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tradefuncs import *
from speaktextng import speak_text
from loguru import logger as mylog

from time import sleep
import time


def disp_day_info(quote, daymean, last3mean, hlmax, homean, olmean):
    gapcover = False
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    print(quote.datetime, quote.instrument_id, '缺口：', gap, '回补:', gapcover, '最大价差:', hlmax, '平均价差:', daymean, '三日平均:',
          last3mean, '上价差:', int(homean), '下价差:', int(olmean), '现价:', quote.last_price, 'BB:', BB, '今日价差：', dayvib,
          '上价差:', upvib, '下价差:', dnvib)


def MaCrossCaculate(C, period, quote):
    trmac = ma(C, period)
    ups = crossup(C, trmac)
    dns = crossdown(C, trmac)
    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)
    if bkdist > skdist:
        signalnow = 'SK'
        distnow = skdist
    else:
        signalnow = 'BK'
        distnow = bkdist

    signalprice = C.iloc[-distnow]
    sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
    # print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)
    uplist = ups.tolist()
    average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
    print('平均信号距离：', average_signal_distance)
    print('upsignal:  ', ups.iloc[-40:].tolist())
    print('downsignal:', dns.iloc[-40:].tolist())


def ma_cross(api, SYMBOL, interval, single_volume, bklimit, sklimit, period=13):
    strategyname = 'macross'
    acct = api.get_account()
    userid = acct.user_id.split('-')[0]
    logfilename = '_'.join([userid, SYMBOL, strategyname])
    mylog.add(logfilename + '.log', encoding='utf-8')
    savebars = False
    quote = api.get_quote(SYMBOL)

    # if '@' in symbol:
    #     SYMBOL = quote.underlying_symbol
    # else:
    #     SYMBOL = symbol
    #
    # trading_symbol = SYMBOL.split('.')[1]

    ticks = api.get_tick_serial(SYMBOL)
    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines

    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()
    hl = daybar.high - daybar.low
    ho = daybar.high - daybar.open
    ol = daybar.open - daybar.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    N = 23
    O = klines1.open
    H = klines1.high
    L = klines1.low
    C = klines1.close

    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    KNUM = len(klines1)
    gapcover = False

    position = api.get_position(SYMBOL)
    acc = api.get_account()
    bkvol = position.pos_long
    bkvol_yd = position.pos_long_his
    bkvol_td = position.pos_long_today

    skvol = position.pos_short
    skvol_yd = position.pos_short_his
    skvol_td = position.pos_short_today

    skflag = True
    bkflag = True
    spflag = True
    bpflag = True
    speakyn = True
    dayopensp = True
    dayopenbp = True

    fundavailable = acc.available
    disp_day_info(quote, daymean, last3mean, hlmax, homean, olmean)
    MaCrossCaculate(C, period, quote)
    # print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)

    while True:

        dthour = time.localtime().tm_hour
        dtmin = time.localtime().tm_min

        if (dthour == 15 and (0 < dtmin < 30)) or (dthour == 2 and (30 < dtmin < 45)):
            klines1.to_csv(SYMBOL + '.csv')
            api.close()
            mylog.info('非交易时间, 退出系统.')
            return

        api.wait_update()

        if api.is_changing(daybars.iloc[-1], "datetime"):
            savebars = True
            dayopensp = True
            dayopenbp = True

            hl = daybar.high - daybar.low
            ho = daybar.high - daybar.open
            ol = daybar.open - daybar.low
            daymean = int(hl.mean())
            homean = ho.mean()
            olmean = ol.mean()
            hlmax = max(hl)
            last3mean = int(hl.iloc[-4:-1].mean())
            dayopen = quote.open
            dayhigh = quote.highest
            daylow = quote.lowest
            preclose = quote.pre_close
            dayvib = dayhigh - daylow
            gap = dayopen - preclose
            gapcover = False
            print('日线数据更新完成。。。')
            mylog.info('日线数据更新完成。。。')

        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            # 恢复下单标志, 确保k线周期内如果有多个信号的话,执行一次.
            disp_day_info(quote, daymean, last3mean, hlmax, homean, olmean)
            speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                klines1 = klines1.append(newk)

            O = klines1.open
            H = klines1.high
            L = klines1.low
            C = klines1.close

            hl = ema(H - L, 23).iloc[-1]
            cover_gap = max(int(hl), 3)

            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today
            bkfreeze = position.volume_long_frozen

            # volume_long_frozen_today: int
            # volume_long_frozen_his: int

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today
            skfreeze = position.volume_short_frozen

            trmac = ma(C, period)
            # trmac = ma(C, 10)
            ups = crossup(C, trmac)
            dns = crossdown(C, trmac)
            upslist = ups.tolist()
            dnslist = dns.tolist()
            bkdist = be_apart_from(upslist)
            skdist = be_apart_from(dnslist)
            if bkdist > skdist:
                signalnow = 'SK'
                distnow = skdist
            else:
                signalnow = 'BK'
                distnow = bkdist

            signalprice = C.iloc[-distnow]
            sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
            print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price,
                  '信号盈亏:', sigfloatprofit)
            uplist = ups.tolist()
            average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
            print('平均信号距离：', average_signal_distance)
            print('upsignal:  ', ups.iloc[-40:].tolist())
            print('downsignal:', dns.iloc[-40:].tolist())

            # disp_day_info(quote,daymean,last3mean,hlmax,homean,olmean)

            # 交易部分
            basevalue = average_signal_distance * 2
            order_volume = (basevalue - distnow) * single_volume if distnow < average_signal_distance else single_volume
            order_volume = single_volume

            if upslist[-1]:
                mylog.info('发出做多信号....')
                speak_text('发出做多信号')
                skvol = position.pos_short

                bkprice = quote.last_price
                mylog.info(['bkprice', bkprice, 'skprofit', position.float_profit_short, 'skvol', skvol])
                if skvol > 0:
                    if position.float_profit_short > 5*skvol:  # 空单盈利
                        BP(api, symbol=SYMBOL, order_price=bkprice, volume=skvol)
                        mylog.info(['ping kong dan...', 'volume', skvol, 'price', bkprice])
                    else:
                        mylog.info('shor float proft doesnt match, dont cover.')



                if skvol == 0:
                    if bkvol == 0:
                        BK(api, symbol=SYMBOL, order_price=bkprice, volume=single_volume)
                        mylog.info(['open new short pos', 'volume', single_volume, 'price', bkprice])


                else:
                    orderVol = min(bklimit - bkvol, skvol * 2 - bkvol)
                    if orderVol <= 0:
                        mylog.info('持仓数量已经达到,不下单...')
                    else:
                        BK(api, symbol=SYMBOL, order_price=bkprice, volume=orderVol)
                        mylog.info(['open new long pos', 'volume', orderVol, 'price', bkprice])

            if dnslist[-1]:
                mylog.info('发出做空信号....')
                speak_text('发出做空信号')
                skprice = quote.last_price
                bkvol = position.pos_long
                bkvol_cost = position.open_price_long

                mylog.info(['skprice', skprice, 'bkprofit', position.float_profit_long, 'bkvol', bkvol])

                if bkvol > 0:
                    if position.float_profit_long > 5 * bkvol:  # 多单盈利
                        SP(api, symbol=SYMBOL, order_price=skprice, volume=bkvol)
                        mylog.info(['ping duo dan.', 'volume', bkvol, 'price', skprice])

                    else:
                        mylog.info('float profit of long pos does not match the condition. dont cover.')

                if skvol==0 and bkvol == 0:
                    SK(api, symbol=SYMBOL, order_price=skprice, volume=single_volume)
                    mylog.info(['open new short pos', 'volume', single_volume, 'price', skprice])

                else:
                    mylog.info('reach sklimit, do not open new short pos.')




                if bkvol >0 and skvol<sklimit:
                    orderVol = min(sklimit - skvol, bkvol * 2 - skvol)
                    if orderVol <= 0:
                        mylog.info('持仓数量已达要求,不下单.')

                    else:
                        SK(api, symbol=SYMBOL, order_price=skprice, volume=orderVol)
                        mylog.info(['open short pos', 'volume', orderVol, 'price', skprice])

            #
            # if C.iloc[-1] > trmac.iloc[-1]:
            #     print('下多单信号区间。。。')
            #     if bkvol < bklimit:  # and hlmax < daymean
            #         print('持仓小于持仓限额，下多单。。。')
            #         bkprice = quote.last_price + 1
            #
            #         BK(api, symbol=SYMBOL, order_price=bkprice, volume=order_volume)
            #
            #         spprice = max(H.iloc[-2], bkprice + cover_gap+2)
            #         SP(api, symbol=SYMBOL, order_price=spprice, volume=order_volume, today=True)
            #         mylog.info(['bk', SYMBOL, bkprice, order_volume, cover_gap])
            #         mylog.info(['sp', SYMBOL, spprice, order_volume, spprice - bkprice])
            #         bkflag = False
            #
            #     else:
            #         print('持仓限额:', bklimit, '多单持仓:', bkvol, '持仓超过持仓限额，不再下单。。。')
            #
            # if C.iloc[-1] < trmac.iloc[-1]:
            #     print('做空区间。。。。')
            #     if skvol < sklimit:  # and hlmax < daymean:
            #         print('持仓小于持仓限额， 下空单。。。')
            #         skprice = quote.last_price - 1
            #         SK(api, symbol=SYMBOL, order_price=quote.last_price - 1, volume=order_volume)
            #
            #         bpprice = min(L.iloc[-2], skprice - cover_gap-2)
            #         BP(api, symbol=SYMBOL, order_price=bpprice, volume=order_volume, today=True)
            #         mylog.info(['sk', SYMBOL, skprice, order_volume, cover_gap])
            #         mylog.info(['bp', SYMBOL, bpprice, order_volume, skprice - bpprice])
            #     else:
            #         print('持仓限额：', sklimit, '持仓：', skvol, '持仓超过持仓限额， 不再下单。。。。')

        if api.is_changing(quote):
            if gap > 0:
                if quote.lowest < quote.pre_close:
                    gapcover = True
            else:
                if quote.highest > quote.pre_close:
                    gapcover = True

        if api.is_changing(position):
            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today

        hr=time.localtime().tm_hour
        mi=time.localtime().tm_min
        if ((time.localtime().tm_hour > 15 and time.localtime().tm_min > 15) or (time.localtime().tm_hour > 23 and time.localtime().tm_min > 0)) and savebars:
            klines1.to_csv(SYMBOL + '.csv')
            savebars = False
            api.close()
            mylog.info('no trading time, quit.')
            sys.exit(0)


def runstrategy():

    from tqsdk import TqApi, TqKq

    product = 'OI'
    interval = 60*60*24
    bklimit = 15
    sklimit = 15
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_zcn,Qiai1301")
    # symbol = api.query_cont_quotes(product_id=product)[0]
    symbol = '<EMAIL>'
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)




if __name__ == "__main__":
   runstrategy()