import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMA_102 import trmastrategy
from time import sleep
import time

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    symbol = 'DCE.pp2109'
    symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 40
    sklimit = 40
    single_volume = 1

    config = {
        symbol: 'DCE.pp2105',
        interval: 15,
        bklimit: 2660000,
        sklimit: 2660000,
        single_volume: 100
    }

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_thj,Qiai1301")

    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)
