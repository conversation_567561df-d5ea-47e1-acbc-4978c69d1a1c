"""
测试修正后的回测逻辑
验证"不盈利不平仓"策略的正确实现
"""

import pandas as pd
import numpy as np
from loguru import logger
from llt_multi_contract_analyzer import ContractAnalyzer, ContractConfig, LLTIndicator

def create_test_data_with_trend():
    """创建有明确趋势的测试数据"""
    np.random.seed(42)
    n_points = 200
    
    # 创建先下跌后上涨的价格数据
    base_price = 100
    
    # 前半段下跌趋势
    downtrend = np.linspace(0, -10, n_points//2)
    # 后半段上涨趋势  
    uptrend = np.linspace(-10, 20, n_points//2)
    
    trend = np.concatenate([downtrend, uptrend])
    noise = np.random.randn(n_points) * 0.5  # 减少噪声
    prices = base_price + trend + noise
    
    # 确保价格为正
    prices = np.maximum(prices, 50)
    
    dates = pd.date_range('2024-01-01', periods=n_points, freq='5min')
    
    klines_data = pd.DataFrame({
        'datetime': dates,
        'open': prices + np.random.randn(n_points) * 0.1,
        'high': prices + np.abs(np.random.randn(n_points)) * 0.3,
        'low': prices - np.abs(np.random.randn(n_points)) * 0.3,
        'close': prices,
        'volume': np.random.randint(100, 1000, n_points)
    })
    
    return klines_data


def test_corrected_backtest():
    """测试修正后的回测逻辑"""
    print("=" * 60)
    print("测试修正后的回测逻辑")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_test_data_with_trend()
        print(f"测试数据: {len(klines_data)}条K线")
        print(f"价格范围: {klines_data.close.min():.2f} - {klines_data.close.max():.2f}")
        
        # 创建合约配置
        contract = ContractConfig("TEST.symbol", "测试合约", "TEST")
        
        # 测试D_VALUE=14（对应llt_strategy_3.py的最优参数）
        d_value = 14
        alpha = 2 / (d_value + 1)
        
        print(f"\n🔍 测试参数: D_VALUE={d_value}, ALPHA={alpha:.6f}")
        
        # 使用修正后的回测逻辑
        analyzer = ContractAnalyzer(contract, klines_data, "profit_only")
        result = analyzer._run_backtest(alpha)
        
        print(f"\n📊 回测结果:")
        print(f"收益率: {result['return_rate']:.2f}%")
        print(f"累计盈亏: {result['total_pnl']:.2f}点")
        print(f"交易次数: {result['total_trades']}")
        print(f"胜率: {result['win_rate']:.1f}%")
        print(f"盈利交易: {result['winning_trades']}")
        print(f"亏损交易: {result['losing_trades']}")
        print(f"平均盈利: {result['avg_win']:.2f}")
        print(f"平均亏损: {result['avg_loss']:.2f}")
        print(f"盈亏比: {result['profit_factor']:.2f}")
        print(f"最大回撤: {result['max_drawdown']:.2f}")
        
        # 验证关键特征
        print(f"\n✅ 验证结果:")
        if result['losing_trades'] == 0:
            print("✅ 胜率100% - 符合'不盈利不平仓'策略")
        else:
            print(f"⚠️  存在亏损交易: {result['losing_trades']}笔")
        
        if result['total_trades'] > 0:
            print(f"✅ 产生了 {result['total_trades']} 笔交易")
        else:
            print("⚠️  没有产生交易")
        
        if result['return_rate'] > 0:
            print(f"✅ 正收益: {result['return_rate']:.2f}%")
        else:
            print(f"⚠️  负收益: {result['return_rate']:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_parameter_optimization():
    """测试参数优化"""
    print("=" * 60)
    print("测试参数优化")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_test_data_with_trend()
        contract = ContractConfig("TEST.symbol", "测试合约", "TEST")
        
        # 创建分析器
        analyzer = ContractAnalyzer(contract, klines_data, "profit_only")
        
        print(f"\n🔄 开始参数优化...")
        
        # 测试小范围参数
        optimization_results = analyzer.optimize_parameters(range(10, 21))
        
        if optimization_results:
            print(f"\n📊 优化结果 (前5名):")
            print("-" * 60)
            print(f"{'排名':<4} {'D_VALUE':<8} {'收益率':<8} {'胜率':<8} {'交易次数':<8}")
            print("-" * 60)
            
            for i, result in enumerate(optimization_results[:5], 1):
                print(f"{i:<4} {result['D_VALUE']:<8} {result['return_rate']:>6.2f}% "
                     f"{result['win_rate']:>6.1f}% {result['total_trades']:>8d}")
            
            # 检查最优结果
            best = optimization_results[0]
            print(f"\n🏆 最优参数:")
            print(f"D_VALUE: {best['D_VALUE']}")
            print(f"ALPHA: {best['ALPHA']:.6f}")
            print(f"收益率: {best['return_rate']:.2f}%")
            print(f"胜率: {best['win_rate']:.1f}%")
            print(f"交易次数: {best['total_trades']}")
            
            # 验证胜率
            if best['win_rate'] == 100.0:
                print("✅ 最优结果胜率100% - 策略逻辑正确")
            else:
                print(f"⚠️  最优结果胜率不是100%: {best['win_rate']:.1f}%")
        else:
            print("❌ 没有获得优化结果")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_signal_analysis():
    """测试信号分析"""
    print("=" * 60)
    print("测试信号分析")
    print("=" * 60)
    
    try:
        # 创建简单测试数据
        prices = pd.Series([100, 99, 98, 99, 101, 103, 102, 101, 100, 102, 104, 105])
        d_value = 14
        alpha = 2 / (d_value + 1)
        
        print(f"测试价格: {list(prices)}")
        print(f"D_VALUE: {d_value}, ALPHA: {alpha:.6f}")
        
        # 计算LLT和信号
        llt_values = LLTIndicator.calculate(prices, alpha)
        signals = LLTIndicator.generate_signals(llt_values)
        
        print(f"\nLLT值: {[f'{v:.3f}' for v in llt_values]}")
        print(f"信号: {signals}")
        
        # 分析信号
        buy_signals = [(i, prices[i]) for i, s in enumerate(signals) if s == 1]
        sell_signals = [(i, prices[i]) for i, s in enumerate(signals) if s == -1]
        
        print(f"\n买入信号: {buy_signals}")
        print(f"卖出信号: {sell_signals}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("修正后回测逻辑测试")
    print("=" * 80)
    
    tests = [
        ("信号分析", test_signal_analysis),
        ("修正后回测逻辑", test_corrected_backtest),
        ("参数优化", test_parameter_optimization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 修正后回测逻辑测试成功！")
        print("\n📋 现在可以使用正确的交易逻辑:")
        print("# 快速分析（使用正确的交易逻辑）")
        print("python llt_multi_contract_analyzer.py --mode quick")
        print("")
        print("# 完整分析（使用正确的交易逻辑）")
        print("python llt_multi_contract_analyzer.py --mode full")
        print("")
        print("# 所有主力合约分析")
        print("python llt_multi_contract_analyzer.py --mode all-main")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
