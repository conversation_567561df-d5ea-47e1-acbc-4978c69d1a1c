"""
快速回测演示 - 使用真实TqSDK数据
展示多时间周期策略的基本效果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

try:
    from tqsdk import TqApi, TqKq
    TQSDK_AVAILABLE = True
except ImportError:
    print("警告: TqSDK未安装，将使用模拟数据")
    TQSDK_AVAILABLE = False

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def fetch_real_data(symbol: str = "<EMAIL>", timeframe: int = 900,
                   length: int = 1000, auth: str = "smartmanp,ftp123") -> pd.DataFrame:
    """获取真实的TqSDK数据"""
    if not TQSDK_AVAILABLE:
        print("TqSDK不可用，使用模拟数据")
        return generate_sample_data()

    try:
        print(f"正在获取 {symbol} 的真实数据...")
        print(f"时间周期: {timeframe}秒 ({timeframe//60}分钟)")
        print(f"数据量: {length} 条")

        # 初始化API
        api = TqApi(TqKq(), auth=auth, disable_print=True)

        # 获取合约信息
        quote = api.get_quote(symbol)
        print(f"合约名称: {quote.instrument_name}")
        print(f"当前价格: {quote.last_price}")

        # 获取K线数据
        klines = api.get_kline_serial(symbol, timeframe, length)
        df = klines.copy()

        # 关闭API
        api.close()

        if len(df) > 0:
            start_time = pd.to_datetime(df.datetime.iloc[0])
            end_time = pd.to_datetime(df.datetime.iloc[-1])
            price_range = f"{df.close.min():.2f} - {df.close.max():.2f}"

            print(f"✓ 数据获取成功: {len(df)} 条记录")
            print(f"时间范围: {start_time} 到 {end_time}")
            print(f"价格范围: {price_range}")

            return df
        else:
            print("✗ 未获取到数据，使用模拟数据")
            return generate_sample_data()

    except Exception as e:
        print(f"获取真实数据失败: {e}")
        print("使用模拟数据替代")
        return generate_sample_data()


def generate_sample_data(days: int = 7) -> pd.DataFrame:
    """生成样本数据（备用方案）"""
    np.random.seed(42)

    # 生成时间序列（7天，每15分钟一个数据点）
    start_time = datetime.now() - timedelta(days=days)
    intervals = days * 24 * 4  # 每15分钟一个点

    timestamps = [start_time + timedelta(minutes=15*i) for i in range(intervals)]

    # 生成价格数据
    base_price = 9600
    trend = np.sin(np.linspace(0, 4*np.pi, intervals)) * 100  # 趋势
    noise = np.cumsum(np.random.randn(intervals) * 3)  # 随机游走
    prices = base_price + trend + noise

    # 生成OHLC数据
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        if i == 0:
            open_price = close
        else:
            open_price = data[i-1]['close']

        volatility = np.random.uniform(1, 5)
        high = max(open_price, close) + np.random.uniform(0, volatility)
        low = min(open_price, close) - np.random.uniform(0, volatility)
        volume = np.random.randint(100, 1000)

        data.append({
            'datetime': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })

    return pd.DataFrame(data)


def calculate_ma_signals(df: pd.DataFrame, short_period: int = 5, long_period: int = 20) -> pd.DataFrame:
    """计算均线信号"""
    df = df.copy()
    df['ma_short'] = df['close'].rolling(short_period).mean()
    df['ma_long'] = df['close'].rolling(long_period).mean()
    df['ma_diff'] = df['ma_short'] - df['ma_long']
    
    # 金叉死叉信号
    df['golden_cross'] = (df['ma_short'] > df['ma_long']) & (df['ma_short'].shift(1) <= df['ma_long'].shift(1))
    df['death_cross'] = (df['ma_short'] < df['ma_long']) & (df['ma_short'].shift(1) >= df['ma_long'].shift(1))
    
    # 回归信号
    df['ma_diff_change'] = df['ma_diff'].diff()
    df['upward_regression'] = (df['ma_diff'] < 0) & (df['ma_diff_change'] > 0) & (df['ma_diff_change'].shift(1) > 0)
    df['downward_regression'] = (df['ma_diff'] > 0) & (df['ma_diff_change'] < 0) & (df['ma_diff_change'].shift(1) < 0)
    
    return df


def simple_backtest(df: pd.DataFrame, initial_capital: float = 100000) -> dict:
    """简单回测"""
    capital = initial_capital
    position = 0  # 0: 无持仓, 1: 多仓, -1: 空仓
    entry_price = 0
    trades = []
    equity_curve = [capital]
    
    commission_rate = 0.0003
    position_size_ratio = 0.2  # 每次使用20%资金
    
    for i in range(len(df)):
        current = df.iloc[i]
        current_price = current['close']
        
        # 检查开仓信号
        if position == 0:  # 无持仓
            # 多仓条件：金叉 + 阳线
            if (current['golden_cross'] and current['close'] > current['open']):
                position = 1
                entry_price = current_price
                quantity = int(capital * position_size_ratio / current_price)
                commission = current_price * quantity * commission_rate
                capital -= commission
                
                trades.append({
                    'timestamp': current['datetime'],
                    'action': 'BUY',
                    'price': current_price,
                    'quantity': quantity,
                    'commission': commission
                })
            
            # 空仓条件：死叉 + 阴线
            elif (current['death_cross'] and current['close'] < current['open']):
                position = -1
                entry_price = current_price
                quantity = int(capital * position_size_ratio / current_price)
                commission = current_price * quantity * commission_rate
                capital -= commission
                
                trades.append({
                    'timestamp': current['datetime'],
                    'action': 'SELL',
                    'price': current_price,
                    'quantity': quantity,
                    'commission': commission
                })
        
        # 检查平仓信号
        elif position == 1:  # 多仓
            # 平多条件：死叉或止损
            if current['death_cross'] or current_price < entry_price * 0.98:  # 2%止损
                pnl = (current_price - entry_price) * quantity
                commission = current_price * quantity * commission_rate
                capital += pnl - commission
                
                trades.append({
                    'timestamp': current['datetime'],
                    'action': 'SELL_CLOSE',
                    'price': current_price,
                    'quantity': quantity,
                    'pnl': pnl - commission,
                    'commission': commission
                })
                
                position = 0
                entry_price = 0
        
        elif position == -1:  # 空仓
            # 平空条件：金叉或止损
            if current['golden_cross'] or current_price > entry_price * 1.02:  # 2%止损
                pnl = (entry_price - current_price) * quantity
                commission = current_price * quantity * commission_rate
                capital += pnl - commission
                
                trades.append({
                    'timestamp': current['datetime'],
                    'action': 'BUY_CLOSE',
                    'price': current_price,
                    'quantity': quantity,
                    'pnl': pnl - commission,
                    'commission': commission
                })
                
                position = 0
                entry_price = 0
        
        # 更新权益曲线
        if position != 0:
            if position == 1:
                unrealized_pnl = (current_price - entry_price) * quantity
            else:
                unrealized_pnl = (entry_price - current_price) * quantity
            equity_curve.append(capital + unrealized_pnl)
        else:
            equity_curve.append(capital)
    
    # 计算结果
    total_return = capital - initial_capital
    return_rate = total_return / initial_capital * 100
    
    # 交易统计
    closed_trades = [t for t in trades if 'pnl' in t]
    total_trades = len(closed_trades)
    
    if total_trades > 0:
        winning_trades = len([t for t in closed_trades if t['pnl'] > 0])
        win_rate = winning_trades / total_trades * 100
        avg_pnl = np.mean([t['pnl'] for t in closed_trades])
        total_pnl = sum([t['pnl'] for t in closed_trades])
    else:
        winning_trades = 0
        win_rate = 0
        avg_pnl = 0
        total_pnl = 0
    
    # 最大回撤
    equity_series = pd.Series(equity_curve)
    rolling_max = equity_series.expanding().max()
    drawdown = (equity_series - rolling_max) / rolling_max * 100
    max_drawdown = drawdown.min()
    
    return {
        'final_capital': capital,
        'total_return': total_return,
        'return_rate': return_rate,
        'max_drawdown': max_drawdown,
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'win_rate': win_rate,
        'avg_pnl': avg_pnl,
        'total_pnl': total_pnl,
        'equity_curve': equity_curve,
        'trades': trades
    }


def plot_backtest_results(df: pd.DataFrame, results: dict):
    """绘制回测结果"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 价格和均线
    ax1.plot(df.index, df['close'], label='价格', linewidth=1)
    ax1.plot(df.index, df['ma_short'], label='短均线(5)', alpha=0.7)
    ax1.plot(df.index, df['ma_long'], label='长均线(20)', alpha=0.7)
    
    # 标记交易点
    for trade in results['trades']:
        trade_idx = df[df['datetime'] <= trade['timestamp']].index[-1]
        if trade['action'] in ['BUY', 'SELL']:
            color = 'green' if trade['action'] == 'BUY' else 'red'
            marker = '^' if trade['action'] == 'BUY' else 'v'
            ax1.scatter(trade_idx, trade['price'], color=color, marker=marker, s=100, alpha=0.8)
    
    ax1.set_title('价格走势与交易信号')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 权益曲线
    ax2.plot(results['equity_curve'])
    ax2.set_title('权益曲线')
    ax2.set_ylabel('资金')
    ax2.grid(True, alpha=0.3)
    
    # 回撤曲线
    equity_series = pd.Series(results['equity_curve'])
    rolling_max = equity_series.expanding().max()
    drawdown = (equity_series - rolling_max) / rolling_max * 100
    
    ax3.fill_between(range(len(drawdown)), drawdown, 0, alpha=0.3, color='red')
    ax3.set_title('回撤曲线')
    ax3.set_ylabel('回撤 (%)')
    ax3.grid(True, alpha=0.3)
    
    # 盈亏分布
    if results['trades']:
        closed_trades = [t for t in results['trades'] if 'pnl' in t]
        if closed_trades:
            pnls = [t['pnl'] for t in closed_trades]
            ax4.hist(pnls, bins=10, alpha=0.7, color='blue', edgecolor='black')
            ax4.axvline(x=0, color='red', linestyle='--', alpha=0.8)
            ax4.set_title('盈亏分布')
            ax4.set_xlabel('盈亏')
            ax4.set_ylabel('频次')
            ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('quick_backtest_results.png', dpi=300, bbox_inches='tight')
    plt.show()


def main(use_real_data: bool = True, auth: str = "smartmanp,ftp123"):
    """主函数"""
    print("=" * 60)
    print("多时间周期策略快速回测演示 - 使用真实数据")
    print("=" * 60)

    # 获取数据
    if use_real_data:
        print("获取真实TqSDK数据...")
        df = fetch_real_data(
            symbol="<EMAIL>",
            timeframe=900,  # 15分钟
            length=10000,    # 1000条数据
            auth=auth
        )
    else:
        print("生成模拟数据...")
        df = generate_sample_data(days=7)

    print(f"数据准备完成: {len(df)} 条15分钟数据")

    # 计算技术指标
    print("计算技术指标...")
    df = calculate_ma_signals(df)

    # 运行回测
    print("运行回测...")
    results = simple_backtest(df)

    # 显示结果
    print("\n" + "=" * 60)
    print("回测结果")
    print("=" * 60)
    print(f"初始资金: 100,000.00")
    print(f"最终资金: {results['final_capital']:,.2f}")
    print(f"总收益: {results['total_return']:,.2f}")
    print(f"收益率: {results['return_rate']:.2f}%")
    print(f"最大回撤: {results['max_drawdown']:.2f}%")
    print(f"总交易次数: {results['total_trades']}")
    print(f"盈利交易: {results['winning_trades']}")
    print(f"胜率: {results['win_rate']:.2f}%")
    print(f"平均盈亏: {results['avg_pnl']:.2f}")
    print(f"总盈亏: {results['total_pnl']:.2f}")

    # 绘制图表
    print("\n生成图表...")
    plot_backtest_results(df, results)
    print("图表已保存为 quick_backtest_results.png")

    # 保存交易记录
    if results['trades']:
        trade_df = pd.DataFrame(results['trades'])
        trade_df.to_csv('real_data_trade_log.csv', index=False, encoding='utf-8')
        print("交易记录已保存为 real_data_trade_log.csv")

    # 显示部分交易记录
    if results['trades']:
        print(f"\n前5笔交易记录:")
        for i, trade in enumerate(results['trades'][:5]):
            if isinstance(trade['timestamp'], str):
                timestamp_str = trade['timestamp']
            else:
                timestamp_str = trade['timestamp'].strftime('%m-%d %H:%M')

            print(f"  {i+1}. {timestamp_str} "
                  f"{trade['action']} {trade['price']:.2f}")
            if 'pnl' in trade:
                print(f"     盈亏: {trade['pnl']:.2f}")

    # 显示数据统计
    print(f"\n数据统计:")
    print(f"数据时间范围: {pd.to_datetime(df.iloc[0]['datetime'])} 到 {pd.to_datetime(df.iloc[-1]['datetime'])}")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    print(f"平均成交量: {df['volume'].mean():.0f}")

    return results


if __name__ == "__main__":
    import sys

    # 默认使用真实数据
    use_real_data = True
    auth = "smartmanp,ftp123"

    # 命令行参数处理
    if len(sys.argv) > 1:
        if sys.argv[1] == "mock":
            use_real_data = False
            print("使用模拟数据模式")
        elif sys.argv[1] == "real":
            use_real_data = True
            auth = sys.argv[2] if len(sys.argv) > 2 else "smartmanp,ftp123"
            print(f"使用真实数据模式，认证: {auth}")
        else:
            print("用法:")
            print("  python quick_backtest_demo.py real [auth]  # 使用真实数据")
            print("  python quick_backtest_demo.py mock        # 使用模拟数据")
            sys.exit(1)

    try:
        main(use_real_data=use_real_data, auth=auth)
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        print("尝试使用模拟数据...")
        main(use_real_data=False)
