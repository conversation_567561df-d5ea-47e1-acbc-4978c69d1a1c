"""
测试广州期货交易所（GFEX）品种的交易时间处理
"""

import sys
from datetime import datetime
from unittest.mock import patch

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_gfex_contracts():
    """测试广期所合约的交易时间配置"""
    print("=" * 60)
    print("测试广州期货交易所（GFEX）品种")
    print("=" * 60)
    
    try:
        from utils.utils import get_contract_trading_times, extract_contract_code
        
        # 广期所品种
        gfex_contracts = [
            ('GFEX.si2512', '工业硅'),
            ('GFEX.lc2512', '碳酸锂'),
            # 测试不同格式
            ('gfex.SI2512', '工业硅（大写）'),
            ('GFEX.LC2512', '碳酸锂（大写）'),
        ]
        
        print("广期所品种交易时间配置:")
        print(f"{'合约代码':<15} {'品种':<12} {'提取代码':<8} {'交易时间段'}")
        print("-" * 80)
        
        for symbol, name in gfex_contracts:
            extracted_code = extract_contract_code(symbol)
            trading_times = get_contract_trading_times(symbol)
            periods_str = ', '.join([f"{p[0]}-{p[1]}" for p in trading_times])
            print(f"{symbol:<15} {name:<12} {extracted_code:<8} {periods_str}")
        
        # 验证交易时间是否正确
        expected_periods = [
            ['09:00:00', '11:30:00'],
            ['13:30:00', '15:00:00'],
            ['21:00:00', '23:00:00']
        ]
        
        si_periods = get_contract_trading_times('GFEX.si2512')
        lc_periods = get_contract_trading_times('GFEX.lc2512')
        
        if si_periods == expected_periods and lc_periods == expected_periods:
            print("\n✓ 广期所品种交易时间配置正确")
            return True
        else:
            print(f"\n✗ 广期所品种交易时间配置错误")
            print(f"工业硅实际: {si_periods}")
            print(f"碳酸锂实际: {lc_periods}")
            print(f"预期: {expected_periods}")
            return False
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gfex_trading_time_judgment():
    """测试广期所品种的交易时间判断"""
    print("=" * 60)
    print("测试广期所品种交易时间判断")
    print("=" * 60)
    
    try:
        from utils.utils import is_contract_trading_time
        
        # 测试不同时间点
        test_cases = [
            # 时间, 合约, 预期结果, 说明
            (10, 0, 'GFEX.si2512', True, '工业硅日盘上午'),
            (14, 0, 'GFEX.si2512', True, '工业硅日盘下午'),
            (22, 0, 'GFEX.si2512', True, '工业硅夜盘'),
            (16, 0, 'GFEX.si2512', False, '工业硅非交易时间'),
            (0, 30, 'GFEX.si2512', False, '工业硅夜盘结束后'),
            
            (10, 0, 'GFEX.lc2512', True, '碳酸锂日盘上午'),
            (14, 0, 'GFEX.lc2512', True, '碳酸锂日盘下午'),
            (22, 0, 'GFEX.lc2512', True, '碳酸锂夜盘'),
            (16, 0, 'GFEX.lc2512', False, '碳酸锂非交易时间'),
            (0, 30, 'GFEX.lc2512', False, '碳酸锂夜盘结束后'),
        ]
        
        print(f"{'时间':<8} {'合约':<15} {'预期':<6} {'实际':<6} {'结果':<6} {'说明'}")
        print("-" * 70)
        
        all_passed = True
        
        for hour, minute, symbol, expected, description in test_cases:
            # 模拟指定时间
            test_datetime = datetime.now().replace(hour=hour, minute=minute, second=0)
            
            with patch('utils.utils.datetime') as mock_datetime:
                mock_datetime.now.return_value = test_datetime
                mock_datetime.strptime = datetime.strptime
                
                actual = is_contract_trading_time(symbol)
                
                time_str = f"{hour:02d}:{minute:02d}"
                expected_str = "是" if expected else "否"
                actual_str = "是" if actual else "否"
                result_str = "✓" if actual == expected else "✗"
                
                print(f"{time_str:<8} {symbol:<15} {expected_str:<6} {actual_str:<6} {result_str:<6} {description}")
                
                if actual != expected:
                    all_passed = False
        
        if all_passed:
            print("\n✓ 广期所品种交易时间判断测试通过")
            return True
        else:
            print("\n✗ 广期所品种交易时间判断测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_all_exchanges():
    """测试所有交易所的品种"""
    print("=" * 60)
    print("测试所有交易所品种覆盖")
    print("=" * 60)
    
    try:
        from utils.utils import get_contract_trading_times
        
        # 各交易所代表性品种
        all_exchanges = [
            # 上海期货交易所（SHFE）
            ('SHFE.ag2512', '白银', 'SHFE'),
            ('SHFE.cu2512', '沪铜', 'SHFE'),
            ('SHFE.rb2512', '螺纹钢', 'SHFE'),
            ('SHFE.ru2512', '橡胶', 'SHFE'),
            
            # 大连商品交易所（DCE）
            ('DCE.i2512', '铁矿石', 'DCE'),
            ('DCE.y2512', '豆油', 'DCE'),
            ('DCE.m2512', '豆粕', 'DCE'),
            ('DCE.l2512', '塑料', 'DCE'),
            
            # 郑州商品交易所（CZCE）
            ('CZCE.CF512', '棉花', 'CZCE'),
            ('CZCE.SR512', '白糖', 'CZCE'),
            ('CZCE.OI512', '菜籽油', 'CZCE'),
            ('CZCE.TA512', 'PTA', 'CZCE'),
            
            # 中国金融期货交易所（CFFEX）
            ('CFFEX.IF2512', '沪深300', 'CFFEX'),
            ('CFFEX.T2512', '10年期国债', 'CFFEX'),
            
            # 上海国际能源交易中心（INE）
            ('INE.sc2512', '原油', 'INE'),
            
            # 广州期货交易所（GFEX）
            ('GFEX.si2512', '工业硅', 'GFEX'),
            ('GFEX.lc2512', '碳酸锂', 'GFEX'),
        ]
        
        print(f"{'交易所':<8} {'合约代码':<15} {'品种':<10} {'交易时间段'}")
        print("-" * 80)
        
        exchange_counts = {}
        
        for symbol, name, exchange in all_exchanges:
            trading_times = get_contract_trading_times(symbol)
            periods_str = ', '.join([f"{p[0]}-{p[1]}" for p in trading_times])
            print(f"{exchange:<8} {symbol:<15} {name:<10} {periods_str}")
            
            # 统计各交易所品种数量
            exchange_counts[exchange] = exchange_counts.get(exchange, 0) + 1
        
        print(f"\n交易所覆盖统计:")
        for exchange, count in exchange_counts.items():
            print(f"  {exchange}: {count}个品种")
        
        # 验证广期所品种是否正确处理
        gfex_si = get_contract_trading_times('GFEX.si2512')
        gfex_lc = get_contract_trading_times('GFEX.lc2512')
        
        expected_gfex = [
            ['09:00:00', '11:30:00'],
            ['13:30:00', '15:00:00'],
            ['21:00:00', '23:00:00']
        ]
        
        if gfex_si == expected_gfex and gfex_lc == expected_gfex:
            print(f"\n✓ 所有交易所品种处理正确，包括广期所")
            return True
        else:
            print(f"\n✗ 广期所品种处理有误")
            return False
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_gfex_info():
    """显示广期所信息"""
    print("=" * 60)
    print("广州期货交易所（GFEX）信息")
    print("=" * 60)
    
    gfex_info = {
        "成立时间": "2021年4月19日",
        "主要品种": [
            "工业硅（si）- 2021年12月22日上市",
            "碳酸锂（lc）- 2022年7月22日上市"
        ],
        "交易时间": {
            "日盘": "09:00-11:30, 13:30-15:00",
            "夜盘": "21:00-23:00"
        },
        "特点": [
            "中国第五家期货交易所",
            "服务粤港澳大湾区建设",
            "专注服务绿色发展",
            "新能源产业链品种"
        ]
    }
    
    print(f"成立时间: {gfex_info['成立时间']}")
    print(f"\n主要品种:")
    for variety in gfex_info['主要品种']:
        print(f"  • {variety}")
    
    print(f"\n交易时间:")
    print(f"  日盘: {gfex_info['交易时间']['日盘']}")
    print(f"  夜盘: {gfex_info['交易时间']['夜盘']}")
    
    print(f"\n特点:")
    for feature in gfex_info['特点']:
        print(f"  • {feature}")


def main():
    """主测试函数"""
    print("广州期货交易所（GFEX）品种处理测试")
    print("=" * 80)
    
    # 显示广期所信息
    show_gfex_info()
    
    tests = [
        ("广期所合约配置", test_gfex_contracts),
        ("广期所交易时间判断", test_gfex_trading_time_judgment),
        ("所有交易所覆盖", test_all_exchanges),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 广州期货交易所品种处理成功！")
        print("\n现在支持的交易所:")
        print("1. ✓ 上海期货交易所（SHFE）")
        print("2. ✓ 大连商品交易所（DCE）")
        print("3. ✓ 郑州商品交易所（CZCE）")
        print("4. ✓ 中国金融期货交易所（CFFEX）")
        print("5. ✓ 上海国际能源交易中心（INE）")
        print("6. ✓ 广州期货交易所（GFEX）")
        print("\n广期所品种:")
        print("• 工业硅（si）- 日盘+夜盘（21:00-23:00）")
        print("• 碳酸锂（lc）- 日盘+夜盘（21:00-23:00）")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
