import pandas as pd
import numpy as np
import mplfinance as mpf

# 保留之前的 preprocess_data, flexible_HHV, flexible_LLV, 和 wh8_indicator 函数不变
def preprocess_data(df):
    df['datetime'] = pd.to_datetime(df['datetime'].astype(float), unit='ns')
    df.set_index('datetime', inplace=True)
    for col in ['open', 'high', 'low', 'close', 'volume']:
        df[col] = pd.to_numeric(df[col])
    return df


def flexible_HHV(X, N):
    result = np.full_like(X, np.nan, dtype=float)
    for i in range(len(X)):
        if np.isscalar(N):
            n = int(N)
        else:
            n = int(N[i]) if not np.isnan(N[i]) else 0

        if n == 0:
            result[i] = np.nanmax(X[:i + 1])
        elif np.isnan(n):
            result[i] = np.nan
        else:
            result[i] = np.nanmax(X[max(0, i - n + 1):i + 1])
    return result


def flexible_LLV(X, N):
    result = np.full_like(X, np.nan, dtype=float)
    for i in range(len(X)):
        if np.isscalar(N):
            n = int(N)
        else:
            n = int(N[i]) if not np.isnan(N[i]) else 0

        if n == 0:
            result[i] = np.nanmin(X[:i + 1])
        elif np.isnan(n):
            result[i] = np.nan
        else:
            result[i] = np.nanmin(X[max(0, i - n + 1):i + 1])
    return result


def wh8_indicator(df):
    HIGH = df['high'].values
    LOW = df['low'].values
    CLOSE = df['close'].values

    def REF(arr, n):
        return np.roll(arr, n)

    def BARSLAST(condition):
        return np.maximum.accumulate(np.where(condition, 0, np.arange(len(condition))))

    def VALUEWHEN(condition, value):
        result = np.full_like(value, np.nan)
        mask = condition.astype(bool)
        result[mask] = value[mask]
        return result

    def CROSS(a, b):
        return (a > b) & (REF(a, 1) <= REF(b, 1))

    X_34 = np.where(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
    X_35 = np.where(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)
    X_36 = flexible_HHV(X_34, 3)
    X_37 = flexible_LLV(X_35, 3)
    X_38 = BARSLAST(X_37 < REF(X_37, 1))
    X_39 = BARSLAST(X_36 > REF(X_36, 1))
    X_40 = np.where(flexible_HHV(X_34, X_39 + 1) == X_34, 1, 0)
    X_41 = np.where(flexible_LLV(X_35, X_38 + 1) == X_35, 1, 0)
    X_42 = BARSLAST(X_40)
    X_43 = REF(flexible_LLV(X_35, 3), X_42)
    X_44 = BARSLAST(X_41)
    X_45 = REF(flexible_HHV(X_34, 3), X_44)
    X_46 = VALUEWHEN(X_45 > 0, X_45)
    X_47 = VALUEWHEN(X_43 > 0, X_43)
    X_48 = np.where(CLOSE > X_46, -1, np.where(CLOSE < X_47, 1, 0))
    X_49 = VALUEWHEN(X_48 != 0, X_48)
    X_50 = BARSLAST(CROSS(0, X_49))
    X_51 = BARSLAST(CROSS(X_49, 0))
    X_52 = np.where(X_49 == 1, np.where(flexible_LLV(X_46, X_51 + 1) == X_46, X_46, flexible_LLV(X_46, X_51 + 1)), X_46)
    X_53 = np.where(X_49 == -1, np.where(flexible_HHV(X_47, X_50 + 1) == X_47, X_47, flexible_HHV(X_47, X_50 + 1)), X_47)
    X_54 = np.where(CLOSE > X_52, -1, np.where(CLOSE < X_53, 1, 0))
    X_55 = VALUEWHEN(X_54 != 0, X_54)
    X_56 = BARSLAST(CROSS(0, X_54))
    X_57 = BARSLAST(CROSS(X_54, 0))
    X_58 = np.where(X_55 == 1,
                    np.where(flexible_LLV(X_52, X_57 + 1) == X_52, X_52, flexible_LLV(X_52, X_57 + 1)),
                    np.where(flexible_HHV(X_53, X_56 + 1) == X_53, X_53, flexible_HHV(X_53, X_56 + 1)))

    long_stop_loss = np.where(X_55 < 0, X_58, np.nan)
    short_stop_loss = np.where(X_55 > 0, X_58, np.nan)
    G = X_58
    W1 = X_55 < 0
    W2 = X_55 > 0

    return pd.DataFrame({
        'long_stop_loss': long_stop_loss,
        'short_stop_loss': short_stop_loss,
        'G': G,
        'W1': W1,
        'W2': W2
    }, index=df.index)

#
# def plot_wh8_indicator(df, indicator_df, last_n_candles=100):
#     # 只选择最后 n 根 K 线
#     df_plot = df.tail(last_n_candles).copy()
#     indicator_df_plot = indicator_df.tail(last_n_candles)
#
#     df_plot['long_stop_loss'] = indicator_df_plot['long_stop_loss']
#     df_plot['short_stop_loss'] = indicator_df_plot['short_stop_loss']
#
#     apds = [
#         mpf.make_addplot(df_plot['long_stop_loss'], color='red', width=1.5),
#         mpf.make_addplot(df_plot['short_stop_loss'], color='green', width=1.5)
#     ]
#
#     mpf.plot(df_plot, type='candle', style='charles',
#              title=f'WH8 Technical Indicator - Last {last_n_candles} Candles\n{df_plot.index[0]} to {df_plot.index[-1]}',
#              ylabel='Price',
#              volume=True,
#              addplot=apds,
#              figsize=(16, 10))  # 增加图表大小以显示更多细节
#
# # 主程序
# if __name__ == "__main__":
#     # 读取数据
#     df = pd.read_csv('900.csv')
#
#     # 预处理数据
#     df = preprocess_data(df)
#
#     # 计算WH8指标
#     indicator_df = wh8_indicator(df)
#
#     # 绘制图表，只显示最后100根K线
#     plot_wh8_indicator(df, indicator_df, last_n_candles=100)


def plot_wh8_indicator(df, indicator_df, last_n_candles=100):
    # 只选择最后 n 根 K 线
    df_plot = df.tail(last_n_candles).copy()
    indicator_df_plot = indicator_df.tail(last_n_candles)

    df_plot['long_stop_loss'] = indicator_df_plot['long_stop_loss']
    df_plot['short_stop_loss'] = indicator_df_plot['short_stop_loss']

    # 检查数据是否有效
    if df_plot.empty or df_plot.isnull().all().any():
        print("错误：数据为空或包含全为 NaN 的列。无法绘制图表。")
        return

    # 移除全为 NaN 的列
    df_plot = df_plot.dropna(axis=1, how='all')

    # 检查是否还有足够的数据来绘图
    if len(df_plot) < 2:
        print("错误：没有足够的有效数据来绘制图表。")
        return

    # 准备附加图表数据
    apds = []
    for col in ['long_stop_loss', 'short_stop_loss']:
        if not df_plot[col].isnull().all():
            apds.append(mpf.make_addplot(df_plot[col], color='red' if col == 'long_stop_loss' else 'green', width=1.5))

    # 使用 try-except 来捕获可能的绘图错误
    try:
        mpf.plot(df_plot, type='candle', style='charles',
                 title=f'WH8 Technical Indicator - Last {last_n_candles} Candles\n{df_plot.index[0]} to {df_plot.index[-1]}',
                 ylabel='Price',
                 volume=True,
                 addplot=apds,
                 figsize=(16, 10))
    except ValueError as e:
        print(f"绘图时发生错误：{e}")
        print("正在尝试使用 matplotlib 绘制基本图表...")

        # 使用 matplotlib 绘制基本图表
        plt.figure(figsize=(16, 10))
        plt.plot(df_plot.index, df_plot['close'], label='Close Price')
        plt.title(f'WH8 Technical Indicator - Last {last_n_candles} Candles\n{df_plot.index[0]} to {df_plot.index[-1]}')
        plt.xlabel('Date')
        plt.ylabel('Price')
        plt.legend()
        plt.show()


# 主程序
if __name__ == "__main__":
    # 读取数据
    df = pd.read_csv('900.csv')

    # 预处理数据
    df = preprocess_data(df)

    # 计算WH8指标
    indicator_df = wh8_indicator(df)

    # 绘制图表，只显示最后100根K线
    plot_wh8_indicator(df, indicator_df, last_n_candles=100)