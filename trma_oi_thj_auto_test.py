
from loguru import logger as mylog
from strategyTRMAsdk2 import trmastrategy
import time
from utils.utils import tradingTime
test = False

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    config = {
        'symbol': 'CZCE.OI301',
        'interval': 15,
        'bklimit': 150,
        'sklimit': 150,
        'single_volume': 1
    }


    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_thj,Qiai1301", disable_print=True)
    symbol = api.query_cont_quotes(product_id='OI')[0]

    while True:
        # if True:

        if tradingTime():
            try:
                print('交易时间。。。', time.asctime())
                api = TqApi(TqKq(), auth="quant_thj,Qiai1301", disable_print=True)
                trmastrategy(api, symbol=symbol, interval=config['interval'], single_volume=config['single_volume'], bklimit=config['bklimit'], sklimit=config['sklimit'])

            except:
                print('api connect failed, please check...', time.asctime())


        else:
            print('not in the trading time...', time.asctime())
            api.close()
            print(api)
            time.sleep(60)

