from datetime import datetime, timedelta
from tqsdk import TqApi
import time
from time import sleep
def parse_time(time_str):
    hours, minutes, seconds = map(int, time_str.split(':'))
    return timedelta(hours=hours, minutes=minutes, seconds=seconds)

def get_time_period(api: TqApi, symbol: str):
    # 查询合约信息
    symbol_info = api.query_symbol_info(symbol)

    # 获取交易时间
    trading_time_day = symbol_info['trading_time_day'].to_list()[0]
    trading_time_night = symbol_info['trading_time_night'].to_list()[0]

    # 合并日间和夜间交易时间
    trading_periods = trading_time_day + trading_time_night

    return trading_periods

def is_trading_time(trading_periods):

    # 获取当前时间
    now = datetime.now()
    current_time = timedelta(hours=now.hour, minutes=now.minute, seconds=now.second)

    # 检查当前时间是否在任何一个交易时间段内
    for period in trading_periods:
        start_time = parse_time(period[0])
        end_time = parse_time(period[1])

        # 处理跨日的情况
        if end_time <= start_time:
            end_time += timedelta(days=1)

        # 调整当前时间以处理跨日情况
        adjusted_current_time = current_time
        if current_time < start_time:
            adjusted_current_time += timedelta(days=1)

        if start_time <= adjusted_current_time <= end_time:
            return True

    return False


# 使用示例
if __name__ == "__main__":
    from tqapi import api
    symbol = "SHFE.ag2412"  # 以上海期货交易所的铜期货为例
    # symbol = api.query_cont_quotes(product_id='OI')[0]
    trading_periods = get_time_period(api, symbol)
    while True:
        try:
            klines = api.get_kline_serial(symbol, duration_seconds=10, data_length=10)
            while True:
                if is_trading_time(trading_periods):
                    if api:
                        api.wait_update()
                        if api.is_changing(klines.iloc[-1], "datetime"):
                            print(f"{symbol} 当前是否在交易时间: True")
                            print(klines.close.iloc[-1])
                            # sleep(60)  # 每隔一分钟检查一次
                    else:
                        from tqapi import api
                        print(f"{symbol}")
                else:
                    print(f"{symbol} 当前是否在交易时间: False ")
                    if api:
                        api.close()
                        # del api
                        print("api已关闭", time.asctime(time.localtime()))
                    else:
                        print("api未开启")
                    sleep(60)  # 每隔一分钟检查一次

        except Exception as e:
            print(e)
            del api
            from tqapi import api

        finally:
            api.close()
