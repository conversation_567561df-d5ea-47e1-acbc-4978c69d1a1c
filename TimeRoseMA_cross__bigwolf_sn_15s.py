from utils.utils import parse_time, get_time_period, is_trading_time
from strategies.TimeRoseMA_cross_speak import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    import time
    product_id = 'sn'
    interval = 15
    bklimit = 10000
    sklimit = 100000
    single_volume = 10

    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123")
    symbol = api.query_cont_quotes(product_id=product_id)[0]
    time_periods = get_time_period(api, symbol)

    try:

        symbol = api.query_cont_quotes(product_id=product_id)[0]
        print(f"交易的合约是: {symbol}")

        while True:

            if is_trading_time(time_periods):
                try:
                    api = TqApi(TqKq(), auth="bigwolf,ftp123")
                    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)
                    while True:
                        api.wait_update()
                except Exception as e:
                    print(e)
                    time.sleep(10)
            else:
                print('非交易时间:', time.asctime())
                api.close()
                time.sleep(1)
                # continue

    except KeyboardInterrupt:
        print("策略已停止")

    finally:
        api.close()

if __name__ == "__main__":
    runstrategy()
