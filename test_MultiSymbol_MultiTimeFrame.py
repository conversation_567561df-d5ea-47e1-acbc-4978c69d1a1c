"""
多合约多时间周期策略测试脚本
验证重构后的功能是否正常
"""

import sys
import time
from strategies.TimeRoseMA_cross_MultiSymbol_MultiTimeFrame import (
    SymbolTimeFrameConfig,
    SignalInfo,
    MultiSymbolMultiTimeFrameManager,
    create_default_configs,
    create_custom_configs
)


def test_symbol_timeframe_config():
    """测试合约时间周期配置"""
    print("=== 测试SymbolTimeFrameConfig ===")
    
    config = SymbolTimeFrameConfig(
        symbol='SH',
        interval=300,
        bklimit=2,
        sklimit=2,
        single_volume=1,
        period=13
    )
    
    assert config.symbol == 'SH'
    assert config.interval == 300
    assert config.timeframe_name == '5m'
    assert config.config_id == 'SH_5m'
    assert config.enabled == True
    
    print("✓ SymbolTimeFrameConfig测试通过")


def test_signal_info():
    """测试信号信息"""
    print("=== 测试SignalInfo ===")
    
    signal = SignalInfo(
        symbol='SH',
        interval=300,
        signal_type='多',
        signal_price=100.0,
        current_price=102.0,
        signal_profit=2.0,
        duration=5,
        timestamp='09:30:00'
    )
    
    signal_dict = signal.to_dict()
    expected_keys = ['合约', '周期', '时间', '当前信号', '持续周期', '信号价格', '现价', '信号盈亏']
    
    for key in expected_keys:
        assert key in signal_dict
    
    assert signal_dict['合约'] == 'SH'
    assert signal_dict['当前信号'] == '多'
    assert signal_dict['信号盈亏'] == 2.0
    
    print("✓ SignalInfo测试通过")


def test_create_default_configs():
    """测试默认配置创建"""
    print("=== 测试create_default_configs ===")
    
    configs = create_default_configs()
    
    # 应该有3个合约 × 4个时间周期 = 12个配置
    assert len(configs) == 12
    
    # 检查合约
    symbols = set(config.symbol for config in configs)
    expected_symbols = {'SH', 'OI', 'ag'}
    assert symbols == expected_symbols
    
    # 检查时间周期
    intervals = set(config.interval for config in configs)
    expected_intervals = {60, 180, 300, 900}
    assert intervals == expected_intervals
    
    # 检查每个合约都有4个时间周期
    symbol_counts = {}
    for config in configs:
        symbol_counts[config.symbol] = symbol_counts.get(config.symbol, 0) + 1
    
    for symbol, count in symbol_counts.items():
        assert count == 4, f"{symbol} 应该有4个时间周期，实际有{count}个"
    
    print("✓ create_default_configs测试通过")


def test_create_custom_configs():
    """测试自定义配置创建"""
    print("=== 测试create_custom_configs ===")
    
    symbol_timeframe_map = {
        'SH': [60, 300],
        'OI': [180, 900],
        'ag': [300]
    }
    
    default_params = {
        'bklimit': 2,
        'sklimit': 2,
        'single_volume': 1,
        'period': 20,
        'enabled': True
    }
    
    configs = create_custom_configs(symbol_timeframe_map, default_params)
    
    # 应该有2+2+1=5个配置
    assert len(configs) == 5
    
    # 检查参数是否正确应用
    for config in configs:
        assert config.bklimit == 2
        assert config.sklimit == 2
        assert config.period == 20
        assert config.enabled == True
    
    # 检查特定配置
    sh_configs = [c for c in configs if c.symbol == 'SH']
    assert len(sh_configs) == 2
    sh_intervals = [c.interval for c in sh_configs]
    assert set(sh_intervals) == {60, 300}
    
    print("✓ create_custom_configs测试通过")


def test_config_id_generation():
    """测试配置ID生成"""
    print("=== 测试配置ID生成 ===")
    
    test_cases = [
        ('SH', 60, 'SH_1m'),
        ('OI', 180, 'OI_3m'),
        ('ag', 300, 'ag_5m'),
        ('rb', 900, 'rb_15m'),
        ('CZCE.OI2501', 1800, 'CZCE.OI2501_30m'),
    ]
    
    for symbol, interval, expected_id in test_cases:
        config = SymbolTimeFrameConfig(symbol=symbol, interval=interval)
        assert config.config_id == expected_id, f"期望{expected_id}，实际{config.config_id}"
    
    print("✓ 配置ID生成测试通过")


def test_timeframe_name_generation():
    """测试时间周期名称生成"""
    print("=== 测试时间周期名称生成 ===")
    
    test_cases = [
        (60, '1m'),
        (180, '3m'),
        (300, '5m'),
        (900, '15m'),
        (1800, '30m'),
        (3600, '60m'),
    ]
    
    for interval, expected_name in test_cases:
        config = SymbolTimeFrameConfig(symbol='TEST', interval=interval)
        assert config.timeframe_name == expected_name, f"期望{expected_name}，实际{config.timeframe_name}"
    
    print("✓ 时间周期名称生成测试通过")


def test_config_grouping():
    """测试配置分组功能"""
    print("=== 测试配置分组 ===")
    
    configs = [
        SymbolTimeFrameConfig(symbol='SH', interval=60),
        SymbolTimeFrameConfig(symbol='SH', interval=300),
        SymbolTimeFrameConfig(symbol='OI', interval=180),
        SymbolTimeFrameConfig(symbol='OI', interval=900),
        SymbolTimeFrameConfig(symbol='ag', interval=300),
    ]
    
    # 模拟管理器的分组逻辑
    symbol_configs = {}
    for config in configs:
        if config.symbol not in symbol_configs:
            symbol_configs[config.symbol] = []
        symbol_configs[config.symbol].append(config)
    
    # 检查分组结果
    assert len(symbol_configs) == 3
    assert len(symbol_configs['SH']) == 2
    assert len(symbol_configs['OI']) == 2
    assert len(symbol_configs['ag']) == 1
    
    # 检查SH的时间周期
    sh_intervals = [c.interval for c in symbol_configs['SH']]
    assert set(sh_intervals) == {60, 300}
    
    print("✓ 配置分组测试通过")


def test_import_dependencies():
    """测试依赖导入"""
    print("=== 测试依赖导入 ===")
    
    try:
        from tqsdk import TqApi, TqKq
        print("✓ TqSDK导入成功")
    except ImportError as e:
        print(f"✗ TqSDK导入失败: {e}")
        return False
    
    try:
        from tradefuncs import BK, SK, BP, SP
        print("✓ 交易函数导入成功")
    except ImportError as e:
        print(f"✗ 交易函数导入失败: {e}")
        return False
    
    try:
        from speaktext import speak_text
        print("✓ 语音函数导入成功")
    except ImportError as e:
        print(f"✗ 语音函数导入失败: {e}")
        return False
    
    try:
        from signalSend_noblock import socket, sendsignal
        print("✓ 信号发送函数导入成功")
    except ImportError as e:
        print(f"✗ 信号发送函数导入失败: {e}")
        return False
    
    return True


def run_basic_functionality_test():
    """运行基本功能测试"""
    print("=== 基本功能测试 ===")
    
    # 测试配置创建
    configs = create_default_configs()
    print(f"创建了{len(configs)}个默认配置")
    
    # 按合约分组显示
    symbol_groups = {}
    for config in configs:
        if config.symbol not in symbol_groups:
            symbol_groups[config.symbol] = []
        symbol_groups[config.symbol].append(config.timeframe_name)
    
    for symbol, timeframes in symbol_groups.items():
        print(f"  {symbol}: {', '.join(timeframes)}")
    
    # 测试自定义配置
    custom_map = {
        'TEST1': [60, 300],
        'TEST2': [180]
    }
    custom_configs = create_custom_configs(custom_map)
    print(f"创建了{len(custom_configs)}个自定义配置")
    
    print("✓ 基本功能测试通过")


def run_all_tests():
    """运行所有测试"""
    print("开始运行多合约多时间周期策略测试...\n")
    
    tests = [
        test_symbol_timeframe_config,
        test_signal_info,
        test_create_default_configs,
        test_create_custom_configs,
        test_config_id_generation,
        test_timeframe_name_generation,
        test_config_grouping,
        test_import_dependencies,
        run_basic_functionality_test,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
            print()
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            failed += 1
            print()
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False


def show_usage_examples():
    """显示使用示例"""
    print("""
=== 使用示例 ===

1. 运行所有测试:
   python test_MultiSymbol_MultiTimeFrame.py

2. 运行特定测试:
   python test_MultiSymbol_MultiTimeFrame.py config
   python test_MultiSymbol_MultiTimeFrame.py import
   python test_MultiSymbol_MultiTimeFrame.py basic

3. 实际策略运行示例:
   # 默认配置
   python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py default
   
   # 单合约多时间周期
   python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py single_symbol SH
   
   # 多合约单时间周期
   python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py multi_symbol SH OI ag

注意: 实际运行策略需要有效的TqSDK认证和网络连接
""")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        
        if test_name == "config":
            test_symbol_timeframe_config()
            test_create_default_configs()
            test_create_custom_configs()
        elif test_name == "import":
            test_import_dependencies()
        elif test_name == "basic":
            run_basic_functionality_test()
        elif test_name == "help":
            show_usage_examples()
        else:
            print(f"未知测试: {test_name}")
            print("可用测试: config, import, basic, help")
    else:
        # 运行所有测试
        success = run_all_tests()
        sys.exit(0 if success else 1)
