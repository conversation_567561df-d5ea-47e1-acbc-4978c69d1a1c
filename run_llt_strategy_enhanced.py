"""
LLT策略增强版运行脚本
包含详细的执行步骤提示和时间统计
"""

import sys
import argparse
import time
from datetime import datetime
from loguru import logger
from llt_strategy_refactored import *
from llt_config import get_config, load_config, save_config


class EnhancedProgressTracker:
    """增强版进度跟踪器"""
    
    def __init__(self, total_steps: int, task_name: str = "任务"):
        self.total_steps = total_steps
        self.current_step = 0
        self.task_name = task_name
        self.start_time = time.time()
        self.step_times = []
        self.step_names = []

        logger.info("=" * 60)
        logger.info(f"🎯 开始任务: {self.task_name}")
        logger.info(f"📊 总步骤数: {self.total_steps}")
        logger.info(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)
    
    def step(self, step_name: str = None, details: str = None):
        """记录步骤进度"""
        self.current_step += 1
        current_time = time.time()
        
        if self.step_times:
            step_duration = current_time - self.step_times[-1]
        else:
            step_duration = current_time - self.start_time
        
        self.step_times.append(current_time)
        self.step_names.append(step_name or f"步骤{self.current_step}")
        
        progress = (self.current_step / self.total_steps) * 100
        elapsed_total = current_time - self.start_time
        
        # 计算预计剩余时间
        if self.current_step > 1:
            avg_step_time = elapsed_total / self.current_step
            eta = avg_step_time * (self.total_steps - self.current_step)
            eta_str = f" | 🕐 预计剩余: {eta:.1f}秒"
        else:
            eta_str = ""
        
        # 进度条
        bar_length = 30
        filled_length = int(bar_length * progress / 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        
        logger.info(f"📈 步骤 {self.current_step}/{self.total_steps}: {step_name or '执行中'}")
        logger.info(f"   ▶️  进度: [{bar}] {progress:.1f}%")
        logger.info(f"   ⏱️  本步耗时: {step_duration:.3f}秒 | 总耗时: {elapsed_total:.3f}秒{eta_str}")

        if details:
            logger.info(f"   💡 详情: {details}")

        logger.info("-" * 40)
    
    def finish(self, success: bool = True):
        """完成任务"""
        total_time = time.time() - self.start_time
        avg_step_time = total_time / self.total_steps if self.total_steps > 0 else 0
        
        status = "🎉 任务完成" if success else "❌ 任务失败"
        
        logger.info("=" * 60)
        if success:
            logger.success(f"{status}: {self.task_name}")
        else:
            logger.error(f"{status}: {self.task_name}")
        logger.info(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"⏱️  总耗时: {total_time:.3f}秒 ({total_time/60:.1f}分钟)")
        logger.info(f"📊 平均每步: {avg_step_time:.3f}秒")

        if success and len(self.step_names) > 0:
            logger.info("📋 执行步骤回顾:")
            for i, (name, step_time) in enumerate(zip(self.step_names, self.step_times), 1):
                if i == 1:
                    duration = step_time - self.start_time
                else:
                    duration = step_time - self.step_times[i-2]
                logger.info(f"   {i}. {name}: {duration:.3f}秒")

        logger.info("=" * 60)


def run_optimization_enhanced(config: TradingConfig):
    """运行增强版参数优化"""
    logger = logging.getLogger(__name__)
    
    # 创建进度跟踪器
    tracker = EnhancedProgressTracker(6, "参数优化流程")
    
    try:
        # 步骤1: 初始化数据管理器
        tracker.step("初始化数据管理器", f"合约: {config.symbol}")
        data_manager = DataManager(config)
        
        # 步骤2: 建立API连接
        tracker.step("建立API连接", f"用户: {config.auth_username}")
        api = data_manager.initialize_api()
        
        # 步骤3: 加载历史数据
        tracker.step("加载历史数据", f"数据长度: {config.kline_data_length}条")
        klines = data_manager.load_historical_data()
        
        # 步骤4: 创建优化器
        tracker.step("创建参数优化器", f"优化范围: {config.optimize_d_value_range}")
        optimizer = ParameterOptimizer(klines)
        
        # 步骤5: 执行参数优化
        tracker.step("执行参数优化", f"测试参数: {len(range(*config.optimize_d_value_range))}个")
        results = optimizer.optimize_d_value(range(*config.optimize_d_value_range))
        
        # 步骤6: 处理优化结果
        tracker.step("处理优化结果", f"有效结果: {len(results)}个")
        
        if results:
            logger.info("\n🏆 参数优化结果汇总:")
            logger.info("排名 | D_VALUE | 交易次数 | 累计盈亏 | 胜率 | 收益率")
            logger.info("-" * 60)
            
            for i, result in enumerate(results, 1):
                logger.info(
                    f"{i:4d} | {result['D_VALUE']:7d} | {result['total_trades']:8d} | "
                    f"{result['total_pnl']:8.2f} | {result['win_rate']:5.1f}% | "
                    f"{result['return_rate']:6.2f}%"
                )
            
            # 保存最优配置
            best_result = results[0]
            config.d_value = best_result['D_VALUE']
            save_config(config, "llt_config_optimized.json")
            
            logger.info(f"\n🎯 最优参数:")
            logger.info(f"   D_VALUE: {config.d_value}")
            logger.info(f"   ALPHA: {config.alpha:.6f}")
            logger.info(f"   预期收益率: {best_result['return_rate']:.2f}%")
            logger.info(f"   历史胜率: {best_result['win_rate']:.1f}%")
            logger.info(f"   配置已保存: llt_config_optimized.json")
            
            tracker.finish(True)
            return best_result
        else:
            logger.warning("⚠️  优化未找到有效结果")
            tracker.finish(False)
            return None
            
    except Exception as e:
        logger.error(f"❌ 参数优化失败: {e}")
        tracker.finish(False)
        return None
    finally:
        if 'api' in locals():
            api.close()


def run_backtest_enhanced(config: TradingConfig):
    """运行增强版回测"""
    logger = logging.getLogger(__name__)
    
    # 创建进度跟踪器
    tracker = EnhancedProgressTracker(5, "策略回测流程")
    
    try:
        # 步骤1: 初始化数据管理器
        tracker.step("初始化数据管理器", f"合约: {config.symbol}")
        data_manager = DataManager(config)
        
        # 步骤2: 建立API连接
        tracker.step("建立API连接", f"用户: {config.auth_username}")
        api = data_manager.initialize_api()
        
        # 步骤3: 加载历史数据
        tracker.step("加载历史数据", f"数据长度: {config.kline_data_length}条")
        klines = data_manager.load_historical_data()
        
        # 步骤4: 执行回测
        tracker.step("执行策略回测", f"D_VALUE: {config.d_value}, ALPHA: {config.alpha:.6f}")
        optimizer = ParameterOptimizer(klines)
        result = optimizer._run_backtest(config.alpha)
        
        # 步骤5: 生成回测报告
        tracker.step("生成回测报告", f"交易次数: {len(result['trades'])}")
        
        logger.info("\n📊 回测结果详细报告:")
        logger.info("=" * 50)
        logger.info(f"📈 策略参数:")
        logger.info(f"   合约代码: {config.symbol}")
        logger.info(f"   D_VALUE: {config.d_value}")
        logger.info(f"   ALPHA: {config.alpha:.6f}")
        logger.info(f"   交易手数: {config.volume}")
        logger.info("")
        logger.info(f"📊 交易统计:")
        logger.info(f"   总交易次数: {len(result['trades'])}")
        logger.info(f"   盈利交易: {result['winning_trades']}")
        logger.info(f"   亏损交易: {result['losing_trades']}")
        logger.info(f"   胜率: {result['win_rate']:.1f}%")
        logger.info("")
        logger.info(f"💰 盈亏分析:")
        logger.info(f"   累计盈亏: {result['total_pnl']:.2f}点")
        logger.info(f"   收益率: {result['return_rate']:.2f}%")
        
        if result['winning_trades'] > 0 and result['losing_trades'] > 0:
            avg_win = result['total_pnl'] / result['winning_trades'] if result['winning_trades'] > 0 else 0
            avg_loss = -result['total_pnl'] / result['losing_trades'] if result['losing_trades'] > 0 else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            
            logger.info(f"   平均盈利: {avg_win:.2f}点")
            logger.info(f"   平均亏损: {avg_loss:.2f}点")
            logger.info(f"   盈亏比: {profit_factor:.2f}")
        
        logger.info("=" * 50)
        
        tracker.finish(True)
        return result
        
    except Exception as e:
        logger.error(f"❌ 回测失败: {e}")
        tracker.finish(False)
        return None
    finally:
        if 'api' in locals():
            api.close()


def run_live_trading_enhanced(config: TradingConfig):
    """运行增强版实时交易"""
    logger = logging.getLogger(__name__)
    
    # 创建进度跟踪器
    tracker = EnhancedProgressTracker(6, "实时交易启动流程")
    
    try:
        # 步骤1: 初始化组件
        tracker.step("初始化策略组件", f"最大持仓: {config.max_position}")
        data_manager = DataManager(config)
        position_manager = PositionManager(config.max_position)
        risk_manager = RiskManager(config.max_daily_loss, config.max_drawdown)
        trade_logger = TradeLogger(config.trade_log_file)
        
        # 步骤2: 建立API连接
        tracker.step("建立API连接", f"用户: {config.auth_username}")
        api = data_manager.initialize_api()
        
        # 步骤3: 加载历史数据
        tracker.step("加载历史数据", f"数据长度: {config.kline_data_length}条")
        klines = data_manager.load_historical_data()
        
        # 步骤4: 验证策略参数
        tracker.step("验证策略参数", f"D_VALUE: {config.d_value}, ALPHA: {config.alpha:.6f}")
        logger.info(f"🎯 策略配置验证:")
        logger.info(f"   合约代码: {config.symbol}")
        logger.info(f"   D_VALUE: {config.d_value}")
        logger.info(f"   ALPHA: {config.alpha:.6f}")
        logger.info(f"   交易手数: {config.volume}")
        logger.info(f"   最大持仓: {config.max_position}")
        logger.info(f"   日亏损限制: {config.max_daily_loss}")
        logger.info(f"   最大回撤: {config.max_drawdown:.1%}")
        
        # 步骤5: 创建实时策略
        tracker.step("创建实时交易策略", "初始化策略引擎")
        live_strategy = LLTLiveStrategy(config, api, position_manager, risk_manager, trade_logger)
        
        # 步骤6: 启动实时交易
        tracker.step("启动实时交易", "开始监控市场数据")
        tracker.finish(True)
        
        logger.info("🚀 实时交易策略已启动，按 Ctrl+C 停止...")
        logger.info("💡 策略将在新K线生成时自动执行交易逻辑")
        
        # 运行策略
        live_strategy.run()
        
    except Exception as e:
        logger.error(f"❌ 实时交易失败: {e}")
        tracker.finish(False)
    finally:
        if 'api' in locals():
            api.close()
        
        # 显示最终统计
        if 'trade_logger' in locals():
            stats = trade_logger.get_statistics()
            if stats and stats['total_trades'] > 0:
                logger.info("\n📊 交易会话统计:")
                logger.info("=" * 40)
                logger.info(f"总交易次数: {stats['total_trades']}")
                logger.info(f"盈利交易: {stats['winning_trades']}")
                logger.info(f"亏损交易: {stats['losing_trades']}")
                logger.info(f"胜率: {stats['win_rate']:.1f}%")
                logger.info(f"累计盈亏: {stats['total_pnl']:.2f}")
                if stats['winning_trades'] > 0:
                    logger.info(f"平均盈利: {stats['avg_win']:.2f}")
                if stats['losing_trades'] > 0:
                    logger.info(f"平均亏损: {stats['avg_loss']:.2f}")
                logger.info("=" * 40)


def main():
    """主函数"""
    program_start_time = time.time()
    
    parser = argparse.ArgumentParser(description="LLT策略增强版运行器")
    parser.add_argument('mode', choices=['optimize', 'backtest', 'live'], 
                       help='运行模式: optimize(参数优化), backtest(回测), live(实时交易)')
    parser.add_argument('--config', '-c', default='default', 
                       help='配置名称或配置文件路径')
    parser.add_argument('--symbol', '-s', help='交易合约')
    parser.add_argument('--d-value', '-d', type=int, help='D_VALUE参数')
    parser.add_argument('--volume', '-v', type=int, help='交易手数')
    parser.add_argument('--max-position', '-p', type=int, help='最大持仓')
    parser.add_argument('--log-level', '-l', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 加载配置
    if args.config.endswith('.json'):
        config = load_config(args.config)
    else:
        config = get_config(args.config)
    
    # 覆盖命令行参数
    if args.symbol:
        config.symbol = args.symbol
    if args.d_value:
        config.d_value = args.d_value
    if args.volume:
        config.volume = args.volume
    if args.max_position:
        config.max_position = args.max_position
    
    # 设置日志
    config.log_level = args.log_level
    logger = setup_logging(config.log_level)
    
    # 程序启动信息
    logger.info("🎯 LLT策略增强版启动")
    logger.info(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"🔧 运行模式: {args.mode}")
    logger.info(f"⚙️  使用配置: {args.config}")
    logger.info(f"📊 主要参数: 合约={config.symbol}, D_VALUE={config.d_value}, 手数={config.volume}")
    
    # 运行对应模式
    try:
        if args.mode == 'optimize':
            run_optimization_enhanced(config)
        elif args.mode == 'backtest':
            run_backtest_enhanced(config)
        elif args.mode == 'live':
            run_live_trading_enhanced(config)
    except KeyboardInterrupt:
        logger.info("🛑 程序被用户中断")
    except Exception as e:
        logger.error(f"❌ 程序运行出错: {e}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        sys.exit(1)
    finally:
        # 显示程序总运行时间
        total_runtime = time.time() - program_start_time
        logger.info(f"🏁 程序结束")
        logger.info(f"⏱️  总运行时间: {total_runtime:.3f}秒 ({total_runtime/60:.1f}分钟)")
        logger.info(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
