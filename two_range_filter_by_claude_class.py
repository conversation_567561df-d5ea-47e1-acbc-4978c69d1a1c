import pandas as pd
import numpy as np
from tqsdk import Tq<PERSON><PERSON>, TqAuth, TargetPosTask, TqKq
from tqsdk.ta import EMA

class TwinRangeFilter:
    def __init__(self, symbol, per1=27, mult1=1.6, per2=55, mult2=2):
        self.symbol = symbol
        self.per1 = per1
        self.mult1 = mult1
        self.per2 = per2
        self.mult2 = mult2
        self.position = 0
        self.api = TqApi(TqKq(), auth="quant_ggh,Qiai1301", disable_print=True)
        self.klines = self.api.get_kline_serial(self.symbol, 60)  # 1分钟K线
        self.target_pos = TargetPosTask(self.api, self.symbol)

    def smoothrng(self, x, t, m):
        wper = t * 2 - 1
        avrng = EMA(abs(x - x.shift(1)), t)
        return EMA(avrng, wper) * m

    def rngfilt(self, x, r):
        filt = x.copy()
        for i in range(1, len(x)):
            if x[i] > filt[i-1]:
                filt[i] = max(x[i] - r[i], filt[i-1])
            else:
                filt[i] = min(x[i] + r[i], filt[i-1])
        return filt

    def calculate_signals(self):
        close = self.klines.close.iloc[-200:]  # 获取最近200根K线的收盘价

        smrng1 = self.smoothrng(close, self.per1, self.mult1)
        smrng2 = self.smoothrng(close, self.per2, self.mult2)
        smrng = (smrng1 + smrng2) / 2

        filt = self.rngfilt(close, smrng)

        upward = (filt > filt.shift(1)).astype(int).cumsum()
        upward[filt <= filt.shift(1)] = 0

        downward = (filt < filt.shift(1)).astype(int).cumsum()
        downward[filt >= filt.shift(1)] = 0

        longCond = ((close > filt) & (close > close.shift(1)) & (upward > 0)) | \
                   ((close > filt) & (close < close.shift(1)) & (upward > 0))

        shortCond = ((close < filt) & (close < close.shift(1)) & (downward > 0)) | \
                    ((close < filt) & (close > close.shift(1)) & (downward > 0))

        return longCond.iloc[-1], shortCond.iloc[-1]

    def run(self):
        try:
            while True:
                self.api.wait_update()

                if self.api.is_changing(self.klines.iloc[-1], "datetime"):
                    long, short = self.calculate_signals()

                    if long and self.position <= 0:
                        print("开多单")
                        self.target_pos.set_target_volume(1)
                        self.position = 1
                    elif short and self.position >= 0:
                        print("开空单")
                        self.target_pos.set_target_volume(-1)
                        self.position = -1

        except KeyboardInterrupt:
            print("程序已终止")
        finally:
            self.api.close()

# 使用示例
if __name__ == "__main__":
    '''
    这个类实现了以下功能：

    __init__ 方法初始化了策略所需的参数、TqApi 连接、K线数据和 TargetPosTask。
    smoothrng 和 rngfilt 方法实现了原 Pine 脚本中的相应函数。
    calculate_signals 方法包含了主要的信号计算逻辑，返回多空信号。
    run 方法实现了主循环，持续监控市场数据并执行交易。

    使用这个类的方法如下：
    strategy = TwinRangeFilter("SHFE.au2106", per1=30, mult1=1.8, per2=60, mult2=2.2)
    这种类的实现方式使得策略更容易集成到其他程序中，也方便进行参数优化和回测。记得在实际使用时将 "YOUR_USERNAME" 和 "YOUR_PASSWORD" 替换为你的 TqSdk 账户信息。

    '''

    strategy = TwinRangeFilter("SHFE.rb2410")  # 使用上海期货交易所的黄金期货作为示例
    strategy.run()
