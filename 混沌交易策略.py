#open ai 的deep reserch 生成的.
from tqsdk import TqApi, TqAuth, TqSim, TargetPosTask
from tqsdk.tafunc import ma

SYMBOL = "DCE.j2105"  # 示例合约代码（可替换为目标期货合约）
SHORT_PERIOD = 5      # AO短均线周期
LONG_PERIOD = 34      # AO长均线周期

# 初始化API（使用TqSim进行本地模拟交易）
sim = TqSim()
api = TqApi(account=sim, auth=TqAuth("快期账号", "密码"))

# 获取1分钟K线数据，长度设为LONG_PERIOD+10以供计算AO/AC
klines = api.get_kline_serial(SYMBOL, duration_seconds=60, data_length=LONG_PERIOD+10)

# 目标持仓任务（自动达到目标持仓量）
target_pos = TargetPosTask(api, SYMBOL)

current_pos = 0     # 当前持仓量（>0多头，<0空头）
max_add_times = 3   # 最大加仓次数
add_count = 0       # 已加仓次数
close_count = 0     # 已平仓次数

print("混沌交易策略开始运行")
while True:
    api.wait_update()
    # 当K线产生新bar时更新信号
    if api.is_changing(klines.iloc[-1], "datetime"):
        # 计算价格中点
        mid = (klines["high"] + klines["low"]) / 2
        # 计算AO：5周期均线 - 34周期均线
        ao = ma(mid, SHORT_PERIOD) - ma(mid, LONG_PERIOD)
        # 计算AC：AO - 其5周期均线
        ac = ao - ma(ao, SHORT_PERIOD)
        # 提取最新两根柱的AO/AC值
        last_ao, prev_ao = ao.iloc[-1], ao.iloc[-2]
        last_ac, prev_ac = ac.iloc[-1], ac.iloc[-2]

        # 开仓：当前无仓位时，根据AO零轴穿越信号进场
        if current_pos == 0:
            # AO上穿零轴，进场做多
            if prev_ao < 0 and last_ao > 0:
                current_pos = 1
                target_pos.set_target_volume(current_pos)
                add_count = close_count = 0
                print("AO上穿零轴，开多仓")
            # AO下穿零轴，进场做空
            if prev_ao > 0 and last_ao < 0:
                current_pos = -1
                target_pos.set_target_volume(current_pos)
                add_count = close_count = 0
                print("AO下穿零轴，开空仓")

        # 多头持仓时的加仓/平仓逻辑
        elif current_pos > 0:
            # AC上穿零轴，加仓
            if prev_ac < 0 and last_ac > 0 and add_count < max_add_times:
                current_pos += 1
                target_pos.set_target_volume(current_pos)
                add_count += 1
                print(f"AC上穿零轴，加仓1手，持仓{current_pos}手")
            # AC下穿零轴，平仓1手
            if prev_ac > 0 and last_ac < 0 and close_count < max_add_times and current_pos > 0:
                current_pos -= 1
                target_pos.set_target_volume(current_pos)
                close_count += 1
                print(f"AC下穿零轴，平仓1手，剩余持仓{current_pos}手")
            # AO转负则全部平多
            if prev_ao > 0 and last_ao < 0:
                current_pos = 0
                target_pos.set_target_volume(0)
                add_count = close_count = 0
                print("AO由正转负，平掉所有多单")

        # 空头持仓时的加仓/平仓逻辑（与多头相反）
        elif current_pos < 0:
            # AC下穿零轴，加仓做空
            if prev_ac > 0 and last_ac < 0 and add_count < max_add_times:
                current_pos -= 1
                target_pos.set_target_volume(current_pos)
                add_count += 1
                print(f"AC下穿零轴，加空单1手，持仓{abs(current_pos)}手")
            # AC上穿零轴，平空1手
            if prev_ac < 0 and last_ac > 0 and close_count < max_add_times and current_pos < 0:
                current_pos += 1
                target_pos.set_target_volume(current_pos)
                close_count += 1
                print(f"AC上穿零轴，平空1手，剩余持仓{abs(current_pos)}手")
            # AO转正则全部平空
            if prev_ao < 0 and last_ao > 0:
                current_pos = 0
                target_pos.set_target_volume(0)
                add_count = close_count = 0
                print("AO由负转正，平掉所有空单")
