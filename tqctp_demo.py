from tqsdk import TqApi, TqCtp, TqAuth
# account = TqCtp(account_id="CTP 账户", password="CTP 密码", front_broker="CTP 柜台代码", front_url="CTP 柜台地址", app_id="CTP AppID", auth_code="CTP AuthCode")
account = TqCtp(account_id="********", password="Qiai1301", front_broker="9060", front_url="tcp://120.136.160.67:33437", app_id="SHINNY_Q7V3_3.8", auth_code="PMEUSWTJWSTV0REI")
api = TqApi(account, auth=TqAuth("smartmanp", "ftp123"))

print(api)
acct=api.get_account()
print(acct)
