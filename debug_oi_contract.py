"""
调试菜籽油主力合约获取问题
"""

from multi_contract_config import MainContractFetcher
from loguru import logger

# 设置详细日志
logger.remove()
logger.add(lambda msg: print(msg, end=''), level='DEBUG', colorize=True)

def debug_contract_query():
    """调试合约查询"""
    try:
        with MainContractFetcher() as fetcher:
            print('\n测试菜籽油连续合约查询...')
            
            # 尝试查询菜籽油连续合约
            cont_symbol = '<EMAIL>'
            print(f'查询连续合约: {cont_symbol}')
            
            try:
                quotes = fetcher.api.query_cont_quotes(cont_symbol)
                print(f'查询结果类型: {type(quotes)}')
                print(f'查询结果长度: {len(quotes) if quotes else 0}')
                print(f'查询结果内容: {quotes}')
                
                if quotes and len(quotes) > 0:
                    first_quote = quotes[0]
                    print(f'第一个合约: {first_quote}')
                    if 'instrument_id' in first_quote:
                        print(f'主力合约: {first_quote["instrument_id"]}')
                    else:
                        print(f'可用字段: {list(first_quote.keys())}')
                else:
                    print('查询结果为空')
                    
            except Exception as e:
                print(f'查询异常: {e}')
                import traceback
                traceback.print_exc()
                
            # 也测试一下沪铜
            print('\n测试沪铜连续合约查询...')
            cont_symbol = '<EMAIL>'
            print(f'查询连续合约: {cont_symbol}')
            
            try:
                quotes = fetcher.api.query_cont_quotes(cont_symbol)
                print(f'查询结果类型: {type(quotes)}')
                print(f'查询结果长度: {len(quotes) if quotes else 0}')
                print(f'查询结果内容: {quotes}')
                
                if quotes and len(quotes) > 0:
                    first_quote = quotes[0]
                    print(f'第一个合约: {first_quote}')
                    if 'instrument_id' in first_quote:
                        print(f'主力合约: {first_quote["instrument_id"]}')
                    else:
                        print(f'可用字段: {list(first_quote.keys())}')
                else:
                    print('查询结果为空')
                    
            except Exception as e:
                print(f'查询异常: {e}')
                import traceback
                traceback.print_exc()
                
            # 测试其他可能的查询方式
            print('\n测试其他查询方式...')
            
            # 尝试直接查询菜籽油合约
            try:
                # 获取合约信息
                print('尝试获取菜籽油合约列表...')
                
                # 这里可能需要其他API方法
                # 先看看API有什么方法
                api_methods = [method for method in dir(fetcher.api) if not method.startswith('_')]
                print(f'API可用方法: {api_methods[:10]}...')  # 只显示前10个
                
            except Exception as e:
                print(f'其他查询异常: {e}')
                
    except Exception as e:
        print(f'整体错误: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_contract_query()
