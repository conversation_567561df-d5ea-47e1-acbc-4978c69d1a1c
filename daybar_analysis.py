import copy
import sys

from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tradefuncs import *
from speaktextng import speak_text
from loguru import logger as mylog
from datetime import date

from time import sleep
import time

def bar_info(api, SYMBOL, interval):
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    daybar = daybars.dropna()

    hl = daybar.high - daybar.low
    ho = daybar.high - daybar.open
    ol = daybar.open - daybar.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    print(SYMBOL, interval,  '最大价差:', hlmax, '平均价差:', daymean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:', int(olmean))



def disp_day_info(quote, daymean, last3mean, hlmax, homean, olmean):
    gapcover = False
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    print(quote.datetime, quote.instrument_id, '缺口：', gap, '回补:', gapcover, '最大价差:', hlmax, '平均价差:', daymean, '三日平均:',
          last3mean, '上价差:', int(homean), '下价差:', int(olmean), '现价:', quote.last_price, 'BB:', BB, '今日价差：', dayvib,
          '上价差:', upvib, '下价差:', dnvib)


def MaCrossCaculate(C, period, quote):
    trmac = ma(C, period)
    ups = crossup(C, trmac)
    dns = crossdown(C, trmac)
    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)
    if bkdist > skdist:
        signalnow = 'SK'
        distnow = skdist
    else:
        signalnow = 'BK'
        distnow = bkdist

    signalprice = C.iloc[-distnow]
    sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
    # print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)
    uplist = ups.tolist()
    average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
    print('平均信号距离：', average_signal_distance)
    print('upsignal:  ', ups.iloc[-40:].tolist())
    print('downsignal:', dns.iloc[-40:].tolist())


def ma_cross(api, symbol, interval, single_volume, bklimit, sklimit, period=13):
    strategyname = 'macross'
    acct = api.get_account()
    userid = acct.user_id.split('-')[0]
    logfilename = '_'.join([userid, symbol, strategyname])
    mylog.add(logfilename + '.log', encoding='utf-8')
    savebars = False
    quote = api.get_quote(symbol)

    if '@' in symbol:
        SYMBOL = quote.underlying_symbol
    else:
        SYMBOL = symbol

    trading_symbol = SYMBOL.split('.')[1]

    ticks = api.get_tick_serial(SYMBOL)
    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines

    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()
    hl = daybar.high - daybar.low
    ho = daybar.high - daybar.open
    ol = daybar.open - daybar.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    N = 23
    O = klines1.open
    H = klines1.high
    L = klines1.low
    C = klines1.close

    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    KNUM = len(klines1)
    gapcover = False

    position = api.get_position(SYMBOL)
    acc = api.get_account()
    bkvol = position.pos_long
    bkvol_yd = position.pos_long_his
    bkvol_td = position.pos_long_today

    skvol = position.pos_short
    skvol_yd = position.pos_short_his
    skvol_td = position.pos_short_today

    skflag = True
    bkflag = True
    spflag = True
    bpflag = True
    speakyn = True
    dayopensp = True
    dayopenbp = True

    fundavailable = acc.available
    disp_day_info(quote, daymean, last3mean, hlmax, homean, olmean)
    MaCrossCaculate(C, period, quote)
    # print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)

    while True:

        api.wait_update()

        if api.is_changing(daybars.iloc[-1], "datetime"):
            savebars = True
            dayopensp = True
            dayopenbp = True

            hl = daybar.high - daybar.low
            ho = daybar.high - daybar.open
            ol = daybar.open - daybar.low
            daymean = int(hl.mean())
            homean = ho.mean()
            olmean = ol.mean()
            hlmax = max(hl)
            last3mean = int(hl.iloc[-4:-1].mean())
            dayopen = quote.open
            dayhigh = quote.highest
            daylow = quote.lowest
            preclose = quote.pre_close
            dayvib = dayhigh - daylow
            gap = dayopen - preclose
            gapcover = False
            print('日线数据更新完成。。。')
            mylog.info('日线数据更新完成。。。')

        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            # 恢复下单标志, 确保k线周期内如果有多个信号的话,执行一次.
            disp_day_info(quote, daymean, last3mean, hlmax, homean, olmean)
            speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                klines1 = klines1.append(newk)

            O = klines1.open
            H = klines1.high
            L = klines1.low
            C = klines1.close

            hl = ema(H - L, 23).iloc[-1]
            cover_gap = max(int(hl), 3)

            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today
            bkfreeze = position.volume_long_frozen

            # volume_long_frozen_today: int
            # volume_long_frozen_his: int

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today
            skfreeze = position.volume_short_frozen

            trmac = ma(C, period)
            # trmac = ma(C, 10)
            ups = crossup(C, trmac)
            dns = crossdown(C, trmac)
            upslist = ups.tolist()
            dnslist = dns.tolist()
            bkdist = be_apart_from(upslist)
            skdist = be_apart_from(dnslist)
            if bkdist > skdist:
                signalnow = 'SK'
                distnow = skdist
            else:
                signalnow = 'BK'
                distnow = bkdist

            signalprice = C.iloc[-distnow]
            sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
            print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price,
                  '信号盈亏:', sigfloatprofit)
            uplist = ups.tolist()
            average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
            print('平均信号距离：', average_signal_distance)
            print('upsignal:  ', ups.iloc[-40:].tolist())
            print('downsignal:', dns.iloc[-40:].tolist())


        if api.is_changing(quote):
            if gap > 0:
                if quote.lowest < quote.pre_close:
                    gapcover = True
            else:
                if quote.highest > quote.pre_close:
                    gapcover = True

        if api.is_changing(position):
            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today

        hr=time.localtime().tm_hour
        mi=time.localtime().tm_min
        if ((time.localtime().tm_hour > 15 and time.localtime().tm_min > 15) or (time.localtime().tm_hour > 23 and time.localtime().tm_min > 0)) and savebars:
            klines1.to_csv(SYMBOL + '.csv')
            savebars = False
            api.close()
            mylog.info('no trading time, quit.')
            return





def runstrategy():

    from tqsdk import TqApi, TqKq

    product = 'OI'
    product = 'SR'
    product = 'SR'


    interval = 60
    bklimit = 15
    sklimit = 15
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="smartmanp,ftp123")


    product = api.query_cont_quotes()
    info = api.query_symbol_info(product)

    while True:
        try:
            product = input('input the product id: ')
            symbol = api.query_cont_quotes(product_id=product)[0]

        except:
            continue

        ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)




if __name__ == "__main__":
    import schedule
    import pandas as pd
    from datetime import date
    import datetime

    from tqsdk import TqApi, TqKq
    product = 'OI'




    cld =  pd.read_pickle('cld.pkl')
    # get today()
    td=pd.Timestamp(date.today())

    tradingday = cld.loc[cld.date==td, 'trading'].iloc[0]

    api = TqApi(TqKq(), auth="smartmanp,ftp123")
    # cld = api.get_trading_calendar(start_dt=date(2022, 1, 1), end_dt=date(2022, 12, 31))
    #
    # cld.to_pickle('cld.pkl')

    SYMBOL = api.query_cont_quotes(product_id=product)[0]

    bar_info(api, SYMBOL, 60*15
             )
    # schedule.every(15).minutes.do(job)
    # schedule.every().hour.do(job)
    # schedule.every().day.at("10:30").do(job)
    # schedule.every().monday.do(job)
    # schedule.every().wednesday.at("13:15").do(job)
    # schedule.every().minute.at(":17").do(job)

    schedule.every().day.at("08:45").do(runstrategy)
    schedule.every().day.at("20:10").do(runstrategy)

    schedule.every().monday.at("08:45").do(runstrategy)
    schedule.every().tuesday.at("08:45").do(runstrategy)
    schedule.every().wednesday.at("08:45").do(runstrategy)
    schedule.every().thursday.at("08:45").do(runstrategy)
    schedule.every().friday.at("08:45").do(runstrategy)

    schedule.every().monday.at("20:45").do(runstrategy)
    schedule.every().tuesday.at("20:45").do(runstrategy)
    schedule.every().wednesday.at("20:45").do(runstrategy)
    schedule.every().thursday.at("20:45").do(runstrategy)
    schedule.every().friday.at("20:45").do(runstrategy)

    schedule.run_all()

    # all_jobs = schedule.get_jobs()
    # schedule.clear()



    while True:
        schedule.run_pending()
        time.sleep(1)
