# 主力合约动态获取功能升级

## 🎯 升级目标

解决合约配置中主力合约代码不准确的问题，使用 `api.query_cont_quotes()` 动态获取当前真实的主力合约，确保分析使用的是最活跃的合约。

## ❌ 原始问题

### 静态合约配置的问题
```python
# 原始静态配置 - 可能过时
'agricultural': [
    ContractConfig("CZCE.OI601", "菜籽油", "CZCE"),  # 可能不是当前主力
    ContractConfig("DCE.m2501", "豆粕", "DCE"),      # 可能不是当前主力
    # ...
]
```

**问题**：
- ❌ 主力合约会随时间变化（如从2501变为2505）
- ❌ 静态配置无法跟上市场变化
- ❌ 可能分析到流动性差的非主力合约
- ❌ 影响分析结果的准确性

## ✅ 解决方案

### 1. 动态主力合约获取器
```python
class MainContractFetcher:
    """主力合约获取器"""
    
    def get_main_contract(self, exchange: str, product_id: str) -> Optional[str]:
        """获取指定品种的主力合约"""
        # 使用 api.query_cont_quotes() 获取真实主力合约
        cont_symbol = f"{exchange}.{product_id}@{exchange}.{product_id}"
        quotes = self.api.query_cont_quotes(cont_symbol)
        if quotes and len(quotes) > 0:
            return quotes[0]['instrument_id']  # 第一个是主力合约
```

### 2. 品种基础信息配置
```python
PRODUCT_INFO = {
    'metals': [
        ("SHFE", "cu", "沪铜", 5, 10, 0.05),
        ("SHFE", "al", "沪铝", 5, 5, 0.05),
        # 只配置品种信息，不配置具体合约代码
    ]
}
```

### 3. 智能回退机制
```python
def _infer_main_contract(self, exchange: str, product_id: str) -> Optional[str]:
    """根据交易所规则推断主力合约"""
    # 如果API查询失败，根据交易所规则推断
    # SHFE: 通常下个月或下下个月
    # DCE: 通常1月、5月、9月
    # CZCE: 通常1月、5月、9月
```

## 🧪 测试验证结果

**完整测试通过**：
```
================================================================================
测试结果总结
================================================================================
主力合约获取器             : ✅ 通过
批量获取主力合约            : ✅ 通过
主力合约配置创建            : ✅ 通过
预设配置                : ✅ 通过
合约推断功能              : ✅ 通过
错误处理                : ✅ 通过
----------------------------------------
总计: 6/6 个测试通过
🎉 主力合约功能基本正常！
```

### 实际获取的主力合约
```
✅ 成功获取 8 个金属主力合约:
  沪铜       (SHFE.cu     ): SHFE.cu2510
  沪铝       (SHFE.al     ): SHFE.al2510
  沪锌       (SHFE.zn     ): SHFE.zn2510
  沪铅       (SHFE.pb     ): SHFE.pb2510
  沪镍       (SHFE.ni     ): SHFE.ni2510
  沪锡       (SHFE.sn     ): SHFE.sn2510
  沪金       (SHFE.au     ): SHFE.au2509
  沪银       (SHFE.ag     ): SHFE.ag2509
```

**观察**：
- ✅ 大部分金属主力合约是2510（2025年10月）
- ✅ 贵金属（金银）主力合约是2509（2025年9月）
- ✅ 这些都是当前真实的主力合约，不是静态配置的过时合约

## 🔧 核心功能

### 1. MainContractFetcher类
```python
# 上下文管理器使用
with MainContractFetcher() as fetcher:
    main_contract = fetcher.get_main_contract("SHFE", "cu")
    print(f"沪铜主力: {main_contract}")  # SHFE.cu2510

# 批量获取
main_contracts = fetcher.get_multiple_main_contracts([
    ("SHFE", "cu"),
    ("DCE", "m"),
    ("CZCE", "OI")
])
```

### 2. 便利函数
```python
# 获取当前主力合约
main_contracts = get_current_main_contracts(['metals', 'agricultural'])

# 创建基于主力合约的分析配置
config = create_main_contracts_analysis_config(['metals'])

# 使用预设配置（自动获取主力合约）
config = get_preset_config('main_contracts_analysis')
```

### 3. 预设配置升级
```python
# 新的预设配置默认使用主力合约
PRESET_CONFIGS = {
    'main_contracts_analysis': PresetAnalysisConfig(
        name="主力合约分析",
        description="分析当前主力合约的盈利能力",
        contract_groups=['agricultural', 'metals'],
        use_main_contracts=True  # 启用主力合约
    ),
    
    'full_main_market_scan': PresetAnalysisConfig(
        name="全市场主力扫描",
        description="扫描所有品种的主力合约",
        contract_groups=['agricultural', 'metals', 'chemicals', 'ferrous', 'energy'],
        use_main_contracts=True
    )
}
```

## 🚀 使用方法

### 1. 快速获取主力合约
```bash
# 测试主力合约获取
python test_main_contracts.py

# 查看配置说明
python multi_contract_config.py
```

### 2. 在分析中使用主力合约
```python
from multi_contract_config import get_preset_config
from llt_multi_contract_analyzer import MultiContractAnalyzer

# 使用主力合约预设配置
config = get_preset_config('main_contracts_analysis')
analyzer = MultiContractAnalyzer(config)
results = analyzer.run_analysis()
```

### 3. 自定义主力合约分析
```python
from multi_contract_config import create_main_contracts_analysis_config

# 创建自定义主力合约配置
config = create_main_contracts_analysis_config(
    product_groups=['metals', 'agricultural'],
    d_value_range=(20, 81),
    output_dir="my_main_contracts_analysis"
)
```

### 4. 命令行使用
```bash
# 使用主力合约进行快速分析
python llt_multi_contract_analyzer.py --mode quick

# 使用主力合约进行全市场分析
python llt_multi_contract_analyzer.py --mode custom --groups metals agricultural
```

## 📊 支持的品种

### 农产品 (10个品种)
- **CZCE**: OI(菜籽油), RM(菜籽粕), CF(棉花), SR(白糖), TA(PTA), MA(甲醇)
- **DCE**: m(豆粕), y(豆油), a(豆一), c(玉米), cs(玉米淀粉)

### 金属 (8个品种)
- **SHFE**: cu(沪铜), al(沪铝), zn(沪锌), pb(沪铅), ni(沪镍), sn(沪锡), au(沪金), ag(沪银)

### 化工 (7个品种)
- **DCE**: pp(聚丙烯), l(聚乙烯), v(PVC), eg(乙二醇)
- **SHFE**: ru(橡胶), bu(沥青)
- **CZCE**: MA(甲醇)

### 黑色系 (5个品种)
- **DCE**: i(铁矿石), j(焦炭), jm(焦煤)
- **SHFE**: rb(螺纹钢), hc(热卷)

### 能源 (3个品种)
- **SHFE**: sc(原油), fu(燃料油)
- **INE**: lu(低硫燃料油)

## 🔄 工作流程

### 1. API查询主力合约
```
1. 连接TqSDK API
2. 使用 query_cont_quotes() 查询连续合约
3. 获取第一个合约（主力合约）
4. 缓存结果避免重复查询
```

### 2. 智能回退机制
```
如果API查询失败：
1. 根据交易所规则推断主力合约
2. SHFE: 通常下个月或下下个月
3. DCE/CZCE: 通常1月、5月、9月
4. 返回推断的合约代码
```

### 3. 错误处理
```
1. 单个品种失败不影响其他品种
2. API连接失败自动回退到推断模式
3. 详细的错误日志和警告信息
4. 优雅的异常处理
```

## 🎯 优势对比

### 原始静态配置
- ❌ 合约代码可能过时
- ❌ 需要手动更新
- ❌ 可能分析到非主力合约
- ❌ 影响分析准确性

### 新动态获取
- ✅ 实时获取真实主力合约
- ✅ 自动跟随市场变化
- ✅ 确保分析最活跃合约
- ✅ 提高分析准确性
- ✅ 智能回退机制
- ✅ 完善的错误处理

## 📋 配置选项

### 启用/禁用主力合约
```python
# 启用主力合约（默认）
config = PresetAnalysisConfig(
    use_main_contracts=True,  # 动态获取主力合约
    auth_username="your_username",
    auth_password="your_password"
)

# 禁用主力合约（使用静态配置）
config = PresetAnalysisConfig(
    use_main_contracts=False  # 使用静态合约配置
)
```

### 认证配置
```python
# 自定义认证信息
config = create_main_contracts_analysis_config(
    product_groups=['metals'],
    auth_username="your_username",
    auth_password="your_password"
)
```

## ⚠️ 注意事项

### 1. API连接要求
- 需要有效的TqSDK认证信息
- 需要网络连接获取实时数据
- 首次运行会连接API获取主力合约

### 2. 缓存机制
- 同一会话中会缓存主力合约信息
- 避免重复API查询提高效率
- 重启程序会重新获取最新主力合约

### 3. 回退机制
- API连接失败时自动使用推断模式
- 推断结果可能不如API查询准确
- 建议确保API连接正常

## 🔮 未来扩展

### 1. 定时更新
- 定期检查主力合约变化
- 自动更新分析配置
- 主力合约切换提醒

### 2. 历史主力合约
- 支持获取历史时点的主力合约
- 回测时使用当时的主力合约
- 更准确的历史分析

### 3. 流动性评估
- 评估合约流动性指标
- 自动选择最优交易合约
- 流动性风险提示

---

**升级时间**: 2025年7月27日  
**核心改进**: 动态获取真实主力合约  
**测试状态**: ✅ 全部通过  
**向后兼容**: ✅ 完全兼容静态配置  
**主要优势**: 实时准确、自动更新、智能回退
