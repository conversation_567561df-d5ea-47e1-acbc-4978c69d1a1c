from tqsdk import TqApi, TqAuth, TqKq
from tqsdk.ta import MA, ATR, BOLL
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import pandas as pd
import numpy as np

# 初始化 TqApi
# api = TqApi(auth=TqAuth("YOUR_USERNAME", "YOUR_PASSWORD"))
api = TqApi(TqKq(), auth="wolfquant2023,Qiai1301")
# 获取 CZCE.OI501 的 1 分钟 K 线数据
klines = api.get_kline_serial("CZCE.OI501", 60)

# 计算指标
ma5 = MA(klines, 5)
boll = BOLL(klines, 20, 2)
atr = ATR(klines, 14)


def plot_candlestick(ax, df):
    ax.clear()
    width = 0.8
    width2 = 0.1

    up = df[df.close > df.open]
    down = df[df.close < df.open]
    equal = df[df.close == df.open]

    # 上涨K线 - 红色
    ax.bar(up.index, up.close - up.open, width, bottom=up.open, color='#FF3333')
    ax.vlines(up.index, up.low, up.high, color='#FF3333', linewidth=1)

    # 下跌K线 - 绿色
    ax.bar(down.index, down.close - down.open, width, bottom=down.open, color='#33FF33')
    ax.vlines(down.index, down.low, down.high, color='#33FF33', linewidth=1)

    # 开盘价等于收盘价的K线
    ax.bar(equal.index, width2, width, bottom=equal.open - width2 / 2, color='#00FFFF')
    ax.vlines(equal.index, equal.low, equal.high, color='#00FFFF', linewidth=1)

    # 绘制 MA5 线
    ax.plot(df.index, ma5.ma, color='#FFFF00', linewidth=1, label='MA5')

    # 绘制布林带
    ax.plot(df.index, boll.mid, color='#FFFFFF', linestyle='--', linewidth=1, label='BOLL Mid')
    ax.plot(df.index, boll.top, color='#FF00FF', linestyle='--', linewidth=1, label='BOLL Upper')
    ax.plot(df.index, boll.bottom, color='#00FFFF', linestyle='--', linewidth=1, label='BOLL Lower')

    # 绘制 CCCC, LLLL, HHHH 线
    ax.plot(df.index, df['close'].shift(1), color='#FFFFFF', linestyle='--', linewidth=2, label='CCCC')
    ax.plot(df.index, df[['open', 'close']].shift(1).min(axis=1), color='#00FF00', linestyle='--', linewidth=1,
            label='LLLL')
    ax.plot(df.index, df[['open', 'close']].shift(1).max(axis=1), color='#FF00FF', linestyle='--', linewidth=1,
            label='HHHH')

    # 绘制其他指标线
    for i in range(len(df) - 1):
        if df['close'].iloc[i] > boll.top.iloc[i] and df['close'].iloc[i] > ma5.ma.iloc[i]:
            ax.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['high'].iloc[i]], color='#00FFFF')
        elif df['close'].iloc[i] < boll.bottom.iloc[i] and df['close'].iloc[i] < ma5.ma.iloc[i]:
            ax.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['high'].iloc[i]], color='#FFFF00')

    ax.set_title('CZCE.OI501 1分钟K线图', color='white')
    ax.grid(True, linestyle=':', alpha=0.3, color='#555555')
    ax.legend(loc='upper left', facecolor='black', edgecolor='white', labelcolor='white')
    plt.xticks(rotation=45, color='white')
    plt.yticks(color='white')
    ax.set_facecolor('black')  # 设置图表背景为黑色
    ax.spines['bottom'].set_color('white')
    ax.spines['top'].set_color('white')
    ax.spines['right'].set_color('white')
    ax.spines['left'].set_color('white')
    ax.tick_params(axis='x', colors='white')
    ax.tick_params(axis='y', colors='white')
#
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
fig, ax = plt.subplots(figsize=(15, 8))
plt.style.use('dark_background')
fig.patch.set_facecolor('black')  # 设置整个图形的背景为黑色
ax.set_facecolor('black')  # 设置绘图区域的背景为黑色

def update(frame):
    api.wait_update()
    if api.is_changing(klines.iloc[-1], "datetime"):
        df = pd.DataFrame({
            "open": klines.open,
            "high": klines.high,
            "low": klines.low,
            "close": klines.close
        })

        plot_candlestick(ax, df)


def show_history_data():
    df = pd.DataFrame({
            "open": klines.open,
            "high": klines.high,
            "low": klines.low,
            "close": klines.close
        })

    plot_candlestick(ax, df)
    plt.tight_layout()
    plt.show()

# show_history_data()

ani = FuncAnimation(fig, update, interval=100, cache_frame_data=False)
plt.show()

print('now start main loop')
# 主循环
try:
    while True:
        print('waiting for update...')
        api.wait_update()
except KeyboardInterrupt:
    print("程序已终止")
finally:
    api.close()