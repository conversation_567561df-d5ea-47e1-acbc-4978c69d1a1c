"""
测试主力合约获取功能
"""

import sys
import time
from loguru import logger
from multi_contract_config import *


def test_main_contract_fetcher():
    """测试主力合约获取器"""
    print("=" * 60)
    print("测试主力合约获取器")
    print("=" * 60)
    
    try:
        # 设置日志
        logger.remove()
        logger.add(lambda msg: print(msg, end=""), level="INFO", colorize=True)
        
        # 测试单个合约获取
        print("1. 测试单个合约获取:")
        with MainContractFetcher() as fetcher:
            # 测试几个主要品种
            test_products = [
                ("SHFE", "cu", "沪铜"),
                ("DCE", "m", "豆粕"),
                ("CZCE", "OI", "菜籽油"),
            ]
            
            for exchange, product_id, name in test_products:
                main_contract = fetcher.get_main_contract(exchange, product_id)
                if main_contract:
                    print(f"  ✅ {name} ({exchange}.{product_id}): {main_contract}")
                else:
                    print(f"  ❌ {name} ({exchange}.{product_id}): 获取失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 主力合约获取器测试失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False


def test_batch_main_contracts():
    """测试批量获取主力合约"""
    print("=" * 60)
    print("测试批量获取主力合约")
    print("=" * 60)
    
    try:
        # 设置日志
        logger.remove()
        logger.add(lambda msg: print(msg, end=""), level="INFO", colorize=True)
        
        # 测试批量获取
        print("2. 测试批量获取主力合约:")
        
        # 测试金属品种
        main_contracts = get_current_main_contracts(['metals'])
        
        print(f"✅ 成功获取 {len(main_contracts)} 个金属主力合约:")
        for key, symbol in main_contracts.items():
            exchange, product_id = key.split('.')
            # 查找品种名称
            name = "未知"
            for group_info in PRODUCT_INFO.values():
                for ex, pid, pname, _, _, _ in group_info:
                    if ex == exchange and pid == product_id:
                        name = pname
                        break
            print(f"  {name:8} ({key:12}): {symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量获取测试失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False


def test_main_contracts_config():
    """测试主力合约配置创建"""
    print("=" * 60)
    print("测试主力合约配置创建")
    print("=" * 60)
    
    try:
        # 设置日志
        logger.remove()
        logger.add(lambda msg: print(msg, end=""), level="INFO", colorize=True)
        
        print("3. 测试主力合约配置创建:")
        
        # 创建主力合约配置
        config = create_main_contracts_analysis_config(
            product_groups=['metals'],
            d_value_range=(20, 31),  # 小范围测试
            kline_data_length=100,   # 少量数据
            output_dir="test_main_contracts"
        )
        
        print(f"✅ 主力合约配置创建成功:")
        print(f"  合约数量: {len(config.contracts)}")
        print(f"  参数范围: {config.d_value_range}")
        print(f"  数据长度: {config.kline_data_length}")
        print(f"  输出目录: {config.output_dir}")
        
        print(f"\n📊 包含的合约:")
        for contract in config.contracts:
            print(f"  {contract.name:12}: {contract.symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ 主力合约配置测试失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False


def test_preset_configs():
    """测试预设配置"""
    print("=" * 60)
    print("测试预设配置")
    print("=" * 60)
    
    try:
        # 设置日志
        logger.remove()
        logger.add(lambda msg: print(msg, end=""), level="INFO", colorize=True)
        
        print("4. 测试预设配置:")
        
        # 测试主力合约预设配置
        config = get_preset_config('quick_test')
        
        print(f"✅ 预设配置获取成功:")
        print(f"  配置名称: quick_test")
        print(f"  合约数量: {len(config.contracts)}")
        print(f"  参数范围: {config.d_value_range}")
        
        print(f"\n📊 包含的合约:")
        for contract in config.contracts:
            print(f"  {contract.name:15}: {contract.symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预设配置测试失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False


def test_contract_inference():
    """测试合约推断功能"""
    print("=" * 60)
    print("测试合约推断功能")
    print("=" * 60)
    
    try:
        print("5. 测试合约推断功能:")
        
        # 创建测试用的获取器
        fetcher = MainContractFetcher()
        
        # 测试推断功能
        test_cases = [
            ("SHFE", "cu"),
            ("DCE", "m"),
            ("CZCE", "OI"),
        ]
        
        for exchange, product_id in test_cases:
            inferred = fetcher._infer_main_contract(exchange, product_id)
            print(f"  {exchange}.{product_id} 推断结果: {inferred}")
        
        return True
        
    except Exception as e:
        print(f"❌ 合约推断测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("=" * 60)
    print("测试错误处理")
    print("=" * 60)
    
    try:
        print("6. 测试错误处理:")
        
        # 测试无效品种
        try:
            main_contracts = get_current_main_contracts(['invalid_group'])
            print(f"  无效品种组处理: 返回 {len(main_contracts)} 个合约")
        except Exception as e:
            print(f"  无效品种组处理: 正确抛出异常")
        
        # 测试API连接失败的情况（使用错误的认证信息）
        try:
            with MainContractFetcher("invalid_user", "invalid_pass") as fetcher:
                result = fetcher.get_main_contract("SHFE", "cu")
            print(f"  错误认证处理: 意外成功")
        except Exception as e:
            print(f"  错误认证处理: 正确抛出异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("主力合约功能测试")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("主力合约获取器", test_main_contract_fetcher),
        ("批量获取主力合约", test_batch_main_contracts),
        ("主力合约配置创建", test_main_contracts_config),
        ("预设配置", test_preset_configs),
        ("合约推断功能", test_contract_inference),
        ("错误处理", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed >= total - 1:  # 允许1个测试失败（可能是网络问题）
        print("🎉 主力合约功能基本正常！")
        print("\n📋 使用说明:")
        print("1. 使用 get_current_main_contracts() 获取当前主力合约")
        print("2. 使用 create_main_contracts_analysis_config() 创建分析配置")
        print("3. 预设配置默认使用主力合约")
        print("4. 如果API连接失败，会自动回退到推断模式")
    else:
        print("⚠️  部分测试失败，可能是网络连接问题")
    
    return passed >= total - 1


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
