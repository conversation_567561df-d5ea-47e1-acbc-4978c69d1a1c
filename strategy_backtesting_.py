import pandas as pd
import numpy as np
from dataclasses import dataclass
from typing import Tuple, List, Dict
from datetime import datetime
import math


@dataclass
class BacktestStats:
    """回测统计结果数据类"""
    # 基础信息
    strategy_name: str
    start_date: datetime
    end_date: datetime
    test_days: int
    test_periods: int
    signal_count: int
    executed_signals: int
    contract_unit: int

    # 资金统计
    initial_capital: float
    final_equity: float
    max_equity: float
    min_equity: float
    avg_capital_usage: float
    max_capital_usage: float
    max_capital_usage_time: datetime
    margin_ratio: float
    leverage_ratio: float

    # 收益统计
    total_returns: float
    returns_long: float
    returns_short: float
    returns_rate: float
    returns_rate_long: float
    returns_rate_short: float
    annual_returns: float
    monthly_returns: float
    annual_compound_returns: float
    monthly_compound_returns: float
    avg_margin_returns: float
    max_drawdown: float
    max_drawdown_time: datetime
    max_drawdown_rate: float
    max_drawdown_rate_time: datetime
    longest_drawdown_periods: int
    longest_drawdown_time: Tuple[datetime, datetime]

    # 交易统计
    total_trades: int
    long_trades: int
    short_trades: int
    win_rate: float
    win_rate_long: float
    win_rate_short: float
    profit_factor: float
    profit_factor_long: float
    profit_factor_short: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    r_squared: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float

    # 盈亏统计
    total_profit: float
    total_loss: float
    max_single_profit: float
    max_single_profit_time: datetime
    max_single_loss: float
    max_single_loss_time: datetime
    avg_profit: float
    avg_loss: float
    avg_profit_per_trade: float

    # 持仓统计
    avg_holding_periods: float
    max_holding_periods: int
    avg_profit_holding_periods: float
    avg_loss_holding_periods: float
    avg_position_size: float
    max_position_size: int

    # 交易成本
    commission_total: float
    commission_per_return: float
    slippage_total: float
    slippage_per_return: float
    total_turnover: float

# [Previous code remains the same until backtest method]
class TradingStrategy:
    def __init__(self, lookback_period: int = 20):
        self.lookback_period = lookback_period
        self.positions = []
        self.trades = []

    def calculate_macd(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        # 计算快线和慢线的EMA
        exp12 = data['close'].ewm(span=12, adjust=False).mean()
        exp26 = data['close'].ewm(span=26, adjust=False).mean()

        # 计算DIFF和DEA
        data['diff'] = exp12 - exp26
        data['dea'] = data['diff'].ewm(span=9, adjust=False).mean()
        data['macd'] = 2 * (data['diff'] - data['dea'])

        return data

    def calculate_mean_reversion(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算均值回归信号"""
        # 计算移动平均和标准差
        data['ma'] = data['close'].rolling(window=self.lookback_period).mean()
        data['std'] = data['close'].rolling(window=self.lookback_period).std()

        # 计算上下轨道
        data['upper_band'] = data['ma'] + 2 * data['std']
        data['lower_band'] = data['ma'] - 2 * data['std']

        # 生成均值回归信号
        data['mean_reversion_long'] = (data['close'] < data['lower_band']).astype(int)
        data['mean_reversion_short'] = (data['close'] > data['upper_band']).astype(int)

        return data

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        # 计算指标
        data = self.calculate_macd(data)
        data = self.calculate_mean_reversion(data)

        # 初始化信号列
        data['signal'] = 0

        # MACD穿越0轴信号
        data['macd_cross_up'] = (data['macd'] > 0) & (data['macd'].shift(1) <= 0)
        data['macd_cross_down'] = (data['macd'] < 0) & (data['macd'].shift(1) >= 0)

        # DIFF穿越DEA信号
        data['diff_cross_up'] = (data['diff'] > data['dea']) & (data['diff'].shift(1) <= data['dea'].shift(1))
        data['diff_cross_down'] = (data['diff'] < data['dea']) & (data['diff'].shift(1) >= data['dea'].shift(1))

        # 生成最终信号
        for i in range(1, len(data)):
            # 开多仓条件
            if data.iloc[i]['macd_cross_up']:
                data.iloc[i, data.columns.get_loc('signal')] = 1

            # 开空仓条件
            elif data.iloc[i]['macd_cross_down']:
                data.iloc[i, data.columns.get_loc('signal')] = -1

            # 平多仓条件
            elif (data.iloc[i]['mean_reversion_short'] or data.iloc[i]['diff_cross_down']) and \
                    data.iloc[i - 1]['signal'] == 1:
                data.iloc[i, data.columns.get_loc('signal')] = 0

            # 平空仓条件
            elif (data.iloc[i]['mean_reversion_long'] or data.iloc[i]['diff_cross_up']) and \
                    data.iloc[i - 1]['signal'] == -1:
                data.iloc[i, data.columns.get_loc('signal')] = 0

            # 保持前一个信号
            else:
                data.iloc[i, data.columns.get_loc('signal')] = data.iloc[i - 1]['signal']

        return data
class EnhancedBacktester:
    # [Previous methods remain the same]
    def __init__(self,
                 initial_capital: float = 500000,
                 margin_ratio: float = 0.12,
                 commission_rate: float = 0.0103,
                 slippage: float = 0,
                 contract_unit: int = 10):
        self.initial_capital = initial_capital
        self.margin_ratio = margin_ratio
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.contract_unit = contract_unit
        self.trades = []
        self.equity_curve = []

    def calculate_macd(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        exp12 = data['close'].ewm(span=12, adjust=False).mean()
        exp26 = data['close'].ewm(span=26, adjust=False).mean()

        data['diff'] = exp12 - exp26
        data['dea'] = data['diff'].ewm(span=9, adjust=False).mean()
        data['macd'] = 2 * (data['diff'] - data['dea'])

        return data

    def calculate_mean_reversion(self, data: pd.DataFrame, lookback_period: int = 20) -> pd.DataFrame:
        """计算均值回归信号"""
        data['ma'] = data['close'].rolling(window=lookback_period).mean()
        data['std'] = data['close'].rolling(window=lookback_period).std()

        data['upper_band'] = data['ma'] + 2 * data['std']
        data['lower_band'] = data['ma'] - 2 * data['std']

        data['mean_reversion_long'] = (data['close'] < data['lower_band']).astype(int)
        data['mean_reversion_short'] = (data['close'] > data['upper_band']).astype(int)

        return data

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        data = self.calculate_macd(data)
        data = self.calculate_mean_reversion(data)

        data['signal'] = 0
        data['position_size'] = 0

        # MACD交叉信号
        data['macd_cross_up'] = (data['macd'] > 0) & (data['macd'].shift(1) <= 0)
        data['macd_cross_down'] = (data['macd'] < 0) & (data['macd'].shift(1) >= 0)

        # DIFF/DEA交叉信号
        data['diff_cross_up'] = (data['diff'] > data['dea']) & (data['diff'].shift(1) <= data['dea'].shift(1))
        data['diff_cross_down'] = (data['diff'] < data['dea']) & (data['diff'].shift(1) >= data['dea'].shift(1))

        position = 0
        for i in range(1, len(data)):
            # 计算可用资金和最大仓位
            equity = self.equity_curve[-1] if self.equity_curve else self.initial_capital
            max_contracts = math.floor(equity * 0.95 / (data.iloc[i]['close'] * self.contract_unit * self.margin_ratio))

            if data.iloc[i]['macd_cross_up']:
                position = max_contracts
                data.iloc[i, data.columns.get_loc('signal')] = 1
            elif data.iloc[i]['macd_cross_down']:
                position = -max_contracts
                data.iloc[i, data.columns.get_loc('signal')] = -1
            elif (data.iloc[i]['mean_reversion_short'] or data.iloc[i]['diff_cross_down']) and \
                    data.iloc[i - 1]['signal'] == 1:
                position = 0
                data.iloc[i, data.columns.get_loc('signal')] = 0
            elif (data.iloc[i]['mean_reversion_long'] or data.iloc[i]['diff_cross_up']) and \
                    data.iloc[i - 1]['signal'] == -1:
                position = 0
                data.iloc[i, data.columns.get_loc('signal')] = 0
            else:
                data.iloc[i, data.columns.get_loc('signal')] = data.iloc[i - 1]['signal']

            data.iloc[i, data.columns.get_loc('position_size')] = position

        return data

    def calculate_returns(self, trade: Dict) -> Dict:
        """计算单笔交易的收益"""
        position = trade['position']
        entry_price = trade['entry_price']
        exit_price = trade['exit_price']
        position_size = trade['position_size']

        # 计算毛收益
        gross_profit = (exit_price - entry_price) * position * position_size * self.contract_unit

        # 计算交易成本
        turnover = (entry_price + exit_price) * abs(position_size) * self.contract_unit
        commission = turnover * self.commission_rate
        slippage_cost = self.slippage * abs(position_size) * 2

        # 计算净收益
        net_profit = gross_profit - commission - slippage_cost

        return {
            **trade,
            'gross_profit': gross_profit,
            'commission': commission,
            'slippage': slippage_cost,
            'net_profit': net_profit,
            'turnover': turnover
        }

    def calculate_drawdown_stats(self, equity_curve: List[float]) -> Tuple[float, int, Tuple[int, int]]:
        """计算回撤统计"""
        max_drawdown = 0
        curr_drawdown = 0
        drawdown_length = 0
        max_drawdown_length = 0
        peak_idx = 0
        max_dd_start = 0
        max_dd_end = 0

        for i, equity in enumerate(equity_curve):
            if equity > equity_curve[peak_idx]:
                peak_idx = i
                curr_drawdown = 0
                drawdown_length = 0
            else:
                curr_drawdown = equity_curve[peak_idx] - equity
                drawdown_length += 1

                if curr_drawdown > max_drawdown:
                    max_drawdown = curr_drawdown
                    max_dd_start = peak_idx
                    max_dd_end = i

                if drawdown_length > max_drawdown_length:
                    max_drawdown_length = drawdown_length

        return max_drawdown, max_drawdown_length, (max_dd_start, max_dd_end)

    def analyze_trades(self, data: pd.DataFrame, trades: List[Dict]) -> BacktestStats:
        """分析交易统计指标"""
        # 基础统计
        stats = {
            'strategy_name': 'MACD_MeanReversion',
            # 'start_date': data.index[0],
            # 'end_date': data.index[-1],
            # 'test_days': len(data.index.unique().date),
            'test_periods': len(data),
            'signal_count': len([t for t in trades if 'entry_time' in t]),
            'executed_signals': len([t for t in trades if 'exit_time' in t]),
            'contract_unit': self.contract_unit
        }

        # 资金统计
        equity_curve = np.array(self.equity_curve)
        stats.update({
            'initial_capital': self.initial_capital,
            'final_equity': equity_curve[-1],
            'max_equity': np.max(equity_curve),
            'min_equity': np.min(equity_curve),
            'avg_capital_usage': np.mean([t.get('margin_occupied', 0) for t in trades]),
            'max_capital_usage': np.max([t.get('margin_occupied', 0) for t in trades]),
            'margin_ratio': self.margin_ratio,
            'leverage_ratio': 1 / self.margin_ratio
        })

        # 收益统计
        total_returns = equity_curve[-1] - self.initial_capital
        returns_rate = total_returns / self.initial_capital
        # years = (data.index[-1] - data.index[0]).days / 365.25

        stats.update({
            'total_returns': total_returns,
            'returns_rate': returns_rate,
            'annual_returns': returns_rate / years,
            'monthly_returns': returns_rate / (years * 12),
            'annual_compound_returns': (1 + returns_rate) ** (1 / years) - 1,
            'monthly_compound_returns': (1 + returns_rate) ** (1 / (years * 12)) - 1
        })

        # 回撤统计
        max_dd, max_dd_length, (max_dd_start, max_dd_end) = self.calculate_drawdown_stats(equity_curve)
        stats.update({
            'max_drawdown': max_dd,
            'max_drawdown_rate': max_dd / np.max(equity_curve[:max_dd_end]),
            'longest_drawdown_periods': max_dd_length
        })

        # 交易统计
        completed_trades = [t for t in trades if 'net_profit' in t]
        winning_trades = [t for t in completed_trades if t['net_profit'] > 0]
        losing_trades = [t for t in completed_trades if t['net_profit'] <= 0]

        stats.update({
            'total_trades': len(completed_trades),
            'win_rate': len(winning_trades) / len(completed_trades) if completed_trades else 0,
            'profit_factor': abs(sum(t['net_profit'] for t in winning_trades)) / abs(sum(t['net_profit'] for t in losing_trades)) if losing_trades else float('inf'),
            'max_consecutive_wins': self.get_max_consecutive(completed_trades, True),
            'max_consecutive_losses': self.get_max_consecutive(completed_trades, False),
            'sharpe_ratio': self.calculate_sharpe_ratio(equity_curve),
            'sortino_ratio': self.calculate_sortino_ratio(equity_curve),
            'calmar_ratio': stats['annual_returns'] / abs(stats['max_drawdown_rate']) if stats['max_drawdown_rate'] != 0 else float('inf')
        })

        # 盈亏统计
        stats.update({
            'total_profit': sum(t['net_profit'] for t in winning_trades),
            'total_loss': sum(t['net_profit'] for t in losing_trades),
            'max_single_profit': max((t['net_profit'] for t in winning_trades), default=0),
            'max_single_loss': min((t['net_profit'] for t in losing_trades), default=0),
            'avg_profit': np.mean([t['net_profit'] for t in winning_trades]) if winning_trades else 0,
            'avg_loss': np.mean([t['net_profit'] for t in losing_trades]) if losing_trades else 0
        })

        # 持仓统计
        stats.update({
            'avg_holding_periods': np.mean([t['holding_periods'] for t in completed_trades]),
            'max_holding_periods': max((t['holding_periods'] for t in completed_trades), default=0),
            'avg_position_size': np.mean([abs(t['position_size']) for t in completed_trades]),
            'max_position_size': max((abs(t['position_size']) for t in completed_trades), default=0)
        })

        # 交易成本
        stats.update({
            'commission_total': sum(t['commission'] for t in completed_trades),
            'slippage_total': sum(t['slippage'] for t in completed_trades),
            'total_turnover': sum(t['turnover'] for t in completed_trades)
        })

        return BacktestStats(**stats)

    def get_max_consecutive(self, trades: List[Dict], winning: bool = True) -> int:
        """计算最大连续盈利/亏损次数"""
        max_consecutive = 0
        current_consecutive = 0

        for trade in trades:
            if (winning and trade['net_profit'] > 0) or (not winning and trade['net_profit'] <= 0):
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def calculate_sharpe_ratio(self, equity_curve: np.ndarray, risk_free_rate: float = 0.02) -> float:
        """计算夏普比率"""
        if len(equity_curve) < 2:
            return 0

        returns = np.diff(equity_curve) / equity_curve[:-1]
        excess_returns = returns - risk_free_rate / 252  # 假设252个交易日

        if np.std(excess_returns) == 0:
            return 0

        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)

    def calculate_sortino_ratio(self, equity_curve: np.ndarray, risk_free_rate: float = 0.02) -> float:
        """计算索提诺比率"""
        if len(equity_curve) < 2:
            return 0

        returns = np.diff(equity_curve) / equity_curve[:-1]
        excess_returns = returns - risk_free_rate / 252

        downside_returns = excess_returns[excess_returns < 0]
        if len(downside_returns) == 0 or np.std(downside_returns) == 0:
            return 0

        return np.mean(excess_returns) / np.std(downside_returns) * np.sqrt(252)

    def backtest(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict], BacktestStats]:
        """执行回测"""
        data = self.generate_signals(data)
        self.equity_curve = [self.initial_capital]
        trades = []
        position = 0
        entry_price = 0
        entry_time = None
        entry_index = 0

        for i in range(1, len(data)):
            current_row = data.iloc[i]
            prev_row = data.iloc[i - 1]

            # 当前持仓价值
            position_value = position * current_row['close'] * self.contract_unit

            # 检查是否需要开仓或平仓
            if current_row['signal'] != prev_row['signal']:
                # 平仓
                if position != 0:
                    trade = {
                        'entry_time': entry_time,
                        'exit_time': current_row.name,
                        'entry_price': entry_price,
                        'exit_price': current_row['close'],
                        'position': 1 if position > 0 else -1,
                        'position_size': abs(position),
                        'holding_periods': i - entry_index,
                        'margin_occupied': abs(position_value) * self.margin_ratio
                    }

                    # 计算收益
                    trade = self.calculate_returns(trade)
                    trades.append(trade)

                    # 更新权益
                    self.equity_curve.append(self.equity_curve[-1] + trade['net_profit'])

                # 开仓
                if current_row['signal'] != 0:
                    position = current_row['position_size']
                    entry_price = current_row['close']
                    entry_time = current_row.name
                    entry_index = i
                else:
                    position = 0

            # 如果没有交易，复制上一个权益值
            if len(self.equity_curve) <= i:
                self.equity_curve.append(self.equity_curve[-1])

        # 强制平掉最后的持仓
        if position != 0:
            trade = {
                'entry_time': entry_time,
                'exit_time': data.index[-1],
                'entry_price': entry_price,
                'exit_price': data.iloc[-1]['close'],
                'position': 1 if position > 0 else -1,
                'position_size': abs(position),
                'holding_periods': len(data) - entry_index,
                'margin_occupied': abs(position_value) * self.margin_ratio
            }
            trade = self.calculate_returns(trade)
            trades.append(trade)
            self.equity_curve.append(self.equity_curve[-1] + trade['net_profit'])

        # 分析交易统计
        stats = self.analyze_trades(data, trades)

        return data, trades, stats


def run_backtest(data_path: str,
                 initial_capital: float = 500000,
                 margin_ratio: float = 0.12,
                 commission_rate: float = 0.0103,
                 slippage: float = 0,
                 contract_unit: int = 10) -> Tuple[pd.DataFrame, List[Dict], BacktestStats]:
    """运行回测的便捷函数"""
    # 读取数据
    data = pd.read_csv(data_path, parse_dates=['datetime'], index_col='datetime')

    # 初始化回测器
    backtest = EnhancedBacktester(
        initial_capital=initial_capital,
        margin_ratio=margin_ratio,
        commission_rate=commission_rate,
        slippage=slippage,
        contract_unit=contract_unit
    )

    # 执行回测
    results_data, trades, stats = backtest.backtest(data)

    return results_data, trades, stats


def plot_results(data: pd.DataFrame, trades: List[Dict], stats: BacktestStats):
    """绘制回测结果图表"""
    import matplotlib.pyplot as plt
    import seaborn as sns

    plt.style.use('seaborn')
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), gridspec_kw={'height_ratios': [3, 1, 1]})

    # 绘制价格和均线
    ax1.plot(data.index, data['close'], label='Close Price', alpha=0.7)
    ax1.plot(data.index, data['ma'], label='MA', alpha=0.7)
    ax1.fill_between(data.index, data['upper_band'], data['lower_band'], alpha=0.1)

    # 标记交易点
    for trade in trades:
        if trade['position'] > 0:
            ax1.scatter(trade['entry_time'], trade['entry_price'], color='g', marker='^', s=100)
            ax1.scatter(trade['exit_time'], trade['exit_price'], color='r', marker='v', s=100)
        else:
            ax1.scatter(trade['entry_time'], trade['entry_price'], color='r', marker='v', s=100)
            ax1.scatter(trade['exit_time'], trade['exit_price'], color='g', marker='^', s=100)

    ax1.set_title('Trading Signals & Price')
    ax1.legend()

    # 绘制MACD
    ax2.plot(data.index, data['diff'], label='DIFF')
    ax2.plot(data.index, data['dea'], label='DEA')
    ax2.bar(data.index, data['macd'], label='MACD', alpha=0.5)
    ax2.legend()
    ax2.set_title('MACD Indicator')

    # 绘制权益曲线
    ax3.plot(data.index[:len(stats.equity_curve)], stats.equity_curve, label='Equity')
    ax3.set_title('Equity Curve')
    ax3.legend()

    plt.tight_layout()
    plt.show()

    # 打印主要统计指标
    print(f"\nBacktest Results ({stats.start_date.date()} - {stats.end_date.date()}):")
    print(f"Total Return: {stats.total_returns:,.2f} ({stats.returns_rate:.2%})")
    print(f"Annual Return: {stats.annual_returns:.2%}")
    print(f"Sharpe Ratio: {stats.sharpe_ratio:.2f}")
    print(f"Max Drawdown: {stats.max_drawdown_rate:.2%}")
    print(f"Win Rate: {stats.win_rate:.2%}")
    print(f"Profit Factor: {stats.profit_factor:.2f}")
    print(f"Total Trades: {stats.total_trades}")


if __name__ == '__main__':
    # 使用示例
    data_path = 'data.csv'
    results_data, trades, stats = run_backtest(
        data_path,
        initial_capital=500000,
        margin_ratio=0.12,
        commission_rate=0.0103,
        slippage=1,
        contract_unit=10
    )

    plot_results(results_data, trades, stats)