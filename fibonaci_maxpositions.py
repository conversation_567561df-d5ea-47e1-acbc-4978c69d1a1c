'''
使用fibonaci数列来计算最大持仓
1. 计算fibonaci数列
2.时间间隔15秒, 最小时间周期15秒, 最大时间周期15分钟
3.设定多单和空单的合计最大持仓为500.
4.根据时间周期计算每个时间周期的最大持仓,采用fibonaci数列的逆向计算,即最小周期仓位最大
5.结果生成一个字典:{15:{'long':500, 'short': 500}, 30:{'long': 300, 'short': 300}, 60:{'long': 200, 'short': 200}, 120:{'long': 100, 'short': 100}, 180:{'long': 50, 'short': 50}, 300:{'long': 30, 'short': 30}, 600:{'long': 20, 'short': 20}, 900:{'long': 10,
'short': 10}} 类似这样.
'''
import json

def save_positions_to_json(max_positions, filename='positions.json'):
    """保存持仓设置到JSON文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(max_positions, f, indent=2)
        print(f"持仓设置已保存到 {filename}")
    except Exception as e:
        print(f"保存文件失败: {e}")

def generate_timeframes():
    """生成时间周期列表，从15秒到5分钟，以15秒为间隔"""
    return [i * 15 for i in range(1, 21)]  # 15秒到5分钟(300秒)

def fibonacci_sequence(n):
    """生成斐波那契数列"""
    fib = [1, 1]
    for i in range(2, n):
        fib.append(fib[i-1] + fib[i-2])
    return fib

def calculate_max_positions(timeframes, total_positions=500):
    """
    计算每个时间周期的最大持仓
    使用反向斐波那契数列，使得较小周期获得较大仓位
    """
    n = len(timeframes)
    fib = fibonacci_sequence(n)
    total_fib = sum(fib)

    # 反转斐波那契数列，使小周期对应大数字
    fib.reverse()

    # 计算每个周期的基础仓位
    position_per_fib = total_positions / total_fib

    # 生成结果字典
    max_positions = {}
    for i, tf in enumerate(timeframes):
        pos = max(1, int(fib[i] * position_per_fib))
        max_positions[tf] = {'long': pos, 'short': pos}

    return max_positions


def main():
    timeframes = generate_timeframes()
    max_positions = calculate_max_positions(timeframes)

    print("\n各周期最大持仓量：")
    total_long_short = 0
    for tf in sorted(max_positions.keys()):
        pos = max_positions[tf]
        total_long_short += pos['long'] + pos['short']
        minutes = tf // 60
        seconds = tf % 60
        time_str = f"{minutes}分" if minutes > 0 else ""
        time_str += f"{seconds}秒" if seconds > 0 else ""
        print(f"{time_str}: 多头={pos['long']}, 空头={pos['short']}")
    print(f"\n总持仓量: {total_long_short}")

    # 保存到JSON文件
    save_positions_to_json(max_positions)

def calculate_arithmetic_positions(timeframes, total_positions=500):
    """
    使用等差数列计算每个时间周期的最大持仓
    较小周期获得较大仓位
    """
    n = len(timeframes)

    # 计算等差数列的首项和差值
    # 设最小周期仓位为 a，差值为 d
    # 则 a + (a-d) + (a-2d) + ... + (a-(n-1)d) = total_positions/2
    # 且 a-(n-1)d = 1 (确保最小仓位为1)

    d = (total_positions/2 - n) / (n * (n-1) / 2)
    a = 1 + (n-1) * d

    # 生成结果字典
    max_positions = {}
    for i, tf in enumerate(timeframes):
        pos = max(1, int(a - i * d))
        max_positions[tf] = {'long': pos, 'short': pos}

    return max_positions


def calculate_arithmetic_positions(timeframes, total_positions=500):
    """
    使用变化的等差数列计算每个时间周期的最大持仓
    特点：相邻周期的差值递减
    """
    n = len(timeframes)
    max_positions = {}

    # 计算差值序列
    differences = []
    for i in range(n - 1):
        diff = max(1, int((n - i) * total_positions / (n * n)))
        differences.append(diff)

    # 从最大值开始，根据差值序列计算每个周期的仓位
    positions = [total_positions // 2]  # 第一个周期的仓位
    for diff in differences:
        next_pos = max(1, positions[-1] - diff)
        positions.append(next_pos)

    # 生成结果字典
    for i, tf in enumerate(timeframes):
        pos = positions[i]
        max_positions[tf] = {'long': pos, 'short': pos}

    return max_positions

def save_positions_to_json(positions_fib, positions_arith, filename='positions_combined.json'):
    """保存两种计算方式的持仓设置到JSON文件"""
    combined = {
        'fibonacci': positions_fib,
        'arithmetic': positions_arith
    }
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(combined, f, indent=2)
        print(f"持仓设置已保存到 {filename}")
    except Exception as e:
        print(f"保存文件失败: {e}")
def normalize_positions(max_positions, total_limit=500):
    """
    Normalize the total positions to stay within the specified limit.
    """
    total = sum(pos['long'] + pos['short'] for pos in max_positions.values())
    scaling_factor = total_limit / total

    for tf in max_positions:
        max_positions[tf]['long'] = max(1, int(max_positions[tf]['long'] * scaling_factor))
        max_positions[tf]['short'] = max(1, int(max_positions[tf]['short'] * scaling_factor))

    return max_positions

def main():
    timeframes = generate_timeframes()

    # Calculate positions using Fibonacci
    positions_fib = calculate_max_positions(timeframes)
    positions_fib = normalize_positions(positions_fib)

    # Calculate positions using Arithmetic
    positions_arith = calculate_arithmetic_positions(timeframes)
    positions_arith = normalize_positions(positions_arith)




    # Calculate and print total long and short positions for Fibonacci
    total_fib_long = sum(pos['long'] for pos in positions_fib.values())
    total_fib_short = sum(pos['short'] for pos in positions_fib.values())
    print(f"Fibonacci Total Long: {total_fib_long}, Total Short: {total_fib_short}")

    # Calculate and print total long and short positions for Arithmetic
    total_arith_long = sum(pos['long'] for pos in positions_arith.values())
    total_arith_short = sum(pos['short'] for pos in positions_arith.values())
    print(f"Arithmetic Total Long: {total_arith_long}, Total Short: {total_arith_short}")

    # Print and save results
    save_positions_to_json(positions_fib, positions_arith)

# def main():
#     timeframes = generate_timeframes()
#
#     # 计算两种方式的持仓
#     positions_fib = calculate_max_positions(timeframes)
#     positions_arith = calculate_arithmetic_positions(timeframes)
#
#     # 打印斐波那契序列结果
#     print("\n斐波那契序列最大持仓量：")
#     total_fib = 0
#     for tf in sorted(positions_fib.keys()):
#         pos = positions_fib[tf]
#         total_fib += pos['long'] + pos['short']
#         minutes = tf // 60
#         seconds = tf % 60
#         time_str = f"{minutes}分" if minutes > 0 else ""
#         time_str += f"{seconds}秒" if seconds > 0 else ""
#         print(f"{time_str}: 多头={pos['long']}, 空头={pos['short']}")
#     print(f"斐波那契总持仓量: {total_fib}")
#
#     # 打印等差序列结果
#     print("\n等差序列最大持仓量：")
#     total_arith = 0
#     for tf in sorted(positions_arith.keys()):
#         pos = positions_arith[tf]
#         total_arith += pos['long'] + pos['short']
#         minutes = tf // 60
#         seconds = tf % 60
#         time_str = f"{minutes}分" if minutes > 0 else ""
#         time_str += f"{seconds}秒" if seconds > 0 else ""
#         print(f"{time_str}: 多头={pos['long']}, 空头={pos['short']}")
#     print(f"等差序列总持仓量: {total_arith}")
#
#     # 保存两种结果到JSON
#     save_positions_to_json(positions_fib, positions_arith)

if __name__ == "__main__":
    main()
