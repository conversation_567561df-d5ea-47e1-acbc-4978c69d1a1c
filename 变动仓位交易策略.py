import asyncio
import numpy as np
from datetime import datetime, time

from tqsdk import Tq<PERSON><PERSON>, Tq<PERSON><PERSON>, TargetPosTask, TqSim, tafunc
from tqsdk.objs import Quote

# --- Strategy Parameters ---
SYMBOL = "SHFE.rb2410"  # Example: Shanghai Futures Exchange, rebar contract
BAR_DURATION_SECONDS = 24 * 60 * 60  # Daily K-lines
# BAR_DURATION_SECONDS = 4 * 60 * 60  # 4-hour K-lines

# KDJ Variant Parameters
N = 9
M1 = 3
M2 = 3

# Risk Control Parameters
ATR_PERIOD = 14  # For volatility filter and dynamic sizing adjustment
ATR_AVG_PERIOD = 20  # For comparing current ATR
STOP_LOSS_RANGE_PERIOD = 14  # For HHV/LLV range in stop loss
STOP_LOSS_ATR_FACTOR = 0.3  # Factor for stop loss range calculation (strategy says 0.3 of HHV-LLV range)

MAX_ACCOUNT_RISK_PERCENTAGE = 0.80  # e.g., 80% of total funds
DAILY_MAX_LOSS_PERCENTAGE = 0.02  # 2% of account total
SYMBOL_MAX_LOSS_PERCENTAGE = 0.05  # 5% of allocated capital (interpreted as position value)
ACCOUNT_DRAWDOWN_LIMIT_PERCENTAGE = 0.10  # 10% drawdown

# --- Global State Variables ---
# (These will be stored in a class or dict to manage state per symbol if multi-symbol later)
position_state = {
    "long_entry_price": 0.0,
    "short_entry_price": 0.0,
    "current_long_pos": 0,
    "current_short_pos": 0,
    "peak_equity": 0.0,
    "start_of_day_equity": 0.0,
    "last_day_checked": None
}


async def calculate_indicators(klines, N, M1, M2):
    """Calculates all necessary KDJ variant indicators."""
    indicators = {}
    if len(klines.close) < max(N, M1, M2, 20) + 5:  # Ensure enough data, +5 for safety margin
        # print(f"Not enough kline data: {len(klines.close)}")
        return None

    close = klines.close.to_numpy()
    high = klines.high.to_numpy()
    low = klines.low.to_numpy()

    # Fix for potential all-NaN slices if tafunc returns Series and then we try to use .iloc[-1]
    # Ensure inputs to tafunc are numpy arrays.

    llv_low_n = tafunc.llv(low, N)
    hhv_high_n = tafunc.hhv(high, N)

    denominator = hhv_high_n - llv_low_n
    # Replace 0 in denominator with a small number to avoid division by zero, or np.nan
    denominator[denominator == 0] = np.nan

    rsvu = (close - llv_low_n) / denominator * 100
    rsvd = (hhv_high_n - close) / denominator * 100

    # Handle NaNs that can propagate from rsvu/rsvd if not enough data for initial HHV/LLV
    rsvu = np.nan_to_num(rsvu, nan=50.0)  # Replace NaNs with a neutral value like 50
    rsvd = np.nan_to_num(rsvd, nan=50.0)

    indicators["KU"] = tafunc.ma(rsvu, M1)
    indicators["DU"] = tafunc.ma(indicators["KU"], M2)
    indicators["JU"] = 3 * indicators["KU"] - 2 * indicators["DU"]

    indicators["KDA"] = tafunc.ma(rsvd, M1)
    indicators["UD"] = tafunc.ma(indicators["KDA"], M2)
    indicators["JD"] = 3 * indicators["KDA"] - 2 * indicators["UD"]

    # Ensure JU and JD have enough data points before MA calculation
    if len(indicators["JU"]) < 20 or len(indicators["JD"]) < 20:
        # print("Not enough JU/JD data for MA(20)")
        return None

    ma_ju_20 = tafunc.ma(indicators["JU"], 20)
    ma_jd_20 = tafunc.ma(indicators["JD"], 20)

    indicators["涨上限仓"] = np.floor(ma_ju_20 / 10) * 10
    indicators["跌上限仓"] = np.floor(ma_jd_20 / 10) * 10

    indicators["涨下限仓"] = (indicators["涨上限仓"] - indicators["跌上限仓"]) / 2 + 25
    indicators["跌下限仓"] = (indicators["跌上限仓"] - indicators["涨上限仓"]) / 2 + 25

    indicators["JJ"] = np.floor(indicators["JU"] / 10) * 10
    indicators["标准仓"] = np.floor(indicators["DU"] / 10) * 10  # Used for dynamic sizing adjustment reference

    # ATR for volatility based adjustments
    indicators["ATR"] = tafunc.atr(high, low, close, ATR_PERIOD)
    if len(indicators["ATR"]) < ATR_AVG_PERIOD:
        # print("Not enough ATR data for MA(ATR_AVG_PERIOD)")
        return None
    indicators["AVG_ATR"] = tafunc.ma(indicators["ATR"], ATR_AVG_PERIOD)

    # For stop loss: HHV(HIGH,N) - LLV(LOW,N)
    indicators["STOP_LOSS_HHV"] = tafunc.hhv(high, STOP_LOSS_RANGE_PERIOD)
    indicators["STOP_LOSS_LLV"] = tafunc.llv(low, STOP_LOSS_RANGE_PERIOD)

    return indicators


async def main_strategy():
    # api = TqApi(TqSim(), auth=TqAuth("your_shinny_account", "your_shinny_password")) # For real trading
    api = TqApi(TqSim(init_balance=1000000), auth=TqAuth("bigwolf", "ftp123"))  # For simulation

    klines = await api.get_kline_serial(SYMBOL, BAR_DURATION_SECONDS, data_length=200)  # Increased data_length
    quote = await api.get_quote(SYMBOL)
    account = await api.get_account()

    # Target position tasks for independent long and short positions
    # TQSDK manages positions as net. To simulate separate long/short, we need to be careful.
    # However, for futures, long and short are distinct.
    # TargetPosTask manages a *net* position for a symbol.
    # To have "多空共存", it means the *signals* for long and short are independent.
    # The final position will be net. E.g., if long signal says +2 and short signal says -1, net target is +1.
    # If the strategy implies truly separate long and short books (not typical for single futures account),
    # then TqSdk's TargetPosTask might not directly model it without custom logic.
    # Let's assume "多空共存" means independent signal generation, but net position execution.
    # The strategy document however describes opening long and short separately and their sizes.
    # This suggests we might need to track "logical" long and short positions.
    # For now, let's calculate desired long size and desired short size, then net them.
    # This is a common interpretation for "hedging" or "multi-signal" systems.

    # Let's assume the strategy implies desired *absolute* quantities for long and short,
    # and the final position is the sum. E.g. target_long_vol = 2, target_short_vol = 1. Net = +1.
    # This means target_pos_task will manage the net position.

    target_pos_task = TargetPosTask(api, SYMBOL)

    position_state["peak_equity"] = account.balance
    position_state["start_of_day_equity"] = account.balance
    position_state["last_day_checked"] = datetime.now().date()

    print("Strategy started. Waiting for market data...")

    while True:
        await api.wait_update()
        if api.is_changing(klines.iloc[-1], "datetime"):  # New bar

            current_time = datetime.fromtimestamp(klines.iloc[-1].datetime / 1e9)
            print(f"\nNew Bar Detected: {current_time}")

            # --- Update daily/peak equity ---
            today = datetime.now().date()
            if today != position_state["last_day_checked"]:
                position_state["start_of_day_equity"] = account.balance
                position_state["last_day_checked"] = today
                print(f"New trading day. Start of day equity: {position_state['start_of_day_equity']:.2f}")

            position_state["peak_equity"] = max(position_state["peak_equity"], account.balance)

            # --- Calculate Indicators ---
            # Need to ensure klines is updated before passing to indicator calculation
            # The new bar check `api.is_changing(klines.iloc[-1], "datetime")` ensures klines has the new bar

            # Use .copy() to avoid issues with underlying data changing during calculations
            # Pass klines dataframe, not series, to tafunc if possible or ensure series are right length

            # The tafunc functions expect pandas Series.
            # klines itself is a TqDataFrame. klines.close is a TqSeries.
            # Ensure enough data for all calculations:
            required_data_length = max(N, M1, M2, 20, ATR_PERIOD, ATR_AVG_PERIOD, STOP_LOSS_RANGE_PERIOD) + 5  # +5 buffer
            if len(klines.id) < required_data_length:
                print(f"Not enough kline data for calculation ({len(klines.id)} < {required_data_length}). Skipping cycle.")
                continue

            inds = await calculate_indicators(klines, N, M1, M2)
            if inds is None:
                print("Indicator calculation failed or not enough data. Skipping cycle.")
                continue

            # --- Get latest values from indicators ---
            # We operate on the recently closed bar, so [-2] for most recent full bar, [-1] for current forming bar
            # Let's assume signals are on CLOSE of bar, so use [-1] after new bar forms.
            # Tafunc results are numpy arrays, so [-1] is the latest.

            # Safety check for indicator array lengths
            min_len = float('inf')
            valid_inds = True
            for k, v_arr in inds.items():
                if not isinstance(v_arr, np.ndarray) or len(v_arr) == 0:
                    print(f"Indicator {k} is empty or not an array.")
                    valid_inds = False
                    break
                min_len = min(min_len, len(v_arr))

            if not valid_inds or min_len == 0:
                print("Some indicators are not properly calculated. Skipping cycle.")
                continue

            idx = -1  # Use the latest completed values

            jj = inds["JJ"][idx]
            du = inds["DU"]  # Full series for trend check
            ud = inds["UD"]  # Full series for trend check
            涨上限仓_val = inds["涨上限仓"][idx]
            跌上限仓_val = inds["跌上限仓"][idx]
            涨下限仓_val = inds["涨下限仓"][idx]
            跌下限仓_val = inds["跌下限仓"][idx]
            atr_val = inds["ATR"][idx]
            avg_atr_val = inds["AVG_ATR"][idx]
            stop_loss_hhv_val = inds["STOP_LOSS_HHV"][idx]
            stop_loss_llv_val = inds["STOP_LOSS_LLV"][idx]

            # --- Current Position Info ---
            current_pos = await api.get_position(SYMBOL)
            position_state["current_long_pos"] = current_pos.pos_long
            position_state["current_short_pos"] = current_pos.pos_short

            target_long_percentage = 0.0
            target_short_percentage = 0.0  # Represent as positive percentage

            # --- Stop Loss Logic ---
            # This should take precedence and can override trading signals
            stop_loss_triggered_long = False
            stop_loss_triggered_short = False
            stop_range = (stop_loss_hhv_val - stop_loss_llv_val) * STOP_LOSS_ATR_FACTOR
            if np.isnan(stop_range) or stop_range <= 0: stop_range = quote.last_price * 0.01  # Fallback

            if position_state["current_long_pos"] > 0 and position_state["long_entry_price"] > 0:
                long_stop_price = position_state["long_entry_price"] - stop_range
                if quote.last_price < long_stop_price:
                    print(f"Long Stop-Loss triggered! Entry: {position_state['long_entry_price']:.2f}, Stop: {long_stop_price:.2f}, Current: {quote.last_price:.2f}")
                    stop_loss_triggered_long = True

            if position_state["current_short_pos"] > 0 and position_state["short_entry_price"] > 0:
                short_stop_price = position_state["short_entry_price"] + stop_range
                if quote.last_price > short_stop_price:
                    print(f"Short Stop-Loss triggered! Entry: {position_state['short_entry_price']:.2f}, Stop: {short_stop_price:.2f}, Current: {quote.last_price:.2f}")
                    stop_loss_triggered_short = True

            if stop_loss_triggered_long:
                target_long_percentage = -1  # Signal to close
            if stop_loss_triggered_short:
                target_short_percentage = -1  # Signal to close

            # --- Long Trade Logic ---
            if not stop_loss_triggered_long:
                du_down_trend = len(du) >= 3 and du[idx] < du[idx - 1] and du[idx - 1] < du[idx - 2]

                # 1. Open condition
                if jj > 跌下限仓_val and 涨下限仓_val < 跌下限仓_val:
                    if position_state["current_long_pos"] == 0:  # Only if no current long
                        print(f"Long Open Signal: JJ({jj:.2f}) > 跌下限仓({跌下限仓_val:.2f}) AND 涨下限仓({涨下限仓_val:.2f}) < 跌下限仓({跌下限仓_val:.2f})")
                        target_long_percentage = (涨上限仓_val - 涨下限仓_val) / 100.0
                        position_state["long_entry_price"] = quote.last_price  # Record entry for new position
                # 2. Add position condition (adjust to target)
                elif jj > 跌下限仓_val and 涨下限仓_val > 跌下限仓_val:
                    print(f"Long Add/Adjust Signal: JJ({jj:.2f}) > 跌下限仓({跌下限仓_val:.2f}) AND 涨下限仓({涨下限仓_val:.2f}) > 跌下限仓({跌下限仓_val:.2f})")
                    target_long_percentage = (涨上限仓_val - 涨下限仓_val) / 100.0
                    if position_state["current_long_pos"] == 0:  # If adding turns into opening
                        position_state["long_entry_price"] = quote.last_price
                # 3. Reduce position condition (adjust to target) - This implies target size is smaller
                elif jj < 涨上限仓_val and 涨下限仓_val < 跌下限仓_val:  # This condition might be rare or overlap
                    print(f"Long Reduce Signal: JJ({jj:.2f}) < 涨上限仓({涨上限仓_val:.2f}) AND 涨下限仓({涨下限仓_val:.2f}) < 跌下限仓({跌下限仓_val:.2f})")
                    target_long_percentage = (涨上限仓_val - 涨下限仓_val) / 100.0
                # 4. Close condition
                elif (jj < 跌下限仓_val or du_down_trend) and position_state["current_long_pos"] > 0:
                    print(f"Long Close Signal: JJ({jj:.2f}) < 跌下限仓({跌下限仓_val:.2f}) or DU declining: {du_down_trend}")
                    target_long_percentage = -1  # Signal to close
                    position_state["long_entry_price"] = 0.0
                elif position_state["current_long_pos"] > 0:  # Hold existing long position if no other signal
                    target_long_percentage = (涨上限仓_val - 涨下限仓_val) / 100.0  # Maintain based on current logic for adjustment

            # --- Short Trade Logic ---
            if not stop_loss_triggered_short:
                ud_up_trend = len(ud) >= 3 and ud[idx] > ud[idx - 1] and ud[idx - 1] > ud[idx - 2]

                # 1. Open condition
                if jj < 跌上限仓_val and 涨下限仓_val < 跌下限仓_val:
                    if position_state["current_short_pos"] == 0:
                        print(f"Short Open Signal: JJ({jj:.2f}) < 跌上限仓({跌上限仓_val:.2f}) AND 涨下限仓({涨下限仓_val:.2f}) < 跌下限仓({跌下限仓_val:.2f})")
                        target_short_percentage = (跌上限仓_val - 跌下限仓_val) / 100.0
                        position_state["short_entry_price"] = quote.last_price
                # 2. Add position condition (adjust to target)
                elif jj < 涨上限仓_val and 涨下限仓_val > 跌下限仓_val:  # This condition is for short, rule says JJ < ZSX
                    print(f"Short Add/Adjust Signal: JJ({jj:.2f}) < 涨上限仓({涨上限仓_val:.2f}) AND 涨下限仓({涨下限仓_val:.2f}) > 跌下限仓({跌下限仓_val:.2f})")
                    target_short_percentage = (跌上限仓_val - 跌下限仓_val) / 100.0
                    if position_state["current_short_pos"] == 0:
                        position_state["short_entry_price"] = quote.last_price
                # 3. Reduce position condition (adjust to target)
                elif jj > 跌下限仓_val and 涨下限仓_val > 跌下限仓_val:  # This condition for short, rule says JJ > DXX
                    print(f"Short Reduce Signal: JJ({jj:.2f}) > 跌下限仓({跌下限仓_val:.2f}) AND 涨下限仓({涨下限仓_val:.2f}) > 跌下限仓({跌下限仓_val:.2f})")
                    target_short_percentage = (跌上限仓_val - 跌下限仓_val) / 100.0
                # 4. Close condition
                elif (jj > 涨上限仓_val or ud_up_trend) and position_state["current_short_pos"] > 0:
                    print(f"Short Close Signal: JJ({jj:.2f}) > 涨上限仓({涨上限仓_val:.2f}) or UD rising: {ud_up_trend}")
                    target_short_percentage = -1  # Signal to close
                    position_state["short_entry_price"] = 0.0
                elif position_state["current_short_pos"] > 0:  # Hold existing short
                    target_short_percentage = (跌上限仓_val - 跌下限仓_val) / 100.0

            # Clamp percentages (e.g., KDJ values can go wild)
            target_long_percentage = max(0, target_long_percentage) if target_long_percentage != -1 else -1
            target_short_percentage = max(0, target_short_percentage) if target_short_percentage != -1 else -1

            # Apply Max Caps from 涨上限仓 and 跌上限仓 themselves
            # "多头最大仓位百分比 = 涨上限仓"
            # "空头最大仓位百分比 = 跌上限仓"
            # Ensure these are positive if used as caps
            max_long_cap_pct = max(0, 涨上限仓_val) / 100.0
            max_short_cap_pct = max(0, 跌上限仓_val) / 100.0

            if target_long_percentage > 0:
                target_long_percentage = min(target_long_percentage, max_long_cap_pct)
            if target_short_percentage > 0:
                target_short_percentage = min(target_short_percentage, max_short_cap_pct)

            # --- Volatility Filter for *New* Positions ---
            # (Applied if we are about to open a new position)
            is_opening_long = (target_long_percentage > 0 and position_state["current_long_pos"] == 0)
            is_opening_short = (target_short_percentage > 0 and position_state["current_short_pos"] == 0)

            if not np.isnan(atr_val) and not np.isnan(avg_atr_val) and avg_atr_val > 0:
                if atr_val < 0.5 * avg_atr_val:
                    if is_opening_long:
                        print(f"ATR ({atr_val:.2f}) < 50% of Avg ATR ({avg_atr_val:.2f}). Avoid new long.")
                        target_long_percentage = 0
                    if is_opening_short:
                        print(f"ATR ({atr_val:.2f}) < 50% of Avg ATR ({avg_atr_val:.2f}). Avoid new short.")
                        target_short_percentage = 0
                elif atr_val > 1.5 * avg_atr_val:
                    if is_opening_long:
                        print(f"ATR ({atr_val:.2f}) > 150% of Avg ATR ({avg_atr_val:.2f}). Reduce new long by 50%.")
                        target_long_percentage *= 0.5
                    if is_opening_short:
                        print(f"ATR ({atr_val:.2f}) > 150% of Avg ATR ({avg_atr_val:.2f}). Reduce new short by 50%.")
                        target_short_percentage *= 0.5

            # --- Dynamic Position Sizing (ATR based, applied to existing/new targets) ---
            # "在高波动市场，减少基础仓位的20%" , "在低波动市场，增加基础仓位的10%"
            # This rule refers to "标准仓" to adjust "基础仓位".
            # Interpretation: If volatility is high/low, adjust the calculated target percentages.
            pos_adjustment_factor = 1.0
            if not np.isnan(atr_val) and not np.isnan(avg_atr_val) and avg_atr_val > 0:
                if atr_val > 1.5 * avg_atr_val:  # High Volatility (consistent with above)
                    pos_adjustment_factor = 0.8  # Reduce by 20%
                    print(f"High Volatility Detected. Reducing targets by 20%.")
                elif atr_val < 0.5 * avg_atr_val:  # Low Volatility (consistent with above)
                    pos_adjustment_factor = 1.1  # Increase by 10%
                    print(f"Low Volatility Detected. Increasing targets by 10%.")

            if target_long_percentage > 0: target_long_percentage *= pos_adjustment_factor
            if target_short_percentage > 0: target_short_percentage *= pos_adjustment_factor

            # --- Overall Risk Control ---
            # 1. Daily Max Loss
            if (position_state["start_of_day_equity"] - account.balance) / position_state["start_of_day_equity"] > DAILY_MAX_LOSS_PERCENTAGE:
                print(f"Daily Max Loss ({DAILY_MAX_LOSS_PERCENTAGE * 100}%) hit. Closing all positions.")
                target_long_percentage = -1
                target_short_percentage = -1

            # 2. Account Drawdown
            if (position_state["peak_equity"] - account.balance) / position_state["peak_equity"] > ACCOUNT_DRAWDOWN_LIMIT_PERCENTAGE:
                print(f"Account Drawdown ({ACCOUNT_DRAWDOWN_LIMIT_PERCENTAGE * 100}%) hit. Halving positions.")
                if target_long_percentage > 0: target_long_percentage *= 0.5
                if target_short_percentage > 0: target_short_percentage *= 0.5

            # 3. Symbol Max Loss (on current position value)
            # For futures, float_profit reflects this. Position value is harder to define precisely without margin details.
            # Let's use float_profit vs initial margin of position.
            # This is tricky for combined long/short. Let's check net position's float profit.
            # If current_pos.float_profit is significantly negative related to its 'cost'.
            # A simpler proxy: if current_pos.float_profit < -SYMBOL_MAX_LOSS_PERCENTAGE * account.balance (of total balance for simplicity)
            # This rule is hard to apply precisely as "allocated capital" isn't fixed for one symbol.
            # Let's use a simplified check on overall position profit/loss for the symbol
            if current_pos.pos_long > 0 or current_pos.pos_short > 0:  # If any position exists
                # Approximate position value: (pos_long * open_avg_price_long + pos_short * open_avg_price_short) * mult
                # The strategy says "当单个品种亏损超过分配资金的5%时". Assume "分配资金" is the margin used.
                # current_pos.position_cost or current_pos.open_cost might be useful.
                # Let's check `current_pos.float_profit`. If it's very negative compared to margin.
                # `current_pos.margin` is the margin occupied by this symbol's positions.
                if current_pos.margin > 0 and current_pos.float_profit < -SYMBOL_MAX_LOSS_PERCENTAGE * current_pos.margin:
                    print(f"Symbol Max Loss ({SYMBOL_MAX_LOSS_PERCENTAGE * 100}% of margin) hit. Halving positions.")
                    if target_long_percentage > 0: target_long_percentage *= 0.5
                    if target_short_percentage > 0: target_short_percentage *= 0.5

            # --- Position Balancing (Total Account Risk) ---
            # Current interpretation is target_long_percentage and target_short_percentage are % of equity for each side.
            # The rule is "当多空头寸总和超过账户最大风险承受能力(例如总资金的80%)时，按比例缩小仓位"
            # This implies the *sum of allocations* shouldn't exceed max risk.

            current_total_target_percentage = 0
            if target_long_percentage > 0: current_total_target_percentage += target_long_percentage
            if target_short_percentage > 0: current_total_target_percentage += target_short_percentage

            if current_total_target_percentage > MAX_ACCOUNT_RISK_PERCENTAGE:
                print(f"Total target allocation ({current_total_target_percentage * 100:.2f}%) exceeds max account risk ({MAX_ACCOUNT_RISK_PERCENTAGE * 100:.2f}%). Scaling down.")
                scale_factor = MAX_ACCOUNT_RISK_PERCENTAGE / current_total_target_percentage
                if target_long_percentage > 0: target_long_percentage *= scale_factor
                if target_short_percentage > 0: target_short_percentage *= scale_factor

            # --- Convert Percentages to Target Volumes ---
            final_target_long_volume = 0
            final_target_short_volume = 0  # Positive number for volume

            # Available equity for calculations (static_balance is more stable)
            equity = account.static_balance
            contract_value = quote.last_price * quote.volume_multiple

            # Use a reasonable fixed margin rate if quote.margin is too low or unreliable
            # For example, use 0.15 (15%) if quote.margin is not specific enough
            margin_rate_estimate = quote.margin if quote.margin > 0.01 else 0.15

            if contract_value > 0 and margin_rate_estimate > 0:
                cost_per_lot = contract_value * margin_rate_estimate
                if cost_per_lot > 0:
                    if target_long_percentage == -1:  # Close signal
                        final_target_long_volume = 0
                    elif target_long_percentage > 0:
                        final_target_long_volume = int(equity * target_long_percentage / cost_per_lot)

                    if target_short_percentage == -1:  # Close signal
                        final_target_short_volume = 0
                    elif target_short_percentage > 0:
                        final_target_short_volume = int(equity * target_short_percentage / cost_per_lot)
                else:
                    print("Warning: Cost per lot is zero, cannot calculate target volumes.")
            else:
                print("Warning: Contract value or margin rate is zero, cannot calculate target volumes.")

            # Net position for TargetPosTask
            net_target_volume = final_target_long_volume - final_target_short_volume

            print(f"Target Long %: {target_long_percentage:.4f} -> Vol: {final_target_long_volume}")
            print(f"Target Short %: {target_short_percentage:.4f} -> Vol: {final_target_short_volume}")
            print(f"Net Target Volume: {net_target_volume}")
            print(f"Current Position: Long={current_pos.pos_long}, Short={current_pos.pos_short}, Net={current_pos.pos}")
            print(f"Account: Balance={account.balance:.2f}, Equity={account.static_balance:.2f}, Available={account.available:.2f}")

            # Update global state for entry prices if positions were indeed changed towards these targets
            # This is tricky as TargetPosTask is async. For simplicity, if target > 0, we assume entry might happen.
            # A more robust way is to check trades.
            if final_target_long_volume > 0 and position_state["current_long_pos"] == 0:
                # This condition means we intend to open or increase a long position.
                # If it was 0, then this is a new entry.
                if current_pos.pos_long == 0:  # check actual current position before setting entry price
                    pass  # position_state["long_entry_price"] already set at signal generation
            elif final_target_long_volume == 0:
                position_state["long_entry_price"] = 0.0

            if final_target_short_volume > 0 and position_state["current_short_pos"] == 0:
                if current_pos.pos_short == 0:
                    pass  # position_state["short_entry_price"] already set at signal generation
            elif final_target_short_volume == 0:
                position_state["short_entry_price"] = 0.0

            await target_pos_task.set_target_volume(net_target_volume)

        # Update current position state based on actual fills (if needed for more precision)
        # This is complex as fills can be partial / delayed.
        # For now, relying on `api.get_position()` at the start of the loop.

        # Check for manual intervention or other external position changes
        if api.is_changing(account) or (quote and api.is_changing(quote)):
            # print("Account or Quote changed, re-evaluating...")
            pass  # Main loop will re-evaluate on next bar or relevant change


if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(main_strategy())
    except KeyboardInterrupt:
        print("Strategy interrupted by user.")
    finally:
        # Close API connection if main_strategy finishes or is interrupted
        # This needs the `api` object from `main_strategy`.
        # A common pattern is `api.close()` in a finally block within `main_strategy` itself.
        # For this structure, it's harder. If TqApi is global, it could be closed here.
        print("Strategy finished or terminated.")