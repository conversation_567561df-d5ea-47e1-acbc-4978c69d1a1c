import time
from utils.utils import tradingTime
test = False

from tqsdk import TqAccount
from tqsdk import TqApi, TqKq
from datetime import date
# from accounts_thd import tgjythd as acct


product = 'OI'
interval = 60*3
bklimit = 5
sklimit = 3
single_volume = 1
print('api初始化...')
api = TqApi(TqKq(), auth="bigwolf,ftp123")


while True:
    # if True:

    if tradingTime():
        try:
            api = TqApi(TqKq(), auth="bigwolf,ftp123")
            time.sleep(60)
            print('交易时间。。。')
        except:
            print('api connect failed, please check...')
            # if apistatus:
            #     print('api 连接成功。。。')
            #     runstrategy()
            #
            # else:
            #     print('api 连接失败，请检查。')


    else:
        print('not in the trading time...', time.asctime())
        api.close()
        print(api)
        time.sleep(60)
