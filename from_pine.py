from tqsdk import Tq<PERSON><PERSON>, TqAuth, TargetPosTask
from tqsdk.ta import ATR, EMA, SAR, BOLL
import datetime


class MultiOrderBreakoutStrategy:
    def __init__(self, api, symbol, risk_per_trade=1.0, lookback=20, breakout_mult=2.0,
                 stop_loss_percent=2.0, max_positions=5, atr_period=14, ma_len=100):
        self.api = api
        self.symbol = symbol
        self.risk_per_trade = risk_per_trade
        self.lookback = lookback
        self.breakout_mult = breakout_mult
        self.stop_loss_percent = stop_loss_percent
        self.max_positions = max_positions
        self.atr_period = atr_period
        self.ma_len = ma_len

        self.quote = api.get_quote(symbol)
        self.klines = api.get_kline_serial(symbol, duration_seconds=60 * 60 * 24)  # Daily K-lines

        self.target_pos = TargetPosTask(api, symbol)
        self.open_positions = 0

        # Indicators
        self.boll = BOLL(self.klines, self.lookback, self.breakout_mult)
        self.atr = ATR(self.klines, self.atr_period)
        self.sar = SAR(self.klines, 0.02, 0.02, 0.2)
        self.ma = EMA(self.klines.close, self.ma_len)
        self.atr_sma = EMA(self.atr.atr, 100)

    def run(self):
        while True:
            self.api.wait_update()

            if self.api.is_changing(self.klines.iloc[-1], "datetime"):
                self.on_bar()

    def on_bar(self):
        close = self.klines.close.iloc[-1]
        upper = self.boll.upper.iloc[-1]
        middle = self.boll.mid.iloc[-1]
        lower = self.boll.lower.iloc[-1]
        sar = self.sar.sar.iloc[-1]
        ma = self.ma.iloc[-1]
        atr = self.atr.atr.iloc[-1]
        atr_sma = self.atr_sma.iloc[-1]

        # Entry conditions
        long_condition = (close > upper) and (close > sar) and (close > ma)

        # Exit conditions
        exit_condition = ((self.klines.close.iloc[-2] > middle) and (close <= middle)) or \
                         ((self.klines.close.iloc[-2] > sar) and (close <= sar))

        # Dynamic position sizing
        position_size = int((self.api.get_account().balance * self.risk_per_trade / 100) /
                            (close * self.stop_loss_percent / 100))

        # Strategy execution
        if long_condition and self.open_positions < self.max_positions and atr > atr_sma and position_size > 0:
            self.target_pos.set_target_volume(position_size)
            self.open_positions += 1

            # Set stop loss
            stop_price = close * (1 - self.stop_loss_percent / 100)
            self.api.insert_order(symbol=self.symbol, direction="SELL", offset="CLOSE",
                                  volume=position_size, price=stop_price, type="STOP")

        # Close all positions on exit condition
        if exit_condition and self.open_positions > 0:
            self.target_pos.set_target_volume(0)
            self.open_positions = 0


def run_strategy():
    api = TqApi(auth=TqAuth("wolfquant", "ftp123"))

    try:
        strategy = MultiOrderBreakoutStrategy(api, symbol="SHFE.au2012")  # Example: Gold futures
        strategy.run()
    finally:
        api.close()


if __name__ == "__main__":
    run_strategy()