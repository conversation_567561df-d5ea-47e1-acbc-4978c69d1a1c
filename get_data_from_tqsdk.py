from tqsdk import TqApi, TqKq
import click
import pandas_ta as ta


@click.command()
@click.option("-s", "--symbol", default="OI")
@click.option('-i', '--interval', default=900)
def main(symbol: str, interval: int):
    api = TqApi(TqKq(), auth=("bigwolf,ftp123"), disable_print=True)
    #
    # productid = 'OI'
    # interval = 60

    symbol = api.query_cont_quotes(product_id=symbol)[0]

    klines = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964).dropna()
    klines.to_csv(f"{symbol}_{interval}.csv")
    api.close()
    # while True:
    #     api.wait_update()
    #     if api.is_changing(klines.iloc[-1], "close"):
    #         RSI = ta.rsi(klines.close, 6).tolist()
    #         print(RSI[-40:])
    #
    #         api.close()

if __name__ == "__main__":
    main()
