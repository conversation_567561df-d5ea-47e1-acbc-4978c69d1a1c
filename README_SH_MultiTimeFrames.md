# SH多时间周期策略重构说明

## 概述

本项目将原来分散的 `TimeRoseMA_cross_SH_1m_speak.py`, `TimeRoseMA_cross_SH_3m_speak.py`, `TimeRoseMA_cross_SH_5m_speak.py`, `TimeRoseMA_cross_SH_15m_speak.py` 四个文件重构为一个统一的多时间周期策略文件，实现了共用API和统一管理。

## 文件结构

### 主要文件

1. **`TimeRoseMA_cross_SH_MultiTimeFrames.py`** - 主策略文件
   - 包含多种运行模式
   - 支持单个或多个时间周期
   - 提供统一的API接口

2. **`example_usage_SH_MultiTimeFrames.py`** - 使用示例
   - 展示不同的使用方式
   - 提供配置示例

3. **`README_SH_MultiTimeFrames.md`** - 本说明文件

### 原有文件（可保留作为备份）

- `TimeRoseMA_cross__SH_1m_speak.py`
- `TimeRoseMA_cross__SH_3m_speak.py`
- `TimeRoseMA_cross__SH_5m_speak.py`
- `TimeRoseMA_cross__SH_15m_speak.py`

## 主要特性

### 1. 统一API管理
- 所有时间周期共享配置
- 减少重复代码
- 统一的错误处理

### 2. 多种运行模式

#### Independent模式（推荐）
- 每个时间周期独立进程
- 最高稳定性
- 避免API冲突

#### Threaded模式
- 多线程并行处理
- 每个线程独立API
- 资源占用适中

#### Optimized模式
- 单API多时间周期
- 需要修改ma_cross函数
- 最低资源占用

### 3. 灵活配置
- 可自定义时间周期
- 可调整持仓限制
- 可修改交易量

## 使用方法

### 1. 命令行使用

```bash
# 运行所有时间周期（独立模式）
python TimeRoseMA_cross_SH_MultiTimeFrames.py independent

# 运行单个时间周期
python TimeRoseMA_cross_SH_MultiTimeFrames.py 1m
python TimeRoseMA_cross_SH_MultiTimeFrames.py 3m
python TimeRoseMA_cross_SH_MultiTimeFrames.py 5m
python TimeRoseMA_cross_SH_MultiTimeFrames.py 15m

# 不同运行模式
python TimeRoseMA_cross_SH_MultiTimeFrames.py threaded
python TimeRoseMA_cross_SH_MultiTimeFrames.py optimized
```

### 2. 代码中使用

```python
from TimeRoseMA_cross_SH_MultiTimeFrames import run_sh_multi_timeframe_strategy

# 运行所有时间周期
run_sh_multi_timeframe_strategy("independent")

# 运行单个时间周期
from TimeRoseMA_cross_SH_MultiTimeFrames import run_single_timeframe
run_single_timeframe("5m")
```

### 3. 自定义配置

```python
from TimeRoseMA_cross_SH_MultiTimeFrames import (
    TimeFrameConfig, 
    SimpleMultiTimeFrameStrategy
)

# 自定义时间周期配置
configs = [
    TimeFrameConfig(interval=60, bklimit=2, sklimit=2, single_volume=1),
    TimeFrameConfig(interval=300, bklimit=3, sklimit=3, single_volume=2),
]

# 创建策略
strategy = SimpleMultiTimeFrameStrategy(
    symbol='SH',
    timeframe_configs=configs,
    auth="your_auth_string"
)

strategy.run_strategy()
```

## 配置参数

### TimeFrameConfig参数

- `interval`: 时间间隔（秒）
  - 60 = 1分钟
  - 180 = 3分钟
  - 300 = 5分钟
  - 900 = 15分钟

- `bklimit`: 多单持仓限制
- `sklimit`: 空单持仓限制
- `single_volume`: 单次交易量

### 认证配置

默认使用 `"quant_ggh,Qiai1301"`，可根据需要修改。

## 迁移指南

### 从原有文件迁移

1. **备份原有文件**
   ```bash
   mkdir backup
   mv TimeRoseMA_cross__SH_*m_speak.py backup/
   ```

2. **使用新的统一文件**
   ```bash
   # 替代原来的1分钟策略
   python TimeRoseMA_cross_SH_MultiTimeFrames.py 1m
   
   # 替代原来运行所有策略的方式
   python TimeRoseMA_cross_SH_MultiTimeFrames.py independent
   ```

3. **验证功能**
   - 确认所有时间周期正常运行
   - 检查交易信号是否正确
   - 验证持仓管理功能

## 优势

1. **代码维护性**
   - 减少重复代码
   - 统一的配置管理
   - 更容易添加新时间周期

2. **运行效率**
   - 共享API连接（某些模式）
   - 统一的资源管理
   - 更好的错误处理

3. **扩展性**
   - 容易添加新的时间周期
   - 支持不同的运行模式
   - 可配置的参数

## 注意事项

1. **网络稳定性**
   - 确保网络连接稳定
   - 监控API连接状态

2. **资源使用**
   - Independent模式占用更多进程
   - Threaded模式需要注意线程安全

3. **测试建议**
   - 先在模拟环境测试
   - 逐步验证各个时间周期
   - 监控策略表现

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查认证信息
   - 确认网络连接
   - 重启策略

2. **进程冲突**
   - 使用Independent模式
   - 检查端口占用

3. **策略不执行**
   - 检查时间周期配置
   - 确认交易时间
   - 查看日志输出

### 日志查看

策略运行时会输出详细日志，包括：
- 策略启动信息
- 交易信号
- 错误信息
- 持仓状态

## 联系支持

如有问题，请检查：
1. 配置参数是否正确
2. 网络连接是否稳定
3. 认证信息是否有效
4. 查看错误日志信息
