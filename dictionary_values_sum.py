max_positions = {
    15: {'long': 5, 'short': 5},
    60: {'long': 1, 'short': 1},
    180: {'long': 1, 'short': 1},
    300: {'long': 1, 'short': 1},
    800: {'long': 1, 'short': 1},
    900: {'long': 1, 'short': 1}
}
total_sum_long =sum(position['long'] for position in max_positions.values())
total_sum_short = sum(position['short'] for position in max_positions.values())
total_sum = sum(position['long'] + position['short'] for position in max_positions.values())

print(f"所有值的总和是: {total_sum}")