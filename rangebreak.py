import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional


class RangeBreakoutStrategy:
    def __init__(self,
                 trade_length: int = 20,
                 long_percent: float = 0.25,
                 short_percent: float = 0.75,
                 exit_long_percent: float = 0.1,
                 exit_short_percent: float = 0.9,
                 top_range_multiplier: float = 1.0,
                 bottom_range_multiplier: float = 0.0):
        """
        区间突破趋势跟踪策略

        Parameters:
        - trade_length: 交易区间计算周期 (默认20)
        - long_percent: 多头进场因子 (默认0.25)
        - short_percent: 空头进场因子 (默认0.75)
        - exit_long_percent: 多头出场因子 (默认0.1)
        - exit_short_percent: 空头出场因子 (默认0.9)
        - top_range_multiplier: 顶部区间乘数
        - bottom_range_multiplier: 底部区间乘数
        """
        self.trade_length = trade_length
        self.long_percent = long_percent
        self.short_percent = short_percent
        self.exit_long_percent = exit_long_percent
        self.exit_short_percent = exit_short_percent
        self.top_range_multiplier = top_range_multiplier
        self.bottom_range_multiplier = bottom_range_multiplier

        # 交易状态
        self.market_position = 0  # 1: 多头, -1: 空头, 0: 无持仓
        self.trend_direction = 0  # 1: 上涨趋势, -1: 下降趋势

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算策略指标
        """
        df = df.copy()

        # 计算滚动高点和低点
        df['highest_high'] = df['high'].rolling(window=self.trade_length).max()
        df['lowest_low'] = df['low'].rolling(window=self.trade_length).min()

        # 计算交易区间
        df['trade_range'] = (df['highest_high'] - df['lowest_low']) / 100

        # 计算进场和出场价格
        df['long_entry_price'] = df['lowest_low'] + df['trade_range'] * self.long_percent
        df['short_entry_price'] = df['lowest_low'] + df['trade_range'] * self.short_percent
        df['long_exit_price'] = df['lowest_low'] + df['trade_range'] * self.exit_long_percent
        df['short_exit_price'] = df['lowest_low'] + df['trade_range'] * self.exit_short_percent

        # 计算顶部和底部区间
        df['top_range'] = df['lowest_low'] + df['trade_range'] * self.top_range_multiplier
        df['bottom_range'] = df['lowest_low'] + df['trade_range'] * self.bottom_range_multiplier

        return df

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        """
        df = self.calculate_indicators(df)

        # 初始化信号列
        df['trend_direction'] = 0
        df['signal'] = 0  # 1: 买入, -1: 卖出, 0: 无信号
        df['position'] = 0  # 当前持仓状态
        df['entry_price'] = np.nan
        df['exit_price'] = np.nan

        # 逐行处理信号
        for i in range(len(df)):
            if i < self.trade_length:
                continue

            # 判断趋势方向
            if df.loc[i, 'high'] > df.loc[i, 'top_range']:
                df.loc[i, 'trend_direction'] = 1
            elif df.loc[i, 'low'] < df.loc[i, 'bottom_range']:
                df.loc[i, 'trend_direction'] = -1
            else:
                # 保持之前的趋势方向
                if i > 0:
                    df.loc[i, 'trend_direction'] = df.loc[i - 1, 'trend_direction']

            # 获取当前持仓
            current_position = df.loc[i - 1, 'position'] if i > 0 else 0

            # 生成交易信号
            if current_position == 0:  # 无持仓时
                if df.loc[i, 'trend_direction'] == 1:
                    # 趋势上涨，做多
                    df.loc[i, 'signal'] = 1
                    df.loc[i, 'position'] = 1
                    df.loc[i, 'entry_price'] = df.loc[i, 'long_entry_price']
                elif df.loc[i, 'trend_direction'] == -1:
                    # 趋势下跌，做空
                    df.loc[i, 'signal'] = -1
                    df.loc[i, 'position'] = -1
                    df.loc[i, 'entry_price'] = df.loc[i, 'short_entry_price']
                else:
                    df.loc[i, 'position'] = current_position

            elif current_position == 1:  # 持有多头
                # 检查多头出场条件
                if df.loc[i, 'low'] <= df.loc[i, 'long_exit_price']:
                    df.loc[i, 'signal'] = -1  # 平多
                    df.loc[i, 'position'] = 0
                    df.loc[i, 'exit_price'] = df.loc[i, 'long_exit_price']
                else:
                    df.loc[i, 'position'] = current_position

            elif current_position == -1:  # 持有空头
                # 检查空头出场条件
                if df.loc[i, 'high'] >= df.loc[i, 'short_exit_price']:
                    df.loc[i, 'signal'] = 1  # 平空
                    df.loc[i, 'position'] = 0
                    df.loc[i, 'exit_price'] = df.loc[i, 'short_exit_price']
                else:
                    df.loc[i, 'position'] = current_position

        return df

    def backtest(self, df: pd.DataFrame, initial_capital: float = 100000) -> Dict:
        """
        回测策略
        """
        df = self.generate_signals(df)

        # 计算收益
        df['returns'] = 0.0
        df['equity'] = initial_capital

        for i in range(1, len(df)):
            if df.loc[i, 'signal'] != 0:
                if df.loc[i - 1, 'position'] == 1:  # 平多
                    # 多头收益 = (出场价 - 进场价) / 进场价
                    entry_idx = df[:i][df['signal'] == 1].index[-1]
                    entry_price = df.loc[entry_idx, 'entry_price']
                    exit_price = df.loc[i, 'exit_price']
                    df.loc[i, 'returns'] = (exit_price - entry_price) / entry_price

                elif df.loc[i - 1, 'position'] == -1:  # 平空
                    # 空头收益 = (进场价 - 出场价) / 进场价
                    entry_idx = df[:i][df['signal'] == -1].index[-1]
                    entry_price = df.loc[entry_idx, 'entry_price']
                    exit_price = df.loc[i, 'exit_price']
                    df.loc[i, 'returns'] = (entry_price - exit_price) / entry_price

            # 更新权益
            df.loc[i, 'equity'] = df.loc[i - 1, 'equity'] * (1 + df.loc[i, 'returns'])

        # 计算策略统计
        total_return = (df['equity'].iloc[-1] - initial_capital) / initial_capital
        trades = df[df['signal'] != 0]
        win_trades = trades[trades['returns'] > 0]
        lose_trades = trades[trades['returns'] < 0]

        stats = {
            'total_return': total_return,
            'total_trades': len(trades),
            'win_trades': len(win_trades),
            'lose_trades': len(lose_trades),
            'win_rate': len(win_trades) / len(trades) if len(trades) > 0 else 0,
            'avg_return': trades['returns'].mean() if len(trades) > 0 else 0,
            'max_return': trades['returns'].max() if len(trades) > 0 else 0,
            'min_return': trades['returns'].min() if len(trades) > 0 else 0,
            'final_equity': df['equity'].iloc[-1]
        }

        return df, stats

    def plot_results(self, df: pd.DataFrame, figsize: Tuple[int, int] = (15, 10)):
        """
        绘制回测结果
        """
        fig, axes = plt.subplots(3, 1, figsize=figsize, sharex=True)

        # 价格图
        axes[0].plot(df.index, df['close'], label='Close Price', linewidth=1)
        axes[0].plot(df.index, df['long_entry_price'], label='Long Entry', alpha=0.7)
        axes[0].plot(df.index, df['short_entry_price'], label='Short Entry', alpha=0.7)
        axes[0].plot(df.index, df['long_exit_price'], label='Long Exit', alpha=0.7)
        axes[0].plot(df.index, df['short_exit_price'], label='Short Exit', alpha=0.7)

        # 标记交易信号
        buy_signals = df[df['signal'] == 1]
        sell_signals = df[df['signal'] == -1]

        axes[0].scatter(buy_signals.index, buy_signals['close'],
                        color='green', marker='^', s=100, label='Buy Signal')
        axes[0].scatter(sell_signals.index, sell_signals['close'],
                        color='red', marker='v', s=100, label='Sell Signal')

        axes[0].set_title('Price and Trading Signals')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # 持仓状态
        axes[1].plot(df.index, df['position'], label='Position', linewidth=2)
        axes[1].set_title('Position Status')
        axes[1].set_ylabel('Position')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        # 权益曲线
        axes[2].plot(df.index, df['equity'], label='Equity Curve', linewidth=2)
        axes[2].set_title('Equity Curve')
        axes[2].set_ylabel('Equity')
        axes[2].set_xlabel('Date')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()


# 使用示例
if __name__ == "__main__":
    # 创建示例数据 (OHLC格式)
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=500, freq='D')

    # 生成模拟股价数据
    price = 100
    prices = [price]
    for _ in range(499):
        price = price * (1 + np.random.normal(0, 0.02))
        prices.append(price)

    # 创建OHLC数据
    df = pd.DataFrame(index=dates)
    df['close'] = prices
    df['open'] = df['close'].shift(1) * (1 + np.random.normal(0, 0.005, len(df)))
    df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.abs(np.random.normal(0, 0.01, len(df))))
    df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.abs(np.random.normal(0, 0.01, len(df))))
    df = df.dropna()

    # 运行策略
    strategy = RangeBreakoutStrategy(
        trade_length=20,
        long_percent=25,  # 对应原代码的百分比
        short_percent=75,
        exit_long_percent=10,
        exit_short_percent=90
    )

    result_df, stats = strategy.backtest(df)

    # 打印统计结果
    print("策略回测统计:")
    print(f"总收益率: {stats['total_return']:.2%}")
    print(f"总交易次数: {stats['total_trades']}")
    print(f"胜率: {stats['win_rate']:.2%}")
    print(f"平均收益: {stats['avg_return']:.2%}")
    print(f"最大单次收益: {stats['max_return']:.2%}")
    print(f"最大单次亏损: {stats['min_return']:.2%}")
    print(f"最终权益: {stats['final_equity']:.2f}")

    # 绘制结果
    strategy.plot_results(result_df)