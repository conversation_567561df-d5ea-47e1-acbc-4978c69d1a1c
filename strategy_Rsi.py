import pandas as pd
from strategies.rsi_strategy import rsi_info_display
from strategies.paths import Paths
from loguru import logger as mylog
import copy
import time
from tqsdk.tafunc import time_to_str
def stragey_rsi(api,symbol, interval):
    strategyname = 'rsicross'
    acct = api.get_account()
    # disp_account(acct)
    try:
        userid = acct.user_id.split('-')[0]
    except:
        userid = 'moni'

    logfilename = Paths.log('_'.join([userid, symbol, strategyname]))

    mylog.add(logfilename + '.log', encoding='utf-8')
    savebars = False
    quote = api.get_quote(symbol)

    if '@' in symbol:
        SYMBOL = quote.underlying_symbol
    else:
        SYMBOL = symbol

    trading_symbol = SYMBOL.split('.')[1]

    ticks = api.get_tick_serial(SYMBOL)
    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines

    signals= rsi_info_display(klines1)

    productid = SYMBOL.split('.')[1]
    minutes = str(int(klines_tmp.duration.iloc[0] / 60))
    soundmsg = ''.join([productid, minutes])

    # 设置语音提醒信息
    openlong_sound = soundmsg + '分钟' + '发出做多信号'
    openshort_sound = soundmsg + '分钟' + '发出做空信号'
    closelong_sound = soundmsg + '分钟' + '发出平多单信号'
    closeshort_sound = soundmsg + '分钟' + '发出平空单信号'

    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()
    hl = daybar.high - daybar.low
    ho = daybar.high - daybar.open
    ol = daybar.open - daybar.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    C = klines1.close

    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    KNUM = len(klines1)
    gapcover = False

    position = api.get_position(SYMBOL)
    acc = api.get_account()

    # disp_day_info(daymean, last3mean, hlmax, homean, olmean)
    # disp_0Day_info(quote)
    # disp_account(acct)

    while True:

        api.wait_update()

        if api.is_changing(daybars.iloc[-1], "datetime"):
            savebars = True
            dayopensp = True
            dayopenbp = True

            hl = daybar.high - daybar.low
            ho = daybar.high - daybar.open
            ol = daybar.open - daybar.low
            daymean = int(hl.mean())
            homean = ho.mean()
            olmean = ol.mean()
            hlmax = max(hl)
            last3mean = int(hl.iloc[-4:-1].mean())
            dayopen = quote.open
            dayhigh = quote.highest
            daylow = quote.lowest
            preclose = quote.pre_close
            dayvib = dayhigh - daylow
            gap = dayopen - preclose
            gapcover = False
            print('日线数据更新完成。。。')
            mylog.info('日线数据更新完成。。。')

        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            # disp_day_info(daymean, last3mean, hlmax, homean, olmean)
            # disp_0Day_info(quote)
            # print('balance:', acct.balance, 'poslong:', position.pos_long, 'posshort:', position.pos_short,
            #       'poslong float profit:', position.float_profit_long, 'posshort float profit:',
            #       position.float_profit_short)

            speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                newk = newk.to_frame()
                newk = newk.T
                klines1 = pd.concat([klines1, newk], ignore_index=True)

                # klines1 = klines1.append(newk)

            signal = rsi_info_display(klines1)
            print(signal)

            # 交易部分
            # basevalue = average_signal_distance * 2
            # order_volume = (basevalue - distnow) * single_volume if distnow < average_signal_distance else single_volume
            # order_volume = single_volume
        #
        #     if upslist[-1]:
        #         mylog.info('发出做多信号....')
        #         speak_text(openlong_sound)
        #
        #         bkprice = quote.last_price + 1
        #         mylog.info(['bkprice', bkprice, 'skprofit', position.float_profit_short, 'skvol', skvol])
        #
        #         # 下多单
        #         if bkvol < bklimit and sigfloatprofit > 0:
        #             # orderVol = min(bklimit - bkvol, skvol - bkvol + 1)
        #             print('signalprofit:', sigfloatprofit)
        #             orderVol = single_volume
        #             if orderVol <= 0:
        #                 mylog.info('持仓数量已经达到,不下单...')
        #             else:
        #                 BK(api, symbol=SYMBOL, order_price=bkprice, volume=orderVol)
        #                 mylog.info([SYMBOL, '下多单:', '数量', orderVol, '价格', bkprice])
        #         # 平空单
        #         if skvol > single_volume:
        #             if position.float_profit_short > 5 * skvol and skvol >= bkvol - 1:  # 空单盈利
        #                 # BP(api, symbol=SYMBOL, order_price=bkprice, volume=skvol)
        #                 BP(api, symbol=SYMBOL, order_price=bkprice, volume=single_volume)
        #                 mylog.info([SYMBOL, '平空单', '数量', single_volume, '价格', bkprice])
        #
        #             else:
        #                 mylog.info('short float proft doesnt match, dont cover.')
        #
        #     if dnslist[-1]:
        #         mylog.info('发出做空信号....')
        #         speak_text(openshort_sound)
        #
        #         skprice = quote.last_price - 1
        #         bkvol = position.pos_long
        #         bkvol_cost = position.open_price_long
        #
        #         mylog.info(['skprice', skprice, 'bkprofit', position.float_profit_long, 'bkvol', bkvol])
        #
        #         # 下空单
        #         print('skvol:', skvol, 'sklimit:', sklimit)
        #         if skvol < sklimit:
        #             # orderVol = min(sklimit - skvol, bkvol + 1 - skvol)
        #             orderVol = single_volume
        #             if orderVol <= 0:
        #                 mylog.info('持仓数量已达要求,不下单.')
        #
        #             else:
        #                 SK(api, symbol=SYMBOL, order_price=skprice, volume=orderVol)
        #                 mylog.info([SYMBOL, '下空单', '数量', orderVol, '价格', skprice])
        #
        #         # 平多单
        #         if bkvol >= skvol and bkvol > 0:
        #             if position.float_profit_long > 2 * bkvol:  # 多单盈利
        #                 # SP(api, symbol=SYMBOL, order_price=skprice, volume=bkvol)
        #                 SP(api, symbol=SYMBOL, order_price=skprice, volume=single_volume)
        #                 mylog.info(['ping duo dan.', 'volume', single_volume, 'price', skprice])
        #
        #             else:
        #                 mylog.info('float profit of long pos does not match the condition. dont cover.')
        #
        # if api.is_changing(quote):
        #     if gap > 0:
        #         if quote.lowest < quote.pre_close:
        #             gapcover = True
        #     else:
        #         if quote.highest > quote.pre_close:
        #             gapcover = True
        #
        #     # if signalnow == 'BK':
        #     #     sigfloatprofit = quote.last_price - signalprice
        #     # if signalnow == 'SK':
        #     #     sigfloatprofit = signalprice - quote.last_price
        #
        #     # print('当前信号:', signalnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)
        #
        #     # updatetime = quote.datetime.split(' ')[1].split('.')[0]
        #     # signal = {'合约': SYMBOL, '周期': interval, '时间': updatetime, '当前信号': signalnow,
        #     #           '持续周期': distnow, '信号价格': signalprice, '现价': quote.last_price,
        #     #           '信号盈亏': sigfloatprofit}
        #     # sendsignal(socket, strategyname, signal)
        #
        # if api.is_changing(position):
        #     bkvol = position.pos_long
        #     bkvol_yd = position.pos_long_his
        #     bkvol_td = position.pos_long_today
        #
        #     skvol = position.pos_short
        #     skvol_yd = position.pos_short_his
        #     skvol_td = position.pos_short_today
        #
        # hr = time.localtime().tm_hour
        # mi = time.localtime().tm_min
        # if ((time.localtime().tm_hour == 15 and time.localtime().tm_min > 15) or (
        #         time.localtime().tm_hour == 23 and time.localtime().tm_min > 30)):
        #     klines1.to_csv(SYMBOL + '.csv')
        #     api.close()
        #     mylog.info('no trading time, quit.')
        #     sys.exit(0)


def runstrategy():
    from tqsdk import TqApi, TqAccount
    from datetime import date
    from accounts_zjy import cyzjy as acct


    product = 'OI'
    interval = 60
    bklimit = 4
    sklimit = 3
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    symbol = api.query_cont_quotes(product_id=product)[0]
    stragey_rsi(api, symbol, interval)


runstrategy()
