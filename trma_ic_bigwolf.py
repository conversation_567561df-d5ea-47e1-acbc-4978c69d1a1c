# import copy
# from myfunction import *

# from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
# from tradefuncs import *
# from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMAsdk2 import trmastrategy
# from time import sleep
# import time
import os
basedir = os.path.dirname(__file__)

mylog.add(os.path.join(basedir, 'ema.log'), encoding='utf-8')


if __name__ == "__main__":
    from pytermgui import tim
    from pytermgui import pretty
    # tim.setup_displayhook()


    from tqsdk import TqApi, TqKq

    config = {
        'symbol': 'CZCE.OI301',
        'interval': 15,
        'bklimit': 32000,
        'sklimit': 25000,
        'single_volume': 100
    }

    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123")
    symbol = api.query_cont_quotes(product_id='IC')[0]
    trmastrategy(api, symbol=symbol, interval=config['interval'], single_volume=config['single_volume'], bklimit=config['bklimit'], sklimit=config['sklimit'])
