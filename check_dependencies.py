#!/usr/bin/env python3
"""
依赖检查脚本
检查项目所需的Python包是否正确安装
"""

import sys
import importlib
import subprocess
from typing import Dict, List, Tuple


def get_package_version(package_name: str) -> str:
    """获取包版本"""
    try:
        module = importlib.import_module(package_name)
        return getattr(module, '__version__', 'Unknown')
    except ImportError:
        return 'Not installed'


def check_package(package_name: str, import_name: str = None) -> Tuple[bool, str]:
    """检查包是否安装并获取版本"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'Unknown')
        return True, version
    except ImportError:
        return False, 'Not installed'


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 7):
        print("❌ 警告: 建议使用Python 3.7或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True


def main():
    """主函数"""
    print("🔍 检查项目依赖...")
    print("=" * 60)
    
    # 检查Python版本
    python_ok = check_python_version()
    print()
    
    # 定义需要检查的包
    packages = {
        # 核心依赖
        'tqsdk': {
            'import_name': 'tqsdk',
            'description': 'TqSDK交易接口',
            'critical': True
        },
        'pandas': {
            'import_name': 'pandas',
            'description': '数据处理',
            'critical': True
        },
        'numpy': {
            'import_name': 'numpy',
            'description': '数值计算',
            'critical': True
        },
        'loguru': {
            'import_name': 'loguru',
            'description': '日志记录',
            'critical': True
        },
        'zmq': {
            'import_name': 'zmq',
            'description': 'ZeroMQ通信',
            'critical': True
        },
        'websockets': {
            'import_name': 'websockets',
            'description': 'WebSocket通信',
            'critical': True
        },
        'addict': {
            'import_name': 'addict',
            'description': '字典增强',
            'critical': True
        },
        'pyttsx3': {
            'import_name': 'pyttsx3',
            'description': '语音合成',
            'critical': True
        },
        'crontab': {
            'import_name': 'crontab',
            'description': '任务调度',
            'critical': True
        },
        'cloudpickle': {
            'import_name': 'cloudpickle',
            'description': '对象序列化',
            'critical': False
        },
        
        # 可选依赖
        'PyQt5': {
            'import_name': 'PyQt5',
            'description': '图形界面',
            'critical': False
        },
        'matplotlib': {
            'import_name': 'matplotlib',
            'description': '图表绘制',
            'critical': False
        },
        'concurrent.futures': {
            'import_name': 'concurrent.futures',
            'description': '并发处理',
            'critical': False
        },
    }
    
    # 检查包状态
    critical_missing = []
    optional_missing = []
    installed_packages = []
    
    print("📦 包检查结果:")
    print("-" * 60)
    
    for package_name, info in packages.items():
        import_name = info['import_name']
        description = info['description']
        is_critical = info['critical']
        
        installed, version = check_package(package_name, import_name)
        
        if installed:
            status = "✅"
            installed_packages.append((package_name, version, description))
            print(f"{status} {package_name:<15} {version:<12} {description}")
        else:
            status = "❌" if is_critical else "⚠️"
            if is_critical:
                critical_missing.append((package_name, description))
            else:
                optional_missing.append((package_name, description))
            print(f"{status} {package_name:<15} {'未安装':<12} {description}")
    
    print("\n" + "=" * 60)
    
    # 总结
    print("📊 检查总结:")
    print(f"✅ 已安装包: {len(installed_packages)}")
    print(f"❌ 缺失关键包: {len(critical_missing)}")
    print(f"⚠️ 缺失可选包: {len(optional_missing)}")
    
    # 显示缺失的关键包
    if critical_missing:
        print("\n❌ 缺失的关键包:")
        for package, description in critical_missing:
            print(f"   - {package} ({description})")
        print("\n💡 安装命令:")
        packages_to_install = [pkg for pkg, _ in critical_missing]
        print(f"   pip install {' '.join(packages_to_install)}")
    
    # 显示缺失的可选包
    if optional_missing:
        print("\n⚠️ 缺失的可选包:")
        for package, description in optional_missing:
            print(f"   - {package} ({description})")
    
    # 显示安装建议
    print("\n💡 安装建议:")
    if critical_missing:
        print("1. 首先安装缺失的关键包")
        print("   pip install -r requirements.txt")
    else:
        print("1. ✅ 所有关键包已安装")
    
    if optional_missing:
        print("2. 根据需要安装可选包")
        print("3. 运行 python install_dependencies.py 进行自动安装")
    
    print("4. 运行测试脚本验证功能")
    
    # 检查pip版本
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, check=True)
        pip_version = result.stdout.strip()
        print(f"\n📦 {pip_version}")
    except:
        print("\n⚠️ 无法获取pip版本信息")
    
    # 返回状态
    if not python_ok or critical_missing:
        print("\n❌ 环境检查未通过，请安装缺失的依赖")
        return False
    else:
        print("\n✅ 环境检查通过，可以运行项目")
        return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
