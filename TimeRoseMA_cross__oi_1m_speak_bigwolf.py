from strategies.TimeRoseMA_cross_speak import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date


    product = 'OI'
    symbol=product
    interval = 60
    bklimit = 100000
    sklimit = 100000
    single_volume = 1000

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    # api = TqApi(TqKq(), auth="wolfquant,ftp123")
    api = TqApi(TqKq(), auth="bigwolf,ftp123")
    symbol = api.query_cont_quotes(product_id=product)[0]
    symbol = 'CZCE.OI601'
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)

if __name__ == '__main__':
    runstrategy()
