import zmq
import time

# url = "tcp://localhost:5556"

server = '**********'
url = f"tcp://{server}:3333"
from signal_receive_server import server
url = f"tcp://{server}:3333"

context = zmq.Context()
socket = context.socket(zmq.REQ)
socket.connect(url)
socket.setsockopt(zmq.RCVTIMEO, 1000)  # Set receive timeout to 1000ms (1 second)


def sendsignal(socket, client_name, signal):
    """Sends a signal using non-blocking I/O.

    Args:
        socket: The ZMQ socket.
        client_name: The name of the client.
        signal: The signal to send.
    """

    try:
        # Send the signal (non-blocking)
        socket.send_pyobj([client_name, signal], flags=zmq.NOBLOCK)
        print('signal sent...',signal)
        # Poll the socket for a reply
        poller = zmq.Poller()
        poller.register(socket, zmq.POLLIN)
        socks = dict(poller.poll(1000)) # Wait up to 1000ms for a reply


        if socket in socks and socks[socket] == zmq.POLLIN:
            # Receive the reply
            reply = socket.recv_pyobj(flags=zmq.NOBLOCK)
            print(f"Received reply: {reply}")  # Or handle the reply as needed
        else:
            print("No reply received within timeout.")
    except zmq.error.Again:
        print("Send operation would block.")  # Handle the case where sending would block
    except zmq.error.ZMQError as e:
        print(f"ZMQ error: {e}")
    finally:
        pass

if __name__=='__main__':
    signal={'合约': 'SHFE.sn2408', '周期': 900, '时间': '09:45:00', '当前信号': '空', '持续周期': 17, '信号价格': 274170.0, '现价': 266810.0, '信号盈亏': 7370.0}
    sendsignal(socket, 'test', signal)
    print('signal sent...')
    time.sleep(2) # Allow time for the server to process and reply.