#!/usr/bin/env python
#  -*- coding: utf-8 -*-
__author__ = 'yanqi<PERSON>'

from tqsdk import TqApi, TargetPosScheduler
from tqsdk.algorithm import vwap_table

"""
在 600s 时间内，按照历史成交量比例调整目标净持仓到 -100
"""

api = TqApi(auth="信易账号,用户密码")
quote = api.get_quote("CZCE.MA109")

# 设置twap任务参数
time_table = vwap_table(api, "CZCE.MA109", -100, 600)  # 目标持仓 -100 手，600s 内完成
print(time_table.to_string())

target_pos_sch = TargetPosScheduler(api, "CZCE.MA109", time_table)
# 启动循环
while not target_pos_sch.is_finished():
    api.wait_update()
api.close()