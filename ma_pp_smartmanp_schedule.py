import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog

from time import sleep
import time
from strategyEMA import emastrategy
mylog.add('ema.log', encoding='utf-8')

def runstrategy():
    from tqsdk import TqApi, TqKq
    product = 'pp'
    interval = 15
    bklimit = 100
    sklimit = 100
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="smartmanp,ftp123")
    symbol = api.query_cont_quotes(product_id=product)[0]
    emastrategy(api, symbol, interval, single_volume, bklimit, sklimit)


if __name__ == "__main__":
    import schedule
    import time

    from speaktextng import speak_text


    # schedule.every(15).minutes.do(runstrategy)
    # schedule.every().hour.do(job)
    schedule.every().day.at("20:35").do(runstrategy)
    schedule.every().day.at("08:35").do(runstrategy)
    # schedule.every().monday.do(job)
    # schedule.every().wednesday.at("13:15").do(job)
    # schedule.every().minute.at(":17").do(job)

    while True:
        schedule.run_pending()
        time.sleep(1)

