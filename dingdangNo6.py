'''
//叮当六号 2017/06/07-2018-12-20

N:=23;

KNUM:=COUNT(C>0,0);
初始:REF(MONEYREAL/10000,KNUM-1),NODRAW;
QY..MONEYREAL/10000,COLORCYAN,LINETHICK3;
QYMA..EMA(QY, 13),COLORYELLOW,LINETHICK6;
//CROSS(QY, QYMA),ICON(0,'ICO158');
//CROSSDOWN(QY, QYMA),ICON(1,'ICO157');
//QYTMA:TRMA(C,21),COLORGREEN,LINETHICK6;

//MIDLINE:(H+L+O+C)/4,LINETHICK3,COLORMAGENTA;


//最小..LV(当前,0);
最大..HV(QY,0), LINETHICK3, COLORGREEN;


NN:=BARSLAST(DATE<>REF(DATE,1))+1;//当天开盘一共走了多少根K线
KH:=HHV(NN,0),NODRAW;//当前周期一天的K线数

SIGSUM:COUNTSIG(BPK,KH)+COUNTSIG(SP,KH)+COUNTSIG(SPK,KH)+COUNTSIG(BP,KH)+COUNTSIG(BK,KH)+COUNTSIG(SK,KH),NODRAW;
//最近一天内发出的信号数量

HSIG:HV(SIGSUM,0),NODRAW;
LSIG:=LV(SIGSUM,0),NODRAW;

PP:MIN((SIGSUM+1)*10,80),NODRAW;

N1:=BARSLAST(DATE<>REF(DATE,1))+1;
N2:=REF(N1,N1);
开盘:VALUEWHEN(N1=1,O),COLORWHITE,LINETHICK3;
MA21:EMA(C,21),COLORWHITE,LINETHICK3;
//MA23:MA(C,23),COLORWHITE,LINETHICK3;
CH:HV(H,N),LINETHICK2;
CL:LV(L,N),LINETHICK2;
MID:=(CH+CL)/2;

NC..(CH-CL)/((CH+CL)*0.5)*100,COLORYELLOW,BOLD, NODRAW;


SIGALL:=COUNT(CROSS(C,MID),0);
//SIGALL:=COUNTSIG(BPK,0)+COUNTSIG(SP,0)+COUNTSIG(SPK,0)+COUNTSIG(BP,0)+COUNTSIG(BK,0)+COUNTSIG(SK,0),NODRAW;
AVSIGP..KNUM/SIGALL,NODRAW;
SIGSUM2:=COUNTSIG(BPK,AVSIGP)+COUNTSIG(SP,AVSIGP)+COUNTSIG(SPK,AVSIGP)+COUNTSIG(BP,AVSIGP)+COUNTSIG(BK,AVSIGP)+COUNTSIG(SK,AVSIGP),NODRAW;
SIGSUM3:=COUNT(CROSS(C,MID),AVSIGP)+COUNT(CROSSDOWN(C,MID),AVSIGP),NODRAW;
SIGSUM4:=SIGSUM3*CC;
SIGSUM5..VALUEWHEN(CROSS(C,MID) OR CROSSDOWN(C,MID),SIGSUM4),NODRAW;

PP:MIN((SIGSUM5+1)*10,80),NODRAW;
SETDEALPERCENT(PP);
//,LINETHICK3,COLORRED;
IF C<MID THEN
   BEGIN
   MID,COLORGREEN,LINETHICK3;
   END

 IF C>=MID THEN
   BEGIN
   MID,COLORRED,LINETHICK3;
   END

//,COLORGREEN,COLORRED);
CHUP:=MID+2*(CH-MID)/3,COLORWHITE;
CHDN:=MID-2*(CH-MID)/3,COLORWHITE;

//CHUP1:7*CH/8+CL/8,COLORYELLOW;
//CHA..CHUP-CHUP1,COLORWHITE,NODRAW;
//CROSSDOWN(C,CHUP),BPK;
//CROSS(C,CHDN),SPK;

//C>=开盘 AND C>MID, BPK;
//C<开盘 AND C<MID, SPK;

IF C>=开盘 THEN
 BEGIN
   BKVOL=0 && C>MID,BK;
   CROSS(C,MID),BPK;
   //CROSSDOWN(C,MID)&&L<REF(L,1),SP;
	CROSSDOWN(C,MID),SP;
   //CROSSDOWN(C,MID),SP;
  END


IF C<开盘 THEN
   BEGIN
	BKVOL>0, SP;

   SKVOL=0 &&C<MID,SK;

   CROSSDOWN(C,MID),SPK;
   CROSS(C,MID),BP;
   END

AUTOFILTER;





//振幅计算
PV:SUM(H-L,0)/COUNT(C>0,0),NODRAW;
PH:HHV(H-L,0),NODRAW;
PL:=LLV(H-L,0),NODRAW;

/*
//#IMPORT [DAY,1,INSSTATS] AS VAR;
#IMPORT[DAY,1,INSSTATS] AS VAR
DV:VAR.PVIB,COLORYELLOW,LINETHICK2,NODRAW;
DH:VAR.HVIB,NODRAW;
DL:VAR.LVIB,NODRAW;
*/
//缺口计算



HH2:=REF(HHV(H,N2),N2+N1);//前日高点
LL2:=REF(LLV(L,N2),N2+N1);//前日低点
前日:HH2-LL2,NODRAW;

HH1:=REF(HHV(H,N2),N1);//昨日高点
LL1:=REF(LLV(L,N2),N1);//昨日低点
昨振:HH1-LL1,NODRAW;

平均:(前日+昨振)/2,COLORGREEN,NODRAW;

今振:HHV(H,N1)-LLV(L,N1),COLORYELLOW,NODRAW;

今高:=HHV(H,N1),NODRAW;
今低:=LLV(L,N1),NODRAW;
BB:(C-今低)/(今高-今低),NODRAW;

昨收:=REF(C,N1),NODRAW;
ZO:=REF(O,N1);
ZH:=REF(H,N1);
ZL:=REF(L,N1);
//昨日振幅:ZH-ZL,NODRAW;

缺口:(开盘-昨收),NODRAW;
ZD:=缺口/昨收;
//QKQK: 1,高开，-1，低开，0，无缺口
QKQK:=IFELSE(ABS(缺口)>0,IFELSE(缺口>0,1,-1),0);
回补:IFELSE(QKQK=1,IFELSE(今低<=昨收,1,0),IFELSE(今高>=昨收,1,0)),NODRAW;
网格:平均*0.8/8,NODRAW;


HHL:HHV(H-L,0),NODRAW;
LHL:LLV(H-L,0),NODRAW;

'''
