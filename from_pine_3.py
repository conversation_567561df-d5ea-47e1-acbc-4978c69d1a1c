from tqsdk import Tq<PERSON><PERSON>, TqAuth, TargetPosTask, TqKq
from tqsdk.ta import ATR, MA, BOLL
import pandas as pd
from MyTT import MA

class MultiOrderBreakoutStrategy:
    def __init__(self, api, symbol, risk_per_trade=1.0, lookback=20, breakout_mult=2.0,
                 stop_loss_percent=2.0, max_positions=5, atr_period=14, ma_len=100):
        self.api = api
        self.symbol = symbol
        self.risk_per_trade = risk_per_trade
        self.lookback = lookback
        self.breakout_mult = breakout_mult
        self.stop_loss_percent = stop_loss_percent
        self.max_positions = max_positions
        self.atr_period = atr_period
        self.ma_len = ma_len

        self.quote = api.get_quote(symbol)
        self.klines = api.get_kline_serial(symbol, duration_seconds=60 * 60 * 24)  # Daily K-lines

        self.target_pos = TargetPosTask(api, symbol)
        self.open_positions = 0

        # Indicators
        self.boll = BOLL(self.klines, self.lookback, self.breakout_mult)
        self.atr = ATR(self.klines, self.atr_period)
        self.ma = MA(self.klines, self.ma_len)
        self.atr_sma = MA(self.atr.atr, 100)

    def run(self):
        while True:
            self.api.wait_update()

            if self.api.is_changing(self.klines.iloc[-1], "datetime"):
                self.on_bar()

    def on_bar(self):
        current_close = self.klines.close.iloc[-1]
        current_upper = self.boll.upper.iloc[-1]
        current_middle = self.boll.mid.iloc[-1]
        current_lower = self.boll.lower.iloc[-1]
        current_ma = self.ma.ma.iloc[-1]
        current_atr = self.atr.atr.iloc[-1]
        current_atr_sma = self.atr_sma.ma.iloc[-1]

        previous_close = self.klines.close.iloc[-2]
        previous_middle = self.boll.mid.iloc[-2]

        # Entry conditions
        long_condition = (current_close > current_upper) and (current_close > current_ma)

        # Exit conditions
        exit_condition = (previous_close > previous_middle) and (current_close <= current_middle)

        # Dynamic position sizing
        position_size = int((self.api.get_account().balance * self.risk_per_trade / 100) /
                            (current_close * self.stop_loss_percent / 100))

        # Strategy execution
        if long_condition and self.open_positions < self.max_positions and current_atr > current_atr_sma and position_size > 0:
            self.target_pos.set_target_volume(position_size)
            self.open_positions += 1

            # Set stop loss
            stop_price = current_close * (1 - self.stop_loss_percent / 100)
            self.api.insert_order(symbol=self.symbol, direction="SELL", offset="CLOSE",
                                  volume=position_size, price=stop_price, type="STOP")

        # Close all positions on exit condition
        if exit_condition and self.open_positions > 0:
            self.target_pos.set_target_volume(0)
            self.open_positions = 0


def run_strategy():
    # api = TqApi(auth=TqAuth("YOUR_ACCOUNT", "YOUR_PASSWORD"))
    api = TqApi(TqKq(), auth="wolfquant2023,Qiai1301")

    try:
        strategy = MultiOrderBreakoutStrategy(api, symbol="SHFE.au2306")  # Example: Gold futures
        strategy.run()
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        api.close()


if __name__ == "__main__":
    run_strategy()