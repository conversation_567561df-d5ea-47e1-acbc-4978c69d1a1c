# import copy
# from myfunction import *

# from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
# from tradefuncs import *
# from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMA_smallvolume import trmastrategy
# from time import sleep
# import time

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    config = {
        'symbol': 'CZCE.OI305',
        'interval': 15,
        'bklimit': 7100000,
        'sklimit': 5000000,
        'single_volume': 100
    }

    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123", debug=False, disable_print=True)
    # symbol = api.query_cont_quotes(product_id='OI')[0]
    trmastrategy(api, symbol=config['symbol'], interval=config['interval'], single_volume=config['single_volume'], bklimit=config['bklimit'], sklimit=config['sklimit'])
