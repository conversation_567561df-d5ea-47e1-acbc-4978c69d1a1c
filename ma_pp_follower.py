import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog

from time import sleep
import time

mylog.add('ema.log', encoding='utf-8')

from strategyEMA import emastrategy

if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    # symbol = 'SHFE.ag2106'
    # symbol = 'CZCE.SR101'
    symbol = 'DCE.pp2105'
    # symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 50
    sklimit = 50
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="follower,ftp123")
    emastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
