import sys
import copy
import os.path
import pickle
import gc
import time


from tqsdk import TqApi, TqKq

api = TqApi(TqKq(), auth="follower,ftp123", disable_print=True, debug=False)


def get_product_ids(api):
    productidlist = []

    symbols = api.query_cont_quotes()
    for s in symbols:
        exchangeid = s.split('.')[0]
        symbolid = ''.join(filter(str.isalpha, s.split('.')[1]))
        indexsymbolid = ''.join(['KQ.i@', exchangeid, '.', symbolid])
        productidlist.append(indexsymbolid)

    with open('productids.pkl', 'wb') as f:
        pickle.dump(productidlist, f)

    return productidlist



productids = get_product_ids(api)
api.close()

havedownloaded = ['IC', 'pb', 'SF', 'zn', 'rr', 'pp', 'hc', 'lu', 'WH', 'TF', 'UR', 'fb', 'OI',
                  'eg', 'fu', 'sc', 'sp', 'p', 'bb', 'JR', 'RI', 'i', 'CJ', 'bc', 'jm', 'nr', 'RS', 'IF',
                  'bu', 'AP', 'al', 'TA', 'SA', 'PF', 'cs', 'CY', 'SM', 'IH', 'eb', 'b', 'm', 'cu', 'l',
                  'rb', 'T', 'PK', 'a', 'IM', 'j', 'y', 'RM', 'lh', 'FG', 'au', 'ni', 'CF', 'pg', 'LR', 'v', 'c', 'ZC',
                  'sn', 'SR', 'ss', 'wr', 'ag', 'ru', 'jd', 'TS', 'MA']
havedownloaded =[]
deadproductid = ['bb', 'PM', 'JR', 'RI', 'LR', 'RS', 'wr']

def get_bars_by_indexid(indexid, datalength=8964):
    api = TqApi(TqKq(), auth="smartmanp,ftp123", disable_print=True, debug=False)
    inst = indexid.split('@')[1].split('.')[1]


    symbolbars = []
    for i in range(60):
        interval = 60 * (i + 1)
        print(i, indexid, interval)
        bars = api.get_kline_serial(indexid, duration_seconds=interval, data_length=datalength).dropna()
        symbolbars.append(copy.deepcopy(bars))
        del bars

    with open(filename, 'wb') as f:
        pickle.dump(symbolbars, f)
        del symbolbars
        gc.collect()
        print(filename, 'saved...')
        # time.sleep(3)
    return True

from multiprocessing import Process
datalenth= int(sys.argv[1])

for indexid in productids:
    print(indexid)
    filename = indexid.split('@')[1].split('.')[1] + 'barsdata.pkl'
    inst = indexid.split('@')[1].split('.')[1]
    if inst in havedownloaded or inst in deadproductid or os.path.isfile(filename):
        continue
    p=Process(target=get_bars_by_indexid, args=(indexid,datalenth))
    p.start()
    p.join()
    time.sleep(1)
    # input('continue?')



print('all download is finished...')
api.close()
