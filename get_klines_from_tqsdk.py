from tqapi import myapi as api
from strategies.weiswave_claude_dataframe import calculate_signals

product_id = 'OI'
# product_id = 'ag'
# product_id = 'SA'
# api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
symbol = api.query_cont_quotes(product_id=product_id)[0]
# symbol = 'CZCE.OI505'
print(f"交易的合约是: {symbol}")
baseinterval = 60
minutes = 15
interval = baseinterval*minutes
# interval = 15
klines=api.get_kline_serial(symbol, interval, data_length=20000).dropna()
klines.to_csv(f"data_{product_id}_{minutes}.csv")
print('数据已经保存到',f"data_{product_id}_{minutes}.csv")
api.close()




