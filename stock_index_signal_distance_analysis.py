import sys
import copy
import os.path
import pickle
import gc
import time

from tqsdk import TqApi, TqKq

api = TqApi(TqKq(), auth="follower,ftp123", disable_print=True, debug=False)
deadproductid = ['bb', 'PM', 'JR', 'RI', 'LR', 'RS', 'wr']
from signal_distance_analysis import MaCrossCaculate, zjtj


def get_product_ids(api):
    productidlist = []

    symbols = api.query_cont_quotes()
    for s in symbols:
        exchangeid = s.split('.')[0]
        symbolid = ''.join(filter(str.isalpha, s.split('.')[1]))
        indexsymbolid = ''.join(['KQ.i@', exchangeid, '.', symbolid])
        productidlist.append(indexsymbolid)

    with open('productids.pkl', 'wb') as f:
        pickle.dump(productidlist, f)

    return productidlist


def get_symbol_bars(api, symbol, interval, datalength=8964):
    # from tqsdk import TqApi, TqKq
    # api = TqApi(TqKq(), auth="smartmanp,ftp123", disable_print=True, debug=False)
    bars = api.get_kline_serial(symbol, duration_seconds=interval, data_length=datalength).dropna()

    # api.close()
    return bars


if __name__ == '__main__':

    stock_index=['000016',
                 '000300',
                 '000905',
                 '000852',
                 # '510050',
                 # '510300',

                 ]

    for s in stock_index:
        indexid = 'SSE.'+ s
        interval = 60*60
        bars = get_symbol_bars(api, symbol=indexid, interval=interval)
        avgdistance = MaCrossCaculate(bars, period=13)
        print(indexid, avgdistance)

    api.close()
