# import copy
# from myfunction import *

# from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
# from tradefuncs import *
# from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMAsdk2 import trmastrategy
# from time import sleep
# import time

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq
    from addict import Dict

    config = {
        'symbol': 'CZCE.OI505',
        'interval': 15,
        'bklimit': 2000000,
        'sklimit': 200000,
        'single_volume': 1000
    }
    config = Dict(config)
    print(config)
    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123", debug=False, disable_print=True)
    symbol = api.query_cont_quotes(product_id='OI')[0]
    # from strategies.alive_orders_save_and_replay import save_alive_orders,replay_alive_orders
    symbol ='CZCE.OI505'
    # save_alive_orders(api)
    # try:
    #     replay_alive_orders(api)
    # except:
    #     print('replay order error.')
    # trmastrategy(api, symbol=symbol, interval=config['interval'], single_volume=config['single_volume'], bklimit=config['bklimit'], sklimit=config['sklimit'])
    trmastrategy(api, symbol=symbol, interval=config.interval, single_volume=config.single_volume, bklimit=config.bklimit, sklimit=config.sklimit)
