import json
import os
import copy
from datetime import datetime
from tqsdk import TqApi, TqAuth
import pandas as pd
from tqapi import myapi as api
from loguru import logger as log

class MultiTimeframeStrategy:
    def __init__(self, api, symbol, max_positions, positions_file='positions.json', initial_klines_length=1000):
        self.api = api
        self.symbol = symbol
        self.max_positions = max_positions
        self.timeframes = list(max_positions.keys())
        self.positions_file = self.symbol + '_' + positions_file
        self.klines = {}
        self.log = log.info
        self.load_positions()

        for tf in self.timeframes:
            self.klines[tf] = copy.deepcopy(self.api.get_kline_serial(self.symbol, tf, initial_klines_length))

    def load_positions(self):
        if os.path.exists(self.positions_file):
            with open(self.positions_file, 'r') as f:
                self.positions = json.load(f)
            self.log("Loaded positions from file.")
        else:
            self.positions = {str(tf): {'long': 0, 'short': 0} for tf in self.timeframes}
            self.log("Initialized new positions.")

    def save_positions(self):
        with open(self.positions_file, 'w') as f:
            json.dump(self.positions, f)
        self.log("Saved positions to file.")

    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")

    def calculate_ma(self, series, period):
        return series.rolling(window=period).mean()

    def check_crossover(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-2] <= ma.iloc[-2] and close.iloc[-1] > ma.iloc[-1]

    def check_crossunder(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-2] >= ma.iloc[-2] and close.iloc[-1] < ma.iloc[-1]

    def get_position_profit(self, position_type):
        position = self.api.get_position(self.symbol)
        if position_type == 'long':
            return position.float_profit_long
        else:
            return position.float_profit_short

    def execute_trade(self, timeframe, action, position_type):
        timeframe_str = str(timeframe)
        current_price = self.klines[timeframe].close.iloc[-1]
        current_time = self.api.get_quote(self.symbol).datetime

        if action == 'open':
            if self.positions[timeframe_str][position_type] < self.max_positions[timeframe][position_type]:
                self.positions[timeframe_str][position_type] += 1
                volume = 1 if position_type == 'long' else -1
                self.api.insert_order(symbol=self.symbol, direction="BUY" if position_type == 'long' else "SELL",
                                      offset="OPEN", volume=abs(volume))
                self.log(
                    f"交易信号: 合约={self.symbol}, 时间={current_time}, 周期={timeframe}秒, 方向={'多' if position_type == 'long' else '空'}, 动作=开仓, 价格={current_price}")
                self.save_positions()
        elif action == 'close':
            if self.positions[timeframe_str][position_type] > 0:
                profit = self.get_position_profit(position_type)
                if profit >= 0:
                    self.positions[timeframe_str][position_type] -= 1
                    volume = 1 if position_type == 'long' else -1
                    self.api.insert_order(symbol=self.symbol, direction="SELL" if position_type == 'long' else "BUY",
                                          offset="CLOSE", volume=abs(volume))
                    self.log(
                        f"交易信号: 合约={self.symbol}, 时间={current_time}, 周期={timeframe}秒, 方向={'多' if position_type == 'long' else '空'}, 动作=平仓, 价格={current_price}")
                    self.save_positions()
                else:
                    self.log(f"{timeframe}秒周期: {position_type}单亏损,不平仓")

    def update_klines(self, timeframe):
        temp_klines = self.api.get_kline_serial(self.symbol, timeframe, data_length=2)
        if self.api.is_changing(temp_klines.iloc[-1], "datetime"):
            # If there's a new complete K-line, append it to our stored K-lines
            new_kline = temp_klines.iloc[-2]
            # self.klines[timeframe] = self.klines[timeframe].append(new_kline)
            # 假设 new_kline 是一个新的 DataFrame 或 Series
            self.klines[timeframe] = pd.concat([self.klines[timeframe], new_kline], ignore_index=True)
            print(f"最新K线数量:{len(self.klines[timeframe])}")
            return True
        return False

    def update_klines(self, timeframe):
        temp_klines = self.api.get_kline_serial(self.symbol, timeframe, data_length=2)
        if self.api.is_changing(temp_klines.iloc[-1], "datetime"):
            new_kline = temp_klines.iloc[-2]
            self.klines[timeframe] = pd.concat([self.klines[timeframe], pd.DataFrame([new_kline])])
            return True
        return False

    def run(self, timeframe):
        kline = self.klines[timeframe]
        ma13 = self.calculate_ma(kline.close, 13)

        if self.check_crossover(kline.close, ma13):
            self.execute_trade(timeframe, 'open', 'long')
            self.execute_trade(timeframe, 'close', 'short')
            self.log(f"{timeframe}秒周期: 金叉信号, 开多仓, 平空仓")
        elif self.check_crossunder(kline.close, ma13):
            self.execute_trade(timeframe, 'open', 'short')
            self.execute_trade(timeframe, 'close', 'long')
            self.log(f"{timeframe}秒周期: 死叉信号, 开空仓, 平多仓")
    def update(self):
        for timeframe in self.timeframes:
            if self.update_klines(timeframe):
                self.log(f"{timeframe}秒周期: K线更新")
                self.run(timeframe)


# 主程序
try:
    product_id = 'OI'
    symbol = api.query_cont_quotes(product_id=product_id)[0]
    print(f"交易的合约是: {symbol}")

    # 定义交易周期（以秒为单位）和每个周期的持仓限制
    max_positions = {
        60: {'long': 1, 'short': 1},  # 1分钟
        180: {'long': 2, 'short': 1},  # 3分钟
        800: {'long': 1, 'short': 2},  # 13分20秒
        900: {'long': 2, 'short': 2}  # 15分钟
    }

    strategy = MultiTimeframeStrategy(api, symbol, max_positions)

    while True:
        api.wait_update()
        strategy.update()

except KeyboardInterrupt:
    print("策略已停止")
finally:
    api.close()
