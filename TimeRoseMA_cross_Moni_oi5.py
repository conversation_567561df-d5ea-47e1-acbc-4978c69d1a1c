
from strategies.TimeRoseMA_cross_moni import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date


    product = 'CZCE.OI301'
    symbol=product
    interval = 60*5
    bklimit = 160
    sklimit = 160
    single_volume = 20

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301", disable_print=True)
    # symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, product, interval, single_volume, bklimit, sklimit)


runstrategy()
