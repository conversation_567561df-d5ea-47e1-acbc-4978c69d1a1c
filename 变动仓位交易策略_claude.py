#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于KDJ变体的多空共存期货交易策略
使用天勤量化(TQSdk)实现 by claude.ai
"""

from tqsdk import TqApi, TqAuth, TargetPosTask
from tqsdk.tafunc import sma, ma, hhv, llv

from tqsdk.ta import ATR
import numpy as np
import pandas as pd
import time
import datetime


class KDJVariantStrategy:
    def __init__(self, api, symbol, account_id=None):
        """
        初始化策略

        Args:
            api: TqApi实例
            symbol: 合约代码，例如 "SHFE.au2106"
            account_id: 账户ID，默认为None表示使用主账户
        """
        self.api = api
        self.symbol = symbol
        self.account_id = account_id

        # 获取合约信息
        self.quote = self.api.get_quote(self.symbol)
        self.klines = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 60 * 24)  # 使用日线

        # 获取账户信息
        self.account = self.api.get_account(account_id=self.account_id)

        # 创建持仓管理任务
        self.target_pos = TargetPosTask(self.api, symbol, account_id=self.account_id)

        # 策略参数
        self.N = 9  # 窗口大小
        self.M1 = 3  # K值平滑
        self.M2 = 3  # D值平滑

        # 仓位控制参数
        self.max_pos_ratio = 0.8  # 最大总仓位比例
        self.max_daily_loss_ratio = 0.02  # 单日最大亏损比例
        self.single_loss_ratio = 0.05  # 单笔最大亏损比例
        self.account_drawdown_limit = 0.10  # 账户回撤限制

        # 持仓信息
        self.long_pos = 0  # 当前多头持仓
        self.short_pos = 0  # 当前空头持仓
        self.long_entry_price = 0  # 多头开仓价格
        self.short_entry_price = 0  # 空头开仓价格
        self.long_stop_loss = 0  # 多头止损价格
        self.short_stop_loss = 0  # 空头止损价格

        # 交易状态
        self.last_du_values = []  # 存储历史DU值，用于判断趋势
        self.last_ud_values = []  # 存储历史UD值，用于判断趋势
        self.last_trade_date = ""  # 上次交易日期
        self.initial_account_balance = self.account.balance  # 初始账户余额

        # 记录交易
        self.trades = []

    def calculate_indicators(self):
        """计算所有技术指标"""
        # 确保有足够的数据
        if len(self.klines.close) < self.N + 20:
            return False

        # 获取最近N个周期的最高和最低价
        high_n = HHV(self.klines.high, self.N)
        low_n = LLV(self.klines.low, self.N)
        close = self.klines.close

        # 计算价格区间
        price_range = high_n - low_n

        # 计算RSVU和RSVD
        rsvu = (close - low_n) / price_range * 100
        rsvd = (high_n - close) / price_range * 100

        # 计算KU和KDA
        ku = SMA(rsvu, self.M1, 1)
        kda = SMA(rsvd, self.M1, 1)

        # 计算DU和UD
        du = SMA(ku, self.M2, 1)
        ud = SMA(kda, self.M2, 1)

        # 计算JU和JD
        ju = 3 * ku - 2 * du
        jd = 3 * kda - 2 * ud

        # 计算涨上限仓和跌上限仓
        ma_ju_20 = MA(ju, 20)
        ma_jd_20 = MA(jd, 20)

        zhang_upper_pos = np.floor(ma_ju_20[-1] / 10) * 10
        die_upper_pos = np.floor(ma_jd_20[-1] / 10) * 10

        # 计算涨下限仓和跌下限仓
        zhang_lower_pos = (zhang_upper_pos - die_upper_pos) / 2 + 25
        die_lower_pos = (die_upper_pos - zhang_upper_pos) / 2 + 25

        # 计算JJ和标准仓
        jj = np.floor(ju[-1] / 10) * 10
        standard_pos = np.floor(du[-1] / 10) * 10

        # 更新DU和UD历史值，用于判断趋势
        self.last_du_values.append(du[-1])
        self.last_ud_values.append(ud[-1])
        if len(self.last_du_values) > 3:
            self.last_du_values.pop(0)
        if len(self.last_ud_values) > 3:
            self.last_ud_values.pop(0)

        # 计算ATR用于止损设置和波动率过滤
        atr_value = ATR(self.klines.high, self.klines.low, self.klines.close, 14)[-1]
        atr_ma_20 = MA(ATR(self.klines.high, self.klines.low, self.klines.close, 14), 20)[-1]

        return {
            "zhang_upper_pos": zhang_upper_pos,
            "die_upper_pos": die_upper_pos,
            "zhang_lower_pos": zhang_lower_pos,
            "die_lower_pos": die_lower_pos,
            "jj": jj,
            "standard_pos": standard_pos,
            "du": du[-1],
            "ud": ud[-1],
            "atr": atr_value,
            "atr_ma_20": atr_ma_20,
            "price_range": price_range[-1]
        }

    def check_du_downtrend(self):
        """检查DU是否连续3个周期下降"""
        if len(self.last_du_values) < 3:
            return False
        return self.last_du_values[0] > self.last_du_values[1] > self.last_du_values[2]

    def check_ud_uptrend(self):
        """检查UD是否连续3个周期上升"""
        if len(self.last_ud_values) < 3:
            return False
        return self.last_ud_values[0] < self.last_ud_values[1] < self.last_ud_values[2]

    def calculate_position_size(self, indicator_pos_pct):
        """根据指标建议的仓位百分比计算实际下单手数"""
        # 获取合约乘数
        contract_size = self.quote.volume_multiple
        # 计算当前价格
        current_price = self.quote.last_price

        # 计算每手价值
        per_hand_value = contract_size * current_price

        # 计算账户总价值
        account_value = self.account.balance

        # 计算建议的资金使用量
        suggested_value = account_value * indicator_pos_pct / 100

        # 计算建议手数，向下取整
        suggested_hands = int(suggested_value / per_hand_value)

        # 进行风险控制，最大单笔开仓不超过账户价值的x%
        max_hands = int(account_value * 0.1 / per_hand_value)
        suggested_hands = min(suggested_hands, max_hands)

        return max(1, suggested_hands)  # 至少1手

    def adjust_position(self, indicators):
        """根据计算的指标调整持仓"""
        # 当前价格
        current_price = self.quote.last_price

        # 获取当前实际持仓
        position = self.api.get_position(self.symbol, account_id=self.account_id)
        self.long_pos = position.pos_long
        self.short_pos = position.pos_short

        # 计算多空头寸的指标
        jj = indicators["jj"]
        zhang_upper_pos = indicators["zhang_upper_pos"]
        die_upper_pos = indicators["die_upper_pos"]
        zhang_lower_pos = indicators["zhang_lower_pos"]
        die_lower_pos = indicators["die_lower_pos"]
        atr = indicators["atr"]
        atr_ma_20 = indicators["atr_ma_20"]
        price_range = indicators["price_range"]

        # 获取波动率状态，用于调整仓位
        volatility_ratio = atr / atr_ma_20 if atr_ma_20 > 0 else 1.0

        # 多头交易逻辑
        long_signal = False
        long_exit = False
        long_pos_size = 0

        # 多头开仓条件
        if (jj > die_lower_pos and zhang_lower_pos < die_lower_pos) and self.long_pos == 0:
            # 开立多头头寸
            long_signal = True
            # 计算多头仓位大小
            long_pos_size = self.calculate_position_size(zhang_upper_pos - zhang_lower_pos)
            # 根据波动率调整仓位
            if volatility_ratio > 1.5:
                long_pos_size = int(long_pos_size * 0.5)  # 高波动减仓
            elif volatility_ratio < 0.5:
                long_pos_size = 0  # 过低波动不开仓
                long_signal = False

            if long_signal:
                # 记录开仓价格
                self.long_entry_price = current_price
                # 设置止损价格
                self.long_stop_loss = current_price - price_range * 0.3

                # 记录交易
                self.trades.append({
                    "time": self.api.get_server_time(),
                    "action": "OPEN_LONG",
                    "price": current_price,
                    "volume": long_pos_size,
                    "reason": f"JJ({jj}) > 跌下限仓({die_lower_pos}),涨下限仓({zhang_lower_pos}) < 跌下限仓({die_lower_pos})"
                })

        # 多头加仓条件
        elif (jj > die_lower_pos and zhang_lower_pos > die_lower_pos) and self.long_pos > 0:
            # 计算理想仓位
            ideal_pos = self.calculate_position_size(zhang_upper_pos - zhang_lower_pos)
            # 根据波动率调整仓位
            if volatility_ratio > 1.5:
                ideal_pos = int(ideal_pos * 0.5)  # 高波动减仓

            # 如果理想仓位大于当前仓位，加仓
            if ideal_pos > self.long_pos:
                long_signal = True
                long_pos_size = ideal_pos

                # 记录交易
                self.trades.append({
                    "time": self.api.get_server_time(),
                    "action": "ADD_LONG",
                    "price": current_price,
                    "volume": ideal_pos - self.long_pos,
                    "reason": f"JJ({jj}) > 跌下限仓({die_lower_pos}),涨下限仓({zhang_lower_pos}) > 跌下限仓({die_lower_pos})"
                })

        # 多头平仓条件
        if (jj < die_upper_pos or self.check_du_downtrend()) and self.long_pos > 0:
            long_exit = True
            long_signal = False

            # 记录交易
            self.trades.append({
                "time": self.api.get_server_time(),
                "action": "CLOSE_LONG",
                "price": current_price,
                "volume": self.long_pos,
                "reason": f"JJ({jj}) < 跌上限仓({die_upper_pos}) 或 DU连续下降3期"
            })

        # 多头止损检查
        if self.long_pos > 0 and current_price < self.long_stop_loss:
            long_exit = True
            long_signal = False

            # 记录交易
            self.trades.append({
                "time": self.api.get_server_time(),
                "action": "STOP_LOSS_LONG",
                "price": current_price,
                "volume": self.long_pos,
                "reason": f"价格({current_price}) < 止损价({self.long_stop_loss})"
            })

        # 空头交易逻辑
        short_signal = False
        short_exit = False
        short_pos_size = 0

        # 空头开仓条件
        if (jj < die_upper_pos and zhang_lower_pos < die_lower_pos) and self.short_pos == 0:
            # 开立空头头寸
            short_signal = True
            # 计算空头仓位大小
            short_pos_size = self.calculate_position_size(die_upper_pos - die_lower_pos)
            # 根据波动率调整仓位
            if volatility_ratio > 1.5:
                short_pos_size = int(short_pos_size * 0.5)  # 高波动减仓
            elif volatility_ratio < 0.5:
                short_pos_size = 0  # 过低波动不开仓
                short_signal = False

            if short_signal:
                # 记录开仓价格
                self.short_entry_price = current_price
                # 设置止损价格
                self.short_stop_loss = current_price + price_range * 0.3

                # 记录交易
                self.trades.append({
                    "time": self.api.get_server_time(),
                    "action": "OPEN_SHORT",
                    "price": current_price,
                    "volume": short_pos_size,
                    "reason": f"JJ({jj}) < 跌上限仓({die_upper_pos}),涨下限仓({zhang_lower_pos}) < 跌下限仓({die_lower_pos})"
                })

        # 空头加仓条件
        elif (jj < zhang_upper_pos and zhang_lower_pos > die_lower_pos) and self.short_pos > 0:
            # 计算理想仓位
            ideal_pos = self.calculate_position_size(die_upper_pos - die_lower_pos)
            # 根据波动率调整仓位
            if volatility_ratio > 1.5:
                ideal_pos = int(ideal_pos * 0.5)  # 高波动减仓

            # 如果理想仓位大于当前仓位，加仓
            if ideal_pos > self.short_pos:
                short_signal = True
                short_pos_size = ideal_pos

                # 记录交易
                self.trades.append({
                    "time": self.api.get_server_time(),
                    "action": "ADD_SHORT",
                    "price": current_price,
                    "volume": ideal_pos - self.short_pos,
                    "reason": f"JJ({jj}) < 涨上限仓({zhang_upper_pos}),涨下限仓({zhang_lower_pos}) > 跌下限仓({die_lower_pos})"
                })

        # 空头平仓条件
        if (jj > zhang_upper_pos or self.check_ud_uptrend()) and self.short_pos > 0:
            short_exit = True
            short_signal = False

            # 记录交易
            self.trades.append({
                "time": self.api.get_server_time(),
                "action": "CLOSE_SHORT",
                "price": current_price,
                "volume": self.short_pos,
                "reason": f"JJ({jj}) > 涨上限仓({zhang_upper_pos}) 或 UD连续上升3期"
            })

        # 空头止损检查
        if self.short_pos > 0 and current_price > self.short_stop_loss:
            short_exit = True
            short_signal = False

            # 记录交易
            self.trades.append({
                "time": self.api.get_server_time(),
                "action": "STOP_LOSS_SHORT",
                "price": current_price,
                "volume": self.short_pos,
                "reason": f"价格({current_price}) > 止损价({self.short_stop_loss})"
            })

        # 账户回撤检查
        current_drawdown = (self.initial_account_balance - self.account.balance) / self.initial_account_balance
        if current_drawdown > self.account_drawdown_limit:
            # 如果账户回撤超过限制，减半仓位
            if self.long_pos > 0:
                long_pos_size = max(1, self.long_pos // 2)
                long_signal = True
            if self.short_pos > 0:
                short_pos_size = max(1, self.short_pos // 2)
                short_signal = True

            # 记录风险控制
            self.trades.append({
                "time": self.api.get_server_time(),
                "action": "RISK_CONTROL",
                "price": current_price,
                "reason": f"账户回撤({current_drawdown:.2%})超过限制({self.account_drawdown_limit:.2%})"
            })

        # 执行交易
        # 多头交易
        if long_exit:
            self.target_pos.set_target_volume(0)  # 平掉多头
        elif long_signal:
            self.target_pos.set_target_volume(long_pos_size)  # 设置多头目标仓位

        # 空头交易
        if short_exit:
            self.target_pos.set_target_volume(0)  # 平掉空头
        elif short_signal:
            self.target_pos.set_target_volume(-short_pos_size)  # 设置空头目标仓位（负数表示空头）

        # 日志记录
        self.log_status(indicators)

    def log_status(self, indicators):
        """记录当前策略状态"""
        log_time = self.api.get_server_time()
        formatted_time = datetime.datetime.fromtimestamp(log_time / 1e9).strftime('%Y-%m-%d %H:%M:%S')

        log_msg = (
            f"[{formatted_time}] 价格: {self.quote.last_price}, "
            f"JJ: {indicators['jj']}, 涨上限仓: {indicators['zhang_upper_pos']}, "
            f"跌上限仓: {indicators['die_upper_pos']}, 涨下限仓: {indicators['zhang_lower_pos']}, "
            f"跌下限仓: {indicators['die_lower_pos']}, 标准仓: {indicators['standard_pos']}, "
            f"多头持仓: {self.long_pos}, 空头持仓: {self.short_pos}, "
            f"账户余额: {self.account.balance}, 浮动盈亏: {self.account.float_profit}"
        )

        print(log_msg)

    def run(self):
        """运行策略"""
        print("策略启动...")

        try:
            while True:
                # 更新行情
                self.api.wait_update()

                # 检查是否有行情更新
                if self.api.is_changing(self.klines.iloc[-1], "datetime"):
                    # 计算指标
                    indicators = self.calculate_indicators()
                    if indicators:
                        # 调整仓位
                        self.adjust_position(indicators)

                # 检查止损
                if self.api.is_changing(self.quote, "last_price"):
                    current_price = self.quote.last_price

                    # 多头止损检查
                    if self.long_pos > 0 and current_price < self.long_stop_loss:
                        self.target_pos.set_target_volume(0)
                        print(f"[{self.api.get_server_time()}] 多头止损触发: 价格={current_price}, 止损价={self.long_stop_loss}")

                    # 空头止损检查
                    if self.short_pos > 0 and current_price > self.short_stop_loss:
                        self.target_pos.set_target_volume(0)
                        print(f"[{self.api.get_server_time()}] 空头止损触发: 价格={current_price}, 止损价={self.short_stop_loss}")

        except Exception as e:
            print(f"策略运行异常: {e}")
        finally:
            # 清空所有持仓
            if self.long_pos != 0 or self.short_pos != 0:
                self.target_pos.set_target_volume(0)
                print("清空所有持仓")

            # 输出交易记录
            self.print_trade_summary()

    def print_trade_summary(self):
        """打印交易汇总"""
        if not self.trades:
            print("无交易记录")
            return

        print("\n===== 交易记录汇总 =====")
        for i, trade in enumerate(self.trades):
            trade_time = datetime.datetime.fromtimestamp(trade["time"] / 1e9).strftime('%Y-%m-%d %H:%M:%S')
            print(f"{i + 1}. [{trade_time}] {trade['action']} 价格:{trade.get('price', 'N/A')} 数量:{trade.get('volume', 'N/A')}")
            print(f"   原因: {trade.get('reason', 'N/A')}")

        print("\n===== 策略表现 =====")
        print(f"初始资金: {self.initial_account_balance}")
        print(f"最终资金: {self.account.balance}")
        print(f"净盈亏: {self.account.balance - self.initial_account_balance}")
        print(f"收益率: {(self.account.balance / self.initial_account_balance - 1) * 100:.2f}%")
        print(f"最大持仓: 多头-{max([t.get('volume', 0) for t in self.trades if t.get('action') in ['OPEN_LONG', 'ADD_LONG']] or [0])}, "
              f"空头-{max([t.get('volume', 0) for t in self.trades if t.get('action') in ['OPEN_SHORT', 'ADD_SHORT']] or [0])}")


def main():
    """主函数，运行策略"""
    # 天勤API认证，需要替换为实际的天勤账户和密码
    api = TqApi(auth=TqAuth("bigwolf", "ftp123"))

    try:
        # 创建策略实例
        symbol = "SHFE.au2512"  # 以上海黄金期货为例，需要替换为实际交易的合约
        strategy = KDJVariantStrategy(api, symbol)

        # 运行策略
        strategy.run()

    except Exception as e:
        print(f"程序运行异常: {e}")

    finally:
        # 关闭API连接
        api.close()
        print("程序已安全退出")


if __name__ == "__main__":
    main()