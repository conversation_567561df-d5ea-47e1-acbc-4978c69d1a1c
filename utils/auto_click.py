import time

import pyautogui


# im1 = pyautogui.screenshot()
# im1.save('my_screenshot.png')
# im2 = pyautogui.screenshot('my_screenshot2.png')
# button7location = pyautogui.locateOnScreen('buy.png')
# buttonx, buttony = pyautogui.center(button7location)
# pyautogui.click(buttonx, buttony)

def get_the_pos():
    while True:
        currentMouseX, currentMouseY = pyautogui.position()
        print(currentMouseX, currentMouseY)


def clickmouse(x, y):
    i = 0
    j = 0
    while j < 2:
        for i in range(100):
            pyautogui.click(x, y)
            time.sleep(1.1)
            i += 1
            print(i)
            # time.sleep(1)

        j += 1


# get_the_pos()
buypos = [252, 1341]
sellpos = [475,1341]

# clickmouse(buypos[0], buypos[1])
clickmouse(sellpos[0], sellpos[1])