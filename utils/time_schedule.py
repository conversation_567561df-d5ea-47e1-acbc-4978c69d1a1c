'''
定时程序：
1、获取交易品种
2、获取当前交易品种的交易时间
3、交易时间段传送给交易时间判断程序
4、开始交易主控程序运行

'''

# 交易品种id
product_id = 'OI'

def get_trading_intervals(product_id):
    from tqsdk import TqApi, TqKq
    trading_intervals = list()
    api = TqApi(TqKq(), auth='wolfquant,ftp123', disable_print=True)
    symbol = api.query_cont_quotes(product_id=product_id)[0]
    symbol_info = api.query_symbol_info(symbol)
    trading_intervals.append(symbol_info.trading_time_day)
    trading_intervals.append(symbol_info.trading_time_night)
    api.close()

    return trading_intervals

trading_intervals= get_trading_intervals(product_id)
print(trading_intervals)/

