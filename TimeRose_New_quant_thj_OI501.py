from TimeRoseMA_cross_multi_interval_class_base_1 import timeRoseMultiInterval, pos_interval_info

if __name__ == '__main__':
    import pandas as pd

    symbol = 'CZCE.OI501'
    account = 'quant_thj,Qiai1301'
    pos_interval_info_file = account.split(',')[0] + '_' + symbol.split('.')[1] + '_pos_interval_info.pkl'

    try:
        instance_map = pd.read_pickle(pos_interval_info_file)
        print('持仓数据读取完成。')
    except Exception as e:
        print(e)

        s60 = pos_interval_info(0, 10, 0, 0, 1)
        s180 = pos_interval_info(0, 30, 0, 0, 3)
        s300 = pos_interval_info(0, 50, 0, 0, 5)
        s900 = pos_interval_info(0, 80, 0, 0, 6)

        instance_map = {
            60: s60,
            180: s180,
            300: s300,
            900: s900
        }
        print('持仓数据不存在，初始化持仓数据完成。')

    bot = timeRoseMultiInterval(account, symbol, bklimit=20, sklimit=20, singlevolume=1,
                                interval_parameters=instance_map, pos_instance_file=pos_interval_info_file)

    bot.run()

