
from strategies.TimeRoseMA_cross import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date


    product = 'OI'
    symbol=product
    interval = 60*15
    bklimit = 300
    sklimit = 300
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True)
    symbol = api.query_cont_quotes(product_id=product)[0]
    symbol = "CZCE.OI501"
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


runstrategy()
