import json
import pandas as pd
import plotly.graph_objects as go

# --- Load Data from JSON File ---
file_path = 'simulatedaysummary_new.json' # Make sure this file is in the same directory or provide full path
try:
    with open(file_path, 'r', encoding='utf-8') as f: # Added encoding='utf-8' for broader compatibility
        data = json.load(f)
except FileNotFoundError:
    print(f"Error: File not found at {file_path}")
    print("Please make sure 'summary_new.json' is in the correct directory.")
    exit()
except json.JSONDecodeError:
    print(f"Error: Could not decode JSO<PERSON> from {file_path}")
    print("Please ensure the file contains valid JSON.")
    exit()

# --- Data Processing ---
processed_data = []
for date_str, records in data.items():
    try:
        date_obj = pd.to_datetime(date_str)
        for record in records:
            # Basic check if 'user_name' and 'balance' exist
            if 'user_name' in record and 'balance' in record:
                processed_data.append({
                    'date': date_obj,
                    'user_name': record['user_name'],
                    'balance': record['balance']
                })
            else:
                print(f"Warning: Skipping record on {date_str} due to missing 'user_name' or 'balance': {record}")
    except ValueError:
        print(f"Warning: Skipping invalid date format: {date_str}")


if not processed_data:
    print("Error: No valid data could be processed from the JSON file.")
    exit()

df = pd.DataFrame(processed_data)

# Ensure balance is numeric, coerce errors to NaN and then drop rows with NaN balance
df['balance'] = pd.to_numeric(df['balance'], errors='coerce')
df.dropna(subset=['balance'], inplace=True)


df = df.sort_values(by=['user_name', 'date'])

# --- Create Plot ---
fig = go.Figure()

# Get unique user names, sort them for consistent dropdown order
user_names = sorted(df['user_name'].unique())

if not user_names:
    print("Error: No users found in the processed data.")
    exit()

# Add traces for each user
for i, user in enumerate(user_names):
    user_df = df[df['user_name'] == user]
    if not user_df.empty: # Only add trace if there's data for the user
        fig.add_trace(
            go.Scatter(
                x=user_df['date'],
                y=user_df['balance'],
                mode='lines+markers',
                name=user,
                visible=(i == 0) # Make only the first user visible initially
            )
        )

# --- Create Dropdown Menu ---
buttons = []
# Create visibility list template
num_traces = len(fig.data) # Get the actual number of traces added

for i, user in enumerate(user_names):
     # Find the index of the trace corresponding to the current user
     # This is safer if some users ended up having no data and no trace was added
    trace_index = -1
    for idx, trace in enumerate(fig.data):
        if trace.name == user:
            trace_index = idx
            break

    if trace_index != -1: # Only add button if a trace exists for the user
        # Create visibility list: True for the current user's trace, False for others
        visibility = [False] * num_traces
        visibility[trace_index] = True

        buttons.append(dict(
            label=user,
            method='update',
            args=[
                {'visible': visibility}, # Update trace visibility
                {'title': f"Account Balance Over Time: {user}"} # Update title
            ]
        ))

# Determine initial active button index
initial_active_index = 0
for idx, btn in enumerate(buttons):
    if btn['label'] == user_names[0]:
        initial_active_index = idx
        break

# Add dropdown to layout
fig.update_layout(
    updatemenus=[
        dict(
            active=initial_active_index, # Default selection index corresponding to the first user's button
            buttons=buttons,
            direction="down",
            pad={"r": 10, "t": 10},
            showactive=True,
            x=0.05, # Horizontal position
            xanchor="left",
            y=1.15, # Vertical position (above the plot)
            yanchor="top",
            bgcolor = '#333333', # Darker grey for dropdown background
            bordercolor = '#FFFFFF',
            font = {'color': '#FFFFFF'}
        )
    ]
)

# --- Configure Layout for Dark Theme ---
fig.update_layout(
    title=f"Account Balance Over Time: {user_names[0]}", # Initial title
    xaxis_title="Date",
    yaxis_title="Balance (Net Value)",
    hovermode="x unified", # Show info for all points at a given x
    template="plotly_dark", # <--- APPLY DARK THEME HERE
    # Optional: Further fine-tuning if needed (plotly_dark usually handles most)
    # paper_bgcolor='black', # Background outside plotting area
    # plot_bgcolor='black',  # Background inside plotting area
    # font=dict(color='white'), # Default font color
    # xaxis=dict(gridcolor='grey'), # Color of vertical grid lines
    # yaxis=dict(gridcolor='grey')  # Color of horizontal grid lines
)

# --- Show Plot ---
fig.show()