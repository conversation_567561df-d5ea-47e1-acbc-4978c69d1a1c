from tqsdk import TqApi, TqAccount, TqKq
from tqsdk.tafunc import time_to_str
from loguru import logger as mylog
import datetime

from data_manager import DataManager
from strategies.weiswave_claude_dataframe import calculate_signals, wave_signal_trading

mylog.add('weiswave' + '.log', encoding='utf-8')

if __name__ == "__main__":

    product = 'rb'
    interval = 60 * 3
    long_limit = 10
    short_limit = 10
    single_volume = 1
    close_today_first = True  # 是否优先平今仓（True=优先平今，False=优先平昨）

    from accounts_zjy import hsqhzjy as acct

    # 交易账号设置
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # api = TqApi(TqKq(), auth="bigwolf,ftp123")
    symbol = api.query_cont_quotes(product_id=product)[0]
    print(symbol)

    # 创建数据管理器
    data_manager = DataManager(symbol, interval)
    mylog.info(f"数据文件名: {data_manager.get_filename()}")

    # 初始化数据（加载历史数据并与API数据合并）
    bars = data_manager.initialize_data(api, data_length=8964)
    
    # 获取实时数据监控用的K线序列
    bartmp = api.get_kline_serial(symbol, duration_seconds=interval, data_length=10)

    # 计算信号并初始化交易（bars保持纯K线数据不变）
    df = calculate_signals(bars)
    position_mgr = wave_signal_trading(df, long_limit, short_limit, single_volume, api, close_today_first)
    
    # 记录初始化交易结果
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    mylog.info(f"=== 初始化交易结果 [{current_time}] ===")
    mylog.info(f"交易品种: {position_mgr.get('symbol', 'N/A')}")
    mylog.info(f"当前价格: {position_mgr.get('current_price', 'N/A')}")
    mylog.info(f"当前信号: {position_mgr.get('current_signal', 'N/A')}")
    mylog.info(f"多单持仓: {position_mgr.get('long_position', 'N/A')}手")
    mylog.info(f"空单持仓: {position_mgr.get('short_position', 'N/A')}手")
    mylog.info(f"信号: {df.signal.iloc[-1]}, 止损价: {df.G.iloc[-1]}")
    mylog.info(f"输出时间: {current_time}")
    
    print(df.signal.iloc[-1], df.G.iloc[-1])

    while True:
        api.wait_update()
        if api.is_changing(bartmp.iloc[-1], "datetime"):

            position = api.get_position(symbol)
            acc = api.get_account()
            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today

            # 使用数据管理器添加新K线数据（bars始终保持纯K线数据）
            newk = bartmp.iloc[-2]
            bars, data_updated = data_manager.add_new_kline(bars, newk)

            # 只有在数据更新时才重新计算信号并执行交易
            if data_updated:
                # 重新计算信号并执行交易（bars保持纯K线数据不变）
                wave_data = calculate_signals(bars)
                position_mgr = wave_signal_trading(wave_data, long_limit, short_limit, single_volume, api, close_today_first)
                
                # 记录交易结果到日志
                output_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                mylog.info(f"=== 交易执行结果 [{output_time}] ===")
                mylog.info(f"K线时间: {time_to_str(newk.datetime)}")
                mylog.info(f"输出时间: {output_time}")
                mylog.info(f"交易品种: {position_mgr.get('symbol', 'N/A')}")
                mylog.info(f"当前价格: {position_mgr.get('current_price', 'N/A')}")
                mylog.info(f"当前信号: {position_mgr.get('current_signal', 'N/A')}")
                mylog.info(f"策略多单持仓: {position_mgr.get('long_position', 'N/A')}手")
                mylog.info(f"策略空单持仓: {position_mgr.get('short_position', 'N/A')}手")
                
                # 记录实际账户持仓对比
                mylog.info(f"实际账户持仓 - 多单总计:{bkvol}手(历史:{bkvol_yd}手,当日:{bkvol_td}手)")
                mylog.info(f"实际账户持仓 - 空单总计:{skvol}手(历史:{skvol_yd}手,当日:{skvol_td}手)")
                
                # 记录信号和止损信息
                mylog.info(f"交易信号: {wave_data.signal.iloc[-1]}")
                mylog.info(f"止损价格: {wave_data.G.iloc[-1]}")
                
                # 如果有持仓管理器对象，记录更多详细信息
                if 'position_manager' in position_mgr and position_mgr['position_manager'] is not None:
                    pos_mgr_obj = position_mgr['position_manager']
                    mylog.info(f"持仓管理器状态: {pos_mgr_obj.positions}")
                
                mylog.info("=" * 60)
                