from strategies.weiswave_claude_dataframe import calculate_signals, wave_signal
from strategies import be_apart_from
import copy
from addict import Dict
from tqsdk import TqApi, TqAccount, TqKq
from tqsdk.tafunc import time_to_str
from tradefuncs import *
from loguru import logger as mylog
import pandas as pd
import json
import argparse
import sys
import os



def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Weiswave trading strategy')
    parser.add_argument('-c', '--config',
                      default='config.json',
                      help='Configuration file path (default: config.json)')
    return parser.parse_args()

def load_config(config_file):
    """Load and validate configuration from JSON file"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # Validate required fields
        required_fields = ['product', 'symbol', 'interval', 'bklimit',
                         'sklimit', 'single_volume', 'auth']
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field '{field}' in config file")

        return config
    except FileNotFoundError:
        print(f"Error: Configuration file '{config_file}' not found")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON format in '{config_file}'")
        sys.exit(1)
    except ValueError as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

def initialize_trading(config):
    """Initialize trading environment with configuration"""
    try:
        # Setup logging with new naming convention
        log_file = f"weiswave_{config['auth']['username']}_{config['product']}_{config['interval']}.log"
        mylog.add(log_file, encoding='utf-8')
        mylog.info(f"Starting strategy with config: {config}")

        # Initialize API connection
        api = TqApi(
            TqKq(),
            auth=f"{config['auth']['username']},{config['auth']['password']}"
        )

        # Get kline data
        barsinit = api.get_kline_serial(
            config['symbol'],
            duration_seconds=config['interval'],
            data_length=8964
        )

        bartmp = api.get_kline_serial(
            config['symbol'],
            duration_seconds=config['interval'],
            data_length=10
        )

        bars = copy.deepcopy(barsinit)
        del barsinit

        return api, bars, bartmp

    except Exception as e:
        mylog.exception(f"Initialization error: {str(e)}")
        sys.exit(1)

def handle_trading_signals(api, df, position, config):
    """Handle trading signals and execute orders"""
    current_signal = df.signal.iloc[-1]
    current_price = df.close.iloc[-1]
    current_G = df.G.iloc[-1]

    try:
        # 处理多信号
        if current_signal == '多':
            # 如果有空头持仓且盈利超过阈值，优先平空
            if position.pos_short > 0 and position.float_profit_short > 5 * position.pos_short:
                order_price = current_price + 1
                BP(api, symbol=config['symbol'], order_price=order_price, volume=config['single_volume'])
                mylog.info(f"空单平仓: 价格={order_price}, 数量={config['single_volume']}")

            # 如果多头持仓小于限制，开多仓
            if position.pos_long < config['bklimit']:
                order_price = current_price + 1
                BK(api, symbol=config['symbol'], order_price=order_price, volume=config['single_volume'])
                mylog.info(f"多单开仓: 价格={order_price}, 数量={config['single_volume']}")

        # 处理空信号
        elif current_signal == '空':
            # 如果有多头持仓且盈利超过阈值，优���平多
            if position.pos_long > 0 and position.float_profit_long > 5 * position.pos_long:
                order_price = current_price - 1
                SP(api, symbol=config['symbol'], order_price=order_price, volume=config['single_volume'])
                mylog.info(f"多单平仓: 价格={order_price}, 数量={config['single_volume']}")

            # 如果空头持仓小于限制，开空仓
            if position.pos_short < config['sklimit']:
                order_price = current_price - 1
                SK(api, symbol=config['symbol'], order_price=order_price, volume=config['single_volume'])
                mylog.info(f"空单开仓: 价格={order_price}, 数量={config['single_volume']}")

        # 记录信号和状态
        mylog.info(f"Signal: {current_signal}, G: {current_G}, Price: {current_price}, "
                   f"Positions - Long: {position.pos_long}, Short: {position.pos_short}, "
                   f"Profit - Long: {position.float_profit_long}, Short: {position.float_profit_short}")

    except Exception as e:
        mylog.exception(f"Trading error: {str(e)}")

def run_strategy(api, bars, bartmp, config):
    """Run the main trading strategy"""
    try:
        # Initial signal calculation
        df = calculate_signals(bars)
        wave_signal(df)
        mylog.info(f"Initial signal: {df.signal.iloc[-1]}, G: {df.G.iloc[-1]}")

        while True:
            api.wait_update()
            if api.is_changing(bartmp.iloc[-1], "datetime"):
                # Get position and account information
                position = api.get_position(config['symbol'])
                acc = api.get_account()

                # Update bars data
                newk = bartmp.iloc[-2]
                bdt = bars.datetime.tolist()
                tt = newk.datetime

                if tt in bdt:
                    mylog.info('发现重复数据, 跳过...', time_to_str(tt))
                else:
                    newk = newk.to_frame()
                    newk = newk.T
                    bars = pd.concat([bars, newk], ignore_index=True)

                # Update signals and handle trading
                df = calculate_signals(bars)
                wave_signal(df)
                handle_trading_signals(api, df, position, config)

    except KeyboardInterrupt:
        mylog.info("Strategy stopped by user")
    except Exception as e:
        mylog.exception(f"Strategy error: {str(e)}")
    finally:
        api.close()

def main():
    """Main entry point"""
    # Parse command line arguments
    args = parse_arguments()

    # Load configuration
    config = load_config(args.config)

    try:
        # Initialize trading environment
        api, bars, bartmp = initialize_trading(config)

        # Run strategy
        run_strategy(api, bars, bartmp, config)

    except Exception as e:
        mylog.exception(f"Fatal error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()