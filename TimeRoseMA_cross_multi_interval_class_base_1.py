import copy
import sys
import os
import pandas as pd
import numpy as np
# from myfunction import *
# from MyTT import CROSS, MA

from tqsdk import TqApi, TqKq, TqAccount, TqSim, TqBacktest, TqAuth
from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from speaktext import speak_text
from loguru import logger as mylog
import time

from datetime import datetime, date, timedelta

speak_text('程序开始运行')


def parse_time(time_str):
    hours, minutes, seconds = map(int, time_str.split(':'))
    return timedelta(hours=hours, minutes=minutes, seconds=seconds)


def get_time_period(api: TqApi, symbol: str):
    # 查询合约信息
    symbol_info = api.query_symbol_info(symbol)

    # 获取交易时间
    trading_time_day = symbol_info['trading_time_day'].to_list()[0]
    trading_time_night = symbol_info['trading_time_night'].to_list()[0]

    # 合并日间和夜间交易时间
    trading_periods = trading_time_day + trading_time_night

    return trading_periods


def is_trading_time(trading_periods):
    # 获取当前时间
    now = datetime.now()
    current_time = timedelta(hours=now.hour, minutes=now.minute, seconds=now.second)

    # 检查当前时间是否在任何一个交易时间段内
    for period in trading_periods:
        start_time = parse_time(period[0])
        end_time = parse_time(period[1])

        # 处理跨日的情况
        if end_time <= start_time:
            end_time += timedelta(days=1)

        # 调整当前时间以处理跨日情况
        adjusted_current_time = current_time
        if current_time < start_time:
            adjusted_current_time += timedelta(days=1)

        if start_time <= adjusted_current_time <= end_time:
            return True

    return False


def MA(series, n):
    return series.rolling(window=n).mean()


def VALUEWHEN(condition, value):
    return value[condition].reindex(value.index).ffill()


def disp_day_info(daymean, last3mean, hlmax, homean, olmean):
    print('最大价差:', hlmax, '平均价差:', daymean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:',
          int(olmean))


def Disp0DayInfo(quote):
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    gapcover = False

    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    print(quote.datetime, quote.instrument_id, '缺口：', gap, '回补:', gapcover, '现价:', quote.last_price, 'BB:', BB,
          '今日价差：',
          dayvib,
          '上价差:', upvib, '下价差:', dnvib)


def disp_account(acct):
    acct_info = dict(acct)
    print(acct_info)


class pos_interval_info:

    def __init__(self, poslong_limit=1, posshort_limit=1, poslong=0, posshort=0, singlevolume=1):
        self.poslong_limit = poslong_limit
        self.posshort_limit = posshort_limit
        self.poslong = poslong
        self.posshort = posshort
        self.singlevolume = singlevolume


class timeRoseMultiInterval:
    def __init__(self, acct, symbol, bklimit, sklimit, singlevolume, interval_parameters=[], pos_instance_file=None):
        print('系统开始初始化。', time.asctime(time.localtime(time.time())))

        self.acct = acct
        self.bklimit = bklimit
        self.sklimit = sklimit
        self.singlevolume = singlevolume
        self.symbol = symbol
        if isinstance(self.acct, str):
            self.api = TqApi(TqKq(), auth=self.acct, disable_print=True)
            self.acctname=self.acct.split(',')[0]
        else:
            self.api = TqApi(TqAccount(self.acct.name, self.acct.investorid, self.acct.password), auth=self.acct.tqacc)
            self.acctname=self.acct.investorid
        if pos_instance_file is None:
            pos_instance_file = self.acctname+self.symbol+"_pos_interval_info_file.pkl"
        self.logfilename=pos_instance_file.split('_')[0]+'_.log'
        mylog.add(self.logfilename, encoding='UTF-8')
        # self.api = TqApi(TqSim(), backtest=TqBacktest(start_dt=date(2024, 1, 1), end_dt=date(2024, 8, 10)), auth=TqAuth("follower","ftp123"))
        self.account = self.api.get_account()
        self.position = self.api.get_position(self.symbol)

        self.s60 = interval_parameters[60]
        self.s180 = interval_parameters[180]
        self.s300 = interval_parameters[300]
        self.s900 = interval_parameters[900]

        self.orderlist = []

        self.pos_instance_file = pos_instance_file
        self.periods = get_time_period(self.api, self.symbol)
        print('初始化完成。。。', time.asctime(time.localtime(time.time())))
        self.api.close()

    def check_fund(self, symbol, volume):
        available_fund = self.api.get_account().available
        quote = self.api.get_quote(symbol)
        fund_need = available_fund - quote.last_price * volume
        if fund_need > 0:
            return True
        else:
            return False

    def get_current_instance(self, duration):
        # 创建实例映射字典
        instance_map = {
            60: self.s60,
            180: self.s180,
            300: self.s300,
            900: self.s900
        }

        # duration = signal['interval']

        # 1. 根据传入的参数,获取相应的实例
        if duration not in instance_map:
            print(f"Error: Invalid duration {duration}")
            return

        instance = instance_map[duration]

        return instance

    def save_pos_instance(self):
        # 创建实例映射字典
        instance_map = {
            60: self.s60,
            180: self.s180,
            300: self.s300,
            900: self.s900
        }
        pd.to_pickle(instance_map, self.pos_instance_file)

    def on_signal(self, signal):
        print('收到交易信号，', signal)
        print('开始处理。。。')

        duration = signal['interval']
        symbol = signal['symbol']

        instance = self.get_current_instance(duration)

        if signal['sigtype'] == 1:
            mylog.info(f"合约：{symbol},周期：{duration},多单{instance.poslong}, 空单：{instance.posshort}, 多限:{instance.poslong_limit}, 空限：{instance.posshort_limit}, {instance.singlevolume}")
            # mylog.info([symbol, '周期', duration, '发出做多信号，下多单。', 'price:', self.quote.last_price, 'volume:', 1])

            if self.check_fund(self.symbol, instance.singlevolume):
                if instance.poslong < instance.poslong_limit:
                    orderid = self.api.insert_order(self.symbol, direction='BUY', offset='OPEN',
                                                    volume=instance.singlevolume,
                                                    limit_price=self.quote.last_price)
                    self.orderlist.append(orderid)
                    instance.poslong += instance.singlevolume

                else:
                    mylog.info(f"持仓达到限额:{instance.poslong_limit}，不再下单。")
            else:
                mylog.info('账户可用资金不足，不下单。')

            # 平仓
            if self.position.float_profit_short > 0 and self.position.position_profit_short > 0 and instance.posshort > 0:
                orderid = self.api.insert_order(self.symbol, direction='BUY', offset='CLOSE',
                                                volume=instance.singlevolume, limit_price=signal['sigprice'])
                instance.posshort -= instance.singlevolume
                mylog.info([symbol, '周期', duration, "发出平空信号，平空单"])

            else:
                mylog.info('没有达到平仓条件.pass。')

        if signal['sigtype'] == -1:
            # 信号 空
            # 下空单
            mylog.info(f"账号：{self.acctname},合约{symbol}，周期：{duration},多单{instance.poslong}, 空单：{instance.posshort}, 多限:{instance.poslong_limit}, 空限：{instance.posshort_limit}, {instance.singlevolume}")
            # mylog.info([symbol, '周期', duration, '发出做多信号，下多单。', 'price:', self.quote.last_price, 'volume:', 1])
            if self.check_fund(self.symbol, instance.singlevolume):
                if instance.posshort < instance.posshort_limit:
                    orderid = self.api.insert_order(self.symbol, direction='SELL', offset='OPEN',
                                                    volume=instance.singlevolume,
                                                    limit_price=self.quote.last_price)
                    self.orderlist.append(orderid)
                    instance.posshort += instance.singlevolume
                    mylog.info(['发出做空信号， 下空单', 'price:', self.quote.last_price, 'volume:', 1])
                else:
                    mylog.info(f"空单持仓达到限额:{instance.posshort_limit}，不再下单。")
            else:
                mylog.info('账户可用资金不足，不下单。')

            # 平多单
            if self.position.position_profit_long > 0 and self.position.float_profit_long > 0 and instance.poslong > 0:
                orderid = self.api.insert_order(self.symbol, direction='SELL', offset='CLOSE',
                                                volume=instance.singlevolume, limit_price=signal['sigprice'])
                instance.poslong -= instance.singlevolume
                mylog.info('发出平多信号，平多单')
            else:
                mylog.info('多单没有达到平仓条件.pass')

        # 保存仓位信息
        self.save_pos_instance()
        print('处理完成。。。')

    def on_bar(self, bars):
        bars = copy.deepcopy(bars)
        symbol = bars.iloc[-1].symbol
        duration = bars.iloc[-1].duration

        bars['MA13'] = MA(bars['close'], 13)
        bars['signal'] = np.where(bars['close'] > bars['MA13'], 1, np.where(bars['close'] < bars['MA13'], -1, 0))

        symbolname = symbol.split('.')[1]
        interval = int(duration / 60)
        instance = self.get_current_instance(duration)
        # print(f"账号{self.acctname}，合约：{self.symbol}, 当前周期:{duration}, 多单:{instance.poslong}, 空单:{instance.posshort}。")
        print(f"账号：{self.acctname},合约{symbol}，周期：{duration},多单{instance.poslong}, 空单：{instance.posshort}, 多限:{instance.poslong_limit}, 空限：{instance.posshort_limit}, {instance.singlevolume}")
        if bars.signal.iloc[-2] == -1 and bars.signal.iloc[-1] == 1:
            signal = {'symbol': symbol, 'interval': duration, 'sigtype': 1, 'sigprice': bars.close.iloc[-1]}
            self.on_signal(signal)
            mylog.info(signal)
            speak_text(symbolname + '周期' + str(interval) + '分钟' + '发出做多信号。')

        if bars.signal.iloc[-2] == 1 and bars.signal.iloc[-1] == -1:
            signal = {'symbol': symbol, 'interval': duration, 'sigtype': -1, 'sigprice': bars.close.iloc[-1]}
            self.on_signal(signal)
            mylog.info(signal)
            speak_text(symbolname + '周期' + str(interval) + '分钟' + '发出做空信号。')

        return bars

    def update_bars(self, bars, bar):
        barsduration = bars.iloc[0].duration
        tmpbarduration = bar.duration
        if barsduration == tmpbarduration:
            tt = bar.datetime
            print(time_to_str(bar.datetime), bar.symbol, bar.duration)
            # print(type(newk.duration))
            bdt = bars.datetime.tolist()
            if tt not in bdt:
                newktmp = bar.to_frame()
                newktmp = newktmp.T
                bars = pd.concat([bars, newktmp], ignore_index=True)
                print(bar.duration, self.barupdateinfo)
                # speak_text(str(newktmp.duration)+'updated.')
            else:
                print('发现重复数据，跳过。。。')

        else:
            print('k线周期不一致，跳过， 请检查程序和数据。')

        return bars

    def run(self):

        while True:
            if is_trading_time(self.periods):
                try:
                    print('交易时间...', time.asctime())
                    print(self.acctname)
                    # del self.api
                    if isinstance(self.acct, str):
                        self.api = TqApi(TqKq(), auth=self.acct, disable_print=True)
                    else:
                        self.api = TqApi(TqAccount(self.acct.name, self.acct.investorid, self.acct.password),
                                         auth=self.acct.tqacc)
                    print('api重新连接成功...', time.asctime())
                    self.account = self.api.get_account()
                    self.position = self.api.get_position(self.symbol)
                    self.quote = self.api.get_quote(self.symbol)
                    self.bars1 = self.api.get_kline_serial(self.symbol, duration_seconds=60, data_length=8964).dropna()
                    self.bars3 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 3,
                                                           data_length=8964).dropna()
                    self.bars5 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 5,
                                                           data_length=8964).dropna()
                    self.bars15 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 15,
                                                            data_length=8964).dropna()

                    self.barstmp1 = self.api.get_kline_serial(self.symbol, duration_seconds=60, data_length=8)
                    self.barstmp3 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 3, data_length=8)
                    self.barstmp5 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 5, data_length=8)
                    self.barstmp15 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 15, data_length=8)
                    self.barupdateinfo = 'Bar Data Updated...'
                    print('初始化完成。。。', time.asctime(time.localtime(time.time())))

                    while True:
                        self.api.wait_update()

                        if self.api.is_changing(self.barstmp1.iloc[-1], "datetime"):
                            newk = self.barstmp1.iloc[-2]
                            self.bars1 = self.update_bars(self.bars1, newk)
                            self.on_bar(self.bars1)

                        if self.api.is_changing(self.barstmp3.iloc[-1], "datetime"):
                            newk = self.barstmp3.iloc[-2]
                            self.bars3 = self.update_bars(self.bars3, newk)
                            self.on_bar(self.bars3)

                        if self.api.is_changing(self.barstmp5.iloc[-1], "datetime"):
                            newk = self.barstmp5.iloc[-2]
                            self.bars5 = self.update_bars(self.bars5, newk)
                            self.on_bar(self.bars5)

                        if self.api.is_changing(self.barstmp15.iloc[-1], "datetime"):
                            newk = self.barstmp15.iloc[-2]
                            self.bars15 = self.update_bars(self.bars15, newk)
                            self.on_bar(self.bars15)

                        if not is_trading_time(self.periods):
                            print('非交易时间，关闭api.', time.asctime())
                            self.api.close()


                except Exception as e:
                    print(e)
                    print('api connect failed, please check...', time.asctime())
                    time.sleep(60)


            else:
                print(f"{self.acctname}:not in the trading time..., {time.asctime()}")
                self.api.close()
                print(self.api)
                time.sleep(60)


if __name__ == "__main__":
    import pandas as pd

    symbol = 'CZCE.OI501'
    account = 'quant_tyc,Qiai1301'
    pos_interval_info_file = account.split(',')[0] + '_' + symbol.split('.')[1] + '_pos_interval_info.pkl'

    try:
        instance_map = pd.read_pickle(pos_interval_info_file)
    except Exception as e:
        print(e)

        s60 = pos_interval_info(1, 1, 0, 0, 1)
        s180 = pos_interval_info(3, 3, 0, 0, 1)
        s300 = pos_interval_info(5, 5, 0, 0, 1)
        s900 = pos_interval_info(15, 15, 0, 0, 1)

        instance_map = {
            60: s60,
            180: s180,
            300: s300,
            900: s900
        }

    bot = timeRoseMultiInterval(account, symbol, bklimit=20, sklimit=20, singlevolume=1,
                                interval_parameters=instance_map, pos_instance_file=pos_interval_info_file)

    bot.run()
