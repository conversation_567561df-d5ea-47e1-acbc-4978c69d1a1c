import pandas as pd
from loguru import logger
from datetime import date, datetime, timedelta
from tqsdk import TqApi, TqAuth, TqSim, TqBacktest, TargetPosTask, BacktestFinished
from czsc import Freq, RawBar
from czsc.strategies import CzscStrategyExample2 as Strategy


def format_kline(df, freq=Freq.F1):
    """对分钟K线进行格式化"""
    freq = Freq(freq)
    rows = df.to_dict('records')
    raw_bars = []
    for i, row in enumerate(rows):
        bar = RawBar(symbol=row['symbol'], id=i, freq=freq,
                     dt=datetime.fromtimestamp(row["datetime"] / 1e9) + timedelta(minutes=1),
                     open=row['open'], close=row['close'], high=row['high'],
                     low=row['low'], vol=row['volume'], amount=row['volume'] * row['close'])
        raw_bars.append(bar)
    return raw_bars


def run_multi_main_symbol(**kwargs):
    """执行多个主力连续合约的策略"""

    tq_user = kwargs.get("tq_user", "bigwolf")
    tq_pwd = kwargs.get("tq_pwd", "ftp123")

    # tq_user = "smartmanp"
    # tq_pwd = "ftp123"

    init_money = int(kwargs.get("init_money", 1000000))
    sdt = kwargs.get("sdt", "20180101")
    edt = kwargs.get("edt", "20250728")
    sdt = pd.to_datetime(sdt).date()
    edt = pd.to_datetime(edt).date()

    api = TqApi(TqSim(init_money), web_gui=True, auth=TqAuth(tq_user, tq_pwd),
                backtest=TqBacktest(start_dt=sdt, end_dt=edt))

    symbols = ["KQ.m@DCE.i", "KQ.m@DCE.j", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    # 货值等权配置手数
    pos_multiplier = {"KQ.m@DCE.i": 3, "KQ.m@DCE.j": 1, "<EMAIL>": 2, "<EMAIL>": 5, "<EMAIL>": 5}

    metas = {symbol: {} for symbol in symbols}
    for symbol in symbols:
        tactic = Strategy(symbol=symbol)
        kline = api.get_kline_serial(symbol, int(tactic.base_freq.strip('分钟')) * 60, data_length=10000, adj_type='B')
        quote = api.get_quote(symbol)
        raw_bars = format_kline(kline, freq=tactic.base_freq)
        trader = tactic.init_trader(raw_bars, sdt=date(2017, 10, 1))
        target_pos = TargetPosTask(api, quote.underlying_symbol)
        metas[symbol] = {
            "symbols": symbol,
            "kline": kline,
            "quote": quote,
            "trader": trader,
            "base_freq": tactic.base_freq,
            "target_pos": target_pos
        }

    try:
        while api.wait_update():
            for symbol, meta in metas.items():
                kline = meta["kline"]
                quote = meta["quote"]
                trader = meta["trader"]
                target_pos = meta["target_pos"]

                if api.is_changing(quote, "underlying_symbol"):
                    # 主力连续合约换月
                    logger.info(f"主力换月：{quote.datetime} - {quote.underlying_symbol}")
                    target_pos.set_target_volume(0)
                    target_pos = TargetPosTask(api, quote.underlying_symbol)

                if api.is_changing(kline.iloc[-1], "datetime"):
                    new_bars = format_kline(kline.tail(10), freq=meta['base_freq'])
                    new_bars = [x for x in new_bars if x.dt > trader.end_dt]

                    for bar in new_bars:
                        trader.update(bar)
                    target_pos.set_target_volume(trader.get_ensemble_pos('vote') * pos_multiplier[symbol])

                metas[symbol] = {
                    "symbols": symbol,
                    "kline": kline,
                    "quote": quote,
                    "trader": trader,
                    "base_freq": meta['base_freq'],
                    "target_pos": target_pos
                }
    except BacktestFinished as e:
        while True:
            api.wait_update()


def show_usage_help():
    """显示使用帮助和示例"""
    print("缠论策略回测程序")
    print("=" * 80)
    print("程序功能:")
    print("  * 基于缠论技术分析的多合约策略回测")
    print("  * 支持多个主力连续合约同时交易")
    print("  * 使用CZSC缠论策略框架")
    print()
    print("默认配置:")
    print("  * 初始资金: 1,000,000 元")
    print("  * 回测时间: 2018-01-01 到 2025-07-28")
    print("  * 交易合约: 铁矿石、焦炭、焦煤、热卷、螺纹钢")
    print("  * 策略类型: CzscStrategyExample2")
    print()
    print("使用示例:")
    print("  1. 使用默认配置:")
    print("     python chanlun_strategy.py")
    print()
    print("  2. 自定义时间范围:")
    print("     python chanlun_strategy.py --start-date 20200101 --end-date 20241231")
    print()
    print("  3. 自定义初始资金:")
    print("     python chanlun_strategy.py --init-money 500000")
    print()
    print("  4. 完整自定义:")
    print("     python chanlun_strategy.py --tq-user your_user --tq-pwd your_pwd --init-money 2000000")
    print()
    print("获取完整帮助: python chanlun_strategy.py --help")
    print("=" * 80)


def interactive_parameter_input():
    """交互式参数输入"""
    print("缠论策略参数配置")
    print("=" * 50)

    params = {}

    # TQ账户信息
    print("TQ账户配置:")
    try:
        tq_user = input("TQ用户名 (默认: smartmanp): ").strip()
        if not tq_user:
            tq_user = "smartmanp"
        params['tq_user'] = tq_user

        tq_pwd = input("TQ密码 (默认: ftp123): ").strip()
        if not tq_pwd:
            tq_pwd = "ftp123"
        params['tq_pwd'] = tq_pwd
    except KeyboardInterrupt:
        print("\n用户取消操作")
        return None

    # 初始资金
    print(f"\n资金配置:")
    try:
        init_money = input("初始资金 (默认: 1000000): ").strip()
        if not init_money:
            init_money = 1000000
        else:
            init_money = int(init_money)
        params['init_money'] = init_money
    except (ValueError, KeyboardInterrupt):
        if isinstance(init_money, str):
            print("初始资金必须是数字")
            return None
        else:
            print("\n用户取消操作")
            return None

    # 回测时间范围
    print(f"\n回测时间配置:")
    try:
        start_date = input("开始日期 (格式: YYYYMMDD, 默认: 20180101): ").strip()
        if not start_date:
            start_date = "20180101"
        params['sdt'] = start_date

        end_date = input("结束日期 (格式: YYYYMMDD, 默认: 20250728): ").strip()
        if not end_date:
            end_date = "20250728"
        params['edt'] = end_date
    except KeyboardInterrupt:
        print("\n用户取消操作")
        return None

    print(f"\n配置完成！")
    print(f"配置参数:")
    print(f"  TQ用户名: {params['tq_user']}")
    print(f"  TQ密码: {'*' * len(params['tq_pwd'])}")
    print(f"  初始资金: {params['init_money']:,} 元")
    print(f"  回测时间: {params['sdt']} - {params['edt']}")

    return params


def main():
    """主函数"""
    import argparse
    import sys

    # 检查是否有命令行参数
    if len(sys.argv) == 1:
        # 没有参数，显示帮助和交互式输入
        show_usage_help()

        while True:
            try:
                choice = input("\n选择操作:\n  1. 交互式配置参数\n  2. 查看完整帮助\n  3. 使用默认配置运行\n  4. 退出\n请输入选择 (1-4): ").strip()

                if choice == "1":
                    # 交互式参数输入
                    params = interactive_parameter_input()
                    if params is None:
                        continue

                    # 确认运行
                    confirm = input(f"\n确认运行缠论策略回测? (y/n): ").strip().lower()
                    if confirm in ['y', 'yes']:
                        print("开始运行缠论策略回测...")
                        run_multi_main_symbol(**params)
                    else:
                        print("取消运行")
                    return

                elif choice == "2":
                    # 显示完整帮助
                    parser = create_argument_parser()
                    parser.print_help()
                    continue

                elif choice == "3":
                    # 使用默认配置
                    print("使用默认配置运行缠论策略回测...")
                    run_multi_main_symbol(
                        tq_user="smartmanp",
                        tq_pwd="ftp123",
                        init_money=1000000,
                        sdt="20180101",
                        edt="20250728"
                    )
                    return

                elif choice == "4":
                    print("再见！")
                    return

                else:
                    print("无效选择，请输入1-4")

            except KeyboardInterrupt:
                print("\n再见！")
                return
    else:
        # 有命令行参数，解析参数
        parser = create_argument_parser()
        args = parser.parse_args()

        # 运行策略
        run_multi_main_symbol(
            tq_user=args.tq_user,
            tq_pwd=args.tq_pwd,
            init_money=args.init_money,
            sdt=args.start_date,
            edt=args.end_date
        )


def create_argument_parser():
    """创建命令行参数解析器"""
    import argparse

    parser = argparse.ArgumentParser(
        description="缠论策略回测程序 - 基于CZSC框架的多合约策略回测",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用默认配置
  python %(prog)s

  # 自定义TQ账户
  python %(prog)s --tq-user your_user --tq-pwd your_pwd

  # 自定义回测时间
  python %(prog)s --start-date 20200101 --end-date 20241231

  # 自定义初始资金
  python %(prog)s --init-money 2000000

  # 完整自定义
  python %(prog)s --tq-user user --tq-pwd pwd --init-money 500000 --start-date 20220101

默认配置:
  TQ用户名: smartmanp
  TQ密码: ftp123
  初始资金: 1,000,000 元
  回测时间: 2018-01-01 到 2025-07-28
  交易合约: 铁矿石、焦炭、焦煤、热卷、螺纹钢
        """
    )

    parser.add_argument('--tq-user', default='smartmanp',
                       help='TQ用户名 (默认: smartmanp)')
    parser.add_argument('--tq-pwd', default='ftp123',
                       help='TQ密码 (默认: ftp123)')
    parser.add_argument('--init-money', type=int, default=1000000,
                       help='初始资金，单位：元 (默认: 1000000)')
    parser.add_argument('--start-date', default='20180101',
                       help='回测开始日期，格式：YYYYMMDD (默认: 20180101)')
    parser.add_argument('--end-date', default='20250728',
                       help='回测结束日期，格式：YYYYMMDD (默认: 20250728)')

    return parser


if __name__ == '__main__':
    main()