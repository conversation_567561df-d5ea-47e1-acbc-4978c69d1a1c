from tqsdk import TqKq, TqApi, TqAuth, TqAccount

api = TqApi(TqKq(), auth="follower,ftp123")
symbol = 'SHFE.rb2301'
# symbol = 'CZCE.OI301'
quote = api.get_quote(symbol)

ask_volume_list = []
bid_volume_list = []

while True:
    api.wait_update()
    if api.is_changing(quote):

        askall = quote.ask_volume1 + quote.ask_volume2 +quote.ask_volume3 + quote.ask_volume4 +quote.ask_volume5
        bidall = quote.bid_volume1 + quote.bid_volume2 +quote.bid_volume3 +quote.bid_volume4 +quote.bid_volume5
        all = askall +bidall

        askratio = askall / all
        bidratio = bidall /all
        print(int(bidratio*100), int(askratio*100))
        # print(quote.bid_volume1)
