import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

from tqsdk import TqApi, TqAuth, TqKq


# 假设我们已经有了包含开高低收(OHLC)数据的DataFrame,命名为'df'
# df = pd.DataFrame({'open': [...], 'high': [...], 'low': [...], 'close': [...]})

def MA(series, n):
    return series.rolling(window=n).mean()


def VALUEWHEN(condition, value):
    return value[condition].reindex(value.index).ffill()


def REFX1(series, n):
    return series.shift(n)


try:
    df = pd.read_csv('data.csv')
except:
    api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
    symbol = api.query_cont_quotes(product_id='OI')[0]
    symbol = 'CZCE.OI501'
    df = api.get_kline_serial(symbol, duration_seconds=60, data_length=200)
    df.to_csv('data.csv')
    api.close()

# 计算指标
df['MA1'] = MA(df['close'], 5)

df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
                     df['high'].shift(2), 0)
df['HH2'] = VALUEWHEN(df['HH1'] > 0, df['HH1'])

df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)), df['low'].shift(2),
                     0)
df['LL2'] = VALUEWHEN(df['LL1'] > 0, df['LL1'])

df['K1'] = np.where(df['close'] > df['HH2'], -3, np.where(df['close'] < df['LL2'], 1, 0))
df['K2'] = VALUEWHEN(df['K1'] != 0, df['K1'])

df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
df['G1'] = df['G'].iloc[-1]

df['W1'] = df['K2']
df['W2'] = df['open'] - df['close']
df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])

df['LLL'] = np.minimum(df['open'].shift(1), df['close'].shift(1))
df['HHH'] = np.maximum(df['open'].shift(1), df['close'].shift(1))

df['CCCC'] = REFX1(df['close'], 1000)
df['LLLL'] = REFX1(df['LLL'], 1000)
df['HHHH'] = REFX1(df['HHH'], 1000)

# 绘图

# 设置绘图风格
plt.style.use('dark_background')
fig, ax = plt.subplots(figsize=(15, 10))


# 绘制K线图

def plot_candlestick(ax, df):
    width = 0.6
    width2 = 0.05

    up = df[df.close > df.open]
    down = df[df.close < df.open]
    equal = df[df.close == df.open]

    # 上涨K线 - 现在是红色
    ax.bar(up.index, up.close - up.open, width, bottom=up.open, color='red')
    ax.vlines(up.index, up.low, up.high, color='red', linewidth=1)

    # 下跌K线 - 现在是绿色
    ax.bar(down.index, down.close - down.open, width, bottom=down.open, color='green')
    ax.vlines(down.index, down.low, down.high, color='green', linewidth=1)

    # 开盘价等于收盘价的K线
    ax.bar(equal.index, width2, width, bottom=equal.open - width2 / 2, color='cyan')
    ax.vlines(equal.index, equal.low, equal.high, color='cyan', linewidth=1)


plot_candlestick(ax, df)


# 绘制MA1线
ma1_up = np.where(df['MA1'] > df['MA1'].shift(1), df['MA1'], np.nan)
ma1_down = np.where(df['MA1'] <= df['MA1'].shift(1), df['MA1'], np.nan)

# plt.plot(df.index, ma1_up, color='yellow', linewidth=4)
# plt.plot(df.index, ma1_down, color='cyan', linewidth=4)

# 绘制其他线条
for i in range(len(df) - 1):
    if df['W1'].iloc[i] == 1:
        plt.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['LT'].iloc[i]], color='cyan')
        plt.plot([df.index[i], df.index[i]], [df['high'].iloc[i], df['HT'].iloc[i]], color='cyan')
    elif df['W1'].iloc[i] == -3:
        plt.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['LT'].iloc[i]], color='red')
        plt.plot([df.index[i], df.index[i]], [df['high'].iloc[i], df['HT'].iloc[i]], color='red')

    if df['W1'].iloc[i] == 1 and df['W1'].iloc[i - 1] == 1:
        plt.plot([df.index[i - 1], df.index[i]], [df['G'].iloc[i - 1], df['G'].iloc[i]], color='limegreen')
    elif df['W1'].iloc[i] == -3 and df['W1'].iloc[i - 1] == -3:
        plt.plot([df.index[i - 1], df.index[i]], [df['G'].iloc[i - 1], df['G'].iloc[i]], color='yellow')

# 绘制CCCC, LLLL, HHHH线
# plt.plot(df.index, df['CCCC'], color='white', linestyle='--', linewidth=2)
# plt.plot(df.index, df['LLLL'], color='limegreen', linestyle='--', linewidth=1)
# plt.plot(df.index, df['HHHH'], color='magenta', linestyle='--', linewidth=1)

# 在最后一个点绘制G1的值
plt.text(df.index[-1], df['G1'].iloc[-1], f"{df['G1'].iloc[-1]:.2f}", color='cyan', fontweight='bold')

plt.title('Custom Indicator', color='white', fontweight='bold')
plt.xlabel('Date', color='white')
plt.ylabel('Price', color='white')
plt.grid(True, color='gray', linestyle=':', alpha=0.5)

# 调整x轴和y轴的颜色
plt.tick_params(axis='x', colors='white')
plt.tick_params(axis='y', colors='white')

# 调整图例
plt.legend(['MA1 Up', 'MA1 Down', 'CCCC', 'LLLL', 'HHHH'], loc='upper left', facecolor='black', edgecolor='white')

plt.tight_layout()
plt.show()
