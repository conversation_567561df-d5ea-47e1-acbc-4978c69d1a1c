import time

# 主程序
from tqsdk import Tq<PERSON><PERSON>, TqAuth, TqKq
from multiTimeFrames_base_with_margin_check import MultiTimeframeStrategy
from utils.utils import parse_time, get_time_period, is_trading_time

if __name__ == "__main__":
    account = "wolfquant,ftp123"
    api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)

    try:
        product_id = 'OI'
        # product_id = 'rb'
        # product_id = 'SA'

        symbol = api.query_cont_quotes(product_id=product_id)[0]
        symbol = 'CZCE.OI505'
        print(f"交易的合约是: {symbol}")
        time_periods = get_time_period(api, symbol)

        # 定义交易周期（以秒为单位）和每个周期的持仓限制
        # timeframes = [60, 180, 800, 900]  # 1分钟, 3分钟, 13分20秒, 15分钟
        max_positions = {
            15: {'long': 10, 'short': 10},
            60: {'long': 5, 'short': 5},
            180: {'long': 5, 'short': 5},
            300: {'long': 5, 'short': 5},
            800: {'long': 5, 'short': 5},
            900: {'long': 5, 'short': 5}
        }

        while True:

            if is_trading_time(time_periods):
                try:
                    api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)
                    symbol = api.query_cont_quotes(product_id=product_id)[0]
                    strategy = MultiTimeframeStrategy(api, symbol, max_positions,positions_file=f"{account.split(',')[0]}.json")

                    while True:
                        api.wait_update()
                        strategy.update()

                except Exception as e:
                    print(e)
                    time.sleep(10)


            else:
                print('非交易时间:', time.asctime())
                time.sleep(1)
                # continue

    except KeyboardInterrupt:
        print("策略已停止")

    finally:
        api.close()
