"""
LLT策略重构版本
采用面向对象设计，提高代码可维护性和可扩展性
"""

import pandas as pd
import numpy as np
from tqsdk import TqApi, TqAuth, TqKq
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from loguru import logger
import json
import time
from functools import wraps


def timing_decorator(step_name: str = None):
    """时间统计装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取类实例（如果存在）
            instance = None
            if args and hasattr(args[0], '__class__') and not isinstance(args[0], (pd.Series, pd.DataFrame, np.ndarray)):
                instance = args[0]

            name = step_name or f"{func.__name__}"
            if instance is not None:
                name = f"{instance.__class__.__name__}.{name}"

            logger.info(f"🔄 开始执行: {name}")
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                elapsed_time = time.time() - start_time
                logger.success(f"✅ 完成执行: {name} | 耗时: {elapsed_time:.3f}秒")
                return result
            except Exception as e:
                elapsed_time = time.time() - start_time
                logger.error(f"❌ 执行失败: {name} | 耗时: {elapsed_time:.3f}秒 | 错误: {e}")
                raise
        return wrapper
    return decorator


class ProgressTracker:
    """进度跟踪器"""

    def __init__(self, total_steps: int, task_name: str = "任务"):
        self.total_steps = total_steps
        self.current_step = 0
        self.task_name = task_name
        self.start_time = time.time()
        self.step_times = []

        logger.info(f"📋 开始任务: {self.task_name} (共 {self.total_steps} 步)")

    def step(self, step_name: str = None):
        """记录步骤进度"""
        self.current_step += 1
        current_time = time.time()

        if self.step_times:
            step_duration = current_time - self.step_times[-1]
        else:
            step_duration = current_time - self.start_time

        self.step_times.append(current_time)

        progress = (self.current_step / self.total_steps) * 100
        elapsed_total = current_time - self.start_time

        if self.current_step > 1:
            avg_step_time = elapsed_total / self.current_step
            eta = avg_step_time * (self.total_steps - self.current_step)
            eta_str = f" | 预计剩余: {eta:.1f}秒"
        else:
            eta_str = ""

        step_info = f" - {step_name}" if step_name else ""

        logger.info(
            f"📊 进度: {self.current_step}/{self.total_steps} ({progress:.1f}%) | "
            f"本步耗时: {step_duration:.3f}秒 | 总耗时: {elapsed_total:.3f}秒{eta_str}{step_info}"
        )

    def finish(self):
        """完成任务"""
        total_time = time.time() - self.start_time
        avg_step_time = total_time / self.total_steps if self.total_steps > 0 else 0

        logger.success(
            f"🎉 任务完成: {self.task_name} | "
            f"总耗时: {total_time:.3f}秒 | 平均每步: {avg_step_time:.3f}秒"
        )


class ExecutionTimer:
    """执行时间计时器"""

    def __init__(self, name: str, use_logger: bool = True):
        self.name = name
        self.use_logger = use_logger
        self.start_time = None

    def __enter__(self):
        if self.use_logger and self.name:
            logger.info(f"⏱️  开始计时: {self.name}")
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if not self.use_logger or not self.name:
            return

        elapsed_time = time.time() - self.start_time
        if exc_type is None:
            logger.success(f"⏱️  计时结束: {self.name} | 耗时: {elapsed_time:.3f}秒")
        else:
            logger.error(f"⏱️  计时结束: {self.name} | 耗时: {elapsed_time:.3f}秒 | 发生异常: {exc_val}")


@dataclass
class TradingConfig:
    """交易配置类"""
    symbol: str = "CZCE.FG509"
    kline_period_seconds: int = 300  # 5分钟
    d_value: int = 60
    volume: int = 1
    kline_data_length: int = 8964
    max_position: int = 100
    auth_username: str = "bigwolf"
    auth_password: str = "ftp123"
    
    @property
    def alpha(self) -> float:
        """计算alpha值"""
        return 2 / (self.d_value + 1)


@dataclass
class TradeRecord:
    """交易记录类"""
    timestamp: datetime
    trade_type: str
    price: float
    volume: int
    pnl: float
    cumulative_pnl: float
    position_type: str = ""
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'trade_type': self.trade_type,
            'price': self.price,
            'volume': self.volume,
            'pnl': self.pnl,
            'cumulative_pnl': self.cumulative_pnl,
            'position_type': self.position_type
        }


class LLTIndicator:
    """LLT指标计算类"""

    @staticmethod
    @timing_decorator("LLT指标计算")
    def calculate(price_series: pd.Series, alpha: float) -> List[float]:
        """
        计算Low-Latency Trendline (LLT)指标
        
        Args:
            price_series: 价格序列
            alpha: 平滑因子
            
        Returns:
            LLT值列表
        """
        logger.info(f"📈 计算LLT指标: 数据长度={len(price_series)}, alpha={alpha:.6f}")

        if len(price_series) < 2:
            logger.warning("⚠️  数据长度不足，返回NaN值")
            return [float('nan')] * len(price_series)

        llt = []

        # 确保使用numpy数组，避免pandas布尔值歧义
        if hasattr(price_series, 'values'):
            price_values = price_series.values.astype(float)
        else:
            price_values = np.array(price_series, dtype=float)

        # 初始化前两个值
        logger.info("🔢 初始化LLT前两个值")
        llt.append(float(price_values[0]))
        llt.append(float(price_values[1]))

        # 计算LLT值
        logger.info(f"🔄 开始计算LLT值，需要计算 {len(price_values) - 2} 个点")

        for i in range(2, len(price_values)):
            try:
                value = (
                    (alpha - alpha ** 2 / 4) * float(price_values[i]) +
                    (alpha ** 2 / 2) * float(price_values[i - 1]) -
                    (alpha - 3 * (alpha ** 2) / 4) * float(price_values[i - 2]) +
                    2 * (1 - alpha) * llt[i - 1] -
                    (1 - alpha) ** 2 * llt[i - 2]
                )
                llt.append(float(value))
            except Exception as e:
                logger.error(f"❌ LLT计算错误 at index {i}: {e}")
                llt.append(llt[-1])  # 使用前一个值

            # 每1000个点显示一次进度
            if (i - 1) % 1000 == 0:
                progress = ((i - 1) / (len(price_values) - 2)) * 100
                logger.info(f"📊 LLT计算进度: {progress:.1f}% ({i-1}/{len(price_values)-2})")

        logger.success(f"✅ LLT计算完成，共计算 {len(llt)} 个值")
        return llt
    
    @staticmethod
    @timing_decorator("信号生成")
    def generate_signals(llt_series: List[float]) -> List[int]:
        """
        根据LLT序列生成交易信号
        
        Args:
            llt_series: LLT值序列
            
        Returns:
            信号列表 (1: 买入, -1: 卖出, 0: 无信号)
        """
        signals = [0]  # 第一个值无信号
        
        for i in range(1, len(llt_series)):
            if llt_series[i] > llt_series[i-1]:
                signals.append(1)  # 买入信号
            elif llt_series[i] < llt_series[i-1]:
                signals.append(-1)  # 卖出信号
            else:
                signals.append(0)  # 无信号
        
        return signals


class ParameterOptimizer:
    """参数优化器"""

    def __init__(self, klines_data: pd.DataFrame):
        self.klines_data = klines_data
    
    @timing_decorator("参数优化")
    def optimize_d_value(self, d_value_range: range) -> List[Dict]:
        """
        优化D_VALUE参数

        Args:
            d_value_range: D_VALUE参数范围

        Returns:
            优化结果列表，按收益率排序
        """
        results = []
        total_params = len(d_value_range)

        logger.info(f"🎯 开始参数优化: 测试 {total_params} 个参数值")
        logger.info(f"📊 参数范围: {d_value_range.start} - {d_value_range.stop-1}")
        logger.info("D_VALUE | 交易次数 | 累计盈亏 | 胜率 | 收益率")
        logger.info("-" * 50)

        # 创建进度跟踪器
        progress_tracker = ProgressTracker(total_params, "参数优化")

        for i, d_value in enumerate(d_value_range):
            try:
                with ExecutionTimer(f"D_VALUE={d_value}参数测试"):
                    alpha = 2 / (d_value + 1)
                    logger.info(f"🔍 测试参数: D_VALUE={d_value}, ALPHA={alpha:.6f}")

                    backtest_result = self._run_backtest(alpha)

                    if backtest_result['trades']:
                        result = {
                            'D_VALUE': d_value,
                            'ALPHA': alpha,
                            'total_trades': len(backtest_result['trades']),
                            'total_pnl': backtest_result['total_pnl'],
                            'win_rate': backtest_result['win_rate'],
                            'return_rate': backtest_result['return_rate'],
                            'winning_trades': backtest_result['winning_trades'],
                            'losing_trades': backtest_result['losing_trades']
                        }
                        results.append(result)

                        logger.info(
                            f"{d_value:7d} | {result['total_trades']:8d} | "
                            f"{result['total_pnl']:8.2f} | {result['win_rate']:5.1f}% | "
                            f"{result['return_rate']:6.2f}%"
                        )
                    else:
                        logger.warning(f"⚠️  D_VALUE={d_value} 无有效交易")

                # 更新进度
                progress_tracker.step(f"D_VALUE={d_value}")

            except Exception as e:
                logger.error(f"❌ D_VALUE {d_value} 计算错误: {e}")
                progress_tracker.step(f"D_VALUE={d_value} (失败)")
                continue

        # 完成进度跟踪
        progress_tracker.finish()

        # 按收益率排序
        logger.info(f"📊 排序优化结果: 共 {len(results)} 个有效结果")
        results.sort(key=lambda x: x['return_rate'], reverse=True)

        top_results = results[:5]  # 返回前5名
        logger.success(f"🏆 返回前 {len(top_results)} 名结果")

        return top_results
    
    def _run_backtest(self, alpha: float) -> Dict:
        """运行回测"""
        try:
            # 确保数据类型正确
            close_prices = pd.Series(self.klines_data.close.values, dtype=float)
            logger.debug(f"📊 回测数据准备: {len(close_prices)}条价格数据")

            # 计算LLT和信号
            llt_series = LLTIndicator.calculate(close_prices, alpha)
            signals = LLTIndicator.generate_signals(llt_series)

            logger.debug(f"📈 信号统计: 买入信号{signals.count(1)}个, 卖出信号{signals.count(-1)}个")

            # 简化的回测逻辑
            trades = []
            position = 0
            entry_price = 0.0
            cumulative_pnl = 0.0

            # 转换为numpy数组以避免pandas布尔值歧义
            price_values = close_prices.values

            for i, (price, signal) in enumerate(zip(price_values, signals)):
                # 确保price是float类型
                price = float(price)

                if signal == 1 and position != 1:  # 买入信号
                    if position == -1:  # 平空头
                        pnl = float(entry_price - price)
                        cumulative_pnl += pnl
                        trades.append({'pnl': pnl, 'type': 'short_close', 'price': price})
                    position = 1
                    entry_price = price

                elif signal == -1 and position != -1:  # 卖出信号
                    if position == 1:  # 平多头
                        pnl = float(price - entry_price)
                        cumulative_pnl += pnl
                        trades.append({'pnl': pnl, 'type': 'long_close', 'price': price})
                    position = -1
                    entry_price = price

            # 计算统计信息
            if trades:
                winning_trades = [t for t in trades if t['pnl'] > 0]
                losing_trades = [t for t in trades if t['pnl'] <= 0]
                win_rate = len(winning_trades) / len(trades) * 100

                # 计算平均盈亏
                avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0
                avg_loss = sum(t['pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0

                logger.debug(f"💰 回测结果: 总交易{len(trades)}次, 盈利{len(winning_trades)}次, 胜率{win_rate:.1f}%")
            else:
                winning_trades = []
                losing_trades = []
                win_rate = 0
                avg_win = 0
                avg_loss = 0
                logger.warning("⚠️  回测期间无交易产生")

            return_rate = (cumulative_pnl / 10000) * 100  # 假设初始资金10000

            return {
                'trades': trades,
                'total_pnl': float(cumulative_pnl),
                'win_rate': float(win_rate),
                'return_rate': float(return_rate),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'avg_win': float(avg_win),
                'avg_loss': float(avg_loss)
            }

        except Exception as e:
            logger.error(f"❌ 回测执行失败: {e}")
            import traceback
            logger.error(f"详细错误:\n{traceback.format_exc()}")
            return {
                'trades': [],
                'total_pnl': 0.0,
                'win_rate': 0.0,
                'return_rate': 0.0,
                'winning_trades': 0,
                'losing_trades': 0,
                'avg_win': 0.0,
                'avg_loss': 0.0
            }


class PositionManager:
    """持仓管理器"""

    def __init__(self, max_position: int = 100):
        self.max_position = max_position
        self.entry_price = 0.0
    
    def can_open_long(self, current_long_position: int) -> bool:
        """检查是否可以开多头"""
        return current_long_position < self.max_position
    
    def can_open_short(self, current_short_position: int) -> bool:
        """检查是否可以开空头"""
        return current_short_position < self.max_position
    
    def calculate_floating_pnl(self, position, current_price: float) -> float:
        """计算浮动盈亏"""
        if position.pos_long > 0:
            long_open_price = position.open_price_long
            if long_open_price > 0:
                return (current_price - long_open_price) * position.pos_long
        elif position.pos_short > 0:
            short_open_price = position.open_price_short
            if short_open_price > 0:
                return (short_open_price - current_price) * position.pos_short
        return 0.0
    
    def should_close_profitable_position(self, position, current_price: float, signal: int) -> Tuple[bool, float]:
        """检查是否应该平掉盈利仓位"""
        if signal == 1 and position.pos_short > 0:  # 做多信号，检查空头
            short_open_price = position.open_price_short
            if short_open_price > 0:
                floating_pnl = (short_open_price - current_price) * position.pos_short
                return floating_pnl > 0, floating_pnl
                
        elif signal == -1 and position.pos_long > 0:  # 做空信号，检查多头
            long_open_price = position.open_price_long
            if long_open_price > 0:
                floating_pnl = (current_price - long_open_price) * position.pos_long
                return floating_pnl > 0, floating_pnl
        
        return False, 0.0


class RiskManager:
    """风险管理器"""

    def __init__(self, max_daily_loss: float = 1000, max_drawdown: float = 0.1):
        self.max_daily_loss = max_daily_loss
        self.max_drawdown = max_drawdown
        self.daily_pnl = 0.0
        self.peak_equity = 0.0

    def check_daily_loss_limit(self, current_pnl: float) -> bool:
        """检查日损失限制"""
        if abs(current_pnl) > self.max_daily_loss:
            logger.warning(f"达到日损失限制: {current_pnl:.2f}")
            return False
        return True

    def check_drawdown_limit(self, current_equity: float) -> bool:
        """检查回撤限制"""
        if current_equity > self.peak_equity:
            self.peak_equity = current_equity

        if self.peak_equity > 0:
            drawdown = (self.peak_equity - current_equity) / self.peak_equity
            if drawdown > self.max_drawdown:
                logger.warning(f"达到回撤限制: {drawdown:.2%}")
                return False
        return True
    
    def can_trade(self, current_pnl: float, current_equity: float) -> bool:
        """检查是否可以继续交易"""
        return (self.check_daily_loss_limit(current_pnl) and 
                self.check_drawdown_limit(current_equity))


class DataManager:
    """数据管理器"""

    def __init__(self, config: TradingConfig):
        self.config = config
        self.api = None
        self.klines = None
    
    @timing_decorator("API连接初始化")
    def initialize_api(self) -> TqApi:
        """初始化API连接"""
        try:
            logger.info("🔌 开始初始化TqSDK API连接...")
            logger.info(f"📝 认证用户: {self.config.auth_username}")

            with ExecutionTimer("创建认证对象"):
                auth = TqAuth(self.config.auth_username, self.config.auth_password)

            with ExecutionTimer("建立API连接"):
                self.api = TqApi(TqKq(), auth=auth, disable_print=True)

            logger.success("✅ API连接成功建立")
            return self.api
        except Exception as e:
            logger.error(f"❌ API连接失败: {e}")
            raise
    
    @timing_decorator("历史数据加载")
    def load_historical_data(self) -> pd.DataFrame:
        """加载历史数据"""
        try:
            logger.info(f"📊 开始加载历史数据...")
            logger.info(f"📈 合约代码: {self.config.symbol}")
            logger.info(f"⏱️  K线周期: {self.config.kline_period_seconds}秒")
            logger.info(f"📏 数据长度: {self.config.kline_data_length}条")

            with ExecutionTimer("获取K线数据"):
                self.klines = self.api.get_kline_serial(
                    self.config.symbol,
                    duration_seconds=self.config.kline_period_seconds,
                    data_length=self.config.kline_data_length
                )

            logger.success(f"✅ 成功获取 {len(self.klines)} 条K线数据")

            if len(self.klines) > 0:
                with ExecutionTimer("数据时间范围分析"):
                    first_time = pd.to_datetime(self.klines.datetime.iloc[0])
                    last_time = pd.to_datetime(self.klines.datetime.iloc[-1])
                    time_span = last_time - first_time

                    logger.info(f"📅 数据时间范围:")
                    logger.info(f"   开始时间: {first_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"   结束时间: {last_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"   时间跨度: {time_span.days}天 {time_span.seconds//3600}小时")

                    # 数据质量检查
                    logger.info(f"📊 数据质量检查:")
                    logger.info(f"   最高价: {self.klines.high.max():.2f}")
                    logger.info(f"   最低价: {self.klines.low.min():.2f}")
                    logger.info(f"   平均成交量: {self.klines.volume.mean():.0f}")
            else:
                logger.warning("⚠️  未获取到任何K线数据")

            return self.klines
        except Exception as e:
            logger.error(f"❌ 加载历史数据失败: {e}")
            raise


class TradeLogger:
    """交易日志记录器"""

    def __init__(self, log_file: str = "llt_trades.json"):
        self.log_file = log_file
        self.trades = []

    def log_trade(self, trade_record: TradeRecord):
        """记录交易"""
        self.trades.append(trade_record)
        logger.info(
            f"交易记录: {trade_record.trade_type} | "
            f"价格: {trade_record.price:.2f} | "
            f"盈亏: {trade_record.pnl:+.2f} | "
            f"累计: {trade_record.cumulative_pnl:.2f}"
        )

    def save_trades(self):
        """保存交易记录到文件"""
        try:
            trade_data = [trade.to_dict() for trade in self.trades]
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(trade_data, f, indent=2, ensure_ascii=False)
            logger.success(f"交易记录已保存到: {self.log_file}")
        except Exception as e:
            logger.error(f"保存交易记录失败: {e}")
    
    def get_statistics(self) -> Dict:
        """获取交易统计"""
        if not self.trades:
            return {}
        
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t.pnl > 0]
        losing_trades = [t for t in self.trades if t.pnl < 0]
        
        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': len(winning_trades) / total_trades * 100 if total_trades > 0 else 0,
            'total_pnl': self.trades[-1].cumulative_pnl if self.trades else 0,
            'avg_win': sum(t.pnl for t in winning_trades) / len(winning_trades) if winning_trades else 0,
            'avg_loss': sum(t.pnl for t in losing_trades) / len(losing_trades) if losing_trades else 0
        }


class LLTLiveStrategy:
    """LLT实时交易策略"""

    def __init__(self, config: TradingConfig, api: TqApi, position_manager: PositionManager,
                 risk_manager: RiskManager, trade_logger: TradeLogger):
        self.config = config
        self.api = api
        self.position_manager = position_manager
        self.risk_manager = risk_manager
        self.trade_logger = trade_logger

        # 获取K线数据
        self.klines = api.get_kline_serial(
            config.symbol,
            duration_seconds=config.kline_period_seconds,
            data_length=config.kline_data_length
        )

        # 交易状态
        self.cumulative_pnl = 0.0
        self.last_summary_time = pd.Timestamp.now()

    @timing_decorator("实时交易策略运行")
    def run(self):
        """运行实时交易策略"""
        logger.info("🚀 开始实时交易监控...")
        logger.info(f"📊 监控合约: {self.config.symbol}")
        logger.info(f"⚙️  策略参数: D_VALUE={self.config.d_value}, ALPHA={self.config.alpha:.6f}")
        logger.info("💡 等待市场数据更新...")

        bar_count = 0
        last_log_time = time.time()

        try:
            while True:
                with ExecutionTimer("等待数据更新") if bar_count % 100 == 0 else ExecutionTimer("", False):
                    self.api.wait_update()

                if self.api.is_changing(self.klines.iloc[-1], "datetime"):
                    bar_count += 1
                    logger.info(f"📈 新K线数据 #{bar_count}: {pd.to_datetime(self.klines.iloc[-1]['datetime']).strftime('%H:%M:%S')}")

                    with ExecutionTimer(f"处理第{bar_count}根K线"):
                        self._process_new_bar()

                # 每5分钟显示一次运行状态
                current_time = time.time()
                if current_time - last_log_time > 300:  # 5分钟
                    logger.info(f"💓 策略运行正常，已处理 {bar_count} 根K线")
                    last_log_time = current_time

        except KeyboardInterrupt:
            logger.info("🛑 策略被用户中断")
        except Exception as e:
            logger.error(f"❌ 实时交易出错: {e}")
        finally:
            with ExecutionTimer("保存交易记录"):
                self.trade_logger.save_trades()
            logger.info(f"📊 策略结束，共处理 {bar_count} 根K线")

    def _process_new_bar(self):
        """处理新的K线"""
        # 计算LLT和信号
        close_prices = pd.Series(self.klines.close)
        llt_series = LLTIndicator.calculate(close_prices, self.config.alpha)

        if len(llt_series) < 2:
            return

        # 生成信号
        signal = 0
        if llt_series[-1] > llt_series[-2]:
            signal = 1
            logger.info(f"买入信号: {self.klines.close.iloc[-1]:.2f}")
        elif llt_series[-1] < llt_series[-2]:
            signal = -1
            logger.info(f"卖出信号: {self.klines.close.iloc[-1]:.2f}")

        if signal != 0:
            self._execute_signal(signal)

        # 定期显示统计信息
        self._periodic_summary()

    def _execute_signal(self, signal: int):
        """执行交易信号"""
        position = self.api.get_position(self.config.symbol)
        current_price = self.klines.close.iloc[-1]
        current_time = pd.to_datetime(self.klines.iloc[-1]["datetime"])

        # 显示当前持仓状态
        self._display_position_status(position, current_price, current_time)

        # 检查风险限制
        current_equity = self.cumulative_pnl + self.position_manager.calculate_floating_pnl(position, current_price)
        if not self.risk_manager.can_trade(self.cumulative_pnl, current_equity):
            logger.warning("触发风险限制，停止交易")
            return

        # 检查并平掉盈利的反向仓位
        should_close, close_pnl = self.position_manager.should_close_profitable_position(position, current_price, signal)
        if should_close:
            self._close_profitable_position(position, current_price, current_time, signal, close_pnl)

        # 执行开仓逻辑
        if signal == 1:  # 买入信号
            self._handle_buy_signal(position, current_price, current_time)
        elif signal == -1:  # 卖出信号
            self._handle_sell_signal(position, current_price, current_time)

    def _display_position_status(self, position, current_price: float, current_time: datetime):
        """显示持仓状态"""
        if position.pos_long > 0:
            long_open_price = position.open_price_long
            if long_open_price > 0:
                pnl_points = (current_price - long_open_price) * position.pos_long
                self.logger.info(
                    f"[{current_time.strftime('%H:%M:%S')}] 多头 {position.pos_long}手 | "
                    f"开仓价: {long_open_price:.2f}, 当前价: {current_price:.2f}, "
                    f"浮动盈亏: {pnl_points:+.2f} 点"
                )
        elif position.pos_short > 0:
            short_open_price = position.open_price_short
            if short_open_price > 0:
                pnl_points = (short_open_price - current_price) * position.pos_short
                self.logger.info(
                    f"[{current_time.strftime('%H:%M:%S')}] 空头 {position.pos_short}手 | "
                    f"开仓价: {short_open_price:.2f}, 当前价: {current_price:.2f}, "
                    f"浮动盈亏: {pnl_points:+.2f} 点"
                )

    def _close_profitable_position(self, position, current_price: float, current_time: datetime, signal: int, pnl: float):
        """平掉盈利仓位"""
        if signal == 1 and position.pos_short > 0:  # 平空头
            self.cumulative_pnl += pnl
            trade_record = TradeRecord(
                timestamp=current_time,
                trade_type="SHORT_CLOSE_ALL",
                price=current_price,
                volume=position.pos_short,
                pnl=pnl,
                cumulative_pnl=self.cumulative_pnl
            )
            self.trade_logger.log_trade(trade_record)
            self.api.insert_order(symbol=self.config.symbol, direction="BUY", offset="CLOSE", volume=position.pos_short)
            self.position_manager.entry_price = 0.0

        elif signal == -1 and position.pos_long > 0:  # 平多头
            self.cumulative_pnl += pnl
            trade_record = TradeRecord(
                timestamp=current_time,
                trade_type="LONG_CLOSE_ALL",
                price=current_price,
                volume=position.pos_long,
                pnl=pnl,
                cumulative_pnl=self.cumulative_pnl
            )
            self.trade_logger.log_trade(trade_record)
            self.api.insert_order(symbol=self.config.symbol, direction="SELL", offset="CLOSE", volume=position.pos_long)
            self.position_manager.entry_price = 0.0

    def _handle_buy_signal(self, position, current_price: float, current_time: datetime):
        """处理买入信号"""
        if not self.position_manager.can_open_long(position.pos_long):
            self.logger.warning(f"多头持仓已达到最大限制 {self.config.max_position}")
            return

        if position.pos_short > 0:
            # 有空头持仓，检查盈亏
            short_open_price = position.open_price_short
            if short_open_price > 0:
                floating_pnl = (short_open_price - current_price) * position.pos_short

                if floating_pnl > 0:  # 盈利则平仓并开多头
                    self._close_and_reverse_position(position, current_price, current_time, "LONG", floating_pnl)
                else:  # 亏损则加仓
                    self._add_reverse_position(position, current_price, current_time, "LONG")
        else:
            # 直接开多头
            self._open_position(current_price, current_time, "LONG")

    def _handle_sell_signal(self, position, current_price: float, current_time: datetime):
        """处理卖出信号"""
        if not self.position_manager.can_open_short(position.pos_short):
            self.logger.warning(f"空头持仓已达到最大限制 {self.config.max_position}")
            return

        if position.pos_long > 0:
            # 有多头持仓，检查盈亏
            long_open_price = position.open_price_long
            if long_open_price > 0:
                floating_pnl = (current_price - long_open_price) * position.pos_long

                if floating_pnl > 0:  # 盈利则平仓并开空头
                    self._close_and_reverse_position(position, current_price, current_time, "SHORT", floating_pnl)
                else:  # 亏损则加仓
                    self._add_reverse_position(position, current_price, current_time, "SHORT")
        else:
            # 直接开空头
            self._open_position(current_price, current_time, "SHORT")

    def _close_and_reverse_position(self, position, current_price: float, current_time: datetime, direction: str, pnl: float):
        """平仓并反向开仓"""
        self.cumulative_pnl += pnl

        if direction == "LONG":
            # 平空头，开多头
            trade_record = TradeRecord(
                timestamp=current_time,
                trade_type="SHORT_CLOSE",
                price=current_price,
                volume=position.pos_short,
                pnl=pnl,
                cumulative_pnl=self.cumulative_pnl
            )
            self.trade_logger.log_trade(trade_record)
            self.api.insert_order(symbol=self.config.symbol, direction="BUY", offset="CLOSE", volume=position.pos_short)
            self._open_position(current_price, current_time, "LONG")
        else:
            # 平多头，开空头
            trade_record = TradeRecord(
                timestamp=current_time,
                trade_type="LONG_CLOSE",
                price=current_price,
                volume=position.pos_long,
                pnl=pnl,
                cumulative_pnl=self.cumulative_pnl
            )
            self.trade_logger.log_trade(trade_record)
            self.api.insert_order(symbol=self.config.symbol, direction="SELL", offset="CLOSE", volume=position.pos_long)
            self._open_position(current_price, current_time, "SHORT")

    def _add_reverse_position(self, position, current_price: float, current_time: datetime, direction: str):
        """加仓反向持仓"""
        if direction == "LONG":
            reverse_volume = min(position.pos_short + self.config.volume,
                               self.config.max_position - position.pos_long)
            if reverse_volume > 0:
                self.position_manager.entry_price = current_price
                self.logger.info(f"空头亏损加仓多头 {reverse_volume} 手")
                self.api.insert_order(symbol=self.config.symbol, direction="BUY", offset="OPEN", volume=reverse_volume)
        else:
            reverse_volume = min(position.pos_long + self.config.volume,
                               self.config.max_position - position.pos_short)
            if reverse_volume > 0:
                self.position_manager.entry_price = current_price
                self.logger.info(f"多头亏损加仓空头 {reverse_volume} 手")
                self.api.insert_order(symbol=self.config.symbol, direction="SELL", offset="OPEN", volume=reverse_volume)

    def _open_position(self, current_price: float, current_time: datetime, direction: str):
        """开仓"""
        self.position_manager.entry_price = current_price

        if direction == "LONG":
            self.logger.info(f"开多头仓位: {current_price:.2f}")
            self.api.insert_order(symbol=self.config.symbol, direction="BUY", offset="OPEN", volume=self.config.volume)
        else:
            self.logger.info(f"开空头仓位: {current_price:.2f}")
            self.api.insert_order(symbol=self.config.symbol, direction="SELL", offset="OPEN", volume=self.config.volume)

    def _periodic_summary(self):
        """定期显示统计摘要"""
        if (pd.Timestamp.now() - self.last_summary_time).total_seconds() > 600:  # 10分钟
            position = self.api.get_position(self.config.symbol)
            current_price = self.klines.close.iloc[-1]
            current_floating_pnl = self.position_manager.calculate_floating_pnl(position, current_price)
            total_pnl = self.cumulative_pnl + current_floating_pnl

            stats = self.trade_logger.get_statistics()

            self.logger.info("=== 盈亏统计 ===")
            self.logger.info(f"累计盈亏: {self.cumulative_pnl:.2f} 点")
            self.logger.info(f"浮动盈亏: {current_floating_pnl:+.2f} 点")
            self.logger.info(f"总盈亏: {total_pnl:.2f} 点")
            self.logger.info(f"交易次数: {stats.get('total_trades', 0)}")
            self.logger.info(f"胜率: {stats.get('win_rate', 0):.1f}%")
            self.logger.info(f"当前持仓: 多头 {position.pos_long}/{self.config.max_position}, 空头 {position.pos_short}/{self.config.max_position}")
            self.logger.info("=" * 40)

            self.last_summary_time = pd.Timestamp.now()


def setup_logging(log_level: str = "INFO"):
    """设置loguru日志"""
    # 移除默认的handler
    logger.remove()

    # 添加控制台输出
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=log_level,
        colorize=True
    )

    # 添加文件输出
    logger.add(
        "llt_strategy.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level=log_level,
        rotation="10 MB",
        retention="7 days",
        encoding="utf-8"
    )

    return logger


if __name__ == "__main__":
    # 程序启动时间
    program_start_time = time.time()

    # 设置日志
    setup_logging()
    logger.info("🎯 LLT策略重构版本启动")
    logger.info(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 创建配置
    with ExecutionTimer("配置初始化"):
        config = TradingConfig()
        logger.info(f"⚙️  交易配置加载完成:")
        logger.info(f"   合约: {config.symbol}")
        logger.info(f"   D_VALUE: {config.d_value}")
        logger.info(f"   交易量: {config.volume}")
        logger.info(f"   最大持仓: {config.max_position}")

    # 初始化组件
    with ExecutionTimer("组件初始化"):
        logger.info("🔧 初始化策略组件...")
        data_manager = DataManager(config)
        position_manager = PositionManager(config.max_position)
        risk_manager = RiskManager()
        trade_logger = TradeLogger()
        logger.success("✅ 所有组件初始化完成")

    try:
        # 步骤1: 初始化API和数据
        logger.info("📋 执行步骤 1/4: API连接和数据加载")
        api = data_manager.initialize_api()
        klines = data_manager.load_historical_data()

        # 步骤2: 参数优化
        logger.info("📋 执行步骤 2/4: 参数优化")
        with ExecutionTimer("完整参数优化流程", logger):
            optimizer = ParameterOptimizer(klines)
            optimization_results = optimizer.optimize_d_value(range(10, 101))

        # 步骤3: 应用最优参数
        logger.info("📋 执行步骤 3/4: 应用最优参数")
        if optimization_results:
            best_result = optimization_results[0]
            config.d_value = best_result['D_VALUE']
            logger.success(f"🏆 使用最优参数:")
            logger.info(f"   D_VALUE: {config.d_value}")
            logger.info(f"   ALPHA: {config.alpha:.6f}")
            logger.info(f"   预期收益率: {best_result['return_rate']:.2f}%")
            logger.info(f"   历史胜率: {best_result['win_rate']:.1f}%")
        else:
            logger.warning("⚠️  未找到最优参数，使用默认配置")

        # 步骤4: 启动实时交易
        logger.info("📋 执行步骤 4/4: 启动实时交易")
        logger.info("🚀 参数优化完成，开始实时交易...")

        # 创建实时交易策略
        with ExecutionTimer("实时交易策略创建"):
            live_strategy = LLTLiveStrategy(config, api, position_manager, risk_manager, trade_logger)

        # 运行策略
        live_strategy.run()

    except Exception as e:
        logger.error(f"❌ 程序运行出错: {e}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
    finally:
        # 清理资源
        with ExecutionTimer("资源清理"):
            if 'api' in locals():
                logger.info("🔌 关闭API连接...")
                api.close()

            logger.info("💾 保存交易记录...")
            trade_logger.save_trades()

        # 显示程序运行总时间
        total_runtime = time.time() - program_start_time
        logger.success(f"🏁 程序结束")
        logger.info(f"⏱️  总运行时间: {total_runtime:.3f}秒 ({total_runtime/60:.1f}分钟)")
        logger.info(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
