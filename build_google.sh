#!/bin/bash

SCRIPT_NAME="weiswave_claude_df_with_cmd_parameters.py"
OUTPUT_DIR="dist"

# 清理旧的构建文件 (可选但推荐)
echo "🧹 Cleaning previous build directories..."
rm -rf "$OUTPUT_DIR" "${SCRIPT_NAME%.py}.build" "${SCRIPT_NAME%.py}.dist"

echo "🚀 Starting Nuitka compilation..."

nuitka \
  --standalone \
  --include-package=tqsdk \
  --include-package=pandas \
  --include-package=websockets \
  --include-module=pandas._config.localization \
  --include-data-files="$(python3 -c 'import tqsdk; import os; print(os.path.join(os.path.dirname(tqsdk.__file__), "expired_quotes.json.lzma"))')=tqsdk/expired_quotes.json.lzma" \
  --static-libpython=no \
  --jobs=30 \
  --output-dir="$OUTPUT_DIR" \
  "$SCRIPT_NAME"

# 编译结果检查
if [ $? -eq 0 ]; then
  EXECUTABLE_NAME="${SCRIPT_NAME%.py}"
  # Nuitka 在 standalone 模式下可能会创建 .dist 目录，实际的可执行文件在里面
  # 但有时也可能在外面，取决于具体版本和参数。我们检查一下 dist 目录。
  if [ -d "$OUTPUT_DIR/$EXECUTABLE_NAME.dist" ]; then
      FINAL_OUTPUT_DIR="$OUTPUT_DIR/$EXECUTABLE_NAME.dist"
      FINAL_EXECUTABLE="$FINAL_OUTPUT_DIR/$EXECUTABLE_NAME"
       # 如果 Linux 上没有 .exe 后缀，需要调整
      if [ ! -f "$FINAL_EXECUTABLE" ] && [ -f "$FINAL_OUTPUT_DIR/${EXECUTABLE_NAME}.bin" ]; then
         FINAL_EXECUTABLE="$FINAL_OUTPUT_DIR/${EXECUTABLE_NAME}.bin" # Nuitka有时用 .bin
      fi
  else
      FINAL_OUTPUT_DIR="$OUTPUT_DIR"
      FINAL_EXECUTABLE="$FINAL_OUTPUT_DIR/$EXECUTABLE_NAME"
      if [ ! -f "$FINAL_EXECUTABLE" ] && [ -f "$FINAL_OUTPUT_DIR/${EXECUTABLE_NAME}.bin" ]; then
         FINAL_EXECUTABLE="$FINAL_OUTPUT_DIR/${EXECUTABLE_NAME}.bin"
      fi
  fi

  echo "✅ 编译成功！"
  echo "   输出目录: $FINAL_OUTPUT_DIR"
  echo "   可执行文件: $FINAL_EXECUTABLE"
  echo ""
  echo "👉 你可以尝试运行它："
  echo "$FINAL_EXECUTABLE" # 直接给出完整路径更容易复制粘贴
else
  echo "❌ 编译失败，请检查上面的错误日志。"
fi

