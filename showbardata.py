import pyqtgraph as pg
from pyqtgraph.Qt import QtCore, QtGui
import numpy as np

app = QtGui.QApplication([])

# 生成随机数据
data = np.random.normal(size=(10,4))

# 创建绘图窗口
win = pg.GraphicsWindow()

# 在绘图窗口中添加K线图
p = win.addPlot(title="股票行情K线图")
candle = pg.CandlestickItem(data)
p.addItem(candle)

# 设置坐标轴标签
p.setLabel('left', "价格", units='元')
p.setLabel('bottom', "时间", units='秒')

# 设置图例
p.addLegend()

# 添加标注
text = pg.TextItem(text='hello world', color=(255, 0, 0))
text.setPos(2, 0)
p.addItem(text)

# 更改颜色
candle.setPen('r')
candle.setBrush(255, 0, 0)

# 显示绘图窗口
win.show()

# 运行应用程序
app.exec_()
