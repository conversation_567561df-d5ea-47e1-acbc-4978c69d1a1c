from strategies.TimeRoseMA_cross_speak_explore import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    # from datetime import date
    # from accounts import cyzjy as acct


    product = 'OI'
    symbol=product
    interval = 60
    bklimit = 200
    sklimit = 200
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    api = TqApi(TqKq(), auth="quant_thj,Qiai1301", disable_print=True, debug=None)
    symbol = api.query_cont_quotes(product_id=product)[0]
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit, period=13)


runstrategy()
