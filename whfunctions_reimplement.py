# 文华财经 (麦语言) 函数 Python/Pandas 实现整合版
# 版本: 1.0
# 日期: 2025-05-29
# 说明: 本文件旨在将文华财经中的函数逻辑用Python和Pandas尽可能地重现。
#       由于平台特性和运行环境的差异，部分函数无法完全等同实现，
#       特别是涉及交易指令、实时状态、特定平台设置和绘图的函数。
#       使用前请仔细阅读各函数的说明和注意事项。

# --- 重要说明和通用约定 ---
# 1. 数据输入: 除非另有说明，所有函数都假设输入数据为一个或多个 Pandas Series。
#    在实际使用中，这些 Series 通常是 Pandas DataFrame 的列。
#    例如: df['open'], df['high'], df['low'], df['close'], df['volume'], df['opi']
#    文华财经中常用的 C, H, L, O, V 分别对应 close, high, low, open, volume。
#
# 2. 空值 (NaN): 文华财经中的“空值”在 Pandas 中通常用 np.nan 表示。
#    许多函数在数据不足时会返回空值。
#
# 3. 交易环境依赖:
#    - 交易指令和状态函数 (如 BK, SK, BKVOL, PROFIT): 依赖于交易执行和状态管理系统。
#      本文件将解释其概念，说明如何在模拟交易循环中实现其逻辑。
#    - 平台特定函数 (如 AUTOFILTER, SIGCHECK): 控制文华财经平台的特定行为，无法在通用 Python 环境中直接实现。
#    - 绘图函数 (如 DRAWTEXT, DRAWLINE): Python 中使用 Matplotlib, Plotly 等库实现绘图。
#      本文件专注于计算可能用于绘图的数据。
#
# 4. REF 函数: 这是一个非常基础和常用的函数，用于引用过去的数据。
#
# 5. 参数 N: 在很多函数中，参数 N 代表周期。文华财经中 N=0 有时有特殊含义。
#
# 6. 测试与验证: 所有实现都基于提供的函数说明。强烈建议用户对照文华财经的实际行为
#    进行充分的测试和验证。

import pandas as pd
import numpy as np


# --- 辅助函数 (会被其他函数频繁调用) ---
# 假设您有一个包含OHLCV数据的DataFrame, 例如:
# df = pd.DataFrame({
# 'open': [10, 10.2, 10.1, 10.5, 10.3],
# 'high': [10.3, 10.4, 10.6, 10.6, 10.5],
# 'low': [9.9, 10.1, 10.0, 10.2, 10.1],
# 'close': [10.2, 10.1, 10.5, 10.3, 10.2],
# 'volume': [1000, 1200, 800, 1500, 900],
# 'opi': [5000, 5100, 4900, 5200, 5000] # 持仓量示例
# })
# # 通常K线数据会有DatetimeIndex
# df.index = pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05'])


def REF(series_x: pd.Series, n: int) -> pd.Series:
    """
    引用X在N个周期前的值.
    文华说明: N为有效值，但当前k线数不足N根，返回空值；N为0时返回当前X值.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")
    if n < 0:
        # 文华REF通常不直接支持负数n作为pandas.shift的直接参数含义
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.shift(n)


def SUM(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的总和.
    文华说明: N包含当前k线. 若N为0则从第一个有效值开始算起.
              当N为有效值，但当前的k线数不足N根，按照实际的根数计算.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n < 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    if n == 0:  # 从第一个有效值开始算起 (expanding sum)
        return series_x.expanding(min_periods=1).sum()
    else:  # 滚动求和，min_periods=1 表示不足N根时按实际根数计算
        return series_x.rolling(window=n, min_periods=1).sum()


def MA(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的简单移动平均.
    文华说明: N包含当前k线. 不足N根，返回空值.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    # min_periods=n 确保在窗口数据不足n时结果为NaN (空值)
    return series_x.rolling(window=n, min_periods=n).mean()


def EMA(series_x: pd.Series, n: int) -> pd.Series:
    """
    求N周期X值的指数加权移动平均.
    文华说明: N包含当前k线. 当N为有效值，但当前的k线数不足N根，按N根计算.
              (这通常意味着EMA从早期数据点开始计算, adjust=False, min_periods 默认或较小)
              EMAWH则明确说明不足N根返回空值, 此时min_periods=n.
              此处采用更通用的EMA定义, adjust=False, min_periods=1 开始计算.
              如果严格要求不足N根返回空值，则应使用 min_periods=n.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.ewm(span=n, adjust=False, min_periods=1).mean()  # min_periods=n 如果需要严格空值


def HHV(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的最高值.
    文华说明: N包含当前k线. 若N为0则从第一个有效值开始算起.
              不足N根，按照实际的根数计算.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")
    if n < 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    if n == 0:
        return series_x.expanding(min_periods=1).max()
    else:
        return series_x.rolling(window=n, min_periods=1).max()


def LLV(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的最小值.
    文华说明: N包含当前k线. 若N为0则从第一个有效值开始算起.
              不足N根，按照实际的根数计算.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n < 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    if n == 0:
        return series_x.expanding(min_periods=1).min()
    else:
        return series_x.rolling(window=n, min_periods=1).min()


def STD(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的样本标准差.
    文华说明: N包含当前k线. 不足N根，返回空值.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n <= 1:  # 样本标准差至少需要2个点
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.rolling(window=n, min_periods=n).std(ddof=1)


def IFELSE(condition: pd.Series, series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """
    若COND条件成立，则返回A，否则返回B.
    确保A和B与condition有相同的索引 (或A, B为标量).
    """
    # Ensure series_a and series_b are Series for np.where if they are not scalars
    # This handles cases where A or B might be scalars or other Series.
    # For simplicity, assuming series_a and series_b are either scalars or pd.Series with compatible index.
    # If series_a/b are single values, np.where broadcasts them.
    # If they are series, their index should align with condition.
    # For robust alignment if A and B are Series:
    # series_a_aligned = series_a.reindex_like(condition) if isinstance(series_a, pd.Series) else series_a
    # series_b_aligned = series_b.reindex_like(condition) if isinstance(series_b, pd.Series) else series_b
    # return pd.Series(np.where(condition, series_a_aligned, series_b_aligned), index=condition.index)
    return pd.Series(np.where(condition, series_a, series_b), index=condition.index)


# --- DataFrame Column Access ---
# 文华财经中的 O, H, L, C, V, OPI 通常直接对应DataFrame的列名。
# 例如, 如果你的DataFrame名为 df:
# OPEN = df['open']
# HIGH = df['high']
# LOW = df['low']
# CLOSE = df['close']
# VOL = df['volume']
# OPI = df['opi'] # 持仓量

# ==============================================================================
# Part 1: 基础数学、数据访问与统计函数
# ==============================================================================
print("--- Part 1: 基础数学、数据访问与统计函数 ---")


def ABS(series_x: pd.Series) -> pd.Series:
    """
    ABS(X)：取的X的绝对值.
    例: ABS(CLOSE-10); ABS(C-O);
    """
    return series_x.abs()


def ACOS(series_x: pd.Series) -> pd.Series:
    """
    ACOS(X)：返回X的反余弦值.
    注: X取值范围[-1, 1]. 若X不在取值范围，返回值为空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = (series_x >= -1) & (series_x <= 1)
    result[mask] = np.arccos(series_x[mask])
    return result


def ASIN(series_x: pd.Series) -> pd.Series:
    """
    ASIN(X)：返回X的反正弦值.
    注: X取值范围[-1, 1]. 若X不在取值范围，返回值为空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = (series_x >= -1) & (series_x <= 1)
    result[mask] = np.arcsin(series_x[mask])
    return result


def ATAN(series_x: pd.Series) -> pd.Series:
    """
    ATAN(X)：返回X的反正切值.
    注: X的取值为R（实数集）.
    """
    return np.arctan(series_x)


def AVEDEV(series_x: pd.Series, n: int) -> pd.Series:
    """
    AVEDEV(X,N)：返回X在N周期内的平均绝对偏差.
    算法举例: AVEDEV(C,3) = (ABS(C-MA(C,3)_win) + ABS(REF(C,1)-MA(C,3)_win) + ABS(REF(C,2)-MA(C,3)_win))/3
     其中 MA(C,3)_win 是指当前窗口内3个值的均值。
    注: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)

    def avedev_calc(window_series):
        return (window_series - window_series.mean()).abs().mean()

    return series_x.rolling(window=n, min_periods=n).apply(avedev_calc, raw=True)


def AVPRICE(df_ohlc: pd.DataFrame) -> pd.Series:
    """
    AVPRICE 取得K线图的均价.
    注: 表示单根K线内的均价. 日线周期上收盘后与SETTLE函数一样取得当日的结算价.
    文华财经对 AVPRICE 的具体计算方法（尤其是在分钟周期内）可能依赖于成交量加权或tick数据。
    如果没有更详细的算法，常见的简单近似有:
    1. (HIGH + LOW) / 2  (Typical Price often uses (H+L+C)/3)
    2. (OPEN + HIGH + LOW + CLOSE) / 4
    3. (HIGH + LOW + CLOSE) / 3
    此处使用 (H+L+C)/3 作为示例。对于日线结算价行为，需要特定数据。
    """
    required_cols = ['high', 'low', 'close']
    if not all(col in df_ohlc.columns for col in required_cols):
        # Fallback or error if columns are missing
        print(f"AVPRICE requires columns: {required_cols}. Returning NaN series.")
        return pd.Series(np.nan, index=df_ohlc.index, name="AVPRICE")

    # Simple approximation for intra-bar average price
    return (df_ohlc['high'] + df_ohlc['low'] + df_ohlc['close']) / 3
    # 注：对于日线的SETTLE行为，此函数不处理，应使用专门的SETTLE数据。


def BETWEEN(series_x: pd.Series, series_y: pd.Series, series_z: pd.Series) -> pd.Series:
    """
    BETWEEN(X,Y,Z) 表示X是否处于Y和Z之间，成立返回1(Yes)，否则返回0(No).
    注: 其中若X=Y、X=Z、或X=Y且Y=Z时函数返回值为1.
    这意味着 Y 和 Z 的顺序不重要. X 在 [min(Y,Z), max(Y,Z)] 之间。
    """
    lower_bound = np.minimum(series_y, series_z)
    upper_bound = np.maximum(series_y, series_z)
    return ((series_x >= lower_bound) & (series_x <= upper_bound)).astype(int)


def CEILING(series_x: pd.Series, series_y_base: pd.Series = None) -> pd.Series:
    """
    CEILING(X,Y): 返回指定实数(X)在沿绝对值增大的方向上第一个能整除基数(Y)的值.
    CEILING(X): (如果只有一个参数) 标准数学向上取整 np.ceil(X).

    文华对 CEILING(X,Y) 的描述:
    例1：CEILING(2.1,1); 求得3.  (ceil(2.1/1)*1 = 3)
    例2：CEILING(-8.8,-2); 求得-10. (ceil(-8.8/-2)*(-2) = ceil(4.4)*(-2) = 5*(-2) = -10)
    例3：CEILING(-7,2); 求得-6.   (Y为正, X为负, "对值按朝向0的方向进行向上舍入")
                                   This means ceil_to_zero(-7/2)*2 = ceil_to_zero(-3.5)*2 = -3*2 = -6
    例4：CEILING(8,-2); 返回无效值. (X为正, Y为负)

    此实现将只处理单参数CEILING(X) 和 X,Y同号或(X<0, Y>0)的情况。
    """
    if series_y_base is None:
        return np.ceil(series_x)
    else:
        # 检查 Y_base 是否为0，避免除零
        if isinstance(series_y_base, pd.Series) and (series_y_base == 0).any():
            # Handle cases where y_base can be zero if it's a series
            # For simplicity, let's assume y_base is non-zero scalar or series here
            pass
        elif not isinstance(series_y_base, pd.Series) and series_y_base == 0:
            return pd.Series(np.nan, index=series_x.index)

        result = pd.Series(np.nan, index=series_x.index, name=series_x.name)

        # Case 1 & 2: X, Y同号
        mask_same_sign_pos = (series_x >= 0) & (series_y_base > 0)
        result[mask_same_sign_pos] = np.ceil(series_x[mask_same_sign_pos] / series_y_base[mask_same_sign_pos]) * series_y_base[mask_same_sign_pos]

        mask_same_sign_neg = (series_x <= 0) & (series_y_base < 0)
        # For negative X and Y, "沿绝对值增大的方向" means more negative
        # ceil(abs(X)/abs(Y)) * (-abs(Y))
        # or ceil(X/Y)*Y if Y is negative, as ceil of positive number times negative base works.
        result[mask_same_sign_neg] = np.ceil(series_x[mask_same_sign_neg] / series_y_base[mask_same_sign_neg]) * series_y_base[mask_same_sign_neg]

        # Case 3: X < 0, Y > 0 ("对值按朝向0的方向进行向上舍入")
        mask_x_neg_y_pos = (series_x < 0) & (series_y_base > 0)
        # temp_div = series_x[mask_x_neg_y_pos] / series_y_base[mask_x_neg_y_pos]
        # result[mask_x_neg_y_pos] = np.trunc(temp_div) * series_y_base[mask_x_neg_y_pos] # incorrect, this is floor towards zero
        # "向上舍入朝向0": e.g., -3.5 -> -3. This is np.trunc if result is non-positive,
        # or a custom ceil_towards_zero. For negative values, this is np.floor(val + eps) if not integer, or math.trunc
        # Let's use floor for negative numbers to achieve "向上舍入朝向0" for negative quotients
        # Example: -7/2 = -3.5. "向上舍入朝向0" is -3. np.ceil(-3.5) is -3.
        # So for X<0, Y>0, X/Y is negative. np.ceil(X/Y) * Y
        result[mask_x_neg_y_pos] = np.ceil(series_x[mask_x_neg_y_pos] / series_y_base[mask_x_neg_y_pos]) * series_y_base[mask_x_neg_y_pos]
        # Test with -7, 2: np.ceil(-7/2)*2 = np.ceil(-3.5)*2 = -3*2 = -6. Correct.

        # Case 4: X > 0, Y < 0 (返回无效值)
        # This is already handled as result elements are np.nan by default.
        mask_x_pos_y_neg = (series_x > 0) & (series_y_base < 0)
        result[mask_x_pos_y_neg] = np.nan

        return result


def COS(series_x: pd.Series) -> pd.Series:
    """COS(X)：返回X的余弦值."""
    return np.cos(series_x)


def CUBE(series_x: pd.Series) -> pd.Series:
    """CUBE(X)：返回X的三次方."""
    return series_x ** 3  # Or np.power(series_x, 3)


def DEVSQ(series_x: pd.Series, n: int) -> pd.Series:
    """
    DEVSQ(X,N)： 计算数据X的N个周期的数据偏差平方和.
    算法: SUM(SQUARE(X_i - MA_window(X_i-N+1 ... X_i))) for i in window
    文华算法举例：DEVSQ(C,3) = SQUARE(C-MA(C,REF(C,1),REF(C,2))) +
                             SQUARE(REF(C,1)-MA(C,REF(C,1),REF(C,2))) +
                             SQUARE(REF(C,2)-MA(C,REF(C,1),REF(C,2)))
    这表示对每个窗口，先计算该窗口的均值，然后计算窗口内每个值与该均值的差的平方和。
    注: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值. N不支持为变量.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)

    def devsq_calc(window_arr):  # window_arr is a numpy array
        # Pandas rolling(min_periods=n) ensures window_arr has at least n elements
        # and no NaNs if series_x has no NaNs in that range.
        # If series_x can have NaNs, they need to be handled.
        # Assuming window_arr is clean here due to min_periods=n
        mean_val = np.mean(window_arr)
        return np.sum((window_arr - mean_val) ** 2)

    return series_x.rolling(window=n, min_periods=n).apply(devsq_calc, raw=True)


def EXP(series_x: pd.Series) -> pd.Series:
    """EXP(X)：求e的X次幂."""
    return np.exp(series_x)


def FLOOR(series_x: pd.Series) -> pd.Series:
    """FLOOR(A)：向数值减小方向舍入 (取整)."""
    return np.floor(series_x)


def INTPART(series_x: pd.Series) -> pd.Series:
    """INTPART(X)：取X的整数部分 (向0截断)."""
    return np.trunc(series_x)


def LN(series_x: pd.Series) -> pd.Series:
    """
    LN(X)：求X的自然对数.
    注: X取值范围为正数. X为0或负数，返回值为空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = series_x > 0
    result[mask] = np.log(series_x[mask])
    return result


def LOG(series_x: pd.Series, series_base: pd.Series) -> pd.Series:  # series_base can also be a scalar
    """
    LOG(X,Y) 求以Y为底X的对数值. (LOG(X, Base))
    注: X>0, Base>0, Base!=1. 不满足则空值.
    """
    log_x = LN(series_x)  # Handles x <= 0 by returning NaN

    # Handle base: base > 0 and base != 1
    if isinstance(series_base, pd.Series):
        log_base_val = pd.Series(np.nan, index=series_base.index)
        valid_base_mask = (series_base > 0) & (series_base != 1)
        log_base_val[valid_base_mask] = np.log(series_base[valid_base_mask])
    else:  # scalar base
        if series_base > 0 and series_base != 1:
            log_base_val = np.log(series_base)
        else:
            return pd.Series(np.nan, index=series_x.index, name=series_x.name)  # Invalid base for all

    # Result is log_x / log_base_val
    # Division by zero (if log_base_val is 0, i.e. base was 1) will result in inf/NaN handled by numpy.
    # NaNs from log_x or log_base_val will propagate.
    return log_x / log_base_val


def LOG10(series_x: pd.Series) -> pd.Series:
    """
    LOG10(X) 求X的常用对数值 (以10为底).
    注: X>0. X为0或负数，返回值为空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = series_x > 0
    result[mask] = np.log10(series_x[mask])
    return result


def MAX(series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """MAX(A,B)：取最大值。取A，B中较大者."""
    # np.maximum handles NaNs by propagating them if one is NaN, unless both are NaN.
    # Or pd.Series.combine(other_series, np.maximum)
    return np.maximum(series_a, series_b)


def MAX1(*series_list: pd.Series) -> pd.Series:
    """
    MAX1(A1,...,A30) 在A1到A30中取最大值.
    支持2-30个序列.
    """
    if not (2 <= len(series_list) <= 30):
        raise ValueError("MAX1 supports between 2 and 30 series arguments.")
    # Ensure all are pd.Series and have the same index for concat
    # This simple version assumes they are already aligned or should be.
    # For safety, one might explicitly align all to the first series' index.
    df_temp = pd.concat(series_list, axis=1)
    return df_temp.max(axis=1)  # skipna=True is default


def MIN(series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """MIN(A,B)：取最小值。取A，B中较小者."""
    return np.minimum(series_a, series_b)


def MIN1(*series_list: pd.Series) -> pd.Series:
    """
    MIN1(A1,...,A30) 在A1到A30中取最小值.
    支持2-30个序列.
    """
    if not (2 <= len(series_list) <= 30):
        raise ValueError("MIN1 supports between 2 and 30 series arguments.")
    df_temp = pd.concat(series_list, axis=1)
    return df_temp.min(axis=1)  # skipna=True is default


def MOD(series_a: pd.Series, series_b: pd.Series) -> pd.Series:  # series_b can be scalar
    """
    MOD(A,B)：取模。返回A对B求模.
    注: 第二个参数最多支持四位小数 (implies series_b might not always be integer).
    Python's % operator and np.mod handle floats correctly for the modulo operation.
    """
    return np.mod(series_a, series_b)


def NOT(condition: pd.Series) -> pd.Series:
    """
    NOT(X)：取非。当X＝0时返回1，否则返回0.
    Assumes X is a series where 0 means False, and non-zero means True.
    """
    # Convert to boolean (0 -> False, non-0 -> True), then invert, then convert to int (True->1, False->0)
    return (~condition.astype(bool)).astype(int)


def POW(series_x: pd.Series, series_y: pd.Series) -> pd.Series:  # series_y can be scalar
    """
    POW(X,Y)：求X的Y次幂.
    注: 当X为负数时，Y必须为整数 (否则空值).
    """
    # np.power handles this: if x is negative and y is not an integer, it returns NaN.
    # If x is negative and y is an integer, it computes correctly.
    return np.power(series_x, series_y)


def REVERSE(series_x: pd.Series) -> pd.Series:
    """REVERSE(X)：取相反值，返回－X."""
    return -series_x


def ROUND(series_x: pd.Series, m: int) -> pd.Series:
    """
    ROUND(N,M) 对数字N进行位数为M的四舍五入.
    M>0: 小数点后M位. M=0: 整数. M<0: 小数点左侧M位.
    """
    if not isinstance(m, int):
        raise TypeError("Parameter 'm' for ROUND must be an integer.")
    return series_x.round(decimals=m)


def SGN(series_x: pd.Series) -> pd.Series:
    """SGN(X)：取符号。若X>0返回1,若X<0返回-1,否则返回0."""
    return np.sign(series_x).astype(int)


def SIN(series_x: pd.Series) -> pd.Series:
    """SIN(X)：求X的正弦值."""
    return np.sin(series_x)


def SQRT(series_x: pd.Series) -> pd.Series:
    """
    SQRT(X)：求X的平方根.
    注: X的取值为正数. X为负数时返回空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = series_x >= 0  # Allow 0 for sqrt(0)=0
    result[mask] = np.sqrt(series_x[mask])
    return result


def SQUARE(series_x: pd.Series) -> pd.Series:
    """SQUARE(X)求X的平方."""
    return series_x ** 2  # Or np.square(series_x)


def TAN(series_x: pd.Series) -> pd.Series:
    """TAN(X)：返回X的正切值."""
    return np.tan(series_x)


def STDP(series_x: pd.Series, n: int) -> pd.Series:
    """
    STDP(X,N)：为X的N周期总体标准差.
    文华说明: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.rolling(window=n, min_periods=n).std(ddof=0)  # ddof=0 for population std


def VAR(series_x: pd.Series, n: int) -> pd.Series:
    """
    VAR(X,N)求X在N周期内的样本方差.
    文华说明: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 1:  # 样本方差至少需要2个点
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.rolling(window=n, min_periods=n).var(ddof=1)  # ddof=1 for sample variance


def VARP(series_x: pd.Series, n: int) -> pd.Series:
    """
    VARP(X,N)：为X的N周期总体方差.
    文华说明: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.rolling(window=n, min_periods=n).var(ddof=0)  # ddof=0 for population variance


# ==============================================================================
# Part 2: 核心技术指标与逻辑函数
# ==============================================================================
print("--- Part 2: 核心技术指标与逻辑函数 ---")


def ADMA(series_x: pd.Series, close_series: pd.Series, n_lookback: int, p_fast_period: int, q_slow_period: int) -> pd.Series:
    """
    ADMA(X,N,P,Q) 考夫曼均值 (文华财经特定变体)
    算法描述:
    ADMA_val = REF(EMA(X,N),1) + CONSTANT * (X - REF(EMA(X,N),1));
    CONSTANT_val = SQUARE( ((ABS(CLOSE-REF(CLOSE,N))) / (SUM(ABS(CLOSE-REF(CLOSE,1)),N))) * ( (2/(P+1)) - (2/(Q+1)) ) + (2/(Q+1)) );

    参数:
    series_x: 目标序列 (例如 df['close'])
    close_series: 用于计算CONSTANT的收盘价序列 (通常与series_x相同)
    n_lookback (N): 用于EMA和效率系数(ER)计算的周期
    p_fast_period (P): 快线频率参数 (for FASTSC)
    q_slow_period (Q): 慢线频率参数 (for SLOWSC)

    注: 多个REF和SUM操作，确保有足够数据.
        N, P, Q 为0或空值，或数据不足N时，函数返回空值.
        P 和 Q 为参数名称，不是变量。
    """
    if n_lookback <= 0 or p_fast_period <= 0 or q_slow_period <= 0:
        return pd.Series(np.nan, index=series_x.index, name="ADMA")
    # Per KAMA, fast period should be smaller than slow period for sensible scaling factor.
    # if p_fast_period >= q_slow_period:
    #     print("Warning: For ADMA/KAMA, P (fast) should typically be less than Q (slow).")
    #     # Depending on platform, might return NaN or proceed. Let's proceed.

    # 1. Calculate ER (Efficiency Ratio) parts
    direction = ABS(close_series - REF(close_series, n_lookback))
    price_change_1per = ABS(close_series - REF(close_series, 1))
    volatility = SUM(price_change_1per, n_lookback)  # Rolling sum of N period changes

    # Efficiency Ratio (ER)
    er = pd.Series(np.nan, index=close_series.index)
    valid_er_mask = (volatility != 0) & (volatility.notna()) & (direction.notna())
    er[valid_er_mask] = direction[valid_er_mask] / volatility[valid_er_mask]
    er = er.clip(0, 1)  # ER is typically between 0 and 1
    er = er.fillna(0)  # Or handle NaNs more gracefully if needed, e.g., forward fill after first calculation

    # 2. Calculate Smoothing Constants (FASTSC, SLOWSC from formula)
    # These are fixed values based on P and Q (p_fast_period, q_slow_period)
    sc_fast = 2 / (p_fast_period + 1)
    sc_slow = 2 / (q_slow_period + 1)

    # 3. Calculate CONSTANT_val (dynamic smoothing factor squared)
    # 文华公式: CONSTANT_val = SQUARE( ER * (sc_fast - sc_slow) + sc_slow )
    smoothing_coeff = er * (sc_fast - sc_slow) + sc_slow
    constant_val_sq = SQUARE(smoothing_coeff)

    # 4. Calculate the EMA term needed for ADMA
    ema_x_n = EMA(series_x, n_lookback)  # Using our defined EMA helper
    ref_ema_x_n_1 = REF(ema_x_n, 1)

    # 5. Calculate ADMA
    # ADMA_val = REF(EMA(X,N),1) + CONSTANT_val * (X - REF(EMA(X,N),1));
    adma_values = ref_ema_x_n_1 + constant_val_sq * (series_x - ref_ema_x_n_1)

    # Handling initial NaNs:
    # The result will have NaNs at the beginning due to REF, SUM, and EMA lookbacks.
    # The longest lookback period is n_lookback.
    # A simple way to ensure "不足N根返回空值" behavior for the final ADMA.
    # This might need more sophisticated NaN handling if components initialize differently.
    # For example, if EMA starts outputting values before n_lookback periods.
    # Our EMA helper with min_periods=1 starts early. If we want EMA(X,N) to be NaN before N periods:
    # ema_x_n_strict = series_x.ewm(span=n_lookback, adjust=False, min_periods=n_lookback).mean()
    # ref_ema_x_n_1_strict = REF(ema_x_n_strict, 1)
    # adma_values = ref_ema_x_n_1_strict + constant_val_sq * (series_x - ref_ema_x_n_1_strict)

    # Let NaNs from components propagate. The effective start will be determined by the
    # component that starts latest (e.g. REF(close_series, n_lookback) or SUM(..., n_lookback)).
    adma_values.name = "ADMA"
    return adma_values


def CROSS(series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """
    CROSS(A,B) 表示A从下方向上穿过B，成立返回1(Yes)，否则返回0(No).
    文华说明: 满足穿越的条件必须上根k线满足A<=B，当根k线满足A>B才被认定为穿越.
    """
    prev_a = REF(series_a, 1)
    prev_b = REF(series_b, 1)
    # Ensure results are boolean before logical_and, then convert to int
    cond1 = series_a > series_b
    cond2 = prev_a <= prev_b
    # Handle NaNs from REF: a cross cannot happen if previous value is unknown
    return (cond1 & cond2 & prev_a.notna() & prev_b.notna()).astype(int)


def CROSSDOWN(series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """
    CROSSDOWN(A,B)：表示当A从上方向下穿B，成立返回1(Yes)，否则返回0(No).
    逻辑: A_prev >= B_prev AND A_curr < B_curr
    """
    prev_a = REF(series_a, 1)
    prev_b = REF(series_b, 1)
    cond1 = series_a < series_b
    cond2 = prev_a >= prev_b
    return (cond1 & cond2 & prev_a.notna() & prev_b.notna()).astype(int)


def CROSSUP(series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """
    CROSSUP(A,B) 表当A从下方向上穿过B，成立返回1(Yes)，否则返回0(No).
    同 CROSS(A,B).
    """
    return CROSS(series_a, series_b)


def DMA(series_x: pd.Series, weight_param_a) -> pd.Series:  # 'a' can be a float or a pd.Series
    """
    DMA(X,A)：求X的动态移动平均，其中A为权重, 必须小于1大于0.
    计算公式：DMA_t = X_t * A_t + DMA_{t-1} * (1 - A_t)
    注: A可以为变量 (pd.Series). 如果A<=0或者A>=1，返回无效值 (NaN).
    """
    dma_values = pd.Series(np.nan, index=series_x.index, name="DMA")

    if isinstance(weight_param_a, pd.Series):
        # Mask for invalid 'a' values in the series
        invalid_a_mask = (weight_param_a <= 0) | (weight_param_a >= 1) | weight_param_a.isna()
        current_a = weight_param_a  # 'a' is a series
    else:  # 'a' is a scalar
        if not (isinstance(weight_param_a, (float, int)) and 0 < weight_param_a < 1):
            return dma_values  # Return all NaNs if scalar 'a' is invalid
        # Create a constant series for invalid_a_mask (all False) and current_a
        invalid_a_mask = pd.Series(False, index=series_x.index)
        current_a = pd.Series(weight_param_a, index=series_x.index)

    # Initialize first DMA value
    first_valid_idx = series_x.first_valid_index()
    if first_valid_idx is not None:
        if not invalid_a_mask.loc[first_valid_idx]:
            dma_values.loc[first_valid_idx] = series_x.loc[first_valid_idx]

    # Iterative calculation
    for i in range(len(series_x)):
        current_idx = series_x.index[i]
        if i == 0:  # Already handled initialization for the very first point if valid
            if pd.isna(dma_values.loc[current_idx]) and not invalid_a_mask.loc[current_idx] and series_x.loc[current_idx] is not np.nan:
                dma_values.loc[current_idx] = series_x.loc[current_idx]  # Re-attempt init if it was NaN but A and X are valid
            continue

        prev_idx = series_x.index[i - 1]

        if invalid_a_mask.loc[current_idx] or pd.isna(series_x.loc[current_idx]):
            dma_values.loc[current_idx] = np.nan
            continue

        if pd.isna(dma_values.loc[prev_idx]):  # If previous DMA is NaN, (re)-initialize with current X
            dma_values.loc[current_idx] = series_x.loc[current_idx]
        else:
            a_val = current_a.loc[current_idx]
            dma_values.loc[current_idx] = series_x.loc[current_idx] * a_val + \
                                          dma_values.loc[prev_idx] * (1 - a_val)
    return dma_values


def EMA2(series_x: pd.Series, n: int) -> pd.Series:  # Also known as WMA (Weighted Moving Average)
    """
    EMA2(X,N)：求N周期X值的线性加权移动平均 (WMA).
    EMA2(X,N)=[N*X0+(N-1)*X1+...+1*X(N-1)]/[N+(N-1)+...+1], X0 is current value.
    注: N包含当前k线. 不足N根，返回空值. N为0或空值时返回值为空值.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name="EMA2")  # Name for clarity

    weights = np.arange(1, n + 1)  # Weights are 1, 2, ..., N for X_N-1, ..., X_0
    # So for X0 (current), weight is N. For X1 (prev), N-1.
    # Pandas window gives [X_N-1, ..., X_1, X_0]
    # So weights should be [1, ..., N-1, N]
    sum_weights = np.sum(weights)  # Denominator: N*(N+1)/2

    def wma_calc(window_series_arr):  # window_series_arr is a numpy array
        return (window_series_arr * weights).sum() / sum_weights

    return series_x.rolling(window=n, min_periods=n).apply(wma_calc, raw=True)


def HARMEAN(series_x: pd.Series, n: int) -> pd.Series:
    """
    HARMEAN(X,N) 求X在N个周期内的调和平均值.
    算法: N / SUM_window(1/X_i)
    注: N包含当前k线. 不足N根，返回空值. X为0或空值在窗口内，返回空值.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name="HARMEAN")

    def harmean_calc(window_arr):  # window_arr is a numpy array
        if np.any(window_arr <= 0) or np.isnan(window_arr).any():  # X must be positive
            return np.nan
        sum_reciprocals = np.sum(1.0 / window_arr)
        if sum_reciprocals == 0:  # Should not happen if all X > 0
            return np.nan
        return len(window_arr) / sum_reciprocals  # Use len(window_arr) instead of n for robustness if min_periods < n

    # min_periods=n ensures full window or NaN
    return series_x.rolling(window=n, min_periods=n).apply(harmean_calc, raw=True)


def KURTOSIS(series_x: pd.Series, n: int) -> pd.Series:
    """
    KURTOSIS(X,N) 求X在N个周期内的峰度系数.
    注: N包含当前k线. 不足N根，返回空值. N至少为4.
    Pandas .kurt() calculates Fisher (excess) kurtosis. Normal dist = 0.
    WenHua formula often refers to Pearson's kurtosis (Normal dist = 3) or specific adjustments.
    The example formula is complex: ((POW(C-MA(C,4),4)+...)...)-3*...
    This suggests an adjusted Pearson's kurtosis or similar.
    For simplicity and common usage, pandas built-in Fisher kurtosis is provided.
    If exact WenHua calculation is needed, its specific formula must be meticulously implemented.
    """
    if n < 4:  # Kurtosis requires at least 4 data points for meaningful calculation
        return pd.Series(np.nan, index=series_x.index, name="KURTOSIS")
    return series_x.rolling(window=n, min_periods=n).kurt()


def MEDIAN(series_x: pd.Series, n: int) -> pd.Series:
    """
    MEDIAN(X,N) 求X在N个周期内居于中间的数值.
    注: N可以为变量(not supported here, N is int). N周期内X如果存在空值，则忽略空值求中间值.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name="MEDIAN")
    # .median() in pandas rolling naturally handles NaNs within the window by ignoring them (skipna=True default)
    # min_periods=1 ensures it calculates if at least one non-NaN value in window.
    return series_x.rolling(window=n, min_periods=1).median()


def SKEWNESS(series_x: pd.Series, n: int) -> pd.Series:
    """
    SKEWNESS(X,N) 求X在N个周期内的偏度系数.
    注: N包含当前k线. 不足N根，返回空值. N至少为3.
    """
    if n < 3:  # Skewness requires at least 3 data points
        return pd.Series(np.nan, index=series_x.index, name="SKEWNESS")
    return series_x.rolling(window=n, min_periods=n).skew()


def SLOPE(series_x: pd.Series, n: int) -> pd.Series:
    """
    SLOPE(X,N)：得到X的N周期的线型回归的斜率.
    注: N包含当前k线. 不足N根，返回空值. N为0,1或空值，返回空值.
    """
    if n <= 1:  # Linear regression needs at least 2 points for a slope
        return pd.Series(np.nan, index=series_x.index, name="SLOPE")

    def linear_slope_calc(window_y_arr):  # window_y_arr is a numpy array
        # raw=True, so window_y_arr is numpy array. No need to check for NaNs inside,
        # as rolling(min_periods=n) should provide full, non-NaN windows if input is clean.
        # If input series_x can have NaNs, then apply may receive windows with NaNs.
        if np.isnan(window_y_arr).any():  # Check if NaNs exist in the window
            return np.nan
        x_coords = np.arange(len(window_y_arr))
        # 기울기, 절편
        slope, _ = np.polyfit(x_coords, window_y_arr, deg=1)
        return slope

    return series_x.rolling(window=n, min_periods=n).apply(linear_slope_calc, raw=True)


def SMA(series_x: pd.Series, n_period: int, m_weight: int) -> pd.Series:
    """
    SMA(X,N,M) 求X的N个周期内的扩展指数加权移动平均. M为权重.
    计算公式：SMA_t = (X_t * M + SMA_{t-1} * (N-M)) / N_period
    This is a specific type of weighted average, not the common Simple Moving Average.
    This is Wilder's Smoothing Average if M=1.
    Assume 0 < M <= N.
    """
    if n_period <= 0 or m_weight <= 0 or m_weight > n_period:
        return pd.Series(np.nan, index=series_x.index, name="SMA_custom")

    sma_values = pd.Series(np.nan, index=series_x.index, name="SMA_custom")

    first_valid_idx = series_x.first_valid_index()
    if first_valid_idx is not None:
        # Initialize first value. Common practice for Wilder's is a simple MA for the first N,
        # or just the first value of X. Here, using first X.
        sma_values.loc[first_valid_idx] = series_x.loc[first_valid_idx]

    # Iterative calculation
    for i in range(len(series_x)):
        current_idx = series_x.index[i]
        if i == 0:  # Already handled for the very first point
            if pd.isna(sma_values.loc[current_idx]) and series_x.loc[current_idx] is not np.nan:
                sma_values.loc[current_idx] = series_x.loc[current_idx]  # Re-attempt init if series_x was NaN at first_valid_idx
            continue

        prev_idx = series_x.index[i - 1]

        if pd.isna(series_x.loc[current_idx]):  # Current X is NaN
            sma_values.loc[current_idx] = sma_values.loc[prev_idx]  # Carry forward previous SMA
            continue

        if pd.isna(sma_values.loc[prev_idx]):  # If previous SMA is NaN (e.g. after NaN X)
            sma_values.loc[current_idx] = series_x.loc[current_idx]  # Re-initialize with current X
        else:
            sma_values.loc[current_idx] = (series_x.loc[current_idx] * m_weight + \
                                           sma_values.loc[prev_idx] * (n_period - m_weight)) / n_period
    return sma_values


def SMMA(series_x: pd.Series, n: int) -> pd.Series:
    """
    SMMA(X,N)，X为变量,N为周期，SMMA(X,N)表示当前K线上X在N个周期的通畅移动平均线 (Smoothed Moving Average)
    算法：SMMA_t = (SMMA_{t-1} * (N-1) + X_t) / N
    This is equivalent to Wilder's Smoothing Average or an EMA with alpha = 1/N.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name="SMMA")
    # EMA span = (2 / alpha) - 1. Here alpha = 1/N. So span = 2*N - 1.
    # Or directly use .ewm(alpha=1/N)
    return series_x.ewm(alpha=1 / n, adjust=False, min_periods=1).mean()  # min_periods=1 or n based on strictness


def TRMA(series_x: pd.Series, n: int) -> pd.Series:
    """
    TRMA(X,N)： 求X在N个周期的三角移动平均值.
    算法：
    N_is_odd: N1 = (N+1)/2. TRMA = MA(MA(X, N1), N1)
    N_is_even: N1 = N/2, N2 = N/2 + 1. TRMA = MA(MA(X, N1), N2)
    注: 不足N根，返回空值. N为0或空值，返回空值.
    The `min_periods=n_val` in each MA call ensures values are NaN until enough data.
    The final result will be NaN until enough data has passed for both MAs.
    The effective lookback for the first value is roughly 'n'.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name="TRMA")

    if n % 2 == 1:  # N is odd
        n1 = (n + 1) // 2
        # Ensure n1 is at least 1
        if n1 == 0: n1 = 1
        ma1 = series_x.rolling(window=n1, min_periods=n1).mean()
        trma_val = ma1.rolling(window=n1, min_periods=n1).mean()
    else:  # N is even
        n1 = n // 2
        n2 = n // 2 + 1
        if n1 == 0: n1 = 1  # Should not happen if n > 0
        if n2 == 0: n2 = 1  # Should not happen if n > 0

        ma1 = series_x.rolling(window=n1, min_periods=n1).mean()
        trma_val = ma1.rolling(window=n2, min_periods=n2).mean()

    trma_val.name = "TRMA"
    # To strictly ensure "不足N根返回空值" for the entire TRMA:
    # The combined effect of min_periods in nested MAs usually handles this correctly.
    # e.g. TRMA(4): MA(MA(X,2),3). MA(X,2) needs 2 bars. MA(MA(X,2),3) needs 3 of MA1, so 2+3-1=4 bars total.
    # So, the min_periods on individual MAs should be sufficient.
    return trma_val


def FORCAST(series_x: pd.Series, n: int) -> pd.Series:
    """
    FORCAST(X,N)：为X的N周期线性回归预测值.
    This is the value of the linear regression line at the N-th point (end point) of the current window.
    注: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 1:  # Linear regression needs at least 2 points
        return pd.Series(np.nan, index=series_x.index, name="FORCAST")

    def linear_forecast_val(window_y_arr):  # window_y_arr is a numpy array
        if np.isnan(window_y_arr).any():
            return np.nan
        x_coords = np.arange(len(window_y_arr))
        slope, intercept = np.polyfit(x_coords, window_y_arr, deg=1)
        # Value of the line at the last point of the window (index N-1 for 0-indexed x_coords)
        return slope * (len(window_y_arr) - 1) + intercept

    return series_x.rolling(window=n, min_periods=n).apply(linear_forecast_val, raw=True)


def COEFFICIENTR(series_x: pd.Series, series_y: pd.Series, n: int) -> pd.Series:
    """
    COEFFICIENTR(X,Y,N) 求X、Y在N个周期内的皮尔森相关系数.
    注: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 1:  # Correlation typically needs at least 2 pairs of points
        return pd.Series(np.nan, index=series_x.index, name="COEFFICIENTR")
    return series_x.rolling(window=n, min_periods=n).corr(series_y)


def COVAR(series_x: pd.Series, series_y: pd.Series, n: int) -> pd.Series:
    """
    COVAR(X,Y,N) 求X、Y在N个周期内的协方差.
    文华说明: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    Pandas .cov() calculates sample covariance (ddof=1).
    WenHua's example COVAR(O,C,3) = (...)/3 suggests population covariance (ddof=0 for variance part).
    To match population covariance: ddof=0 in underlying variance calculations.
    If using pandas .cov(), it's sample by default.
    Let's assume sample covariance as it's more standard for financial library functions.
    """
    if n <= 1:  # Covariance typically needs at least 2 pairs of points
        return pd.Series(np.nan, index=series_x.index, name="COVAR")
    # Pandas rolling().cov() calculates sample covariance.
    return series_x.rolling(window=n, min_periods=n).cov(series_y)


# ==============================================================================
# Part 3: 更多指标、时间日期与信息函数
# ==============================================================================
print("--- Part 3: 更多指标、时间日期与信息函数 ---")


def BARSCOUNT(series_x: pd.Series) -> pd.Series:
    """
    BARSCOUNT(COND) 第一个有效周期到当前的周期数. (COND is effectively series_x here)
    文华说明: 返回值为COND从第一个有效周期(第一个非空值)开始计算，到现在为止的周期数.
              COND的第一个有效数据K线上BARSCOUNT(COND)的返回值为0.
    """
    if series_x.empty:
        return pd.Series(dtype=float, name="BARSCOUNT")

    output = pd.Series(np.nan, index=series_x.index, name="BARSCOUNT")
    first_valid_iloc = -1

    # Find the integer position of the first non-NaN value
    for i in range(len(series_x)):
        if pd.notna(series_x.iloc[i]):
            first_valid_iloc = i
            break

    if first_valid_iloc != -1:
        for i in range(first_valid_iloc, len(series_x)):
            output.iloc[i] = i - first_valid_iloc

    return output


def BARSLAST(condition: pd.Series) -> pd.Series:
    """
    BARSLAST(COND)：上一次条件COND成立到当前的周期数.
    文华说明: 条件成立的当根k线上BARSLAST(COND)的返回值为0.
              如果之前从未成立，则为空值 (NaN).
    """
    condition = condition.astype(bool)  # Ensure boolean
    out = pd.Series(np.nan, index=condition.index, name="BARSLAST")
    last_true_iloc = -1  # Using iloc for integer position

    for i in range(len(condition)):
        if condition.iloc[i]:
            last_true_iloc = i
            out.iloc[i] = 0
        elif last_true_iloc != -1:  # If a True has occurred before
            out.iloc[i] = i - last_true_iloc
    return out


def BARSLASTCOUNT(condition: pd.Series) -> pd.Series:
    """
    BARSLASTCOUNT(COND) 从当前周期向前计算，统计连续满足条件的周期数.
    文华说明: 条件第一次成立的当根k线上BARSLASTCOUNT(COND)的返回值为1.
    """
    condition = condition.astype(bool)
    # This counts consecutive Trues.
    # Group by changes from True to False or vice-versa, then cumsum within groups of Trues.
    # (condition != condition.shift()) identifies change points. cumsum() creates group IDs.
    # If condition.iloc[i] is True, count it. If False, result is 0 for that bar.

    # Calculate blocks of consecutive values
    blocks = (condition != condition.shift()).cumsum()
    # Cumulative sum within each block, then filter by condition
    consecutive_count = condition.groupby(blocks).cumsum()
    # Where condition is False, the count should be 0, not the carried forward count.
    consecutive_count[~condition] = 0

    consecutive_count.name = "BARSLASTCOUNT"
    return consecutive_count


def BARSSINCE(condition: pd.Series) -> pd.Series:
    """
    BARSSINCE(COND) 第一个条件成立到当前的周期数.
    文华说明: 条件第一次成立的当根k线上BARSSINCE(COND)的返回值为0.
              如果从未成立，则为空值 (NaN).
    """
    condition = condition.astype(bool)
    out = pd.Series(np.nan, index=condition.index, name="BARSSINCE")

    first_true_iloc = -1
    # Find the integer position of the first True value
    true_indices = np.where(condition)[0]
    if len(true_indices) > 0:
        first_true_iloc = true_indices[0]
    else:  # No True in condition
        return out  # All NaNs

    for i in range(first_true_iloc, len(condition)):
        out.iloc[i] = i - first_true_iloc

    return out


def BARSSINCEN(condition: pd.Series, n: int) -> pd.Series:
    """
    BARSSINCEN(COND,N) 统计N周期内第一次条件成立到当前的周期数.
    注: N包含当前k线. 若N为0返回无效值. N可以为变量 (here N is int).
        当N为有效值，但当前的k线数不足N根，按照实际的根数计算.
        条件第一次成立的当根k线上返回值为0.
    """
    if n == 0:
        return pd.Series(np.nan, index=condition.index, name="BARSSINCEN")
    if n < 0:
        return pd.Series(np.nan, index=condition.index, name="BARSSINCEN")

    condition = condition.astype(bool)
    out = pd.Series(np.nan, index=condition.index, name="BARSSINCEN")

    for i in range(len(condition)):
        # Determine the actual window start for this iteration
        # Window is from max(0, i - n + 1) to i (inclusive)
        start_idx_for_window = max(0, i - n + 1)
        window = condition.iloc[start_idx_for_window: i + 1]

        true_in_window_indices = np.where(window)[0]  # Relative indices within the window

        if len(true_in_window_indices) > 0:
            first_true_relative_idx = true_in_window_indices[0]
            # Distance from that first true in window to end of window (current point)
            # End of window is at relative index (len(window) - 1)
            out.iloc[i] = (len(window) - 1) - first_true_relative_idx
        # else: out.iloc[i] remains NaN

    return out


def BARPOS(series_for_index: pd.Series) -> pd.Series:
    """
    BARPOS，返回从第一根K线开始到当前的周期数.
    注: 本机已有的第一根K线上返回值为1.
    """
    if series_for_index.empty:
        return pd.Series(dtype=float, name="BARPOS")
    # Assuming series_for_index provides the index structure
    return pd.Series(np.arange(1, len(series_for_index) + 1), index=series_for_index.index, name="BARPOS", dtype=float)


# --- Time and Date related (assuming df.index is DatetimeIndex) ---
# Note: For these functions to work correctly, the DataFrame 'df' should have a pd.DatetimeIndex.
# Example usage: DATE(df.index), HOUR(df.index), etc.

def DATE(idx: pd.DatetimeIndex) -> pd.Series:
    """
    DATE,返回某周期的日期数. YYMMDD.
    文华夜盘: 2013/7/8 21:00 (K线开始时间) -> DATE is 130709 (交易日为次日).
    This simple implementation uses the K-bar's actual date.
    Accurate night session handling requires knowing the specific contract's session rules
    and potentially shifting the date for night K-bars.
    """
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")
    # Simple version: returns the date of the k-bar's timestamp.
    # For specific WenHua night session behavior, more logic is needed.
    return pd.Series(idx.strftime('%y%m%d').astype(int), index=idx, name="DATE")


def DATE1(idx: pd.DatetimeIndex) -> pd.Series:
    """
    DATE1：返回某周期的日期数.
    2000年以前 YYMMDD, 2000年以后 1YYMMDD.
    Similar caveat for night sessions as DATE function.
    """
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")
    year = idx.year
    date_str_series = pd.Series(idx.strftime('%y%m%d'), index=idx)

    # Create result series based on year condition
    result = pd.Series(np.where(year >= 2000, '1' + date_str_series, date_str_series), index=idx)
    return result.astype(int)


def DAY(idx: pd.DatetimeIndex) -> pd.Series:
    """DAY,返回某一周期的日数 (1-31). (Day of the month)"""
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")
    return pd.Series(idx.day, index=idx, name="DAY")


def HOUR(idx: pd.DatetimeIndex) -> pd.Series:
    """HOUR，返回某周期的小时数 (0-23). (Hour of the K-bar start time)"""
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")
    return pd.Series(idx.hour, index=idx, name="HOUR")


def MINUTE(idx: pd.DatetimeIndex) -> pd.Series:
    """MINUTE,返回某个周期的分钟数 (0-59). (Minute of the K-bar start time)"""
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")
    return pd.Series(idx.minute, index=idx, name="MINUTE")


def MONTH(idx: pd.DatetimeIndex) -> pd.Series:
    """MONTH，返回某个周期的月份 (1-12)."""
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")
    return pd.Series(idx.month, index=idx, name="MONTH")


def PERIOD(df_index: pd.Index = None, current_period_code: int = None) -> int:
    """
    PERIOD，返回当前技术分析图表的周期.
    文华返回值代码: 1(1min), ..., 8(1day), 9(1week), ... (see full list in WenHua docs).
    This function is highly context-dependent in Python as there's no global "chart period".
    It should ideally be passed as an argument or inferred from data frequency if possible.

    Args:
    df_index: Optional pandas Index to try to infer frequency.
    current_period_code: Optional integer representing the WenHua period code if known.

    Returns:
    An integer code if determinable, or a placeholder/error.
    """
    if current_period_code is not None:
        return current_period_code  # If explicitly provided

    if isinstance(df_index, pd.DatetimeIndex) and df_index.freqstr:
        freq = df_index.freqstr.upper()
        # Simplified mapping based on common pandas freqstr
        # This is NOT exhaustive for all WenHua period codes.
        mapping = {
            'T': 1, 'MIN': 1, '1T': 1, '1MIN': 1,
            '3T': 2, '3MIN': 2,
            '5T': 3, '5MIN': 3,
            '10T': 4, '10MIN': 4,
            '15T': 5, '15MIN': 5,
            '30T': 6, '30MIN': 6,
            'H': 7, '1H': 7,
            'D': 8,
            'W': 9,  # Usually 'W-MON', 'W-SUN' etc.
            'ME': 10,  # Pandas 'M' or 'BM' for month start/end
            'YE': 11,  # Pandas 'A' or 'BA' for year start/end
            'S': 15,  # 5 seconds (example, codes 12-27 are more varied)
            # Tick (12), 量能 (13), 5S(14), 10S(15), 15S(16), 30S(17)
            # 2H(18), 3H(19), 4H(20), 季(21), 自定义秒(22),分(23),时(24),日(25),月(26),年(27)
        }
        for k, v in mapping.items():
            if k in freq:  # Simple check
                return v
        print(f"Warning: PERIOD function could not map pandas freq '{freq}' to a known WenHua code.")
        return -1  # Unknown
    print("Warning: PERIOD function requires context or a DatetimeIndex with frequency.")
    return -1  # Unknown or placeholder


def QUARTER(idx: pd.DatetimeIndex) -> pd.Series:
    """QUARTER,返回某周期的季度数 (1-4)."""
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")
    return pd.Series(idx.quarter, index=idx, name="QUARTER")


def TIME(idx: pd.DatetimeIndex) -> pd.Series:
    """
    TIME，取K线时间.
    文华说明: K线走完后返回K线的起始时间.
              秒周期返回HHMMSS, 其他周期返回HHMM.
    """
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")

    # Check if the index frequency suggests seconds resolution
    # This is an approximation. A more robust way would be to check if any timestamp has non-zero seconds.
    has_seconds = False
    if idx.freqstr:
        if 'S' in idx.freqstr.upper() and 'MIN' not in idx.freqstr.upper() and 'H' not in idx.freqstr.upper():
            has_seconds = True
    elif (idx.second != 0).any():  # If no freq, check data itself
        has_seconds = True

    if has_seconds:
        return pd.Series(idx.strftime('%H%M%S').astype(int), index=idx, name="TIME")
    else:
        return pd.Series(idx.strftime('%H%M').astype(int), index=idx, name="TIME")


def WEEKDAY(idx: pd.DatetimeIndex) -> pd.Series:
    """
    WEEKDAY,取得星期数.
    Pandas: Monday=0, Tuesday=1, ..., Sunday=6.
    文华财经的星期表示可能不同 (e.g., 0-6 or 1-7, Sunday or Monday as start).
    The example "WEEKDAY=5" implies a 0-6 or 1-7 range.
    Assuming WenHua aligns with Python's datetime.weekday() (Monday=0...Sunday=6).
    If WenHua's Friday is 5, then this is consistent.
    """
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")
    return pd.Series(idx.weekday, index=idx, name="WEEKDAY")


def YEAR(idx: pd.DatetimeIndex) -> pd.Series:
    """YEAR，取得年份 (e.g., 2023)."""
    if not isinstance(idx, pd.DatetimeIndex):
        raise TypeError("Input 'idx' must be a pandas DatetimeIndex.")
    return pd.Series(idx.year, index=idx, name="YEAR")


# --- Informational / Utility Functions ---
# These functions often return scalar values specific to the contract or environment.
# In Python, these would typically be pre-defined variables or looked up from contract specs.

def MINPRICE(instrument_symbol: str = None, known_min_prices: dict = None) -> float:
    """
    MINPRICE 取数据合约的最小变动价位.
    This is context-specific. In a Python script, you'd likely have this info.
    """
    if known_min_prices and instrument_symbol in known_min_prices:
        return known_min_prices[instrument_symbol]['minprice_data']
    # print("MINPRICE: Contract symbol or lookup not provided. Returning placeholder.")
    return 0.01  # Placeholder, replace with actual lookup


def MINPRICE1(instrument_symbol: str = None, known_min_prices: dict = None) -> float:
    """MINPRICE1  取交易合约的最小变动价位."""
    if known_min_prices and instrument_symbol in known_min_prices:
        return known_min_prices[instrument_symbol]['minprice_trade']
    # print("MINPRICE1: Contract symbol or lookup not provided. Returning placeholder.")
    return 0.01  # Placeholder


def UNIT(instrument_symbol: str = None, known_units: dict = None) -> int:
    """UNIT 取数据合约的交易单位."""
    if known_units and instrument_symbol in known_units:
        return known_units[instrument_symbol]['unit_data']
    # print("UNIT: Contract symbol or lookup not provided. Returning placeholder.")
    return 1  # Placeholder


def UNIT1(instrument_symbol: str = None, known_units: dict = None) -> int:
    """UNIT1  取交易合约的交易单位."""
    if known_units and instrument_symbol in known_units:
        return known_units[instrument_symbol]['unit_trade']
    # print("UNIT1: Contract symbol or lookup not provided. Returning placeholder.")
    return 1  # Placeholder


def STKTYPE(instrument_symbol: str = None, known_stk_types: dict = None) -> int:
    """
    STKTYPE 取市场类型.
    文华返回值: 1国内股票, 2美国股票, 6外汇, 7国内期货, 8国内期权, 9外盘, 5其它.
    """
    if known_stk_types and instrument_symbol in known_stk_types:
        return known_stk_types[instrument_symbol]
    # print("STKTYPE: Contract symbol or lookup not provided. Returning placeholder (e.g., 7 for futures).")
    return 7  # Placeholder for domestic futures


# --- More Logic/Statistical Functions ---

def COUNT(condition: pd.Series, n: int) -> pd.Series:
    """
    COUNT(COND,N)：统计N周期中满足COND条件的周期数.
    注: N包含当前k线. 若N为0则从第一个有效值开始算起.
        不足N根，从第一根统计到当前周期 (min_periods=1 for rolling).
    """
    if not isinstance(condition, pd.Series):
        raise TypeError("Input 'condition' must be a pandas Series (boolean or 0/1).")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    condition_int = condition.astype(int)

    if n < 0:
        return pd.Series(np.nan, index=condition.index, name="COUNT")
    if n == 0:
        return condition_int.expanding(min_periods=1).sum()
    else:
        return condition_int.rolling(window=n, min_periods=1).sum()


def EVERY(condition: pd.Series, n: int) -> pd.Series:
    """
    EVERY(COND,N)，判断N周期内，是否一直满足COND条件. 若满足返回1,不满足返回0.
    注: N包含当前k线. 若N是有效数值，但前面没有那么多K线,或者N为空值，代表不成立，返回0.
    """
    if not isinstance(condition, pd.Series):
        raise TypeError("Input 'condition' must be a pandas Series (boolean or 0/1).")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n <= 0:
        return pd.Series(0, index=condition.index, dtype=int, name="EVERY")

    # A rolling window where all elements are True.
    # .rolling(window=n).min() on a boolean series: if all True, min is True. If any False, min is False.
    # min_periods=n makes it NaN if window isn't full. Then fillna(False) for "不成立，返回0".
    result_is_true = condition.astype(bool).rolling(window=n, min_periods=n).min()
    return result_is_true.fillna(False).astype(int)


def EXIST(condition: pd.Series, n: int) -> pd.Series:
    """
    EXIST(COND,N) 判断N个周期内是否有满足COND的条件. (True=1, False=0)
    注: N包含当前k线. 若N是有效数值，但前面没有那么多K线，按实际周期数计算.
    """
    if not isinstance(condition, pd.Series):
        raise TypeError("Input 'condition' must be a pandas Series (boolean or 0/1).")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n <= 0:
        # 文华文档对此情况未明确. 若N=0从头算起，则用expanding().max()
        # 若N<=0无意义，则返回0或NaN. 这里返回0.
        return pd.Series(0, index=condition.index, dtype=int, name="EXIST")

    # Rolling sum of condition (as int). If sum > 0, then at least one True in window.
    # min_periods=1 ensures calculation even if window is not full ("按实际周期数计算").
    any_true_in_window = (condition.astype(int).rolling(window=n, min_periods=1).sum() > 0)
    return any_true_in_window.astype(int)


def FILTER(condition: pd.Series, n_filter_out: int) -> pd.Series:
    """
    FILTER(COND,N) 当COND条件成立，将其后N周期内的数据设置为0.
    (N is filter duration, does not include the trigger bar itself).
    Output: A boolean series, True where COND is True AND not filtered.
    """
    if not isinstance(condition, pd.Series):
        raise TypeError("Input 'condition' must be a pandas Series (boolean or 0/1).")
    if not isinstance(n_filter_out, int) or n_filter_out < 0:
        raise ValueError("Parameter 'n_filter_out' must be a non-negative integer.")

    condition_true = condition.astype(bool)
    output_signal = pd.Series(False, index=condition.index, name="FILTER_Output")
    filter_end_iloc = -1  # Integer location strictly *after* which signals are allowed again

    for i in range(len(condition)):
        if i < filter_end_iloc:  # If current bar is within a filter period (i.e. i <= filter_until_iloc)
            output_signal.iloc[i] = False
        elif condition_true.iloc[i]:  # Condition is true and not currently filtered
            output_signal.iloc[i] = True
            filter_end_iloc = i + n_filter_out + 1  # Filter for the NEXT N bars, so end is i + N_bars + 1 (exclusive)
        # else: Condition is false and not filtered, output_signal.iloc[i] remains False

    return output_signal.astype(int)


def LONGCROSS(series_a: pd.Series, series_b: pd.Series, n_sustain: int) -> pd.Series:
    """
    LONGCROSS(A,B,N) 表示A在N个周期内都小于B，本周期A从下向上穿越B.
    Interpretation:
    1. A standard CROSS(A,B) happens on the current bar.
    2. For `n_sustain` periods *immediately preceding the bar before the cross* (i.e., bars t-2 to t-(N+1) if cross is at t), A was strictly less than B.
       Or, "A在N个周期内都小于B" refers to N periods ending at REF(A,1) < REF(B,1).
       Let's use: CROSS is true AND for k=1 to N, REF(A,k) < REF(B,k).
       This means N bars *before current* had A < B. The CROSS condition implies REF(A,1) <= REF(B,1).
       So, we check A < B for N periods ending one bar ago.
    """
    if not isinstance(series_a, pd.Series) or not isinstance(series_b, pd.Series):
        raise TypeError("Inputs 'series_a' and 'series_b' must be pandas Series.")
    if not isinstance(n_sustain, int) or n_sustain <= 0:
        # If N is 0 or invalid, it might mean no sustain condition or always false.
        # Assuming it means the "sustain" part is not met or function is ill-defined.
        return pd.Series(0, index=series_a.index, dtype=int, name="LONGCROSS")

    is_crossing = CROSS(series_a, series_b).astype(bool)

    sustained_below_prev_n = pd.Series(True, index=series_a.index)  # Default to True
    for k in range(1, n_sustain + 1):
        # Check REF(A,k) < REF(B,k)
        # NaNs from REF will make sustained_below_prev_n False where applicable due to & op with bool
        cond_k = REF(series_a, k) < REF(series_b, k)
        # Handle NaNs carefully: if REF(A,k) or REF(B,k) is NaN, cond_k is False/NaN.
        # We need this condition to be True.
        sustained_below_prev_n = sustained_below_prev_n & cond_k.fillna(False)

    return (is_crossing & sustained_below_prev_n).astype(int)


def RANGE(series_x: pd.Series, series_lower: pd.Series, series_upper: pd.Series) -> pd.Series:
    """
    RANGE(X,Y,Z)：介于某个范围之内。表示X大于Y同时小于Z时返回1，否则返回0.
    (X > Y AND X < Z)
    """
    return ((series_x > series_lower) & (series_x < series_upper)).astype(int)


# ==============================================================================
# Part 4: 交易模拟与状态相关函数 (概念与说明)
# ==============================================================================
print("--- Part 4: 交易模拟与状态相关函数 (概念与说明) ---")


# """
# 这一部分的文华财经函数通常涉及到交易的执行状态、持仓信息、盈亏计算等，
# 这些状态是随时间（逐K线）动态变化的。在Pandas中，对历史数据的静态分析
# 无法直接复现这些函数的行为，因为Pandas的向量化操作一般不维护跨行的状态记忆。

# 要在Python中实现这类函数，通常需要构建一个迭代式的回测或交易模拟循环：
# 1. 循环遍历K线数据。
# 2. 定义并维护状态变量，如：
#    - 当前持仓方向 (无、多、空)
#    - 持仓手数
#    - 平均开仓价格
#    - 最近一次开/平仓的K线位置、价格、手数
#    - 累计盈亏、单笔盈亏、连续盈亏等统计
#    - 当前资金、保证金等
# 3. 在循环的每一步，根据预先计算好的交易信号条件 (这些条件可以用Part 1-3的函数生成)，
#    结合当前状态，来决定是否执行交易动作 (开仓、平仓、加仓、减仓)。
# 4. 执行交易动作后，相应地更新状态变量。
# 5. 文华的这类函数在特定K线上返回的值，就是模拟循环到该K线时对应状态变量的值。

# 下面是一些典型函数的概念性说明：

# --- 交易指令函数 (BK, SK, BP, SP, BPK, SPK, ADD_LONG, LOWER_SHORT 等) ---
# 这些函数在文华中是发出交易指令。在Python模拟中，它们对应于在满足条件时
# 修改持仓状态和记录交易的逻辑。
# 例如，`CROSS(MA(C,5), MA(C,10)), BK;`
# Python中会先计算 `buy_condition = CROSS(MA(df['close'],5), MA(df['close'],10))`
# 然后在循环中:
#   if buy_condition.iloc[current_bar_index] and current_position == 0:
#       # 执行买入开仓逻辑：更新持仓、记录价格、手数等

# --- 持仓状态和价格函数 ---
# * BKPRICE, SKPRICE: 返回最近一次多头/空头开仓信号的价位。
#   - 模拟中：当BK/SK发生时，记录成交价格到一个变量 (e.g., `current_bk_price`)。
#             该变量的值即为BKPRICE在后续K线上的返回值，直到下一次BK。
# * LONG_AVPRICE, SHORT_AVPRICE: 返回多头/空头持仓的平均开仓价。
#   - 模拟中：维护加权平均的开仓成本。
# * BKVOL, SKVOL, BKVOL2, SKVOL2: 返回理论/实际多头/空头持仓手数。
#   - 模拟中：维护 `long_lots` 和 `short_lots` 变量。
# * ADDLONG_PLACE, ADDLONG_PRICE, ADDLONG_VOL (及对应空头/减仓系列):
#   获取一次完整交易中最近一个加/减仓信号的位置、价格、手数。
#   - 模拟中：在一个交易周期内（开仓到平仓），记录所有加减仓事件的详情，
#             并根据当前K线计算与最近一次加/减仓事件的相对信息。

# --- 信号位置历史函数 ---
# * BARSBK, BARSSK, BARSBP, BARSSP: 上一次各类信号发生位置到当前的周期数。
#   - 模拟中：记录每种信号最后发生的K线索引 (e.g., `last_bk_bar_idx`)。
#             函数值为 `current_bar_idx - last_bk_bar_idx`。

# --- 开仓后极值函数 ---
# * BKHIGH, BKLOW, SKHIGH, SKLOW: 开仓信号发生后至今的最高/最低价。
#   - 模拟中：开仓后，在每根新K线，用其high/low更新一个从开仓开始的极值变量。

# --- 盈亏相关函数 ---
# * PROFIT: 理论逐笔浮动盈亏。
#   - 模拟中：若持仓，`profit = (current_price - entry_price) * lots * multiplier`。
# * OFFSETPROFIT, OFFSETPROFIT1: 累计平仓盈亏。
#   - 模拟中：平仓时计算单笔盈亏，累加到 `total_closed_pnl`。
# * LASTOFFSETPROFIT: 最近一次交易的平仓盈亏。
#   - 模拟中：平仓时更新 `last_closed_pnl`。
# * 各种交易统计 (TAVLOSS, TAVWIN, TMAXLOSS, etc.):
#   - 模拟中：记录每一笔已完成交易的详细信息（盈亏、手数等）到一个列表。
#             回测结束后，对此列表进行统计分析得到这些指标。

# --- 资金管理函数 ---
# * MONEY, MONEYTOT, MONEYRATIO, INITMONEY, VOLMARGIN:
#   可用资金、总权益、资金使用率、初始资金、持仓保证金。
#   - 模拟中：维护账户权益模型，根据初始资金、交易盈亏、保证金计算规则来更新。

# --- 概念性回测器示例 ---
class SimpleBacktester:
    def __init__(self, df_ohlc, initial_equity=100000, contract_multiplier=1, default_lots=1):
        self.df = df_ohlc.copy()  # Ensure internal copy
        self.initial_equity = initial_equity
        self.equity = initial_equity
        self.contract_multiplier = contract_multiplier
        self.default_lots = default_lots

        # Position state
        self.position = 0  # 0: flat, 1: long, -1: short
        self.entry_price = 0.0
        self.position_size = 0  # Number of lots

        # WenHua state-like variables (to be populated per bar)
        # These would become series after the backtest if stored for each bar
        self.df['BKPRICE_val'] = np.nan
        self.df['SKPRICE_val'] = np.nan
        self.df['BKVOL_val'] = 0
        self.df['SKVOL_val'] = 0
        self.df['PROFIT_val'] = 0.0
        self.df['LASTOFFSETPROFIT_val'] = np.nan
        self.df['OFFSETPROFIT_val'] = 0.0  # Cumulative closed PnL
        # ... add other state variables as columns if needed for plotting/analysis

        self.trade_log = []
        self.current_bar_idx = 0

        # Variables to track for BARS* functions
        self._last_bk_idx = -1
        self._last_sk_idx = -1
        # ... etc for other signals

    def _update_bars_since_signals(self):
        # Example for BARSBK - distance to last BK
        if hasattr(self, 'BARSBK_val'):  # Check if column exists
            if self._last_bk_idx != -1:
                self.df.loc[self.df.index[self.current_bar_idx], 'BARSBK_val'] = self.current_bar_idx - self._last_bk_idx
            else:
                self.df.loc[self.df.index[self.current_bar_idx], 'BARSBK_val'] = np.nan
        # Similarly for other BARS* functions

    def _get_current_price(self, execution_model='close'):
        # Simplified: assumes trading at current bar's close
        # In reality: next bar's open, or more complex intra-bar logic
        return self.df['close'].iloc[self.current_bar_idx]

    def _execute_trade(self, signal_type, price, lots):
        # Placeholder for fee and slippage simulation
        print(f"{self.df.index[self.current_bar_idx]}: {signal_type} {lots} lots at {price}")

    def process_bar_signals(self, bk_cond, sk_cond, sp_cond, bp_cond):
        # Get conditions for the current bar
        # These conditions (bk_cond etc.) should be boolean pd.Series aligned with self.df

        # --- Update state variables that reflect "current state BEFORE action" ---
        # For example, PROFIT (floating PnL)
        current_market_price = self._get_current_price()
        floating_pnl = 0.0
        if self.position == 1:  # Long
            floating_pnl = (current_market_price - self.entry_price) * self.position_size * self.contract_multiplier
        elif self.position == -1:  # Short
            floating_pnl = (self.entry_price - current_market_price) * self.position_size * self.contract_multiplier
        self.df.loc[self.df.index[self.current_bar_idx], 'PROFIT_val'] = floating_pnl

        # --- Update values for BKPRICE, SKPRICE, BKVOL, SKVOL based on current state ---
        self.df.loc[self.df.index[self.current_bar_idx], 'BKPRICE_val'] = self.entry_price if self.position == 1 else np.nan
        self.df.loc[self.df.index[self.current_bar_idx], 'SKPRICE_val'] = self.entry_price if self.position == -1 else np.nan
        self.df.loc[self.df.index[self.current_bar_idx], 'BKVOL_val'] = self.position_size if self.position == 1 else 0
        self.df.loc[self.df.index[self.current_bar_idx], 'SKVOL_val'] = self.position_size if self.position == -1 else 0

        # --- Update cumulative PnL (OFFSETPROFIT) and LASTOFFSETPROFIT (from previous bar's close if any) ---
        # OFFSETPROFIT is cumulative sum of closed PnL, does not change intra-bar unless a trade closes.
        # LASTOFFSETPROFIT is set when a trade closes.
        # These are typically updated *after* a closing trade.
        # For simplicity, we'll keep OFFSETPROFIT_val as the running total.
        self.df.loc[self.df.index[self.current_bar_idx], 'OFFSETPROFIT_val'] = self.df['OFFSETPROFIT_val'].iloc[self.current_bar_idx - 1] if self.current_bar_idx > 0 else 0.0
        self.df.loc[self.df.index[self.current_bar_idx], 'LASTOFFSETPROFIT_val'] = self.df['LASTOFFSETPROFIT_val'].iloc[self.current_bar_idx - 1] if self.current_bar_idx > 0 else np.nan

        # --- Handle Closing Orders First ---
        closed_this_bar = False
        if self.position == 1 and sp_cond.iloc[self.current_bar_idx]:  # Sell to Close Long
            trade_price = self._get_current_price()  # Execution price
            pnl = (trade_price - self.entry_price) * self.position_size * self.contract_multiplier
            self.equity += pnl
            self._execute_trade("SP", trade_price, self.position_size)
            self.trade_log.append({'entry_idx': self.entry_bar_idx, 'exit_idx': self.current_bar_idx,
                                   'entry_price': self.entry_price, 'exit_price': trade_price,
                                   'lots': self.position_size, 'type': 'long', 'pnl': pnl})
            self.df.loc[self.df.index[self.current_bar_idx], 'LASTOFFSETPROFIT_val'] = pnl
            self.df.loc[self.df.index[self.current_bar_idx], 'OFFSETPROFIT_val'] += pnl
            self.position = 0
            self.position_size = 0
            self.entry_price = 0.0
            closed_this_bar = True

        elif self.position == -1 and bp_cond.iloc[self.current_bar_idx]:  # Buy to Close Short
            trade_price = self._get_current_price()
            pnl = (self.entry_price - trade_price) * self.position_size * self.contract_multiplier
            self.equity += pnl
            self._execute_trade("BP", trade_price, self.position_size)
            self.trade_log.append({'entry_idx': self.entry_bar_idx, 'exit_idx': self.current_bar_idx,
                                   'entry_price': self.entry_price, 'exit_price': trade_price,
                                   'lots': self.position_size, 'type': 'short', 'pnl': pnl})
            self.df.loc[self.df.index[self.current_bar_idx], 'LASTOFFSETPROFIT_val'] = pnl
            self.df.loc[self.df.index[self.current_bar_idx], 'OFFSETPROFIT_val'] += pnl
            self.position = 0
            self.position_size = 0
            self.entry_price = 0.0
            closed_this_bar = True

        # --- Handle Opening Orders (only if flat or closed this bar and no immediate re-entry lock) ---
        if self.position == 0:  # Can only open if flat
            if bk_cond.iloc[self.current_bar_idx]:
                self.position = 1
                self.entry_price = self._get_current_price()
                self.position_size = self.default_lots
                self.entry_bar_idx = self.current_bar_idx
                self._execute_trade("BK", self.entry_price, self.position_size)
                self._last_bk_idx = self.current_bar_idx  # Update for BARSBK
                # BKPRICE is essentially self.entry_price while position is long
                # BKVOL is self.position_size while position is long
            elif sk_cond.iloc[self.current_bar_idx]:
                self.position = -1
                self.entry_price = self._get_current_price()
                self.position_size = self.default_lots
                self.entry_bar_idx = self.current_bar_idx
                self._execute_trade("SK", self.entry_price, self.position_size)
                self._last_sk_idx = self.current_bar_idx  # Update for BARSSK

        # Update BARS* functions
        self._update_bars_since_signals()

    def run_backtest(self, bk_cond_series, sk_cond_series, sp_cond_series, bp_cond_series):
        # Fill initial values for state series if not already zero/NaN
        self.df['OFFSETPROFIT_val'] = self.df['OFFSETPROFIT_val'].fillna(0)
        self.df['LASTOFFSETPROFIT_val'] = self.df['LASTOFFSETPROFIT_val'].fillna(np.nan)
        # ... initialize other BARS* series columns here if used ...
        # e.g., self.df['BARSBK_val'] = np.nan

        for i in range(len(self.df)):
            self.current_bar_idx = i
            self.process_bar_signals(bk_cond_series, sk_cond_series, sp_cond_series, bp_cond_series)

        # Post-backtest calculations for TAVWIN, TMAXLOSS etc. from self.trade_log
        self._calculate_trade_stats()
        return self.df  # DataFrame now contains series for BKPRICE_val, PROFIT_val etc.

    def _calculate_trade_stats(self):
        if not self.trade_log:
            print("No trades made.")
            return

        trades_df = pd.DataFrame(self.trade_log)
        # Example for TMAXLOSS:
        # self.tmaxloss = trades_df[trades_df['pnl'] < 0]['pnl'].min() # Will be most negative
        # if pd.isna(self.tmaxloss): self.tmaxloss = 0 else: self.tmaxloss = abs(self.tmaxloss)
        print("Trade Log:")
        print(trades_df)
        print(f"Final Equity: {self.equity:.2f}")
        # ... Implement other T* stats ...


# To use this:
# 1. Prepare your df_ohlc.
# 2. Calculate your signal condition series (e.g., buy_signals = CROSS(MA(df.close,10), MA(df.close,20)))
# 3. Instantiate SimpleBacktester: bts = SimpleBacktester(df_ohlc, initial_equity=100000)
# 4. Run: result_df = bts.run_backtest(buy_open_signals, sell_open_signals, sell_close_signals, buy_close_signals)
# 5. Access results: result_df['BKPRICE_val'], result_df['PROFIT_val'] etc.
#    Or access trade stats from bts.trade_log or methods like bts.get_tmaxloss() (if implemented).

# """

# ==============================================================================
# Part 5: 平台特定、绘图、跨周期/合约及高级函数 (说明)
# ==============================================================================
print("--- Part 5: 平台特定、绘图、跨周期/合约及高级函数 (说明) ---")

# """
# 这一部分的函数特性使其难以或不适合直接转换为简单的Python/Pandas数据处理函数。
# 它们通常：
# 1.  控制交易平台的特定行为或设置 (AUTOFILTER, SIGCHECK, SETTINGS)。
# 2.  用于图表渲染和可视化 (DRAW*, COLOR*, FONTSIZE)。
# 3.  需要从其他合约或时间周期引用数据，这在Python中需要特定的数据管理 (#CALL, $, $$)。
# 4.  依赖外部数据源 (FINANCE_DATA, COST, WINNER) 或复杂的金融模型 (IMPLIEDVOLATILITY)。

# --- 1. 平台特定控制与环境函数 ---
# 例如: AUTOFILTER, AVAILABLE_OPI, BARINTERVAL, BARTYPE, CODELIKE, NAMELIKE,
#       ISCONTRACT, DAYTRADE, DAYTRADE1, FEE, MARGIN, GROUP-系列, HASTRADEDATA,
#       IDLE, INITMONEY, ISDELIVERYDAY, ISMAINCONTRACT, ISNEARHOLIDAY,
#       ISRECORDDAY, HISEXPDATE, EXPIREDATE, DAYSTOEXPIRED, KLINESTART,
#       ISTIMETOKLINEEND, MYVOL, PLAYSOUND, SOUND, SELECT (选股本身),
#       SETDEALPERCENT, SETEXPIREDATE, SETMOVEOPIPRICE, SETOTHERPRICE,
#       SETQUOTACCOUNT, SETSIGPRICETYPE, SETTRADEACCOUNT, SIGCHECK, SIGCHECK_MIN,
#       STOCKDIVD, TRADE_AGAIN, TRADE_SMOOTHING, TREND, UNITLIMIT.

# 说明:
#   - 这些函数多数是文华财经交易平台的指令或配置项，用于控制策略的执行方式、
#     交易行为过滤、资金手数设置、用户权限、声音提示等。
#   - 在Python中，这些功能通常由您选择的回测框架 (如 Backtrader, Zipline, PyAlgoTrade)
#     或您自定义的交易执行脚本来处理。
#   - 例如，`AUTOFILTER`的“一开一平”逻辑需要在您的策略信号处理部分显式编码。
#     `FEE`和`MARGIN`会是回测器或交易API的参数。`SIGCHECK`定义了信号何时以及
#     如何被评估和执行，这是回测引擎的核心调度逻辑。`STOCKDIVD`（复权）通常在
#     数据获取阶段就已处理，或使用能提供复权数据的库。

# --- 2. 绘图函数 ---
# 例如: ALIGN, VALIGN, FONTSIZE, PRECIS, PRECISION, COLORRed, RGB,
#       CIRCLEDOT, COLORSTICK, DRAWTEXT, DRAWKLINE, FILLRGN, POLYLINE, STICKLINE,
#       VERTLINE, VOLSTICK, 等所有以DRAW*开头的函数，以及相关的绘图属性函数。

# 说明:
#   - 这些函数用于在文华财经的图表界面上绘制各种图形元素。
#   - Python生态系统有强大的绘图库，如 Matplotlib, Seaborn, Plotly, Bokeh。
#   - 实现思路是：首先用Pandas计算出绘图所需的数据序列 (如均线值、指标值、
#     满足特定条件的K线位置、价格点等)，然后将这些数据传递给Python绘图库
#     的相应函数来创建可视化图表。
#   - `NODRAW` (只计算不画线) 和 `NOTEXT` (只画线不显示数值) 是显示层面的控制，
#     在Python绘图时可以通过绘图库的参数灵活控制。

# --- 3. 跨合约与跨周期引用 ---
# 例如: `$` (简化的跨合约: "CODE$PRICE"),
#       `$$` (简化的跨周期: "MIN$15$PRICE"),
#       `#CALL[CODE, FORMULA] AS VAR`,
#       `#CALL_OTHER[FORMULA] AS VAR`,
#       `#CALL_PLUS[CODE,PERIOD,N,FORMULA] AS VAR`,
#       `#IMPORT [PERIOD,N,FORMULA] AS VAR`.

# 说明:
#   - 这些函数用于在一个指标或策略中引用其他合约的数据，或同一合约不同周期的数据。
#   - Python/Pandas 实现方式:
#     1. 数据管理: 你需要一个机制来加载和存储所有相关合约和周期的数据。
#        通常是将不同合约/周期的数据分别存储在不同的Pandas DataFrame中，
#        可能用一个字典来管理这些DataFrames: `dataframes['合约A_1min']`, `dataframes['合约B_daily']`。
#     2. 数据对齐: 关键是时间戳的正确对齐。当从一个序列引用到另一个序列时，
#        需要确保引用的是正确时间点的数据。Pandas的 `reindex`, `merge`, `asof`
#        等方法可用于数据对齐。对于不同周期，通常使用 `ffill()` (前向填充)
#        来获取较低频周期数据在较高频时间点上的值。
#     3. 函数封装: 可以编写Python函数，接收目标合约/周期的标识符和数据字段名作为参数，
#        内部实现数据查找和对齐逻辑。
#     4. `#CALL` 和 `#IMPORT` 如果涉及到引用其他已定义的指标 (FORMULA)，则意味着
#        你需要将那些FORMULA也实现为Python函数，然后在主调函数中调用它们，
#        并将来自其他合约/周期的数据作为参数传入。

# 示例概念 (跨周期引用日线收盘价到分钟线):
#   def get_daily_close_on_minute_bars(df_minute, df_daily):
#       # Ensure daily_close is indexed by date part only if needed for merge_asof
#       daily_close_series = df_daily['close'].rename('daily_close')
#       # Align daily close to minute bar timestamps (forward fill)
#       # This requires df_minute.index and daily_close_series.index to be DatetimeIndex
#       aligned_daily_close = pd.merge_asof(df_minute, daily_close_series,
#                                           left_index=True, right_index=True,
#                                           direction='backward') # or 'forward'
#       return aligned_daily_close['daily_close']
#   # df_minute['daily_C'] = get_daily_close_on_minute_bars(df_minute, df_daily)

# --- 4. 高级/外部数据/特定模型函数 ---
# 例如: COST(X) (成本分布), DIVIDEND, SPLIT (除权除息), DUALVOLUME (多空量),
#       FINANCE_DATA('FIELD') (财务数据), IMPLIEDVOLATILITY, K_STATE* (K线形态),
#       LOOP1, LOOP2 (复杂循环/条件), NORMPDF (正态分布密度), PANZHENG (判断盘整),
#       PCRATE, PCRATETREND (趋势和变化速度), PERCENTILE, RAWDATA (复权前数据),
#       SAR, SAR1 (抛物线转向), SEEK (标签统计), SORT, SORTPOS (排序),
#       T0TOTIME, TIME0, TIMETOT0 (时间秒数转换), TSMA (时间序列三角均线),
#       VOLATILITY (期权历史波动率), VOLTICK, VOLTIME (量能周期相关), WINNER (获利盘比例).

# 说明:
#   - `COST`, `WINNER`: 需要详细的成本分布数据或算法，通常由平台提供或复杂估算。
#   - `DIVIDEND`, `SPLIT`, `FINANCE_DATA`: 需要接入外部的金融数据库 (如 Tushare, Wind, IQuant 等Python库可提供接口)。
#   - `DUALVOLUME`: 可能需要基于逐笔成交数据计算主动性买卖量。
#   - `IMPLIEDVOLATILITY`: 需要期权市场数据和期权定价模型 (如Black-Scholes) 来反解。Python库如 `py_vollib` 可用。
#   - `K_STATE*`: K线形态识别。这些是预定义的形态逻辑，可以逐个用Python的条件判断语句组合来实现。
#   - `LOOP1`, `LOOP2`: 文华特有的循环和条件函数，需要仔细分析其具体逻辑，
#     可能用Python的循环或Pandas的 `rolling().apply()` 结合自定义复杂函数来实现。
#   - `NORMPDF`: 可用 `scipy.stats.norm.pdf` 实现。
#   - `PANZHENG`: 盘整判断通常是一个综合指标，需要复现其内部的具体算法。
#   - `PCRATE`, `PCRATETREND`: 基于多项式回归。`numpy.polyfit` 可用于拟合，然后计算导数。
#   - `PERCENTILE`: `pd.Series.rolling(N).quantile(Per/100.0)` 可实现滚动百分位数。
#   - `SAR`, `SAR1`: 标准的抛物线转向指标，有明确的迭代计算公式，可在Python中逐步实现。
#   - `SORT`, `SORTPOS`: Python列表的 `sort()` 或 `sorted()`，以及Numpy的排序函数可以实现。
#   - 时间转换函数 (`T0TOTIME`等): Python的 `datetime` 模块和 `timedelta` 对象可用于处理。
#   - `TSMA`: 一种特定均线，需要找到或复现其具体算法。
#   - `VOLATILITY`: 历史波动率，通常指标的资产收益率的年化标准差，可以计算。
#   - `VOLTICK`, `VOLTIME`: 量能K线相关，需要tick数据和量能K线合成逻辑。

# 总结:
# 对于Part 5中的函数，实现它们通常意味着：
#   - 平台功能: 在您选择的Python交易框架中用自定义逻辑复现。
#   - 绘图: 使用Python的绘图库 (Matplotlib, Plotly等)。
#   - 跨数据引用: 建立数据管理和对齐机制 (如使用多个DataFrame并合理索引)。
#   - 外部数据/复杂模型: 接入相应的数据API或实现特定的金融模型算法。

# """

print("--- 整合内容结束 ---")
print("您可以将以上所有内容复制到一个 .py 文件中。")
print("对于Part 4和Part 5中的说明部分，它们是以Python多行注释的形式给出的，")
print("您可以保持这种形式，或者将其提取到单独的Markdown文档中以获得更好的可读性。")