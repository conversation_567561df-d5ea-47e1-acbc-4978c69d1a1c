from datetime import datetime
import time
# 判断是否为交易时间, 交易时间True，非交易时间返回False
import time
from datetime import datetime
import pandas as pd
# from utils import tradingTime
from tqsdk import TqApi, TqKq

productid='OI'
api=TqApi(TqKq(), auth='walkquant,ftp123')
symbol = api.query_cont_quotes(product_id=productid)[0]
symbol_info = api.query_symbol_info(symbol)

api.close()

tradingTime = symbol_info.trading_time_day.tolist()[0]
tradingTimeNight = symbol_info.trading_time_night.tolist()

if tradingTimeNight[0] is not None:
    tradingTimeNight=tradingTimeNight[0][0]
    tradingTime.append(tradingTimeNight)

pd.to_pickle(tradingTime, 'tradingtime.pkl')

def tradingtime(timeintervals:list=tradingTime):
    timenow = time.time()
    for interval in range(len(timeintervals)):
        starttime=datetime.now().strftime("%Y-%m-%d") + ' ' + tradingTime[interval][0]
        endtimet= datetime.now().strftime("%Y-%m-%d") + ' ' + tradingTime[interval][1]
        sttime=time.mktime(time.strptime(starttime, '%Y-%m-%d %H:%M:%S'))
        endtime=time.mktime(time.strptime(endtimet, '%Y-%m-%d %H:%M:%S'))
        if sttime<timenow  and timenow <endtime:
            return True

    return False


def tradingTime():
    workTime1 = ['09:30:00', '11:30:00']
    workTime2 = ['13:00:00', '15:00:00']
    dayOfWeek = datetime.now().weekday()
    # dayOfWeek = datetime.today().weekday()
    beginWork1 = datetime.now().strftime("%Y-%m-%d") + ' ' + workTime1[0]
    endWork1 = datetime.now().strftime("%Y-%m-%d") + ' ' + workTime1[1]

    beginWork2 = datetime.now().strftime("%Y-%m-%d") + ' ' + workTime2[0]
    endWork2 = datetime.now().strftime("%Y-%m-%d") + ' ' + workTime2[1]

    beginWorkSeconds1 = time.time() - time.mktime(time.strptime(beginWork1, '%Y-%m-%d %H:%M:%S'))
    endWorkSeconds1 = time.time() - time.mktime(time.strptime(endWork1, '%Y-%m-%d %H:%M:%S'))

    beginWorkSeconds2 = time.time() - time.mktime(time.strptime(beginWork2, '%Y-%m-%d %H:%M:%S'))
    endWorkSeconds2 = time.time() - time.mktime(time.strptime(endWork2, '%Y-%m-%d %H:%M:%S'))

    if (int(dayOfWeek) in range(5)):
        if (int(beginWorkSeconds1) > 0 and int(endWorkSeconds1) < 0) or (
                int(beginWorkSeconds2) > 0 and int(endWorkSeconds2) < 0):
            return True
        else:
            return False
    else:
        return False