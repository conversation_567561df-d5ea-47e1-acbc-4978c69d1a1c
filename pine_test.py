import pandas as pd
import numpy as np
from talib import EMA, MAX, MIN

def smoothrng(x, t, m):
    wper = t * 2 - 1
    avrng = EMA(np.abs(x - np.roll(x, 1)), t)
    smoothrng = EMA(avrng, wper) * m
    return smoothrng

def rngfilt(x, r):
    rngfilt = x
    rngfilt = np.where(x > np.roll(rngfilt, 1), np.where(x - r < np.roll(rngfilt, 1), np.roll(rngfilt, 1), x - r),
                       np.where(x + r > np.roll(rngfilt, 1), np.roll(rngfilt, 1), x + r))
    return rngfilt

def calculate_indicators(data):
    per1 = 27
    mult1 = 1.6
    per2 = 55
    mult2 = 2

    smrng1 = smoothrng(data['close'], per1, mult1)
    smrng2 = smoothrng(data['close'], per2, mult2)
    smrng = (smrng1 + smrng2) / 2

    filt = rngfilt(data['close'], smrng)

    upward = np.zeros(len(data))
    upward[1:] = np.where(filt[1:] > np.roll(filt, 1)[1:], np.where(np.roll(upward, 1)[1:] > 0, np.roll(upward, 1)[1:] + 1, 0), 0)

    downward = np.zeros(len(data))
    downward[1:] = np.where(filt[1:] < np.roll(filt, 1)[1:], np.where(np.roll(downward, 1)[1:] > 0, np.roll(downward, 1)[1:] + 1, 0), 0)

    hband = filt + smrng
    lband = filt - smrng

    longCond = (data['close'] > filt) & (data['close'] > np.roll(data['close'], 1)) & (upward > 0) | \
               (data['close'] > filt) & (data['close'] < np.roll(data['close'], 1)) & (upward > 0)
    shortCond = (data['close'] < filt) & (data['close'] < np.roll(data['close'], 1)) & (downward > 0) | \
                (data['close'] < filt) & (data['close'] > np.roll(data['close'], 1)) & (downward > 0)

    CondIni = np.zeros(len(data))
    CondIni[1:] = np.where(longCond[1:], 1, np.where(shortCond[1:], -1, np.roll(CondIni, 1)[1:]))

    long = longCond & (np.roll(CondIni, 1) == -1)
    short = shortCond & (np.roll(CondIni, 1) == 1)

    return pd.DataFrame({'Long': long, 'Short': short})

# Load your data into a Pandas DataFrame
data = pd.read_csv('data.csv')

# Calculate the indicators
indicators = calculate_indicators(data)

# Plot the indicators
import matplotlib.pyplot as plt

plt.figure(figsize=(12, 6))
plt.plot(data['close'], label='close')
plt.plot(indicators['Long'], label='Long', marker='^', markersize=10, color='g')
plt.plot(indicators['Short'], label='Short', marker='v', markersize=10, color='r')
plt.legend(loc='upper left')
plt.show()