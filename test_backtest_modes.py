"""
测试不同回测模式的差异
对比标准回测和盈利优先回测
"""

import pandas as pd
import numpy as np
from loguru import logger
from llt_multi_contract_analyzer import ContractAnalyzer, ContractConfig, LLTIndicator

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    n_points = 500
    
    # 创建有趋势的价格数据
    base_price = 100
    trend = np.linspace(0, 20, n_points)  # 上升趋势
    noise = np.random.randn(n_points) * 2  # 噪声
    prices = base_price + trend + noise
    
    # 确保价格为正
    prices = np.maximum(prices, 50)
    
    dates = pd.date_range('2024-01-01', periods=n_points, freq='5min')
    
    klines_data = pd.DataFrame({
        'datetime': dates,
        'open': prices + np.random.randn(n_points) * 0.1,
        'high': prices + np.abs(np.random.randn(n_points)) * 0.5,
        'low': prices - np.abs(np.random.randn(n_points)) * 0.5,
        'close': prices,
        'volume': np.random.randint(100, 1000, n_points)
    })
    
    return klines_data


def test_backtest_comparison():
    """对比两种回测模式"""
    print("=" * 60)
    print("对比标准回测 vs 盈利优先回测")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_test_data()
        print(f"测试数据: {len(klines_data)}条K线")
        print(f"价格范围: {klines_data.close.min():.2f} - {klines_data.close.max():.2f}")
        
        # 创建合约配置
        contract = ContractConfig("TEST.symbol", "测试合约", "TEST")
        
        # 测试参数
        test_d_values = [14, 30, 50]
        
        print(f"\n📊 对比结果:")
        print("-" * 80)
        print(f"{'D_VALUE':<8} {'模式':<12} {'收益率':<8} {'胜率':<8} {'交易次数':<8} {'累计盈亏':<10}")
        print("-" * 80)
        
        for d_value in test_d_values:
            alpha = 2 / (d_value + 1)
            
            # 标准回测
            analyzer_std = ContractAnalyzer(contract, klines_data, "standard")
            result_std = analyzer_std._run_backtest(alpha)
            
            # 盈利优先回测
            analyzer_profit = ContractAnalyzer(contract, klines_data, "profit_only")
            result_profit = analyzer_profit._run_backtest(alpha)
            
            # 显示结果
            print(f"{d_value:<8} {'标准':<12} {result_std['return_rate']:>6.2f}% {result_std['win_rate']:>6.1f}% {result_std['total_trades']:>8d} {result_std['total_pnl']:>8.2f}")
            print(f"{d_value:<8} {'盈利优先':<12} {result_profit['return_rate']:>6.2f}% {result_profit['win_rate']:>6.1f}% {result_profit['total_trades']:>8d} {result_profit['total_pnl']:>8.2f}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_detailed_comparison():
    """详细对比分析"""
    print("=" * 60)
    print("详细对比分析")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='DEBUG', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_test_data()
        contract = ContractConfig("TEST.symbol", "测试合约", "TEST")
        
        # 使用D_VALUE=14（对应llt_strategy_3.py的最优参数）
        d_value = 14
        alpha = 2 / (d_value + 1)
        
        print(f"\n🔍 详细分析 D_VALUE={d_value}, ALPHA={alpha:.6f}")
        
        # 标准回测
        print(f"\n--- 标准回测 ---")
        analyzer_std = ContractAnalyzer(contract, klines_data, "standard")
        result_std = analyzer_std._run_backtest(alpha)
        
        print(f"收益率: {result_std['return_rate']:.2f}%")
        print(f"累计盈亏: {result_std['total_pnl']:.2f}点")
        print(f"交易次数: {result_std['total_trades']}")
        print(f"胜率: {result_std['win_rate']:.1f}%")
        print(f"盈利交易: {result_std['winning_trades']}")
        print(f"亏损交易: {result_std['losing_trades']}")
        print(f"平均盈利: {result_std['avg_win']:.2f}")
        print(f"平均亏损: {result_std['avg_loss']:.2f}")
        
        # 盈利优先回测
        print(f"\n--- 盈利优先回测 ---")
        analyzer_profit = ContractAnalyzer(contract, klines_data, "profit_only")
        result_profit = analyzer_profit._run_backtest(alpha)
        
        print(f"收益率: {result_profit['return_rate']:.2f}%")
        print(f"累计盈亏: {result_profit['total_pnl']:.2f}点")
        print(f"交易次数: {result_profit['total_trades']}")
        print(f"胜率: {result_profit['win_rate']:.1f}%")
        print(f"盈利交易: {result_profit['winning_trades']}")
        print(f"亏损交易: {result_profit['losing_trades']}")
        print(f"平均盈利: {result_profit['avg_win']:.2f}")
        print(f"平均亏损: {result_profit['avg_loss']:.2f}")
        
        # 对比分析
        print(f"\n📊 对比分析:")
        print(f"收益率提升: {result_profit['return_rate'] - result_std['return_rate']:+.2f}%")
        print(f"胜率提升: {result_profit['win_rate'] - result_std['win_rate']:+.1f}%")
        print(f"交易次数变化: {result_profit['total_trades'] - result_std['total_trades']:+d}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_llt_calculation():
    """测试LLT计算一致性"""
    print("=" * 60)
    print("测试LLT计算一致性")
    print("=" * 60)
    
    try:
        # 创建简单测试数据
        prices = pd.Series([100, 101, 102, 101, 100, 99, 100, 101, 102, 103])
        alpha = 2 / (14 + 1)  # D_VALUE=14
        
        print(f"测试价格: {list(prices)}")
        print(f"ALPHA: {alpha:.6f}")
        
        # 计算LLT
        llt_values = LLTIndicator.calculate(prices, alpha)
        signals = LLTIndicator.generate_signals(llt_values)
        
        print(f"\nLLT值: {[f'{v:.3f}' for v in llt_values]}")
        print(f"信号: {signals}")
        
        # 统计信号
        buy_signals = signals.count(1)
        sell_signals = signals.count(-1)
        no_signals = signals.count(0)
        
        print(f"\n信号统计:")
        print(f"买入信号: {buy_signals}")
        print(f"卖出信号: {sell_signals}")
        print(f"无信号: {no_signals}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("回测模式对比测试")
    print("=" * 80)
    
    tests = [
        ("LLT计算一致性", test_llt_calculation),
        ("回测模式对比", test_backtest_comparison),
        ("详细对比分析", test_detailed_comparison),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 回测模式对比测试成功！")
        print("\n📋 使用方法:")
        print("# 标准回测（真实交易逻辑）")
        print("python llt_multi_contract_analyzer.py --mode quick")
        print("")
        print("# 盈利优先回测（模拟llt_strategy_3.py）")
        print("python llt_multi_contract_analyzer.py --mode profit-only")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
