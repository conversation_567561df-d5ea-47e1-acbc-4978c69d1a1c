#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金曲线分析GUI程序
独立的图形界面应用程序，用于分析模拟盘资金数据
"""

import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib import rcParams
import os

class FundCurveGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("模拟盘资金曲线分析器")
        self.root.geometry("1200x800")
        
        # 设置中文字体
        rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        rcParams['axes.unicode_minus'] = False
        
        self.data = None
        self.setup_ui()
        
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="数据文件", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="JSON文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, state="readonly")
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(file_frame, text="选择文件", command=self.select_file).grid(row=0, column=2)
        
        # 控制按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        ttk.Button(button_frame, text="分析数据", command=self.analyze_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存图表", command=self.save_chart).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清除图表", command=self.clear_chart).pack(side=tk.LEFT, padx=(0, 5))
        
        # 图表区域
        chart_frame = ttk.LabelFrame(main_frame, text="资金曲线图表", padding="5")
        chart_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        chart_frame.columnconfigure(0, weight=1)
        chart_frame.rowconfigure(0, weight=1)
        
        # 创建matplotlib图形
        self.fig, self.axs = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
        self.fig.suptitle('模拟盘资金曲线分析', fontsize=16)
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, master=chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加工具栏
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("请选择JSON数据文件")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 尝试加载默认文件
        default_file = "simulatedaysummary_new.json"
        if os.path.exists(default_file):
            self.file_path_var.set(default_file)
            self.load_data()
    
    def select_file(self):
        """选择JSON文件"""
        file_path = filedialog.askopenfilename(
            title="选择JSON数据文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
            self.load_data()
    
    def load_data(self):
        """加载JSON数据"""
        file_path = self.file_path_var.get()
        if not file_path:
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                self.data = json.load(file)
            self.status_var.set(f"已加载数据文件: {os.path.basename(file_path)}")
        except Exception as e:
            messagebox.showerror("错误", f"加载文件失败: {str(e)}")
            self.status_var.set("文件加载失败")
    
    def analyze_data(self):
        """分析数据并绘制图表"""
        if not self.data:
            messagebox.showwarning("警告", "请先选择并加载数据文件")
            return
        
        try:
            # 清除之前的图表
            self.axs[0].clear()
            self.axs[1].clear()
            
            # 计算每个交易日的资金合计
            dates = []
            total_balances = []
            
            for date, users in self.data.items():
                total_balance = sum(user["balance"] for user in users)
                dates.append(date)
                total_balances.append(total_balance)
            
            if not dates:
                messagebox.showwarning("警告", "数据文件中没有有效数据")
                return
            
            # 计算百分比变化
            base_value = total_balances[0]
            percentage_changes = [(balance / base_value - 1) * 100 for balance in total_balances]
            
            # 绘制上方资金曲线
            self.axs[0].plot(dates, total_balances, marker='o', linewidth=2, markersize=4, label='资金曲线')
            self.axs[0].set_ylabel('资金合计')
            self.axs[0].set_title('所有账号资金曲线')
            self.axs[0].legend()
            self.axs[0].grid(True, alpha=0.3)
            
            # 绘制下方百分比变化曲线
            self.axs[1].plot(dates, percentage_changes, marker='o', color='orange', 
                           linewidth=2, markersize=4, label='百分比变化')
            self.axs[1].set_xlabel('交易日')
            self.axs[1].set_ylabel('百分比变化 (%)')
            self.axs[1].set_title('资金百分比变化曲线')
            self.axs[1].legend()
            self.axs[1].grid(True, alpha=0.3)
            
            # 设置x轴标签旋转
            plt.setp(self.axs[1].xaxis.get_majorticklabels(), rotation=45)
            
            # 调整布局
            self.fig.tight_layout()
            
            # 更新画布
            self.canvas.draw()
            
            # 更新状态
            total_return = percentage_changes[-1] if percentage_changes else 0
            self.status_var.set(f"分析完成 - 总收益率: {total_return:.2f}% | 数据点: {len(dates)}个")
            
        except Exception as e:
            messagebox.showerror("错误", f"数据分析失败: {str(e)}")
            self.status_var.set("数据分析失败")
    
    def save_chart(self):
        """保存图表"""
        if not self.data:
            messagebox.showwarning("警告", "没有可保存的图表")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"), ("SVG files", "*.svg")]
        )
        
        if file_path:
            try:
                self.fig.savefig(file_path, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到: {file_path}")
                self.status_var.set(f"图表已保存: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def clear_chart(self):
        """清除图表"""
        self.axs[0].clear()
        self.axs[1].clear()
        self.canvas.draw()
        self.status_var.set("图表已清除")

def main():
    """主函数"""
    root = tk.Tk()
    app = FundCurveGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()