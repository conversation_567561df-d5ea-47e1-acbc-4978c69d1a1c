"""
LLT策略配置文件
"""

from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class TradingConfig:
    """交易配置类"""
    # 基本交易参数
    symbol: str = "CZCE.OI601"
    kline_period_seconds: int = 300  # 5分钟
    d_value: int = 60
    volume: int = 1
    kline_data_length: int = 8964
    max_position: int = 100
    
    # 认证信息
    auth_username: str = "bigwolf"
    auth_password: str = "ftp123"
    
    # 风险管理参数
    max_daily_loss: float = 1000.0
    max_drawdown: float = 0.1  # 10%
    
    # 优化参数
    optimize_d_value_range: tuple = (10, 101)
    optimization_enabled: bool = True
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "llt_strategy.log"
    trade_log_file: str = "llt_trades.json"
    
    @property
    def alpha(self) -> float:
        """计算alpha值"""
        return 2 / (self.d_value + 1)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'symbol': self.symbol,
            'kline_period_seconds': self.kline_period_seconds,
            'd_value': self.d_value,
            'alpha': self.alpha,
            'volume': self.volume,
            'kline_data_length': self.kline_data_length,
            'max_position': self.max_position,
            'max_daily_loss': self.max_daily_loss,
            'max_drawdown': self.max_drawdown,
            'optimization_enabled': self.optimization_enabled
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TradingConfig':
        """从字典创建配置"""
        return cls(**config_dict)


# 预定义配置
CONFIGS = {
    'default': TradingConfig(),
    
    'aggressive': TradingConfig(
        d_value=30,
        volume=2,
        max_position=200,
        max_daily_loss=2000.0
    ),
    
    'conservative': TradingConfig(
        d_value=90,
        volume=1,
        max_position=50,
        max_daily_loss=500.0,
        max_drawdown=0.05
    ),
    
    'test': TradingConfig(
        symbol="CZCE.OI601",
        kline_period_seconds=60,  # 1分钟
        d_value=20,
        volume=1,
        kline_data_length=1000,
        max_position=10,
        optimization_enabled=False
    )
}


def get_config(config_name: str = 'default') -> TradingConfig:
    """获取配置"""
    if config_name not in CONFIGS:
        raise ValueError(f"未知配置: {config_name}. 可用配置: {list(CONFIGS.keys())}")
    return CONFIGS[config_name]


def save_config(config: TradingConfig, filename: str = "llt_config.json"):
    """保存配置到文件"""
    import json
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)


def load_config(filename: str = "llt_config.json") -> TradingConfig:
    """从文件加载配置"""
    import json
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        return TradingConfig.from_dict(config_dict)
    except FileNotFoundError:
        print(f"配置文件 {filename} 不存在，使用默认配置")
        return get_config('default')


if __name__ == "__main__":
    # 示例：保存不同配置
    for name, config in CONFIGS.items():
        save_config(config, f"llt_config_{name}.json")
        print(f"已保存配置: {name}")
    
    # 示例：加载和显示配置
    config = load_config("llt_config_default.json")
    print(f"加载的配置: {config}")
