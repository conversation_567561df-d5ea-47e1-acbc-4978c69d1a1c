# 项目依赖管理指南

## 概述

本项目是一个基于Python的量化交易系统，使用了多个第三方库来实现交易、数据处理、通信等功能。

## 依赖文件说明

### 📁 依赖文件

1. **`requirements.txt`** - 生产环境依赖
   - 包含运行项目所需的所有核心包
   - 适用于生产部署

2. **`requirements-dev.txt`** - 开发环境依赖
   - 包含所有生产依赖 + 开发工具
   - 适用于开发和测试

3. **`check_dependencies.py`** - 依赖检查脚本
   - 检查当前环境中包的安装状态
   - 显示版本信息和缺失的包

4. **`install_dependencies.py`** - 自动安装脚本
   - 自动安装所需依赖
   - 支持虚拟环境创建

## 核心依赖包

### 🔧 交易和数据处理
- **tqsdk** (>=2.0.0) - 天勤量化交易接口
- **pandas** (>=1.3.0) - 数据分析和处理
- **numpy** (>=1.20.0) - 数值计算

### 📝 日志和配置
- **loguru** (>=0.5.0) - 高级日志记录
- **addict** (>=2.4.0) - 字典增强功能

### 🔊 语音和通信
- **pyttsx3** (>=2.90) - 文本转语音
- **pyzmq** (>=22.0.0) - ZeroMQ消息队列
- **websockets** (>=10.0) - WebSocket通信
- **cloudpickle** (>=1.3.0) - 对象序列化

### ⏰ 任务调度
- **python-crontab** (>=2.5.0) - Cron任务管理

### 🎨 可选依赖
- **PyQt5** (>=5.15.0) - 图形界面
- **matplotlib** (>=3.3.0) - 数据可视化

## 安装方法

### 方法1: 自动安装（推荐）

```bash
# 运行自动安装脚本
python install_dependencies.py
```

### 方法2: 手动安装

```bash
# 安装生产环境依赖
pip install -r requirements.txt

# 安装开发环境依赖（可选）
pip install -r requirements-dev.txt
```

### 方法3: 逐个安装

```bash
# 核心依赖
pip install tqsdk pandas numpy loguru addict
pip install pyttsx3 pyzmq websockets cloudpickle
pip install python-crontab

# 可选依赖
pip install PyQt5 matplotlib
```

## 环境检查

### 检查依赖状态

```bash
# 运行依赖检查脚本
python check_dependencies.py
```

### 检查结果说明

- ✅ **绿色勾号**: 包已正确安装
- ❌ **红色叉号**: 关键包缺失，需要安装
- ⚠️ **黄色警告**: 可选包缺失，可根据需要安装

## 虚拟环境（推荐）

### 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 虚拟环境优势

1. **隔离性**: 避免包版本冲突
2. **可重现性**: 确保环境一致性
3. **安全性**: 不影响系统Python环境

## 常见问题

### Q1: 安装失败怎么办？

```bash
# 升级pip
pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### Q2: 版本冲突怎么办？

```bash
# 查看已安装包
pip list

# 卸载冲突包
pip uninstall package_name

# 重新安装
pip install package_name==version
```

### Q3: Windows下安装问题

某些包（如PyQt5）在Windows下可能需要额外的系统依赖：

```bash
# 安装Visual C++ Build Tools
# 或下载预编译的wheel文件
pip install --only-binary=all package_name
```

### Q4: 语音功能不工作

```bash
# Windows需要额外的语音引擎
pip install pywin32

# Linux需要espeak
sudo apt-get install espeak espeak-data libespeak-dev
```

## 开发环境设置

### 代码质量工具

```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 代码格式化
black .

# 代码检查
flake8 .

# 类型检查
mypy .
```

### 测试环境

```bash
# 运行测试
pytest

# 测试覆盖率
pytest --cov=.
```

## 部署建议

### 生产环境

1. 使用虚拟环境
2. 固定包版本
3. 定期更新安全补丁
4. 监控依赖漏洞

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main.py"]
```

## 版本更新

### 更新依赖

```bash
# 查看过时的包
pip list --outdated

# 更新特定包
pip install --upgrade package_name

# 更新所有包（谨慎使用）
pip install --upgrade -r requirements.txt
```

### 版本锁定

```bash
# 生成精确版本的requirements
pip freeze > requirements-lock.txt
```

## 支持和帮助

如果遇到依赖安装问题：

1. 运行 `python check_dependencies.py` 检查状态
2. 查看错误日志
3. 检查网络连接
4. 尝试使用不同的镜像源
5. 查阅相关包的官方文档

## 许可证

请确保遵守所有依赖包的许可证要求。
