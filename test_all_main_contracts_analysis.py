"""
测试所有主力合约分析功能
"""

from llt_multi_contract_analyzer import create_all_main_contracts_config, MultiContractAnalyzer
from loguru import logger

def test_all_main_contracts_config():
    """测试所有主力合约配置创建"""
    print("=" * 60)
    print("测试所有主力合约配置创建")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        print("\n1. 创建所有主力合约配置:")
        
        # 创建配置
        config = create_all_main_contracts_config(
            d_value_range=(25, 31),  # 小范围测试
            kline_data_length=100,   # 少量数据
            output_dir="test_all_main_contracts"
        )
        
        print(f"\n✅ 配置创建成功:")
        print(f"  合约数量: {len(config.contracts)}")
        print(f"  参数范围: {config.d_value_range}")
        print(f"  数据长度: {config.kline_data_length}")
        print(f"  输出目录: {config.output_dir}")
        
        print(f"\n📊 包含的合约（前20个）:")
        for i, contract in enumerate(config.contracts[:20]):
            print(f"  {i+1:2d}. {contract.name:15}: {contract.symbol}")
        
        if len(config.contracts) > 20:
            print(f"  ... 还有 {len(config.contracts) - 20} 个合约")
        
        # 按交易所统计
        exchanges = {}
        for contract in config.contracts:
            ex = contract.exchange
            exchanges[ex] = exchanges.get(ex, 0) + 1
        
        print(f"\n📈 按交易所分布:")
        for ex, count in exchanges.items():
            print(f"  {ex}: {count}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_mini_all_main_analysis():
    """测试迷你版所有主力合约分析"""
    print("=" * 60)
    print("测试迷你版所有主力合约分析")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        print("\n2. 运行迷你版所有主力合约分析:")
        
        # 创建小规模配置用于测试
        config = create_all_main_contracts_config(
            d_value_range=(30, 36),  # 只测试6个参数
            kline_data_length=200,   # 少量数据
            output_dir="mini_all_main_test"
        )
        
        # 只取前5个合约进行测试
        config.contracts = config.contracts[:5]
        
        print(f"\n🔄 开始分析 {len(config.contracts)} 个合约:")
        for contract in config.contracts:
            print(f"  - {contract.name}: {contract.symbol}")
        
        # 注意：这里不能真正运行完整分析，因为需要真实的API连接和数据
        # 但我们可以验证配置是否正确
        
        analyzer = MultiContractAnalyzer(config)
        print(f"\n✅ 分析器创建成功")
        print(f"  配置合约数: {len(analyzer.config.contracts)}")
        print(f"  输出目录: {analyzer.output_path}")
        
        # 验证输出目录创建
        if analyzer.output_path.exists():
            print(f"  输出目录已创建: {analyzer.output_path}")
        
        print(f"\n💡 注意：完整分析需要TqSDK API连接和真实数据")
        print(f"💡 可以运行: python llt_multi_contract_analyzer.py --mode all-main")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_contract_comparison():
    """测试合约数量对比"""
    print("=" * 60)
    print("测试合约数量对比")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        print("\n3. 对比不同配置的合约数量:")
        
        # 预定义合约配置
        from llt_multi_contract_analyzer import create_analysis_config, PREDEFINED_CONTRACTS
        
        predefined_config = create_analysis_config(
            contract_groups=list(PREDEFINED_CONTRACTS.keys())
        )
        
        # 所有主力合约配置
        all_main_config = create_all_main_contracts_config()
        
        print(f"\n📊 合约数量对比:")
        print(f"  预定义合约: {len(predefined_config.contracts)} 个")
        print(f"  所有主力合约: {len(all_main_config.contracts)} 个")
        print(f"  增加合约: {len(all_main_config.contracts) - len(predefined_config.contracts)} 个")
        
        # 找出新增的合约
        predefined_symbols = {c.symbol for c in predefined_config.contracts}
        all_main_symbols = {c.symbol for c in all_main_config.contracts}
        
        new_symbols = all_main_symbols - predefined_symbols
        common_symbols = all_main_symbols & predefined_symbols
        
        print(f"\n📈 合约分析:")
        print(f"  共同合约: {len(common_symbols)} 个")
        print(f"  新增合约: {len(new_symbols)} 个")
        
        if new_symbols:
            print(f"\n🆕 新增的主力合约（前10个）:")
            for i, symbol in enumerate(list(new_symbols)[:10]):
                print(f"  {i+1:2d}. {symbol}")
            
            if len(new_symbols) > 10:
                print(f"  ... 还有 {len(new_symbols) - 10} 个")
        
        # 按交易所分析新增合约
        if new_symbols:
            new_exchanges = {}
            for symbol in new_symbols:
                ex = symbol.split('.')[0]
                new_exchanges[ex] = new_exchanges.get(ex, 0) + 1
            
            print(f"\n📊 新增合约按交易所分布:")
            for ex, count in new_exchanges.items():
                print(f"  {ex}: {count}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("所有主力合约分析功能测试")
    print("=" * 80)
    
    tests = [
        ("所有主力合约配置创建", test_all_main_contracts_config),
        ("迷你版分析测试", test_mini_all_main_analysis),
        ("合约数量对比", test_contract_comparison),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:25}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有主力合约分析功能测试成功！")
        print("\n📋 使用方法:")
        print("# 分析所有主力合约")
        print("python llt_multi_contract_analyzer.py --mode all-main")
        print("")
        print("# 快速测试（少量参数）")
        print("python llt_multi_contract_analyzer.py --mode all-main --d-range 30 36")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
