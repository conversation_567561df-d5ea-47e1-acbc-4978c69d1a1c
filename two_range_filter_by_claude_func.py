from tqsdk import Tq<PERSON><PERSON>, TqAuth, TargetPosTask
from tqsdk.ta import EMA

# 初始化TqApi
api = TqApi(auth=TqAuth("YOUR_USERNAME", "YOUR_PASSWORD"))

# 设置交易的合约
symbol = "SHFE.au2106"  # 这里以上海期货交易所的黄金期货为例,请根据实际情况修改

# 获取K线数据
klines = api.get_kline_serial(symbol, 60)  # 获取1分钟K线数据

# 设置参数
per1 = 27
mult1 = 1.6
per2 = 55
mult2 = 2


def smoothrng(x, t, m):
    wper = t * 2 - 1
    avrng = EMA(abs(x - x.shift(1)), t)
    return EMA(avrng, wper) * m


def rngfilt(x, r):
    filt = x.copy()
    for i in range(1, len(x)):
        if x[i] > filt[i - 1]:
            filt[i] = max(x[i] - r[i], filt[i - 1])
        else:
            filt[i] = min(x[i] + r[i], filt[i - 1])
    return filt


# 创建TargetPosTask来管理仓位
target_pos = TargetPosTask(api, symbol)

# 主循环
try:
    position = 0  # 用于跟踪当前仓位

    while True:
        api.wait_update()

        if api.is_changing(klines.iloc[-1], "datetime"):
            close = klines.close.iloc[-200:]  # 获取最近200根K线的收盘价

            smrng1 = smoothrng(close, per1, mult1)
            smrng2 = smoothrng(close, per2, mult2)
            smrng = (smrng1 + smrng2) / 2

            filt = rngfilt(close, smrng)

            upward = (filt > filt.shift(1)).astype(int).cumsum()
            upward[filt <= filt.shift(1)] = 0

            downward = (filt < filt.shift(1)).astype(int).cumsum()
            downward[filt >= filt.shift(1)] = 0

            hband = filt + smrng
            lband = filt - smrng

            longCond = ((close > filt) & (close > close.shift(1)) & (upward > 0)) | \
                       ((close > filt) & (close < close.shift(1)) & (upward > 0))

            shortCond = ((close < filt) & (close < close.shift(1)) & (downward > 0)) | \
                        ((close < filt) & (close > close.shift(1)) & (downward > 0))

            long = longCond.iloc[-1] and position <= 0
            short = shortCond.iloc[-1] and position >= 0

            if long:
                print("开多单")
                target_pos.set_target_volume(1)
                position = 1
            elif short:
                print("开空单")
                target_pos.set_target_volume(-1)
                position = -1

except KeyboardInterrupt:
    print("程序已终止")
finally:
    api.close()