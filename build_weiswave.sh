#!/bin/bash

SCRIPT_NAME="weiswave_claude_df_with_cmd_parameters.py"
OUTPUT_DIR="dist"

nuitka \
  --standalone \
  --include-package=websockets \
  --include-package=pandas \
  --include-module=pandas._config.localization \
  --include-package=tqsdk \
  --include-data-files="$(python3 -c 'import tqsdk; import os; print(os.path.join(os.path.dirname(tqsdk.__file__), "expired_quotes.json.lzma"))')=tqsdk/expired_quotes.json.lzma" \
  --jobs=30 \
  --output-dir="$OUTPUT_DIR" \
  "$SCRIPT_NAME"

if [ $? -eq 0 ]; then
  echo "✅ 编译成功！可执行文件路径："
  echo "$OUTPUT_DIR/${SCRIPT_NAME%.py}"
  echo ""
  echo "👉 你可以运行它："
  echo "./$OUTPUT_DIR/${SCRIPT_NAME%.py}"
else
  echo "❌ 编译失败，请检查错误日志。"
fi
