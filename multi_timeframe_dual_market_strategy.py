"""
连续交易双边市场多时间周期策略
支持5个K线周期：1分钟，15分钟，4小时，日K线，周K线
实现金叉死叉信号、回归信号、移动止损等功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import time
from datetime import datetime, timedelta

try:
    from tqsdk import TqApi, TqKq
    from tqsdk.tafunc import ma, time_to_str
    from tradefuncs import BK, SK, BP, SP
    from loguru import logger
except ImportError:
    print("警告: 部分依赖包未安装，将使用模拟模式")


class SignalType(Enum):
    """信号类型"""
    GOLDEN_CROSS = "金叉"
    DEATH_CROSS = "死叉"
    UPWARD_REGRESSION = "向上回归"
    DOWNWARD_REGRESSION = "向下回归"
    NONE = "无信号"


class PositionDirection(Enum):
    """持仓方向"""
    LONG = "多仓"
    SHORT = "空仓"
    NONE = "无持仓"


@dataclass
class StrategyConfig:
    """策略配置参数"""
    # 时间周期配置（秒）
    timeframes: List[int] = None
    
    # 均线参数
    short_ma_period: int = 5      # 短均线周期
    long_ma_period: int = 20      # 长均线周期
    
    # 止损参数
    stop_loss_periods: int = 10   # 止损计算的K线周期数
    
    # 风险控制参数
    max_position_ratio: float = 0.1  # 最大持仓比例（资金的N%）
    max_leverage: float = 3.0         # 最大杠杆倍数
    
    # 手续费参数
    commission_rate: float = 0.0003   # 双边手续费率
    
    # 移动止损参数
    trailing_stop_enabled: bool = True  # 是否启用移动止损
    profit_threshold_ratio: float = 2.0  # 盈利阈值（手续费的倍数）
    
    def __post_init__(self):
        if self.timeframes is None:
            self.timeframes = [60, 900, 14400, 86400, 604800]  # 1m, 15m, 4h, 1d, 1w


@dataclass
class Position:
    """持仓信息"""
    direction: PositionDirection = PositionDirection.NONE
    entry_price: float = 0.0
    quantity: int = 0
    stop_loss_price: float = 0.0
    entry_time: datetime = None
    timeframe: int = 0
    profit_loss: float = 0.0
    is_trailing: bool = False  # 是否启用移动止损


@dataclass
class MarketSignal:
    """市场信号"""
    timeframe: int
    signal_type: SignalType
    price: float
    timestamp: datetime
    ma_short: float
    ma_long: float
    ma_diff: float


class MultiTimeFrameStrategy:
    """多时间周期策略主类"""
    
    def __init__(self, config: StrategyConfig, symbol: str, initial_capital: float = 100000, csv_file: str = "klines2.csv"):
        self.config = config
        self.symbol = symbol
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.csv_file = csv_file

        # 持仓管理
        self.positions: Dict[int, Position] = {}  # 按时间周期管理持仓

        # 数据存储
        self.klines_data: Dict[int, pd.DataFrame] = {}
        self.signals_history: List[MarketSignal] = []
        self._raw_data = None  # 原始CSV数据缓存

        # 初始化各时间周期的持仓
        for tf in self.config.timeframes:
            self.positions[tf] = Position(timeframe=tf)

        # API连接（如果可用）
        self.api = None
        self.quotes = {}

        # 预加载CSV数据
        print(f"初始化策略，使用数据文件: {csv_file}")
        self._raw_data = self._load_csv_data(csv_file)
        
    def initialize_api(self, auth: str = None):
        """初始化API连接"""
        try:
            if auth:
                self.api = TqApi(TqKq(), auth=auth, disable_print=True)
                # 获取合约信息
                if len(self.symbol) <= 3:
                    self.symbol = self.api.query_cont_quotes(product_id=self.symbol)[0]
                self.quotes[self.symbol] = self.api.get_quote(self.symbol)
                print(f"API初始化成功，交易合约: {self.symbol}")
            else:
                print("未提供认证信息，使用模拟模式")
        except Exception as e:
            print(f"API初始化失败: {e}，使用模拟模式")
    
    def get_klines(self, timeframe: int, length: int = 200) -> pd.DataFrame:
        """获取K线数据"""
        if self.api:
            try:
                klines = self.api.get_kline_serial(self.symbol, timeframe, length)
                return klines.copy()
            except Exception as e:
                print(f"获取{timeframe}秒K线数据失败: {e}")
                return self._get_csv_klines(timeframe, length)
        else:
            return self._get_csv_klines(timeframe, length)

    def _get_csv_klines(self, timeframe: int, length: int = 200) -> pd.DataFrame:
        """从CSV数据获取指定时间周期的K线数据"""
        # 如果还没有加载原始数据，先加载
        if not hasattr(self, '_raw_data') or self._raw_data is None:
            self._raw_data = self._load_csv_data(self.csv_file)

        # 如果请求的是1分钟数据，直接返回
        if timeframe == 60:
            return self._raw_data.tail(length).copy()

        # 否则重采样到指定时间周期
        resampled_data = self._resample_to_timeframe(self._raw_data, timeframe)
        return resampled_data.tail(length).copy()
    
    def _load_csv_data(self, csv_file: str = "klines2.csv") -> pd.DataFrame:
        """从CSV文件加载K线数据"""
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file)

            # 转换时间戳为datetime
            df['datetime'] = pd.to_datetime(df['datetime'], unit='ns')

            # 确保数据类型正确
            df['open'] = df['open'].astype(float)
            df['high'] = df['high'].astype(float)
            df['low'] = df['low'].astype(float)
            df['close'] = df['close'].astype(float)
            df['volume'] = df['volume'].astype(int)

            # 按时间排序
            df = df.sort_values('datetime').reset_index(drop=True)

            print(f"成功加载CSV数据: {len(df)} 条记录")
            print(f"时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
            print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")

            return df

        except Exception as e:
            print(f"加载CSV数据失败: {e}")
            return self._generate_mock_data(60, 1000)

    def _resample_to_timeframe(self, df: pd.DataFrame, timeframe: int) -> pd.DataFrame:
        """将1分钟数据重采样到指定时间周期"""
        try:
            # 设置datetime为索引
            df_resampled = df.set_index('datetime')

            # 重采样规则
            freq = f'{timeframe}S'

            # 重采样OHLCV数据
            ohlc_data = df_resampled.resample(freq).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()

            # 重置索引
            ohlc_data = ohlc_data.reset_index()

            print(f"重采样到{timeframe}秒周期: {len(ohlc_data)} 条记录")

            return ohlc_data

        except Exception as e:
            print(f"重采样失败: {e}")
            return self._generate_mock_data(timeframe, 200)

    def _generate_mock_data(self, timeframe: int, length: int) -> pd.DataFrame:
        """生成模拟K线数据（备用方案）"""
        np.random.seed(42)
        dates = pd.date_range(start='2024-01-01', periods=length, freq=f'{timeframe}s')

        # 生成随机价格数据
        base_price = 8100  # 基于真实数据的价格水平
        price_changes = np.random.randn(length) * 5
        prices = base_price + np.cumsum(price_changes)

        data = {
            'datetime': dates,
            'open': prices,
            'high': prices + np.random.rand(length) * 10,
            'low': prices - np.random.rand(length) * 10,
            'close': prices + np.random.randn(length) * 3,
            'volume': np.random.randint(100, 1000, length)
        }

        df = pd.DataFrame(data)
        df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
        df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))

        return df
    
    def calculate_ma_signals(self, df: pd.DataFrame, timeframe: int) -> MarketSignal:
        """计算均线信号"""
        if len(df) < max(self.config.short_ma_period, self.config.long_ma_period):
            return MarketSignal(timeframe, SignalType.NONE, 0, datetime.now(), 0, 0, 0)
        
        # 计算均线
        df['ma_short'] = df['close'].rolling(self.config.short_ma_period).mean()
        df['ma_long'] = df['close'].rolling(self.config.long_ma_period).mean()
        df['ma_diff'] = df['ma_short'] - df['ma_long']
        
        # 获取最新数据
        current = df.iloc[-1]
        previous = df.iloc[-2] if len(df) > 1 else current
        
        current_price = current['close']
        current_ma_short = current['ma_short']
        current_ma_long = current['ma_long']
        current_ma_diff = current['ma_diff']
        
        # 判断信号类型
        signal_type = SignalType.NONE
        
        # 金叉死叉信号
        if (previous['ma_short'] <= previous['ma_long'] and 
            current['ma_short'] > current['ma_long']):
            signal_type = SignalType.GOLDEN_CROSS
        elif (previous['ma_short'] >= previous['ma_long'] and 
              current['ma_short'] < current['ma_long']):
            signal_type = SignalType.DEATH_CROSS
        else:
            # 回归信号
            if len(df) >= 3:
                # 计算最近几个周期的ma_diff变化
                recent_diffs = df['ma_diff'].tail(5).values
                
                # 向上回归：从负最小值逐渐增大
                if (current_ma_diff < 0 and 
                    len(recent_diffs) >= 3 and
                    recent_diffs[-1] > recent_diffs[-2] > recent_diffs[-3]):
                    signal_type = SignalType.UPWARD_REGRESSION
                
                # 向下回归：从正最大值逐渐减小
                elif (current_ma_diff > 0 and 
                      len(recent_diffs) >= 3 and
                      recent_diffs[-1] < recent_diffs[-2] < recent_diffs[-3]):
                    signal_type = SignalType.DOWNWARD_REGRESSION
        
        return MarketSignal(
            timeframe=timeframe,
            signal_type=signal_type,
            price=current_price,
            timestamp=current['datetime'],
            ma_short=current_ma_short,
            ma_long=current_ma_long,
            ma_diff=current_ma_diff
        )
    
    def get_higher_timeframe_signal(self, current_timeframe: int) -> Optional[MarketSignal]:
        """获取上一级大周期的信号"""
        current_index = self.config.timeframes.index(current_timeframe)
        if current_index >= len(self.config.timeframes) - 1:
            return None
        
        higher_timeframe = self.config.timeframes[current_index + 1]
        
        # 获取大周期数据和信号
        df = self.get_klines(higher_timeframe, 100)
        return self.calculate_ma_signals(df, higher_timeframe)
    
    def calculate_stop_loss(self, df: pd.DataFrame, direction: PositionDirection) -> float:
        """计算止损价格"""
        if len(df) < self.config.stop_loss_periods:
            periods = len(df)
        else:
            periods = self.config.stop_loss_periods
        
        recent_data = df.tail(periods)
        
        if direction == PositionDirection.LONG:
            # 多仓止损：最近N个周期的最低点
            return recent_data['low'].min()
        else:
            # 空仓止损：最近N个周期的最高点
            return recent_data['high'].max()
    
    def check_position_limit(self, timeframe: int) -> bool:
        """检查是否可以开仓（风险控制）"""
        # 检查该时间周期是否已有持仓
        if self.positions[timeframe].direction != PositionDirection.NONE:
            return False
        
        # 检查总持仓比例
        total_position_value = sum(
            pos.quantity * pos.entry_price 
            for pos in self.positions.values() 
            if pos.direction != PositionDirection.NONE
        )
        
        position_ratio = total_position_value / self.current_capital
        if position_ratio >= self.config.max_position_ratio:
            return False
        
        return True
    
    def calculate_position_size(self, price: float) -> int:
        """计算开仓数量"""
        # 等权重分配资金
        available_capital = self.current_capital * self.config.max_position_ratio / len(self.config.timeframes)
        
        # 考虑杠杆
        leveraged_capital = available_capital * self.config.max_leverage
        
        # 计算数量（最小为1手）
        quantity = max(1, int(leveraged_capital / price))
        
        return quantity

    def should_open_position(self, current_signal: MarketSignal, higher_signal: Optional[MarketSignal],
                           current_kline: pd.Series) -> Tuple[bool, PositionDirection]:
        """判断是否应该开仓"""
        if not higher_signal:
            return False, PositionDirection.NONE

        # 多仓条件：当前周期金叉 + 当前K线涨 + 大周期向上回归
        if (current_signal.signal_type == SignalType.GOLDEN_CROSS and
            current_kline['close'] > current_kline['open'] and
            higher_signal.signal_type == SignalType.UPWARD_REGRESSION):
            return True, PositionDirection.LONG

        # 空仓条件：当前周期死叉 + 当前K线跌 + 大周期向下回归
        elif (current_signal.signal_type == SignalType.DEATH_CROSS and
              current_kline['close'] < current_kline['open'] and
              higher_signal.signal_type == SignalType.DOWNWARD_REGRESSION):
            return True, PositionDirection.SHORT

        return False, PositionDirection.NONE

    def open_position(self, timeframe: int, direction: PositionDirection,
                     price: float, stop_loss_price: float):
        """开仓"""
        if not self.check_position_limit(timeframe):
            print(f"时间周期{timeframe}秒无法开仓：已有持仓或超出风险限制")
            return False

        quantity = self.calculate_position_size(price)

        # 更新持仓信息
        position = self.positions[timeframe]
        position.direction = direction
        position.entry_price = price
        position.quantity = quantity
        position.stop_loss_price = stop_loss_price
        position.entry_time = datetime.now()
        position.is_trailing = False

        # 执行交易（如果有API连接）
        if self.api:
            try:
                if direction == PositionDirection.LONG:
                    BK(self.api, symbol=self.symbol, order_price=price + 1, volume=quantity)
                    print(f"开多仓：{self.symbol}, 价格:{price}, 数量:{quantity}, 止损:{stop_loss_price}")
                else:
                    SK(self.api, symbol=self.symbol, order_price=price - 1, volume=quantity)
                    print(f"开空仓：{self.symbol}, 价格:{price}, 数量:{quantity}, 止损:{stop_loss_price}")
            except Exception as e:
                print(f"开仓失败: {e}")
                # 重置持仓信息
                position.direction = PositionDirection.NONE
                return False
        else:
            print(f"模拟开仓 - 时间周期:{timeframe}秒, 方向:{direction.value}, "
                  f"价格:{price}, 数量:{quantity}, 止损:{stop_loss_price}")

        return True

    def close_position(self, timeframe: int, price: float, reason: str = ""):
        """平仓"""
        position = self.positions[timeframe]
        if position.direction == PositionDirection.NONE:
            return False

        # 计算盈亏
        if position.direction == PositionDirection.LONG:
            pnl = (price - position.entry_price) * position.quantity
        else:
            pnl = (position.entry_price - price) * position.quantity

        # 扣除手续费
        commission = position.entry_price * position.quantity * self.config.commission_rate * 2
        net_pnl = pnl - commission

        # 更新资金
        self.current_capital += net_pnl

        # 执行平仓（如果有API连接）
        if self.api:
            try:
                if position.direction == PositionDirection.LONG:
                    SP(self.api, symbol=self.symbol, order_price=price - 1,
                       volume=position.quantity, today_prefer=True)
                    print(f"平多仓：{reason}, 价格:{price}, 盈亏:{net_pnl:.2f}")
                else:
                    BP(self.api, symbol=self.symbol, order_price=price + 1, volume=position.quantity)
                    print(f"平空仓：{reason}, 价格:{price}, 盈亏:{net_pnl:.2f}")
            except Exception as e:
                print(f"平仓失败: {e}")
                return False
        else:
            print(f"模拟平仓 - 时间周期:{timeframe}秒, {reason}, "
                  f"价格:{price}, 盈亏:{net_pnl:.2f}")

        # 重置持仓信息
        position.direction = PositionDirection.NONE
        position.entry_price = 0.0
        position.quantity = 0
        position.stop_loss_price = 0.0
        position.is_trailing = False

        return True

    def update_trailing_stop(self, timeframe: int, current_price: float, ma_long: float):
        """更新移动止损"""
        position = self.positions[timeframe]
        if position.direction == PositionDirection.NONE:
            return

        # 计算当前盈亏
        if position.direction == PositionDirection.LONG:
            current_pnl = (current_price - position.entry_price) * position.quantity
        else:
            current_pnl = (position.entry_price - current_price) * position.quantity

        # 计算手续费阈值
        commission_threshold = (position.entry_price * position.quantity *
                              self.config.commission_rate * 2 *
                              self.config.profit_threshold_ratio)

        # 如果盈利超过阈值，启用移动止损
        if current_pnl > commission_threshold and not position.is_trailing:
            position.is_trailing = True
            position.stop_loss_price = current_price
            print(f"启用移动止损 - 时间周期:{timeframe}秒, 当前价格:{current_price}")

        # 如果已启用移动止损，更新止损点
        if position.is_trailing:
            # 计算新的止损点：当前价格与长周期均线的中间点
            new_stop_price = (current_price + ma_long) / 2

            if position.direction == PositionDirection.LONG:
                # 多仓：止损点只能向上移动
                if new_stop_price > position.stop_loss_price:
                    position.stop_loss_price = new_stop_price
                    print(f"更新多仓止损点 - 时间周期:{timeframe}秒, 新止损:{new_stop_price:.2f}")
            else:
                # 空仓：止损点只能向下移动
                if new_stop_price < position.stop_loss_price:
                    position.stop_loss_price = new_stop_price
                    print(f"更新空仓止损点 - 时间周期:{timeframe}秒, 新止损:{new_stop_price:.2f}")

    def check_stop_loss(self, timeframe: int, current_price: float) -> bool:
        """检查是否触发止损"""
        position = self.positions[timeframe]
        if position.direction == PositionDirection.NONE:
            return False

        if position.direction == PositionDirection.LONG:
            # 多仓：价格跌破止损点
            if current_price <= position.stop_loss_price:
                self.close_position(timeframe, current_price, "止损")
                return True
        else:
            # 空仓：价格涨破止损点
            if current_price >= position.stop_loss_price:
                self.close_position(timeframe, current_price, "止损")
                return True

        return False

    def get_1min_entry_signal(self, target_timeframe: int, target_direction: PositionDirection) -> bool:
        """使用1分钟K线精确入场"""
        if 60 not in self.config.timeframes:
            return True  # 如果没有1分钟数据，直接返回True

        # 获取1分钟K线数据
        df_1m = self.get_klines(60, 50)
        signal_1m = self.calculate_ma_signals(df_1m, 60)

        # 检查1分钟级别的信号是否与目标方向一致
        if target_direction == PositionDirection.LONG:
            return signal_1m.signal_type == SignalType.GOLDEN_CROSS
        elif target_direction == PositionDirection.SHORT:
            return signal_1m.signal_type == SignalType.DEATH_CROSS

        return False

    def process_timeframe(self, timeframe: int):
        """处理单个时间周期的策略逻辑"""
        # 获取K线数据
        df = self.get_klines(timeframe, 100)
        if len(df) < max(self.config.short_ma_period, self.config.long_ma_period):
            return

        # 计算当前信号
        current_signal = self.calculate_ma_signals(df, timeframe)
        current_kline = df.iloc[-1]

        # 获取大周期信号
        higher_signal = self.get_higher_timeframe_signal(timeframe)

        # 检查是否需要开仓
        should_open, direction = self.should_open_position(current_signal, higher_signal, current_kline)

        if should_open:
            # 使用1分钟K线精确入场
            if self.get_1min_entry_signal(timeframe, direction):
                # 计算止损价格
                stop_loss_price = self.calculate_stop_loss(df, direction)

                # 开仓
                self.open_position(timeframe, direction, current_signal.price, stop_loss_price)

        # 检查现有持仓
        position = self.positions[timeframe]
        if position.direction != PositionDirection.NONE:
            current_price = current_signal.price

            # 检查止损
            if self.check_stop_loss(timeframe, current_price):
                return  # 已平仓，无需继续处理

            # 更新移动止损
            if self.config.trailing_stop_enabled:
                self.update_trailing_stop(timeframe, current_price, current_signal.ma_long)

        # 记录信号历史
        self.signals_history.append(current_signal)

        # 保持历史记录在合理范围内
        if len(self.signals_history) > 1000:
            self.signals_history = self.signals_history[-500:]

    def run_strategy(self, iterations: int = 100):
        """运行策略主循环"""
        print("=" * 60)
        print("启动多时间周期双边市场策略")
        print(f"交易合约: {self.symbol}")
        print(f"初始资金: {self.initial_capital}")
        print(f"时间周期: {[f'{tf//60}分钟' if tf < 3600 else f'{tf//3600}小时' if tf < 86400 else f'{tf//86400}天' for tf in self.config.timeframes]}")
        print("=" * 60)

        for iteration in range(iterations):
            print(f"\n--- 第 {iteration + 1} 次策略执行 ---")

            # 处理每个时间周期
            for timeframe in self.config.timeframes:
                try:
                    self.process_timeframe(timeframe)
                except Exception as e:
                    print(f"处理时间周期 {timeframe} 秒时出错: {e}")

            # 显示当前状态
            self.print_status()

            # 如果使用API，等待更新
            if self.api:
                try:
                    self.api.wait_update()
                except KeyboardInterrupt:
                    print("策略被用户中断")
                    break
                except Exception as e:
                    print(f"API更新出错: {e}")
                    break
            else:
                # 模拟模式下暂停
                time.sleep(1)

        # 策略结束，平掉所有持仓
        self.close_all_positions()
        self.print_final_report()

    def close_all_positions(self):
        """平掉所有持仓"""
        print("\n平掉所有持仓...")
        for timeframe in self.config.timeframes:
            position = self.positions[timeframe]
            if position.direction != PositionDirection.NONE:
                # 获取当前价格
                df = self.get_klines(timeframe, 10)
                current_price = df.iloc[-1]['close']
                self.close_position(timeframe, current_price, "策略结束")

    def print_status(self):
        """打印当前状态"""
        print(f"当前资金: {self.current_capital:.2f}")

        active_positions = 0
        total_pnl = 0

        for timeframe, position in self.positions.items():
            if position.direction != PositionDirection.NONE:
                active_positions += 1

                # 计算当前盈亏
                df = self.get_klines(timeframe, 10)
                current_price = df.iloc[-1]['close']

                if position.direction == PositionDirection.LONG:
                    pnl = (current_price - position.entry_price) * position.quantity
                else:
                    pnl = (position.entry_price - current_price) * position.quantity

                total_pnl += pnl

                tf_name = f"{timeframe//60}分钟" if timeframe < 3600 else f"{timeframe//3600}小时" if timeframe < 86400 else f"{timeframe//86400}天"
                print(f"  {tf_name}: {position.direction.value}, 入场:{position.entry_price:.2f}, "
                      f"当前:{current_price:.2f}, 盈亏:{pnl:.2f}, 止损:{position.stop_loss_price:.2f}")

        if active_positions == 0:
            print("  当前无持仓")
        else:
            print(f"总浮动盈亏: {total_pnl:.2f}")

    def print_final_report(self):
        """打印最终报告"""
        print("\n" + "=" * 60)
        print("策略执行完成 - 最终报告")
        print("=" * 60)

        total_return = self.current_capital - self.initial_capital
        return_rate = (total_return / self.initial_capital) * 100

        print(f"初始资金: {self.initial_capital:.2f}")
        print(f"最终资金: {self.current_capital:.2f}")
        print(f"总收益: {total_return:.2f}")
        print(f"收益率: {return_rate:.2f}%")

        # 统计信号数量
        signal_counts = {}
        for signal in self.signals_history:
            key = f"{signal.timeframe//60}分钟-{signal.signal_type.value}"
            signal_counts[key] = signal_counts.get(key, 0) + 1

        print("\n信号统计:")
        for signal_type, count in signal_counts.items():
            print(f"  {signal_type}: {count} 次")


def create_default_config() -> StrategyConfig:
    """创建默认策略配置"""
    return StrategyConfig(
        timeframes=[60, 900, 14400, 86400, 604800],  # 1m, 15m, 4h, 1d, 1w
        short_ma_period=5,
        long_ma_period=20,
        stop_loss_periods=10,
        max_position_ratio=0.1,  # 10%
        max_leverage=3.0,
        commission_rate=0.0003,
        trailing_stop_enabled=True,
        profit_threshold_ratio=2.0
    )


def run_strategy_example():
    """运行策略示例"""
    # 创建策略配置
    config = create_default_config()

    # 可以根据需要调整参数
    config.short_ma_period = 5      # 短均线周期
    config.long_ma_period = 20      # 长均线周期
    config.stop_loss_periods = 10   # 止损计算周期
    config.max_position_ratio = 0.08  # 最大持仓比例8%
    config.max_leverage = 2.5       # 最大杠杆2.5倍
    config.commission_rate = 0.0003 # 手续费率0.03%

    # 创建策略实例
    strategy = MultiTimeFrameStrategy(
        config=config,
        symbol='rb',  # 螺纹钢主力合约
        initial_capital=100000  # 10万初始资金
    )

    # 初始化API（可选，如果没有认证信息将使用模拟模式）
    # strategy.initialize_api("your_auth_string")

    # 运行策略
    strategy.run_strategy(iterations=50)


def run_custom_strategy(symbol: str, initial_capital: float, auth: str = None, **kwargs):
    """运行自定义策略"""
    # 创建配置
    config = create_default_config()

    # 应用自定义参数
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)

    # 创建策略
    strategy = MultiTimeFrameStrategy(config, symbol, initial_capital)

    # 初始化API
    if auth:
        strategy.initialize_api(auth)

    # 运行策略
    strategy.run_strategy()


def backtest_strategy(symbol: str, start_date: str, end_date: str, **kwargs):
    """回测策略（需要历史数据）"""
    print(f"回测策略: {symbol}")
    print(f"时间范围: {start_date} 到 {end_date}")

    # 创建配置
    config = create_default_config()
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)

    # 创建策略实例
    strategy = MultiTimeFrameStrategy(config, symbol, 100000)

    # 这里可以加载历史数据进行回测
    # 目前使用模拟数据
    strategy.run_strategy(iterations=100)


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1]

        if mode == "example":
            # 运行示例
            run_strategy_example()

        elif mode == "custom":
            # 自定义运行
            symbol = sys.argv[2] if len(sys.argv) > 2 else "rb"
            capital = float(sys.argv[3]) if len(sys.argv) > 3 else 100000

            # 自定义参数示例
            custom_params = {
                'short_ma_period': 3,
                'long_ma_period': 15,
                'max_position_ratio': 0.12,
                'max_leverage': 3.0,
                'stop_loss_periods': 8
            }

            run_custom_strategy(symbol, capital, **custom_params)

        elif mode == "backtest":
            # 回测模式
            symbol = sys.argv[2] if len(sys.argv) > 2 else "rb"
            start_date = sys.argv[3] if len(sys.argv) > 3 else "2024-01-01"
            end_date = sys.argv[4] if len(sys.argv) > 4 else "2024-12-31"

            backtest_strategy(symbol, start_date, end_date)

        else:
            print("用法:")
            print("  python multi_timeframe_dual_market_strategy.py example")
            print("  python multi_timeframe_dual_market_strategy.py custom [symbol] [capital]")
            print("  python multi_timeframe_dual_market_strategy.py backtest [symbol] [start_date] [end_date]")
    else:
        # 默认运行示例
        print("运行默认示例...")
        run_strategy_example()
