
from strategies.TimeRoseMA_cross_moni import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date


    product = 'ni'
    symbol=product
    interval = 15
    bklimit = 20
    sklimit = 5
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301", disable_print=True)
    symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


runstrategy()
