import os
import getpass

from crontab import CronTab

directory=os.getcwd()+r'/'
username = getpass.getuser()

cron = CronTab(user=username)
cron.remove_all()
directory=os.getcwd()+r'/'

#
# job = cron.new(command="python /home/<USER>/rsitrader/speak_test.py")
# job.minute.every(10)
# cron.write()
# #
# job = cron.new(command="python /home/<USER>/rsitrader/clock_alarm.py")
# job.minute.every(15)
# job.hour.on(20)
# # job.minute.on(30)
# # job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
# job.dow.during('mon', 'fri')
# # cron.remove_all()
# cron.write()


programs = [
            # 'TimeRoseMA_cross_thd_cy_oi_1m.py',
            'TimeRoseMA_cross_chenmei_tgjy_oi_3m.py',
            ]


for p in programs:
    prejob =  "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = prejob + '; python ' + directory + p

    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.hour.on(20)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    # job.dow.on()
    # cron.remove_all()
    cron.write()

for p in programs:
    prejob =  "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = prejob + '; python ' + directory + p
    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.hour.on(8)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    # job.dow.on()
    # cron.remove_all()
    cron.write()


for p in programs:
    jobstr = 'python ' + directory + p
    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.every_reboot()
    cron.write()
