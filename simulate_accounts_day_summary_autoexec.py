import time
from tqsdk import *
import math
import numpy as np
import pandas as pd
import json
from accountSimulate_registerbook import account_simulate
import os
# 保存到文件
file_path = 'simulatedaysummary_new.json'

# 直接访问所有实例
account_list = account_simulate.all_instances

def execute_task():
    import datetime
    day_account_info = []
    for account in account_list:
        name = account.tqacc.split(',')[0]
        api = TqApi(TqKq(), auth=account.tqacc)
        account = api.get_account()

        # Manually extract the attributes into a dictionary
        account_info = {
            'user_name': name,
            'day_pnl': account.balance - account.pre_balance,
            'pre_balance': account.pre_balance,
            'balance': account.balance,
            'close_profit': account.close_profit,
            'commission': account.commission,
            'available': account.available,
            'user_id': account.user_id,
            'float_profit': account.float_profit,
            'frozen_commission': account.frozen_commission,
            'frozen_margin': account.frozen_margin,
            'margin': account.margin,
            'risk_ratio': account.risk_ratio
        }

        print(account_info)
        print('当日盈亏: ', account_info['balance'] - account_info['pre_balance'])
        day_account_info.append(account_info)
        api.close()

    print(day_account_info)

    # 定义一个自定义编码函数来处理nan和特殊对象
    class NumpyJSONEncoder(json.JSONEncoder):
        def default(self, obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            if isinstance(obj, np.float32) or isinstance(obj, np.float64):
                return float(obj)
            if isinstance(obj, np.int32) or isinstance(obj, np.int64):
                return int(obj)
            if math.isnan(obj) if isinstance(obj, float) else False:
                return "NaN"
            # 处理tqsdk.objs.Account对象
            if str(type(obj)).find("tqsdk.objs.Account") > -1:
                try:
                    return obj.__dict__
                except:
                    try:
                        return vars(obj)
                    except:
                        try:
                            return dict(obj)
                        except:
                            return str(obj)
            return super(NumpyJSONEncoder, self).default(obj)

    # 添加或更新当前日期
    current_date = datetime.datetime.now().strftime('%Y-%m-%d')
    # 检查文件是否存在
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            all_data = json.load(f)
    else:
        all_data = {}

    all_data[current_date] = day_account_info

    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, ensure_ascii=False, indent=4, cls=NumpyJSONEncoder)

    print(f"数据已更新并保存到{file_path}，当前日期：{current_date}")



def main():
    from datetime import datetime
    while True:
        now = datetime.now()
        if now.weekday() < 5 and now.hour == 15 and now.minute == 20:
            execute_task()
            time.sleep(60)  # 防止在同一分钟内多次执行
        time.sleep(1)

if __name__ == "__main__":
    main()
