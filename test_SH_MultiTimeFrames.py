"""
SH多时间周期策略测试脚本
用于验证重构后的功能是否正常
"""

import sys
import time
from TimeRoseMA_cross_SH_MultiTimeFrames import (
    TimeFrameConfig,
    SimpleMultiTimeFrameStrategy,
    MultiTimeFrameStrategy,
    create_sh_timeframe_configs,
    run_single_timeframe
)


def test_timeframe_config():
    """测试时间周期配置"""
    print("=== 测试TimeFrameConfig ===")
    
    config = TimeFrameConfig(interval=60, bklimit=2, sklimit=2, single_volume=1)
    
    assert config.interval == 60
    assert config.bklimit == 2
    assert config.sklimit == 2
    assert config.single_volume == 1
    assert config.name == "1m"
    
    print("✓ TimeFrameConfig测试通过")


def test_create_configs():
    """测试配置创建函数"""
    print("=== 测试create_sh_timeframe_configs ===")
    
    configs = create_sh_timeframe_configs()
    
    assert len(configs) == 4
    assert configs[0].interval == 60   # 1m
    assert configs[1].interval == 180  # 3m
    assert configs[2].interval == 300  # 5m
    assert configs[3].interval == 900  # 15m
    
    expected_names = ["1m", "3m", "5m", "15m"]
    actual_names = [config.name for config in configs]
    assert actual_names == expected_names
    
    print("✓ create_sh_timeframe_configs测试通过")


def test_strategy_initialization():
    """测试策略初始化"""
    print("=== 测试策略初始化 ===")
    
    configs = create_sh_timeframe_configs()
    
    # 测试SimpleMultiTimeFrameStrategy初始化
    strategy1 = SimpleMultiTimeFrameStrategy(
        symbol='SH',
        timeframe_configs=configs,
        auth="test_auth"
    )
    
    assert strategy1.symbol == 'SH'
    assert len(strategy1.timeframe_configs) == 4
    assert strategy1.auth == "test_auth"
    
    # 测试MultiTimeFrameStrategy初始化
    strategy2 = MultiTimeFrameStrategy(
        api=None,
        symbol='SH',
        timeframe_configs=configs,
        auth="test_auth"
    )
    
    assert strategy2.symbol == 'SH'
    assert len(strategy2.timeframe_configs) == 4
    assert strategy2.auth == "test_auth"
    
    print("✓ 策略初始化测试通过")


def test_config_validation():
    """测试配置验证"""
    print("=== 测试配置验证 ===")
    
    # 测试不同的时间周期配置
    test_cases = [
        (60, "1m"),
        (180, "3m"),
        (300, "5m"),
        (900, "15m"),
        (1800, "30m"),
    ]
    
    for interval, expected_name in test_cases:
        config = TimeFrameConfig(interval=interval)
        assert config.name == expected_name, f"期望{expected_name}，实际{config.name}"
    
    print("✓ 配置验证测试通过")


def test_import_dependencies():
    """测试依赖导入"""
    print("=== 测试依赖导入 ===")
    
    try:
        from tqsdk import TqApi, TqKq
        print("✓ TqSDK导入成功")
    except ImportError as e:
        print(f"✗ TqSDK导入失败: {e}")
        return False
    
    try:
        from strategies.TimeRoseMA_cross_speak import ma_cross
        print("✓ ma_cross函数导入成功")
    except ImportError as e:
        print(f"✗ ma_cross函数导入失败: {e}")
        return False
    
    return True


def run_basic_functionality_test():
    """运行基本功能测试"""
    print("=== 基本功能测试 ===")
    
    # 测试配置创建
    configs = create_sh_timeframe_configs()
    print(f"创建了{len(configs)}个时间周期配置")
    
    for config in configs:
        print(f"  - {config.name}: {config.interval}秒, 多单限制:{config.bklimit}, 空单限制:{config.sklimit}")
    
    # 测试策略对象创建
    strategy = SimpleMultiTimeFrameStrategy(
        symbol='SH',
        timeframe_configs=configs[:2],  # 只测试前两个
        auth="test_auth"
    )
    
    print(f"策略对象创建成功，包含{len(strategy.timeframe_configs)}个时间周期")
    print("✓ 基本功能测试通过")


def run_all_tests():
    """运行所有测试"""
    print("开始运行SH多时间周期策略测试...\n")
    
    tests = [
        test_timeframe_config,
        test_create_configs,
        test_strategy_initialization,
        test_config_validation,
        test_import_dependencies,
        run_basic_functionality_test,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
            print()
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            failed += 1
            print()
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False


def show_usage_examples():
    """显示使用示例"""
    print("""
=== 使用示例 ===

1. 运行所有测试:
   python test_SH_MultiTimeFrames.py

2. 运行特定测试:
   python test_SH_MultiTimeFrames.py config
   python test_SH_MultiTimeFrames.py import
   python test_SH_MultiTimeFrames.py basic

3. 实际策略运行示例:
   # 运行所有时间周期
   python TimeRoseMA_cross_SH_MultiTimeFrames.py independent
   
   # 运行单个时间周期
   python TimeRoseMA_cross_SH_MultiTimeFrames.py 1m

注意: 实际运行策略需要有效的TqSDK认证和网络连接
""")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        
        if test_name == "config":
            test_timeframe_config()
            test_create_configs()
            test_config_validation()
        elif test_name == "import":
            test_import_dependencies()
        elif test_name == "basic":
            run_basic_functionality_test()
        elif test_name == "help":
            show_usage_examples()
        else:
            print(f"未知测试: {test_name}")
            print("可用测试: config, import, basic, help")
    else:
        # 运行所有测试
        success = run_all_tests()
        sys.exit(0 if success else 1)
