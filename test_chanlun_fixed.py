"""
测试修复后的缠论策略参数提示功能
"""

import subprocess
import sys

def test_help_command():
    """测试帮助命令"""
    print("=" * 60)
    print("测试帮助命令")
    print("=" * 60)
    
    try:
        result = subprocess.run([
            sys.executable, "chanlun_strategy.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            help_text = result.stdout
            print("帮助信息获取成功")
            
            # 检查关键内容
            key_contents = [
                "缠论策略回测程序",
                "--tq-user",
                "--tq-pwd", 
                "--init-money",
                "--start-date",
                "--end-date",
                "使用示例",
                "默认配置"
            ]
            
            found_count = 0
            for content in key_contents:
                if content in help_text:
                    print(f"  ✓ 找到: {content}")
                    found_count += 1
                else:
                    print(f"  ✗ 缺失: {content}")
            
            print(f"\n内容检查: {found_count}/{len(key_contents)} 项通过")
            
            if found_count >= len(key_contents) * 0.8:  # 80%通过即可
                print("✓ 帮助命令测试通过")
                return True
            else:
                print("✗ 帮助命令测试失败")
                return False
        else:
            print(f"✗ 帮助命令执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        return False


def test_function_import():
    """测试函数导入"""
    print("=" * 60)
    print("测试函数导入")
    print("=" * 60)
    
    try:
        # 测试导入主要函数
        from chanlun_strategy import (
            show_usage_help,
            interactive_parameter_input,
            create_argument_parser,
            main
        )
        
        print("✓ 主要函数导入成功")
        
        # 测试参数解析器
        parser = create_argument_parser()
        print("✓ 参数解析器创建成功")
        
        # 测试默认参数解析
        args = parser.parse_args([])
        print(f"✓ 默认参数解析成功:")
        print(f"  TQ用户名: {args.tq_user}")
        print(f"  初始资金: {args.init_money:,}")
        print(f"  开始日期: {args.start_date}")
        print(f"  结束日期: {args.end_date}")
        
        return True
        
    except Exception as e:
        print(f"✗ 函数导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_usage_help_display():
    """测试使用帮助显示"""
    print("=" * 60)
    print("测试使用帮助显示")
    print("=" * 60)
    
    try:
        from chanlun_strategy import show_usage_help
        
        print("调用show_usage_help():")
        print("-" * 40)
        show_usage_help()
        print("-" * 40)
        print("✓ 使用帮助显示成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 使用帮助显示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_parameter_parsing():
    """测试参数解析"""
    print("=" * 60)
    print("测试参数解析")
    print("=" * 60)
    
    try:
        from chanlun_strategy import create_argument_parser
        
        parser = create_argument_parser()
        
        # 测试自定义参数
        test_args = [
            '--tq-user', 'testuser',
            '--tq-pwd', 'testpwd',
            '--init-money', '2000000',
            '--start-date', '20200101',
            '--end-date', '20241231'
        ]
        
        args = parser.parse_args(test_args)
        
        print("✓ 自定义参数解析成功:")
        print(f"  TQ用户名: {args.tq_user}")
        print(f"  TQ密码: {args.tq_pwd}")
        print(f"  初始资金: {args.init_money:,}")
        print(f"  开始日期: {args.start_date}")
        print(f"  结束日期: {args.end_date}")
        
        # 验证参数值
        expected = {
            'tq_user': 'testuser',
            'tq_pwd': 'testpwd',
            'init_money': 2000000,
            'start_date': '20200101',
            'end_date': '20241231'
        }
        
        all_correct = True
        for param, expected_value in expected.items():
            actual_value = getattr(args, param)
            if actual_value == expected_value:
                print(f"  ✓ {param}: {actual_value}")
            else:
                print(f"  ✗ {param}: 期望{expected_value}, 实际{actual_value}")
                all_correct = False
        
        if all_correct:
            print("✓ 参数解析验证通过")
            return True
        else:
            print("✗ 参数解析验证失败")
            return False
        
    except Exception as e:
        print(f"✗ 参数解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_usage_examples():
    """显示使用示例"""
    print("=" * 60)
    print("缠论策略使用示例")
    print("=" * 60)
    
    examples = [
        "# 无参数运行（显示交互式菜单）",
        "python chanlun_strategy.py",
        "",
        "# 查看帮助信息",
        "python chanlun_strategy.py --help",
        "",
        "# 使用默认配置运行",
        "python chanlun_strategy.py --tq-user smartmanp --tq-pwd ftp123",
        "",
        "# 自定义时间范围",
        "python chanlun_strategy.py --start-date 20200101 --end-date 20241231",
        "",
        "# 自定义初始资金",
        "python chanlun_strategy.py --init-money 2000000",
        "",
        "# 完整自定义配置",
        "python chanlun_strategy.py --tq-user myuser --tq-pwd mypwd --init-money 500000 --start-date 20220101 --end-date 20231231"
    ]
    
    for example in examples:
        print(example)


def main():
    """主测试函数"""
    print("缠论策略参数提示功能测试（修复版）")
    print("=" * 80)
    
    tests = [
        ("函数导入", test_function_import),
        ("使用帮助显示", test_usage_help_display),
        ("参数解析", test_parameter_parsing),
        ("帮助命令", test_help_command),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n缠论策略参数提示功能修复成功！")
        show_usage_examples()
    else:
        print("\n部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
