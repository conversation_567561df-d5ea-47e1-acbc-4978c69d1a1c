"""
测试图表显示修复
验证CZCE.OI509合约和图表显示问题的修复
"""

import sys
import os
import pandas as pd
import numpy as np

def test_default_contract_update():
    """测试默认合约更新"""
    print("=" * 60)
    print("测试默认合约更新")
    print("=" * 60)
    
    try:
        # 检查程序文件
        program_file = "波动交易GUI.py"
        if not os.path.exists(program_file):
            print(f"✗ 程序文件不存在: {program_file}")
            return False
        
        # 读取程序内容
        with open(program_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查默认合约设置
        if 'self.symbol = "CZCE.OI509"' in content:
            print("✓ 默认合约已更新为 CZCE.OI509")
        else:
            print("✗ 默认合约未更新")
            return False
        
        # 检查界面默认值
        if 'self.symbol_var = tk.StringVar(value="CZCE.OI509")' in content:
            print("✓ 界面默认合约已更新为 CZCE.OI509")
        else:
            print("✗ 界面默认合约未更新")
            return False
        
        # 检查常用合约列表
        if '"CZCE.OI509"' in content and 'contracts = [' in content:
            print("✓ 常用合约列表已包含 CZCE.OI509")
        else:
            print("✗ 常用合约列表未更新")
            return False
        
        print("✓ 默认合约更新验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 默认合约测试失败: {e}")
        return False

def test_enhanced_logging():
    """测试增强的日志功能"""
    print("=" * 60)
    print("测试增强的日志功能")
    print("=" * 60)
    
    try:
        # 检查程序文件
        program_file = "波动交易GUI.py"
        with open(program_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查新增的日志记录（使用更灵活的匹配）
        enhanced_logs = [
            ('正在获取数据', 'self.add_log("DEBUG", f"正在获取数据'),
            ('开始绘制图表', 'self.add_log("DEBUG", "开始绘制图表'),
            ('绘制K线图', 'self.add_log("DEBUG", "绘制K线图'),
            ('绘制技术指标', 'self.add_log("DEBUG", "绘制技术指标'),
            ('刷新画布', 'self.add_log("DEBUG", "刷新画布'),
            ('图表首次绘制成功', 'self.add_log("INFO", "图表首次绘制成功")'),
            ('错误详情', 'self.add_log("ERROR", f"错误详情:')
        ]

        for log_name, log_pattern in enhanced_logs:
            if log_pattern in content:
                print(f"✓ 增强日志存在: {log_name}")
            else:
                print(f"✗ 增强日志缺失: {log_name}")
                return False
        
        # 检查错误处理增强
        error_handling = [
            'import traceback',
            'traceback.format_exc()',
            'traceback.print_exc()',
            'missing_columns',
            'missing_indicators'
        ]
        
        for handler in error_handling:
            if handler in content:
                print(f"✓ 错误处理增强: {handler}")
            else:
                print(f"✗ 错误处理缺失: {handler}")
        
        print("✓ 增强日志功能验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 增强日志测试失败: {e}")
        return False

def test_chart_plotting_logic():
    """测试图表绘制逻辑"""
    print("=" * 60)
    print("测试图表绘制逻辑")
    print("=" * 60)
    
    try:
        # 模拟数据结构
        print("创建模拟K线数据...")
        
        # 创建模拟的DataFrame
        dates = pd.date_range('2024-01-01', periods=100, freq='1min')
        np.random.seed(42)
        
        # 生成模拟价格数据
        base_price = 3000
        price_changes = np.random.normal(0, 10, 100)
        prices = base_price + np.cumsum(price_changes)
        
        # 创建OHLC数据
        data = []
        for i, price in enumerate(prices):
            high = price + np.random.uniform(5, 20)
            low = price - np.random.uniform(5, 20)
            open_price = prices[i-1] if i > 0 else price
            close_price = price
            
            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': close_price
            })
        
        df = pd.DataFrame(data, index=dates)
        
        print(f"✓ 模拟数据创建成功: {len(df)} 条记录")
        print(f"✓ 价格范围: {df['low'].min():.2f} - {df['high'].max():.2f}")
        
        # 检查数据完整性
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if not missing_columns:
            print("✓ 数据包含所有必要列")
        else:
            print(f"✗ 数据缺少列: {missing_columns}")
            return False
        
        # 模拟K线分类
        up = df[df.close > df.open]
        down = df[df.close < df.open]
        equal = df[df.close == df.open]
        
        print(f"✓ K线统计 - 上涨: {len(up)}, 下跌: {len(down)}, 平盘: {len(equal)}")
        
        # 检查价格范围计算
        price_min = df['low'].min()
        price_max = df['high'].max()
        price_range = price_max - price_min
        margin = price_range * 0.1
        
        print(f"✓ 价格范围计算: 最低 {price_min:.2f}, 最高 {price_max:.2f}, 边距 {margin:.2f}")
        
        # 模拟技术指标数据
        df['W1'] = np.random.choice([1, -3, np.nan], size=len(df), p=[0.3, 0.3, 0.4])
        df['G'] = df['close'] + np.random.normal(0, 5, len(df))
        df['G1'] = df['G'].iloc[-1]
        df['LT'] = df[['open', 'close']].min(axis=1)
        df['HT'] = df[['open', 'close']].max(axis=1)
        
        # 检查技术指标
        required_indicators = ['W1', 'G', 'G1', 'LT', 'HT']
        missing_indicators = [col for col in required_indicators if col not in df.columns]
        
        if not missing_indicators:
            print("✓ 技术指标数据完整")
        else:
            print(f"✗ 缺少技术指标: {missing_indicators}")
        
        # 统计有效指标点
        valid_w1 = df['W1'].notna().sum()
        w1_signals = df[df['W1'].notna()]['W1'].value_counts()
        
        print(f"✓ 有效指标点: {valid_w1}")
        print(f"✓ 信号统计: {dict(w1_signals)}")
        
        print("✓ 图表绘制逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 图表绘制逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_contract_format():
    """测试合约格式"""
    print("=" * 60)
    print("测试合约格式")
    print("=" * 60)
    
    # 测试不同的合约格式
    contracts = [
        ("CZCE.OI509", "郑州商品交易所 - 菜籽油"),
        ("KQ.i@OI", "快期指数 - 铁矿石"),
        ("SHFE.ag2512", "上海期货交易所 - 白银"),
        ("DCE.i2512", "大连商品交易所 - 铁矿石"),
        ("CFFEX.IF2512", "中国金融期货交易所 - 股指")
    ]
    
    print("合约格式验证:")
    print(f"{'合约代码':<15} {'交易所':<20} {'说明'}")
    print("-" * 60)
    
    for contract, description in contracts:
        # 检查合约格式
        if '.' in contract:
            exchange, symbol = contract.split('.', 1)
            format_ok = len(exchange) > 0 and len(symbol) > 0
            status = "✓" if format_ok else "✗"
            print(f"{contract:<15} {description:<20} {status}")
        else:
            print(f"{contract:<15} {description:<20} ✗ 格式错误")
    
    print(f"\n重点测试合约: CZCE.OI509")
    print(f"• 交易所: CZCE (郑州商品交易所)")
    print(f"• 品种: OI (菜籽油)")
    print(f"• 合约月份: 509 (2025年9月)")
    print(f"• 格式验证: ✓ 正确")
    
    return True

def show_troubleshooting_guide():
    """显示故障排除指南"""
    print("=" * 60)
    print("图表显示问题故障排除指南")
    print("=" * 60)
    
    print("常见问题及解决方案:")
    print()
    
    print("1. 图表窗口空白")
    print("   可能原因:")
    print("   • 数据获取失败")
    print("   • 技术指标计算错误")
    print("   • matplotlib绘制异常")
    print("   解决方案:")
    print("   • 检查日志中的ERROR信息")
    print("   • 确认合约代码正确")
    print("   • 查看DEBUG日志了解详细过程")
    print()
    
    print("2. 合约数据无法获取")
    print("   可能原因:")
    print("   • 合约代码错误")
    print("   • 合约已过期")
    print("   • 网络连接问题")
    print("   解决方案:")
    print("   • 使用CZCE.OI509等有效合约")
    print("   • 检查网络连接")
    print("   • 尝试其他合约代码")
    print()
    
    print("3. 技术指标不显示")
    print("   可能原因:")
    print("   • 指标计算失败")
    print("   • 数据列缺失")
    print("   • 绘制参数错误")
    print("   解决方案:")
    print("   • 查看指标计算日志")
    print("   • 检查数据完整性")
    print("   • 调整显示参数")
    print()
    
    print("4. 实时更新停止")
    print("   可能原因:")
    print("   • API连接断开")
    print("   • 动画更新异常")
    print("   • 内存不足")
    print("   解决方案:")
    print("   • 重新启动分析")
    print("   • 减少数据长度")
    print("   • 检查系统资源")

def main():
    """主测试函数"""
    print("图表显示修复测试")
    print("=" * 80)
    
    tests = [
        ("默认合约更新", test_default_contract_update),
        ("增强日志功能", test_enhanced_logging),
        ("图表绘制逻辑", test_chart_plotting_logic),
        ("合约格式验证", test_contract_format),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示故障排除指南
    show_troubleshooting_guide()
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 图表显示修复成功！")
        print("\n修复内容:")
        print("✓ 默认合约改为 CZCE.OI509")
        print("✓ 增强了详细的调试日志")
        print("✓ 改进了错误处理机制")
        print("✓ 添加了数据完整性检查")
        print("✓ 优化了图表绘制逻辑")
        print("✓ 增加了价格范围自动调整")
        print("\n现在程序应该能够:")
        print("• 正确显示CZCE.OI509合约的K线图")
        print("• 提供详细的执行过程日志")
        print("• 自动诊断和报告问题")
        print("• 处理各种数据异常情况")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
