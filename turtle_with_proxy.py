#!c:\python\python.exe
from datetime import datetime
import pandas as pd
import numpy as np
import backtrader as bt
import os

# 在代码中设置代理 - 方法1：设置环境变量
os.environ['HTTP_PROXY'] = 'http://tbox1:2080'
os.environ['HTTPS_PROXY'] = 'http://tbox1:2080'

# 方法2：如果backtrader在底层使用requests库，可以尝试设置requests的代理
try:
    import requests
    proxies = {
        'http': 'http://tbox1:2080',
        'https': 'http://tbox1:2080',
    }
    # 注意：这将影响所有使用默认会话的requests调用
    requests.Session().proxies.update(proxies)
except ImportError:
    pass


class TurtleStrategy(bt.Strategy):
    params = (
        ('atr_period', 20),  # ATR周期
        ('entry_period', 20),  # 入场周期
        ('exit_period', 10),  # 出场周期
        ('position_size', 0.02),  # 头寸规模
    )

    def __init__(self):
        # 计算ATR和唐奇安通道
        self.atr = bt.indicators.ATR(period=self.p.atr_period)
        self.entry_high = bt.indicators.Highest(self.data.high, period=self.p.entry_period)
        self.entry_low = bt.indicators.Lowest(self.data.low, period=self.p.entry_period)
        self.exit_high = bt.indicators.Highest(self.data.high, period=self.p.exit_period)
        self.exit_low = bt.indicators.Lowest(self.data.low, period=self.p.exit_period)

    def next(self):
        if not self.position:  # 没有持仓
            # 突破入场
            if self.data.close[0] > self.entry_high[-1]:
                size = self.position_size_calc()
                self.buy(size=size)
            elif self.data.close[0] < self.entry_low[-1]:
                size = self.position_size_calc()
                self.sell(size=size)

        else:  # 持有多头或空头
            if self.position.size > 0:  # 多头
                if self.data.close[0] < self.exit_low[-1]:
                    self.close()
            else:  # 空头
                if self.data.close[0] > self.exit_high[-1]:
                    self.close()

    def position_size_calc(self):
        # 基于ATR计算头寸规模
        value = self.broker.getvalue()
        unit = value * self.p.position_size
        return int(unit / (self.atr[0] * self.data.close[0]))


# 创建回测引擎
cerebro = bt.Cerebro()

# 加载数据
data = bt.feeds.YahooFinanceData(
    dataname='AAPL',
    fromdate=datetime(2010, 1, 1),
    todate=datetime(2020, 12, 31)
)
cerebro.adddata(data)

# 添加策略
cerebro.addstrategy(TurtleStrategy)

# 设置初始资金
cerebro.broker.setcash(100000.0)

# 设置佣金
cerebro.broker.setcommission(commission=0.001)

# 运行回测
print('初始资金: %.2f' % cerebro.broker.getvalue())
cerebro.run()
print('最终资金: %.2f' % cerebro.broker.getvalue())

# 绘制结果
cerebro.plot()