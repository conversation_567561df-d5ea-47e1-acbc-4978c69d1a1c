import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import mplfinance as mpf
import pandas as pd
from collections import deque


class DynamicCandlestickChart:
    def __init__(self, max_bars=100):
        self.max_bars = max_bars
        self.data = deque(maxlen=max_bars)

        # 创建图形和坐标轴
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.ax.set_title('Real-time Candlestick Chart')
        self.ax.set_xlabel('Date')
        self.ax.set_ylabel('Price')

        # 初始化空的K线图
        self.candlestick = None

    def on_bar(self, new_bar):
        """
        当新的K线数据到达时被调用
        new_bar: 字典，包含 'datetime', 'open', 'high', 'low', 'close' 等数据
        """
        self.data.append(new_bar)
        self.update_chart()

    def update_chart(self):
        self.ax.clear()
        df = pd.DataFrame(list(self.data))
        df.set_index('datetime', inplace=True)

        # 使用 mplfinance 绘制K线图
        mpf.plot(df, type='candle', ax=self.ax, style='charles')

        plt.tight_layout()
        plt.draw()
        plt.pause(0.001)  # 暂停一小段时间以允许图形更新

    def start(self):
        plt.show(block=False)  # 非阻塞式显示图形


# 模拟行情数据源
class MockDataFeed:
    def __init__(self, chart):
        self.chart = chart

    def simulate_data(self):
        import random
        import datetime

        base_price = 100
        date = datetime.datetime.now()

        while True:
            open_price = base_price + random.uniform(-1, 1)
            high_price = open_price + random.uniform(0, 1)
            low_price = open_price - random.uniform(0, 1)
            close_price = random.uniform(low_price, high_price)

            new_bar = {
                'datetime': date,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': random.randint(1000, 5000)
            }

            self.chart.on_bar(new_bar)

            date += datetime.timedelta(minutes=1)
            base_price = close_price

            plt.pause(1)  # 每秒更新一次


# 使用示例
if __name__ == "__main__":
    chart = DynamicCandlestickChart(max_bars=100)
    data_feed = MockDataFeed(chart)

    chart.start()
    data_feed.simulate_data()
