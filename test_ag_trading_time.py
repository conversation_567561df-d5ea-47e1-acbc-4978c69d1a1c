"""
测试ag合约交易时间处理
验证修复后的交易时间判断是否正确
"""

import sys
import os
from datetime import datetime, time as dt_time
from unittest.mock import patch

# 添加utils路径
sys.path.append('utils')

def test_ag_trading_time():
    """测试ag合约交易时间判断"""
    print("=" * 60)
    print("测试ag合约交易时间判断")
    print("=" * 60)
    
    try:
        from utils import is_ag_trading_time, tradingTime
        
        # 测试不同时间点
        test_times = [
            # 日盘时间
            (9, 30, 0, True, "日盘上午"),
            (11, 0, 0, True, "日盘上午末"),
            (11, 45, 0, False, "午休时间"),
            (14, 0, 0, True, "日盘下午"),
            (15, 30, 0, False, "日盘结束后"),
            
            # 夜盘时间
            (21, 30, 0, True, "夜盘开始"),
            (23, 0, 0, True, "夜盘中段"),
            (1, 0, 0, True, "夜盘跨日"),
            (2, 0, 0, True, "夜盘末段"),
            (2, 45, 0, False, "夜盘结束后"),
            
            # 非交易时间
            (8, 0, 0, False, "开盘前"),
            (16, 0, 0, False, "收盘后"),
            (20, 0, 0, False, "夜盘前"),
            (3, 0, 0, False, "深夜"),
        ]
        
        print("时间点测试:")
        print(f"{'时间':<10} {'预期':<6} {'实际':<6} {'结果':<6} {'说明'}")
        print("-" * 50)
        
        all_passed = True
        
        for hour, minute, second, expected, description in test_times:
            # 模拟指定时间
            test_datetime = datetime.now().replace(hour=hour, minute=minute, second=second)
            
            with patch('utils.utils.datetime') as mock_datetime:
                mock_datetime.now.return_value = test_datetime
                mock_datetime.strptime = datetime.strptime
                
                # 测试ag专用函数
                actual = is_ag_trading_time()
                
                time_str = f"{hour:02d}:{minute:02d}:{second:02d}"
                expected_str = "是" if expected else "否"
                actual_str = "是" if actual else "否"
                result_str = "✓" if actual == expected else "✗"
                
                print(f"{time_str:<10} {expected_str:<6} {actual_str:<6} {result_str:<6} {description}")
                
                if actual != expected:
                    all_passed = False
        
        if all_passed:
            print("\n✓ ag合约交易时间判断测试通过")
            return True
        else:
            print("\n✗ ag合约交易时间判断测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_general_trading_time():
    """测试通用交易时间判断（带合约参数）"""
    print("=" * 60)
    print("测试通用交易时间判断")
    print("=" * 60)
    
    try:
        from utils import tradingTime
        
        # 测试不同合约
        test_symbols = [
            ('SHFE.ag2512', 'ag合约'),
            ('SHFE.au2512', 'au合约'),
            ('CZCE.OI505', '其他合约'),
            (None, '无合约参数')
        ]
        
        print("合约测试:")
        print(f"{'合约':<15} {'说明':<10} {'函数调用'}")
        print("-" * 40)
        
        for symbol, description in test_symbols:
            try:
                if symbol:
                    result = tradingTime(symbol)
                    print(f"{symbol:<15} {description:<10} tradingTime('{symbol}') -> {result}")
                else:
                    result = tradingTime()
                    print(f"{'None':<15} {description:<10} tradingTime() -> {result}")
                    
            except Exception as e:
                print(f"{symbol or 'None':<15} {description:<10} 调用失败: {e}")
        
        print("\n✓ 通用交易时间判断测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_strategy_integration():
    """测试策略集成"""
    print("=" * 60)
    print("测试策略集成")
    print("=" * 60)
    
    try:
        # 检查strategyTRMAsdk2.py的导入
        import strategyTRMAsdk2
        print("✓ strategyTRMAsdk2.py 导入成功")
        
        # 检查是否正确导入了新的函数
        from strategyTRMAsdk2 import is_ag_trading_time
        print("✓ is_ag_trading_time 函数导入成功")
        
        # 模拟策略中的交易时间检查
        symbol = 'SHFE.ag2512'
        
        if 'ag' in symbol.lower():
            is_trading = is_ag_trading_time()
            print(f"✓ ag合约使用专用交易时间检查: {is_trading}")
        else:
            from utils.utils import tradingTime
            is_trading = tradingTime(symbol)
            print(f"✓ 其他合约使用通用交易时间检查: {is_trading}")
        
        print("✓ 策略集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 策略集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_ag_trading_schedule():
    """显示ag合约交易时间表"""
    print("=" * 60)
    print("ag合约（白银）交易时间表")
    print("=" * 60)
    
    schedule = [
        ("日盘", "09:00-11:30", "上午交易时间"),
        ("", "13:30-15:00", "下午交易时间"),
        ("夜盘", "21:00-02:30+1", "夜盘交易时间（跨日到次日02:30）"),
        ("休市", "15:00-21:00", "日盘结束到夜盘开始"),
        ("", "02:30-09:00", "夜盘结束到日盘开始"),
        ("周末", "周六、周日", "全天休市")
    ]
    
    print(f"{'时段':<6} {'时间':<15} {'说明'}")
    print("-" * 40)
    
    for period, time_range, description in schedule:
        print(f"{period:<6} {time_range:<15} {description}")
    
    print("\n注意事项:")
    print("1. 夜盘交易跨日，从21:00开始到次日02:30结束")
    print("2. 周一凌晨00:00-02:30不是交易时间（周日没有夜盘）")
    print("3. 法定节假日按交易所公告执行")


def main():
    """主测试函数"""
    print("ag合约交易时间处理测试")
    print("=" * 80)
    
    # 显示ag合约交易时间表
    show_ag_trading_schedule()
    
    tests = [
        ("ag合约交易时间判断", test_ag_trading_time),
        ("通用交易时间判断", test_general_trading_time),
        ("策略集成测试", test_strategy_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\nag合约交易时间处理修复成功！")
        print("\n修复内容:")
        print("1. 添加了is_ag_trading_time()专用函数")
        print("2. 改进了tradingTime()函数，支持合约参数")
        print("3. 修改了strategyTRMAsdk2.py中的交易时间检查逻辑")
        print("4. 正确处理了ag合约的夜盘跨日交易时间")
        print("\n现在ag合约可以正确识别以下交易时间:")
        print("- 日盘：09:00-11:30, 13:30-15:00")
        print("- 夜盘：21:00-02:30+1（次日）")
    else:
        print("\n部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
