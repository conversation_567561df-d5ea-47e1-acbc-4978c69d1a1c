from tqsdk import TqApi, TqAuth, TqKq
from tqsdk.ta import MA
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import pandas as pd
import numpy as np

# 初始化 TqApi
# api = TqApi(auth=TqAuth("YOUR_USERNAME", "YOUR_PASSWORD"))
api = TqApi(TqKq(), auth="wolfquant2023,Qiai1301")
# 获取 CZCE.OI501 的 1 分钟 K 线数据
klines = api.get_kline_serial("CZCE.OI501", 60)

# 计算指标
ma5 = MA(klines, 5)

def plot_candlestick(ax, df):
    ax.clear()
    width = 0.8
    width2 = 0.1

    up = df[df.close > df.open]
    down = df[df.close < df.open]
    equal = df[df.close == df.open]

    # 上涨K线 - 红色
    ax.bar(up.index, up.close-up.open, width, bottom=up.open, color='red')
    ax.vlines(up.index, up.low, up.high, color='red', linewidth=1)

    # 下跌K线 - 绿色
    ax.bar(down.index, down.close-down.open, width, bottom=down.open, color='green')
    ax.vlines(down.index, down.low, down.high, color='green', linewidth=1)

    # 开盘价等于收盘价的K线
    ax.bar(equal.index, width2, width, bottom=equal.open-width2/2, color='cyan')
    ax.vlines(equal.index, equal.low, equal.high, color='cyan', linewidth=1)

    # 绘制 MA5 线
    ax.plot(df.index, ma5.ma, color='yellow', linewidth=1)

    ax.set_title('CZCE.OI501 1分钟K线图')
    ax.grid(True, linestyle=':', alpha=0.3)
    plt.xticks(rotation=45)

fig, ax = plt.subplots(figsize=(15, 8))
plt.style.use('dark_background')

def update(frame):
    api.wait_update()
    if api.is_changing(klines.iloc[-1], "datetime"):
        df = pd.DataFrame({
            "open": klines.open,
            "high": klines.high,
            "low": klines.low,
            "close": klines.close
        })
        plot_candlestick(ax, df)

ani = FuncAnimation(fig, update, interval=100)
plt.show()

update(klines)

api.close()