import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMA_101 import trmastrategy
from time import sleep
import time

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    # symbol = 'SHFE.ag2103'
    # symbol = 'CZCE.SR101'
    symbol = 'DCE.pp2201'
    symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 15
    sklimit = 150
    single_volume = 1

    config = {
        symbol: 'DCE.pp2105',
        interval: 15,
        bklimit: 2660000,
        sklimit: 2660000,
        single_volume: 100
    }

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_tyc,Qiai1301")

    trmastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
