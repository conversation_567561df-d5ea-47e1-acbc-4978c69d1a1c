# 叮当六号期货交易系统

## 项目概述
叮当六号是一个基于Python开发的期货交易系统，主要使用tqsdk库实现期货交易接口。系统包含多种交易策略实现，主要针对商品期货市场。

## 主要功能
- 多种期货交易策略实现
- 账户管理和模拟交易
- 历史数据回测
- 实时行情监控和交易
- 交易信号生成和执行

## 核心文件说明
### 策略文件
- `dingdangNo6.py` - 主策略文件，实现叮当六号基础策略
- `dingdangNo6Solid.py` - 改进版策略实现，增加稳定性
- `class_wave_trading.py` - 波浪交易策略类

### 账户管理
- `accounts.py` - 账户管理模块
- `accountSimulate.py` - 模拟交易实现

### 回测工具
- `backtest_demo.py` - 回测演示
- `backtest_oi_strategy.py` - 特定策略回测

### 数据工具
- `batch_download_bars_to_file.py` - 批量下载行情数据
- `datadownloader_professionalversion.py` - 专业版数据下载器

## 依赖环境
- Python 3.6+
- 主要依赖库：
  - tqsdk
  - pandas
  - numpy
  - loguru

## 使用说明
1. 安装依赖：
```bash
pip install tqsdk pandas numpy loguru
```

2. 运行主策略：
```python
python dingdangNo6Solid.py
```

3. 参数配置：
通过修改`config.json`文件调整交易参数

## 注意事项
1. 实盘交易前请充分测试
2. 注意风险管理，设置合理的止损
3. 交易时段注意监控系统状态
4. 建议先在模拟账户测试策略

## 策略原理
核心策略基于价格通道和均线交叉信号：
1. 计算N日最高价(CH)和最低价(CL)
2. 计算中轨MID = (CH + CL)/2
3. 价格上穿中轨做多，下穿中轨做空
4. 结合开盘价判断增强信号可靠性

## 风险提示
期货交易具有高风险，使用本系统需自行承担风险。建议：
- 充分理解策略原理
- 从小资金开始测试
- 设置严格的风险控制
