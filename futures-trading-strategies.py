import pandas as pd
import numpy as np
from typing import List, Tuple


class FuturesTrading:
    def __init__(self, data_path: str):
        self.data = self.load_data(data_path)

    @staticmethod
    def load_data(file_path: str) -> pd.DataFrame:
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'].astype(float), unit='ns')
        df.set_index('datetime', inplace=True)
        return df

    def trend_following(self, short_window: int = 10, long_window: int = 30) -> List[int]:
        signals = [0] * len(self.data)
        short_ma = self.data['close'].rolling(window=short_window).mean()
        long_ma = self.data['close'].rolling(window=long_window).mean()

        for i in range(long_window, len(self.data)):
            if short_ma.iloc[i] > long_ma.iloc[i] and short_ma.iloc[i - 1] <= long_ma.iloc[i - 1]:
                signals[i] = 1  # 买入信号
            elif short_ma.iloc[i] < long_ma.iloc[i] and short_ma.iloc[i - 1] >= long_ma.iloc[i - 1]:
                signals[i] = -1  # 卖出信号

        return signals

    def mean_reversion(self, window: int = 20, std_dev: float = 2) -> List[int]:
        signals = [0] * len(self.data)
        rolling_mean = self.data['close'].rolling(window=window).mean()
        rolling_std = self.data['close'].rolling(window=window).std()

        for i in range(window, len(self.data)):
            if self.data['close'].iloc[i] < rolling_mean.iloc[i] - std_dev * rolling_std.iloc[i]:
                signals[i] = 1  # 买入信号
            elif self.data['close'].iloc[i] > rolling_mean.iloc[i] + std_dev * rolling_std.iloc[i]:
                signals[i] = -1  # 卖出信号

        return signals

    def momentum_trading(self, lookback: int = 14, threshold: float = 0) -> List[int]:
        signals = [0] * len(self.data)
        momentum = self.data['close'].pct_change(periods=lookback)

        for i in range(lookback, len(self.data)):
            if momentum.iloc[i] > threshold:
                signals[i] = 1  # 买入信号
            elif momentum.iloc[i] < -threshold:
                signals[i] = -1  # 卖出信号

        return signals

    def macd_strategy(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> List[int]:
        signals = [0] * len(self.data)

        ema_fast = self.data['close'].ewm(span=fast_period, adjust=False).mean()
        ema_slow = self.data['close'].ewm(span=slow_period, adjust=False).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal_period, adjust=False).mean()

        for i in range(slow_period, len(self.data)):
            if macd.iloc[i] > signal_line.iloc[i] and macd.iloc[i - 1] <= signal_line.iloc[i - 1]:
                signals[i] = 1  # 买入信号
            elif macd.iloc[i] < signal_line.iloc[i] and macd.iloc[i - 1] >= signal_line.iloc[i - 1]:
                signals[i] = -1  # 卖出信号

        return signals

    def backtest(self, signals: List[int], initial_capital: float = 100000.0) -> Tuple[float, float]:
        position = 0
        capital = initial_capital
        returns = []

        for i in range(1, len(self.data)):
            if signals[i] == 1 and position <= 0:
                position = capital / self.data['close'].iloc[i]
                capital = 0
            elif signals[i] == -1 and position > 0:
                capital = position * self.data['close'].iloc[i]
                position = 0

            current_value = capital + position * self.data['close'].iloc[i]
            returns.append((current_value - initial_capital) / initial_capital)

        total_return = (current_value - initial_capital) / initial_capital
        sharpe_ratio = np.sqrt(252) * np.mean(returns) / np.std(returns)

        return total_return, sharpe_ratio

    def run_all_strategies(self):
        strategies = {
            "Trend Following": self.trend_following(),
            "Mean Reversion": self.mean_reversion(),
            "Momentum Trading": self.momentum_trading(),
            "MACD": self.macd_strategy()
        }

        for name, signals in strategies.items():
            total_return, sharpe_ratio = self.backtest(signals)
            print(f"{name} Strategy:")
            print(f"Total Return: {total_return:.2%}")
            print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
            print()


def main():
    trader = FuturesTrading('data.csv')
    trader.run_all_strategies()


if __name__ == "__main__":
    main()