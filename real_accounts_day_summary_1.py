import tqsdk
from tqsdk import TqApi, TqAccount
import math
import numpy as np
import pandas as pd
import json
import datetime
import os
import sys # Import sys module

# --- Determine the absolute path of the script and its directory ---
try:
    # __file__ is the path to the current script.
    # os.path.abspath gets the absolute path.
    # os.path.dirname gets the directory containing the script.
    script_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    # If __file__ is not defined (e.g., in an interactive session or frozen environment),
    # fallback to the current working directory. This might not be foolproof
    # in all scenarios but is a reasonable fallback.
    script_dir = os.getcwd()
    print("Warning: __file__ not defined, using current working directory as script directory:", script_dir)


# --- Add the script's directory to Python's search path ---
# This ensures that modules in the same directory as the script can be imported
# regardless of where the script is executed from.
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)

# --- Now import the local module ---
# This import should now work as long as accountSimulate_registerbook.py
# is in the same directory as this script.
try:
    from accountSimulate_registerbook import account_real
except ImportError as e:
    print(f"Error importing accountSimulate_registerbook: {e}")
    print(f"Ensure 'accountSimulate_registerbook.py' is in the directory: {script_dir}")
    sys.exit(1) # Exit if the crucial module can't be imported

# --- Define the absolute path for the output file ---
# Use os.path.join to create a path relative to the script's directory.
file_path = os.path.join(script_dir, 'realdaysummary_new.json')
print(f"Data will be saved to: {file_path}")

# --- Rest of your original code ---

# Directly access all instances
account_list = account_real.all_instances
day_account_info = []

for acct in account_list:
    try:
        # print(account.tqacc)
        name = acct.tqacc.split(',')[0]
        print(f"Processing account: {name} ({acct.investorid})")
        api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
        account = api.get_account() # Wait for account data to be available

        # Ensure account data is fetched before proceeding
        if not account:
            print(f"Warning: Could not fetch account data for {name}. Skipping.")
            api.close()
            continue

        # Manually extract the attributes into a dictionary
        # Use .get() for safety in case some attributes might be missing/None in rare cases
        account_info = {
            'user_name': name,
            'day_pnl': account.get('balance', 0) - account.get('pre_balance', 0), # Calculate day_pnl safely
            'pre_balance': account.get('pre_balance'),
            'balance': account.get('balance'),
            'close_profit': account.get('close_profit'),
            'commission': account.get('commission'),
            'available': account.get('available'),
            'user_id': account.get('user_id'),
            'float_profit': account.get('float_profit'),
            'frozen_commission': account.get('frozen_commission'),
            'frozen_margin': account.get('frozen_margin'),
            'margin': account.get('margin'),
            'risk_ratio': account.get('risk_ratio')
        }

        print(account_info)
        # print('当日盈亏: ',account_info['day_pnl']) # Already calculated
        day_account_info.append(account_info)

    except Exception as e:
        print(f"Error processing account {acct.name} ({acct.investorid}): {e}")
    finally:
        # Ensure API is closed even if errors occur
        if 'api' in locals() and api:
            api.close()

print("Collected daily account info:")
print(day_account_info)

# Define a custom JSON encoder function to handle nan and special objects
class NumpyJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        # Check for numpy floats/ints specifically
        if isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
            # Check for NaN specifically *before* converting to float
            if np.isnan(obj):
                return None # Represent NaN as null in JSON, or use "NaN" if preferred
            return float(obj)
        if isinstance(obj, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64, np.uint8, np.uint16, np.uint32, np.uint64)):
            return int(obj)
         # Check for standard Python float NaN
        if isinstance(obj, float) and math.isnan(obj):
             return None # Represent NaN as null in JSON, or use "NaN" if preferred
        # Removed the overly broad tqsdk.objs.Account handling as we extract manually now
        # if str(type(obj)).find("tqsdk.objs.Account") > -1:
        #     # ... (old logic removed)
        return super(NumpyJSONEncoder, self).default(obj)

# Add or update current date
current_date = datetime.datetime.now().strftime('%Y-%m-%d')

# Read existing data or initialize
all_data = {}
if os.path.exists(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            all_data = json.load(f)
            # Ensure it's a dictionary
            if not isinstance(all_data, dict):
                 print(f"Warning: Existing file '{file_path}' does not contain a valid JSON object. Overwriting with new data.")
                 all_data = {}
    except json.JSONDecodeError:
        print(f"Warning: Could not decode JSON from '{file_path}'. File might be corrupted. Overwriting with new data.")
        all_data = {}
    except Exception as e:
        print(f"Error reading file '{file_path}': {e}. Overwriting with new data.")
        all_data = {}
else:
    print(f"File '{file_path}' not found. Creating a new one.")
    all_data = {} # Ensure it's initialized as a dict

# Add/Update the data for the current date
all_data[current_date] = day_account_info

# Save back to the file using the absolute path
try:
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, ensure_ascii=False, indent=4, cls=NumpyJSONEncoder)
    print(f"Data updated and saved to {file_path} for date: {current_date}")
except Exception as e:
    print(f"Error writing data to file '{file_path}': {e}")