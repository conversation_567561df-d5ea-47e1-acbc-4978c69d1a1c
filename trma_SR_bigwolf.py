import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMA import trmastrategy
from time import sleep
import time

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    class straConfig:
        def __init__(self, symbol, interval, bklimit, sklimit, single_volume):
            self.symbol = symbol
            self.interval = interval
            self.bklimit = bklimit
            self.sklimit = sklimit
            self.single_volume = single_volume


    config = straConfig('CZCE.SR109', 15, 550000, 550000, 100)

    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123")

    trmastrategy(api, symbol=config.symbol, interval=config.interval, single_volume=config.single_volume, bklimit=config.bklimit, sklimit=config.sklimit, period=13)
