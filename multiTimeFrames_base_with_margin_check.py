import json
import os
import time
from datetime import datetime
import copy

from utils.utils import parse_time, get_time_period, is_trading_time

import pandas as pd
from loguru import logger as log
logfilespath="./logs/"

class MultiTimeframeStrategy:
    def __init__(self, api, symbol, max_positions, positions_file='positions.json', history_length=1000):
        self.api = api
        self.symbol = symbol
        self.max_positions = max_positions
        self.timeframes = list(self.max_positions.keys())
        self.userid = self.api.get_account().user_id
        self.positions_file =logfilespath + self.symbol + '_' + positions_file
        self.history_length = history_length
        self.klines = {}
        self.last_update_time = {}
        self.time_periods = get_time_period(self.api, self.symbol)
        self.log = log.info
        self.load_positions()

        for tf in self.timeframes:
            # 获取初始历史数据
            klineshis =self.api.get_kline_serial(self.symbol, tf, data_length=self.history_length)
            klines = copy.deepcopy(klineshis)
            self.klines[tf] = pd.DataFrame(klines).set_index('datetime')
            time.sleep(1)

        time.sleep(1)

    def load_positions(self):
        if os.path.exists(self.positions_file):
            with open(self.positions_file, 'r') as f:
                self.positions = json.load(f)
                print(f"当前持仓：{self.positions}")
            self.log("Loaded positions from file.")
        else:
            self.positions = {str(tf): {'long': 0, 'short': 0} for tf in self.timeframes}
            self.log(f"Initialized new positions, {self.positions_file}")

    def save_positions(self):
        with open(self.positions_file, 'w') as f:
            json.dump(self.positions, f)
            print(self.positions)
        self.log("Saved positions to file.")

    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")

    def check_crossover(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] > ma.iloc[-1] and close.iloc[-2] <= ma.iloc[-2]

    def check_crossunder(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] < ma.iloc[-1] and close.iloc[-2] >= ma.iloc[-2]

    def get_position_profit(self, position_type):
        position = self.api.get_position(self.symbol)
        if position_type == 'long':
            return position.float_profit_long
        else:
            return position.float_profit_short

    def smart_close_position(self, timeframe, position_type, volume_to_close):
        """
        智能平仓函数，针对上海期货交易所优化

        :param timeframe: 交易周期
        :param position_type: 持仓类型 ('long' 或 'short')
        :param volume_to_close: 需要平仓的手数
        :return: 实际平仓的手数
        """
        try:
            # 获取持仓信息
            position_info = self.api.get_position(self.symbol)

            # 根据持仓方向获取对应的持仓数量
            if position_type == 'long':
                hist_pos = position_info.volume_long_his
                today_pos = position_info.volume_long_today
                direction = "SELL"
            else:
                hist_pos = position_info.volume_short_his
                today_pos = position_info.volume_short_today
                direction = "BUY"

            total_pos = hist_pos + today_pos
            self.log(f"{timeframe}秒周期: {position_type}持仓详情 - 昨仓:{hist_pos}, 今仓:{today_pos}, 总计:{total_pos}, 需平仓:{volume_to_close}")

            # 检查实际持仓是否足够
            if total_pos < volume_to_close:
                self.log(f"{timeframe}秒周期: 警告 - 实际持仓({total_pos})少于需平仓数量({volume_to_close})，调整为实际持仓数量")
                volume_to_close = total_pos

            if volume_to_close <= 0:
                self.log(f"{timeframe}秒周期: 无持仓可平，跳过平仓操作")
                return 0

            actual_closed = 0
            remaining_volume = volume_to_close

            # 策略1：优先平昨仓（手续费较低）
            if hist_pos > 0 and remaining_volume > 0:
                hist_volume_to_close = min(hist_pos, remaining_volume)
                self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE",
                                      volume=hist_volume_to_close,
                                      limit_price=self.klines[timeframe].iloc[-1].close)
                remaining_volume -= hist_volume_to_close
                actual_closed += hist_volume_to_close
                self.log(f"{timeframe}秒周期: 平{position_type}昨仓 {hist_volume_to_close} 手")

            # 策略2：昨仓不够时平今仓
            if today_pos > 0 and remaining_volume > 0:
                today_volume_to_close = min(today_pos, remaining_volume)
                self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSETODAY",
                                      volume=today_volume_to_close,
                                      limit_price=self.klines[timeframe].iloc[-1].close)
                remaining_volume -= today_volume_to_close
                actual_closed += today_volume_to_close
                self.log(f"{timeframe}秒周期: 平{position_type}今仓 {today_volume_to_close} 手")

            if remaining_volume > 0:
                self.log(f"{timeframe}秒周期: 仍有 {remaining_volume} 手无法平仓")
            else:
                self.log(f"{timeframe}秒周期: 成功平仓 {actual_closed} 手{position_type}仓位")

            return actual_closed

        except Exception as e:
            self.log(f"{timeframe}秒周期: 智能平仓出错 - {str(e)}")
            return 0

    def check_margin(self, timeframe, position_type, volume=1):
        """
        检查是否有足够的保证金开仓

        :param timeframe: 交易周期
        :param position_type: 持仓类型 ('long' 或 'short')
        :param volume: 要开仓的手数，默认为1手
        :return: 是否有足够的保证金
        """
        try:
            # 获取账户信息
            account = self.api.get_account()

            # 获取合约信息
            quote = self.api.get_quote(self.symbol)

            # 获取合约的保证金率
            # margin_rate = quote.margin_rate
            margin_rate = 0.1

            # 获取当前价格
            current_price = self.klines[timeframe].iloc[-1].close

            # 计算开仓所需的保证金
            margin_required = current_price * volume * margin_rate * quote.volume_multiple

            # 获取可用保证金
            available_margin = account.available

            # 记录保证金检查日志
            log_message = (
                f"{self.symbol} {timeframe}秒周期: "
                f"保证金检查 - 当前价格: {current_price}, "
                f"保证金率: {margin_rate}, "
                f"所需保证金: {margin_required}, "
                f"可用保证金: {available_margin}"
            )
            self.log(log_message)

            # 检查是否有足够的保证金
            return available_margin >= margin_required

        except Exception as e:
            self.log(f"保证金检查出错: {str(e)}")
            return False

    def execute_trade(self, timeframe, action, position_type):
        timeframe_str = str(timeframe)
        print(f"持仓限额：{self.max_positions}")
        print(f"当前持仓：{self.positions}")

        if action == 'open':
            if self.positions[timeframe_str][position_type] < self.max_positions[timeframe][position_type]:
                # 增加保证金检查
                if not self.check_margin(timeframe, position_type):
                    self.log(f"{timeframe}秒周期: 保证金不足，无法开{position_type}仓")
                    return

                self.positions[timeframe_str][position_type] += 1
                volume = 1 if position_type == 'long' else -1
                self.api.insert_order(symbol=self.symbol, direction="BUY" if position_type == 'long' else "SELL",
                                      offset="OPEN", volume=abs(volume),
                                      limit_price=self.klines[timeframe].iloc[-1].close)
                self.log(f"{timeframe}秒周期: 开{position_type}单")
                self.save_positions()
                print(f"当前持仓：{self.positions}")
            else:
                self.log(f"{timeframe}秒周期: {position_type}单数量已达到上限，不再开仓。")
        elif action == 'close':
            if self.positions[timeframe_str][position_type] > 0:
                profit = self.get_position_profit(position_type)
                if profit >= 0:
                    volume_to_close = self.positions[timeframe_str][position_type]

                    if self.symbol.startswith("SHFE") or self.symbol.startswith("INE") or self.symbol.startswith("CFFEX"):
                        # 上海期货交易所等，使用智能平仓
                        actual_closed = self.smart_close_position(timeframe, position_type, volume_to_close)

                        # 更新持仓记录
                        if actual_closed > 0:
                            self.positions[timeframe_str][position_type] = max(0, self.positions[timeframe_str][position_type] - actual_closed)
                            self.save_positions()
                            print(f"平仓后持仓：{self.positions}")
                        else:
                            self.log(f"{timeframe}秒周期: 平仓失败，持仓记录未更新")
                    else:
                        # 非上海期货交易所，直接平仓
                        direction = "SELL" if position_type == 'long' else "BUY"
                        try:
                            self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE",
                                                  volume=volume_to_close, limit_price=self.klines[timeframe].iloc[-1].close)
                            self.log(f"{timeframe}秒周期: 平{position_type}单 {volume_to_close} 手")

                            # 更新持仓记录
                            self.positions[timeframe_str][position_type] = 0
                            self.save_positions()
                            print(f"平仓后持仓：{self.positions}")
                        except Exception as e:
                            self.log(f"{timeframe}秒周期: 平仓失败 - {str(e)}")
                else:
                    self.log(f"{timeframe}秒周期: {position_type}单亏损({profit:.2f}),不平仓")

    # 其余方法保持不变，与原代码相同
    def update_klines(self, timeframe):
        temp_klines = self.api.get_kline_serial(self.symbol, timeframe, data_length=2)
        temp_df = pd.DataFrame(temp_klines).set_index('datetime')
        if temp_df.index[-2] > self.klines[timeframe].index[-1]:
            # If there's a new complete K-line, update our stored K-lines
            new_kline = temp_df.iloc[-2]
            if new_kline.name not in self.klines[timeframe].index:
                self.klines[timeframe].loc[new_kline.name] = new_kline
                return True
        return False

    def run(self, timeframe):
        # 检查并更新k线数据
        if self.update_klines(timeframe):
            kline = self.klines[timeframe]

            ma13 = kline.close.rolling(13).mean()
            self.position = self.api.get_position(self.symbol)
            self.total_short_sum = sum(position['short'] for position in self.positions.values())
            self.total_long_sum = sum(position['long'] for position in self.positions.values())
            print(f" total_long_sum:{self.total_long_sum}, total_short_sum:{self.total_short_sum}")
            self.log(f"{self.symbol}:{timeframe}秒周期: 当前持仓: 多单:{self.position.pos_long}, 空单:{self.position.pos_short}")

            if self.check_crossover(kline.close, ma13):
                self.log(f"{self.symbol}:{timeframe}秒周期: 收盘价大于13日均线, 发出开多信号")
                print(self.positions)
                self.execute_trade(timeframe, 'open', 'long')
                self.execute_trade(timeframe, 'close', 'short')
            elif self.check_crossunder(kline.close, ma13):
                print(self.positions)
                self.log(f"{self.symbol}:{timeframe}秒周期: 收盘价小于13日均线, 发出开空信号")
                self.execute_trade(timeframe, 'open', 'short')
                self.execute_trade(timeframe, 'close', 'long')

    def update(self):
        if is_trading_time(self.time_periods):
            for timeframe in self.timeframes:
                self.run(timeframe)
        else:
            self.log(f"非交易时间, 当前时间: {time.asctime()}")
            self.api.close()
            return

# 主程序部分保持不变，与原代码相同

# 主程序
if __name__ == '__main__':
    from tqsdk import TqApi, TqAuth, TqKq
    from accountSimulate import ggh as account

    account = account.tqacc
    api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)
    try:
        product_id = 'OI'
        # product_id = 'rb'
        # product_id = 'SA'

        symbol = api.query_cont_quotes(product_id=product_id)[0]
        # symbol = 'CZCE.OI505'
        print(f"交易的合约是: {symbol}")
        time_periods = get_time_period(api, symbol)

        # 定义交易周期（以秒为单位）和每个周期的持仓限制
        # timeframes = [60, 180, 800, 900]  # 1分钟, 3分钟, 13分20秒, 15分钟
        max_positions = {
            15: {'long': 10, 'short': 10},
            60: {'long': 10, 'short': 10},
            180: {'long': 10, 'short': 10},
            300: {'long': 10, 'short': 10},
            800: {'long': 10, 'short': 10},
            900: {'long': 10, 'short': 10}
        }

        while True:

            if is_trading_time(time_periods):
                try:
                    api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)
                    strategy = MultiTimeframeStrategy(api, symbol, max_positions)
                    while True:
                        api.wait_update()
                        strategy.update()
                except Exception as e:
                    print(e)
                    time.sleep(10)
            else:
                print('非交易时间:', time.asctime())
                api.close()
                time.sleep(1)
                # continue

    except KeyboardInterrupt:
        print("策略已停止")

    finally:
        api.close()
