
from strategies.TimeRoseMA_cross_negtive_gap import ma_cross

# from macross_zjy_oi_5m import config



def runstrategy():
    from tqsdk import TqApi, TqAccount
    from datetime import date
    from accounts_zjy import cyzjy as acct

    #
    product = 'OI'
    interval = 60*5
    bklimit = 3
    sklimit = 3
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, symbol, interval=config['interval'], single_volume=config['single_volume'], bklimit=config['bklimit'], sklimit=config['sklimit'])


runstrategy()


