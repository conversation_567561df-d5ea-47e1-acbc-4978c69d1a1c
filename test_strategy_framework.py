"""
测试通用策略框架
验证LLT、MA、双均线策略
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from loguru import logger
from llt_multi_contract_analyzer import (
    ContractConfig, StrategyConfig, StrategyAnalyzer,
    LLTIndicator, MAIndicator, DualMAIndicator
)

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    n_points = 200
    
    # 创建有趋势的价格数据
    base_price = 100
    trend = np.linspace(0, 20, n_points)  # 上升趋势
    noise = np.random.randn(n_points) * 1.0  # 噪声
    prices = base_price + trend + noise
    
    # 确保价格为正
    prices = np.maximum(prices, 50)
    
    dates = pd.date_range('2024-01-01', periods=n_points, freq='5min')
    
    klines_data = pd.DataFrame({
        'datetime': dates,
        'open': prices + np.random.randn(n_points) * 0.1,
        'high': prices + np.abs(np.random.randn(n_points)) * 0.5,
        'low': prices - np.abs(np.random.randn(n_points)) * 0.5,
        'close': prices,
        'volume': np.random.randint(100, 1000, n_points)
    })
    
    return klines_data


def test_indicators():
    """测试各种指标计算"""
    print("=" * 60)
    print("测试指标计算")
    print("=" * 60)
    
    try:
        # 创建测试数据
        klines_data = create_test_data()
        prices = klines_data['close']
        
        print(f"测试数据: {len(prices)}条价格数据")
        print(f"价格范围: {prices.min():.2f} - {prices.max():.2f}")
        
        # 测试LLT指标
        print(f"\n📈 测试LLT指标:")
        llt_values = LLTIndicator.calculate(prices, alpha=0.1)
        llt_signals = LLTIndicator.generate_signals(llt_values)
        
        print(f"LLT值数量: {len(llt_values)}")
        print(f"LLT信号数量: {len(llt_signals)}")
        print(f"买入信号: {llt_signals.count(1)}")
        print(f"卖出信号: {llt_signals.count(-1)}")
        
        # 测试MA指标
        print(f"\n📈 测试MA指标:")
        ma_values = MAIndicator.calculate(prices, period=13)
        ma_signals = MAIndicator.generate_signals(ma_values, prices=prices.tolist())
        
        print(f"MA值数量: {len(ma_values)}")
        print(f"MA信号数量: {len(ma_signals)}")
        print(f"买入信号: {ma_signals.count(1)}")
        print(f"卖出信号: {ma_signals.count(-1)}")
        
        # 测试双均线指标
        print(f"\n📈 测试双均线指标:")
        dual_ma_values = DualMAIndicator.calculate(prices, fast_period=5, slow_period=13)
        dual_ma_signals = DualMAIndicator.generate_signals(dual_ma_values)
        
        print(f"快线数量: {len(dual_ma_values['fast_ma'])}")
        print(f"慢线数量: {len(dual_ma_values['slow_ma'])}")
        print(f"双均线信号数量: {len(dual_ma_signals)}")
        print(f"金叉信号: {dual_ma_signals.count(1)}")
        print(f"死叉信号: {dual_ma_signals.count(-1)}")
        
        print("✅ 所有指标计算正常")
        return True
        
    except Exception as e:
        print(f"❌ 指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_llt_strategy():
    """测试LLT策略"""
    print("=" * 60)
    print("测试LLT策略")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_test_data()
        contract = ContractConfig("TEST.LLT", "LLT测试合约", "TEST")
        
        # 创建LLT策略配置
        strategy_config = StrategyConfig(
            strategy_type="LLT",
            parameters={"d_value_range": (15, 26)}  # 小范围测试
        )
        
        # 创建策略分析器
        analyzer = StrategyAnalyzer(contract, klines_data, strategy_config, "profit_only")
        
        print(f"策略类型: {analyzer.strategy_config.strategy_type}")
        print(f"指标类: {analyzer.indicator_class.__name__}")
        
        # 运行参数优化
        print(f"\n🔄 开始LLT参数优化...")
        optimization_results = analyzer.optimize_parameters()
        
        if optimization_results:
            print(f"✅ 优化完成，获得 {len(optimization_results)} 个有效结果")
            
            # 显示前3名结果
            print(f"\n📊 前3名LLT参数:")
            for i, result in enumerate(optimization_results[:3], 1):
                print(f"  {i}. D_VALUE={result['D_VALUE']}, 收益率={result['return_rate']:.2f}%, "
                     f"胜率={result['win_rate']:.1f}%, 交易={result['total_trades']}次")
            
            return True
        else:
            print("❌ LLT参数优化失败")
            return False
        
    except Exception as e:
        print(f"❌ LLT策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ma_strategy():
    """测试MA策略"""
    print("=" * 60)
    print("测试MA策略")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_test_data()
        contract = ContractConfig("TEST.MA", "MA测试合约", "TEST")
        
        # 创建MA策略配置
        strategy_config = StrategyConfig(
            strategy_type="MA",
            parameters={"period_range": (10, 21)}  # 小范围测试
        )
        
        # 创建策略分析器
        analyzer = StrategyAnalyzer(contract, klines_data, strategy_config, "profit_only")
        
        print(f"策略类型: {analyzer.strategy_config.strategy_type}")
        print(f"指标类: {analyzer.indicator_class.__name__}")
        
        # 运行参数优化
        print(f"\n🔄 开始MA参数优化...")
        optimization_results = analyzer.optimize_parameters()
        
        if optimization_results:
            print(f"✅ 优化完成，获得 {len(optimization_results)} 个有效结果")
            
            # 显示前3名结果
            print(f"\n📊 前3名MA参数:")
            for i, result in enumerate(optimization_results[:3], 1):
                print(f"  {i}. MA_PERIOD={result['MA_PERIOD']}, 收益率={result['return_rate']:.2f}%, "
                     f"胜率={result['win_rate']:.1f}%, 交易={result['total_trades']}次")
            
            return True
        else:
            print("❌ MA参数优化失败")
            return False
        
    except Exception as e:
        print(f"❌ MA策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dual_ma_strategy():
    """测试双均线策略"""
    print("=" * 60)
    print("测试双均线策略")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_test_data()
        contract = ContractConfig("TEST.DualMA", "双均线测试合约", "TEST")
        
        # 创建双均线策略配置
        strategy_config = StrategyConfig(
            strategy_type="DualMA",
            parameters={"fast_range": (3, 8), "slow_range": (10, 16)}  # 小范围测试
        )
        
        # 创建策略分析器
        analyzer = StrategyAnalyzer(contract, klines_data, strategy_config, "profit_only")
        
        print(f"策略类型: {analyzer.strategy_config.strategy_type}")
        print(f"指标类: {analyzer.indicator_class.__name__}")
        
        # 运行参数优化
        print(f"\n🔄 开始双均线参数优化...")
        optimization_results = analyzer.optimize_parameters()
        
        if optimization_results:
            print(f"✅ 优化完成，获得 {len(optimization_results)} 个有效结果")
            
            # 显示前3名结果
            print(f"\n📊 前3名双均线参数:")
            for i, result in enumerate(optimization_results[:3], 1):
                print(f"  {i}. 快线={result['FAST_PERIOD']}, 慢线={result['SLOW_PERIOD']}, "
                     f"收益率={result['return_rate']:.2f}%, 胜率={result['win_rate']:.1f}%, "
                     f"交易={result['total_trades']}次")
            
            return True
        else:
            print("❌ 双均线参数优化失败")
            return False
        
    except Exception as e:
        print(f"❌ 双均线策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("通用策略框架测试")
    print("=" * 80)
    
    tests = [
        ("指标计算", test_indicators),
        ("LLT策略", test_llt_strategy),
        ("MA策略", test_ma_strategy),
        ("双均线策略", test_dual_ma_strategy),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 通用策略框架测试成功！")
        print("\n📋 现在支持的策略:")
        print("1. LLT策略 - 原有的LLT指标策略")
        print("2. MA策略 - 移动平均线突破策略（默认周期13）")
        print("3. DualMA策略 - 双均线金叉死叉策略")
        print("\n📋 使用方法:")
        print("# LLT策略分析")
        print("python llt_multi_contract_analyzer.py --strategy LLT --mode quick")
        print("# MA策略分析")
        print("python llt_multi_contract_analyzer.py --strategy MA --mode quick")
        print("# 双均线策略分析")
        print("python llt_multi_contract_analyzer.py --strategy DualMA --mode quick")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
