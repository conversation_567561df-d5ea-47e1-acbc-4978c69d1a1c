import zmq

# url = "tcp://localhost:5556"


# url = "tcp://hybox:3333"
from signal_receive_server import server
url = (f"tcp://{server}:3333")

context = zmq.Context()
socket = context.socket(zmq.REQ)
socket.connect(url)

def sendsignal(socket, client_name, signal):
    if not socket:
        context = zmq.Context()
        socket = context.socket(zmq.REQ)
        socket.connect(url)

    socket.send_pyobj([client_name, signal])
    socket.recv_pyobj()

if __name__=='__main__':
    signal={'合约': 'SHFE.sn2408', '周期': 900, '时间': '09:45:00', '当前信号': '空', '持续周期': 17, '信号价格': 274170.0, '现价': 266810.0, '信号盈亏': 7370.0}
    sendsignal(socket, 'test', signal)
    print('signal sent...')
