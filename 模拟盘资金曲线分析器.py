#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟盘资金曲线综合分析器
合并了资金曲线gui.py和模拟盘资金曲线_分帐户.py的功能
支持总体分析和分账户分析
"""

import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import matplotlib
import platform
import os
from datetime import datetime

class ComprehensiveFundAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("模拟盘资金曲线综合分析器")

        # 设置窗口大小和居中
        self.setup_window_center()

        # 设置中文字体支持
        self.setup_chinese_font()
        
        # 数据存储
        self.data = None
        self.dates = []
        self.account_balances = {}
        self.accounts = []
        
        # 当前显示模式
        self.current_mode = tk.StringVar(value="总体分析")
        self.selected_account = tk.StringVar()
        
        # 设置UI
        self.setup_ui()

        # 设置默认模式
        self.set_total_mode()

        # 尝试加载默认文件
        self.try_load_default_file()

    def setup_window_center(self):
        """设置窗口大小并居中显示"""
        # 设置窗口大小
        window_width = 1400
        window_height = 900

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算居中位置
        center_x = int(screen_width / 2 - window_width / 2)
        center_y = int(screen_height / 2 - window_height / 2)

        # 设置窗口大小和位置
        self.root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')

        # 设置最小窗口大小
        self.root.minsize(1200, 700)

    def setup_chinese_font(self):
        """设置matplotlib中文字体支持"""
        system = platform.system()
        
        if system == "Windows":
            fonts = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong']
        elif system == "Darwin":  # macOS
            fonts = ['Arial Unicode MS', 'Heiti TC', 'PingFang SC']
        else:  # Linux
            fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']
        
        for font in fonts:
            try:
                plt.rcParams['font.sans-serif'] = [font]
                plt.rcParams['axes.unicode_minus'] = False
                # 测试字体
                fig, ax = plt.subplots()
                ax.text(0.5, 0.5, '测试中文', fontsize=12)
                plt.close(fig)
                print(f"✓ 成功设置字体: {font}")
                return True
            except:
                continue
        
        print("⚠️  警告: 无法设置中文字体")
        return False
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="数据文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(file_frame, text="JSON文件:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, state="readonly", width=50)
        file_entry.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        
        ttk.Button(file_frame, text="选择文件", command=self.select_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_frame, text="重新加载", command=self.reload_data).pack(side=tk.LEFT)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="分析控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 分析模式按钮区域
        mode_frame = ttk.Frame(control_frame)
        mode_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(mode_frame, text="分析模式:", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=(0, 10))

        # 总体分析按钮
        self.total_analysis_btn = ttk.Button(mode_frame, text="总体分析",
                                           command=self.set_total_mode, width=12)
        self.total_analysis_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 分账户分析按钮
        self.account_analysis_btn = ttk.Button(mode_frame, text="分账户分析",
                                             command=self.set_account_mode, width=12)
        self.account_analysis_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 当前模式指示器
        self.mode_indicator = ttk.Label(mode_frame, text="当前模式: 总体分析",
                                       foreground="blue", font=("Arial", 9))
        self.mode_indicator.pack(side=tk.LEFT, padx=(10, 0))

        # 账户选择区域（分账户模式时显示）
        self.account_frame = ttk.Frame(control_frame)

        ttk.Label(self.account_frame, text="选择账户:", font=("Arial", 10)).pack(side=tk.LEFT, padx=(0, 5))

        self.account_combo = ttk.Combobox(self.account_frame, textvariable=self.selected_account,
                                         state="readonly", width=25)
        self.account_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.account_combo.bind('<<ComboboxSelected>>', self.update_chart)

        # 账户快速切换按钮
        self.prev_account_btn = ttk.Button(self.account_frame, text="◀ 上一个",
                                          command=self.prev_account, width=8)
        self.prev_account_btn.pack(side=tk.LEFT, padx=(0, 2))

        self.next_account_btn = ttk.Button(self.account_frame, text="下一个 ▶",
                                          command=self.next_account, width=8)
        self.next_account_btn.pack(side=tk.LEFT, padx=(2, 0))

        # 初始隐藏账户选择
        self.account_frame.pack_forget()
        
        # 操作按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 左侧操作按钮
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)

        self.analyze_btn = ttk.Button(left_buttons, text="🔍 分析数据",
                                     command=self.analyze_data, width=12)
        self.analyze_btn.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(left_buttons, text="💾 导出图表",
                  command=self.export_chart, width=12).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(left_buttons, text="🗑️ 清除图表",
                  command=self.clear_chart, width=12).pack(side=tk.LEFT, padx=(0, 5))

        # 右侧统计信息显示
        self.stats_label = ttk.Label(button_frame, text="", foreground="blue",
                                    font=("Arial", 9), wraplength=400)
        self.stats_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 图表区域
        chart_frame = ttk.LabelFrame(main_frame, text="资金曲线图表", padding="5")
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建matplotlib图形
        self.setup_chart(chart_frame)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("请选择JSON数据文件")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def setup_chart(self, parent):
        """设置图表"""
        # 创建图形，根据模式决定子图数量
        self.fig, self.axs = plt.subplots(2, 1, figsize=(14, 10), sharex=True)
        self.fig.suptitle('模拟盘资金曲线分析', fontsize=16, fontweight='bold')
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, master=parent)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
    
    def try_load_default_file(self):
        """尝试加载默认文件"""
        default_file = "simulatedaysummary_new.json"
        if os.path.exists(default_file):
            self.file_path_var.set(default_file)
            self.load_data()
    
    def select_file(self):
        """选择JSON文件"""
        file_path = filedialog.askopenfilename(
            title="选择JSON数据文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
            self.load_data()
    
    def load_data(self):
        """加载JSON数据"""
        file_path = self.file_path_var.get()
        if not file_path:
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                self.data = json.load(file)
            
            # 处理数据
            self.process_data()
            
            # 更新UI
            self.update_account_list()
            
            filename = os.path.basename(file_path)
            self.status_var.set(f"已加载: {filename} | {len(self.accounts)}个账户, {len(self.dates)}个交易日")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载文件失败: {str(e)}")
            self.status_var.set("文件加载失败")
    
    def process_data(self):
        """处理数据，提取日期和账户信息"""
        if not self.data:
            return
        
        self.dates = list(self.data.keys())
        self.account_balances = {}
        
        for date in self.dates:
            for user in self.data[date]:
                user_name = user["user_name"]
                if user_name not in self.account_balances:
                    self.account_balances[user_name] = []
                self.account_balances[user_name].append(user["balance"])
        
        self.accounts = list(self.account_balances.keys())
    
    def update_account_list(self):
        """更新账户列表"""
        if self.accounts:
            self.account_combo['values'] = self.accounts
            if not self.selected_account.get() and self.accounts:
                self.selected_account.set(self.accounts[0])
    
    def reload_data(self):
        """重新加载数据"""
        if self.file_path_var.get():
            self.load_data()
        else:
            messagebox.showwarning("警告", "请先选择数据文件")
    
    def set_total_mode(self):
        """设置为总体分析模式"""
        self.current_mode.set("总体分析")

        # 更新按钮状态
        self.total_analysis_btn.configure(state="disabled")
        self.account_analysis_btn.configure(state="normal")

        # 隐藏账户选择
        self.account_frame.pack_forget()

        # 更新模式指示器
        self.mode_indicator.config(text="当前模式: 总体分析", foreground="blue")

        # 清除当前图表
        self.clear_chart()

        # 更新分析按钮文本
        self.analyze_btn.config(text="🔍 总体分析")

    def set_account_mode(self):
        """设置为分账户分析模式"""
        self.current_mode.set("分账户分析")

        # 更新按钮状态
        self.total_analysis_btn.configure(state="normal")
        self.account_analysis_btn.configure(state="disabled")

        # 显示账户选择
        self.account_frame.pack(fill=tk.X, pady=(10, 0))

        # 更新模式指示器
        self.mode_indicator.config(text="当前模式: 分账户分析", foreground="green")

        # 清除当前图表
        self.clear_chart()

        # 更新分析按钮文本
        self.analyze_btn.config(text="🔍 账户分析")

    def prev_account(self):
        """切换到上一个账户"""
        if not self.accounts:
            return

        current_account = self.selected_account.get()
        if current_account in self.accounts:
            current_index = self.accounts.index(current_account)
            prev_index = (current_index - 1) % len(self.accounts)
            self.selected_account.set(self.accounts[prev_index])
            self.update_chart()

    def next_account(self):
        """切换到下一个账户"""
        if not self.accounts:
            return

        current_account = self.selected_account.get()
        if current_account in self.accounts:
            current_index = self.accounts.index(current_account)
            next_index = (current_index + 1) % len(self.accounts)
            self.selected_account.set(self.accounts[next_index])
            self.update_chart()
    
    def analyze_data(self):
        """分析数据并绘制图表"""
        if not self.data:
            messagebox.showwarning("警告", "请先选择并加载数据文件")
            return
        
        mode = self.current_mode.get()
        
        if mode == "总体分析":
            self.analyze_total_data()
        else:
            self.analyze_account_data()
    
    def analyze_total_data(self):
        """总体分析"""
        try:
            # 清除之前的图表
            self.axs[0].clear()
            self.axs[1].clear()
            
            # 计算每个交易日的资金合计
            dates = []
            total_balances = []
            
            for date, users in self.data.items():
                total_balance = sum(user["balance"] for user in users)
                dates.append(date)
                total_balances.append(total_balance)
            
            if not dates:
                messagebox.showwarning("警告", "数据文件中没有有效数据")
                return
            
            # 计算百分比变化
            base_value = total_balances[0]
            percentage_changes = [(balance / base_value - 1) * 100 for balance in total_balances]
            
            # 绘制上方资金曲线
            self.axs[0].plot(dates, total_balances, marker='o', linewidth=2, markersize=4, 
                           color='#1f77b4', label='总资金曲线')
            self.axs[0].set_ylabel('资金合计（元）', fontsize=12)
            self.axs[0].set_title('所有账户资金合计曲线', fontsize=14, fontweight='bold')
            self.axs[0].legend()
            self.axs[0].grid(True, alpha=0.3)
            self.axs[0].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
            
            # 绘制下方百分比变化曲线
            self.axs[1].plot(dates, percentage_changes, marker='o', color='orange', 
                           linewidth=2, markersize=4, label='收益率变化')
            self.axs[1].set_xlabel('交易日', fontsize=12)
            self.axs[1].set_ylabel('收益率 (%)', fontsize=12)
            self.axs[1].set_title('总体收益率变化曲线', fontsize=14, fontweight='bold')
            self.axs[1].legend()
            self.axs[1].grid(True, alpha=0.3)
            self.axs[1].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:+.2f}%'))
            
            # 设置x轴标签旋转
            plt.setp(self.axs[1].xaxis.get_majorticklabels(), rotation=45, ha='right')
            
            # 调整布局
            self.fig.tight_layout()
            
            # 更新画布
            self.canvas.draw()
            
            # 更新统计信息
            total_return = percentage_changes[-1] if percentage_changes else 0
            start_balance = total_balances[0] if total_balances else 0
            end_balance = total_balances[-1] if total_balances else 0
            
            stats_text = f"起始总资金: {start_balance:,.0f}元 | "
            stats_text += f"最终总资金: {end_balance:,.0f}元 | "
            stats_text += f"总收益率: {total_return:+.2f}%"
            
            self.stats_label.config(text=stats_text)
            self.status_var.set(f"总体分析完成 - 总收益率: {total_return:.2f}% | 数据点: {len(dates)}个")
            
        except Exception as e:
            messagebox.showerror("错误", f"总体分析失败: {str(e)}")
            self.status_var.set("总体分析失败")
    
    def analyze_account_data(self):
        """分账户分析"""
        account = self.selected_account.get()
        if not account:
            messagebox.showwarning("警告", "请选择要分析的账户")
            return
        
        self.update_chart()
    
    def update_chart(self, event=None):
        """更新图表（分账户模式）"""
        account = self.selected_account.get()
        if not account or account not in self.account_balances:
            return
        
        try:
            # 清除之前的图表
            self.axs[0].clear()
            self.axs[1].clear()
            
            balances = self.account_balances[account]
            
            # 计算收益率变化
            base_value = balances[0] if balances else 1
            percentage_changes = [(balance / base_value - 1) * 100 for balance in balances]
            
            # 绘制上方资金曲线
            self.axs[0].plot(self.dates, balances, marker='o', linewidth=2, markersize=4, 
                           color='#2ca02c', label=f'{account} 资金曲线')
            self.axs[0].set_ylabel('资金（元）', fontsize=12)
            self.axs[0].set_title(f'{account} 资金曲线', fontsize=14, fontweight='bold')
            self.axs[0].legend()
            self.axs[0].grid(True, alpha=0.3)
            self.axs[0].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
            
            # 绘制下方收益率曲线
            self.axs[1].plot(self.dates, percentage_changes, marker='o', color='red', 
                           linewidth=2, markersize=4, label='收益率变化')
            self.axs[1].set_xlabel('交易日', fontsize=12)
            self.axs[1].set_ylabel('收益率 (%)', fontsize=12)
            self.axs[1].set_title(f'{account} 收益率变化', fontsize=14, fontweight='bold')
            self.axs[1].legend()
            self.axs[1].grid(True, alpha=0.3)
            self.axs[1].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:+.2f}%'))
            
            # 设置x轴标签旋转
            plt.setp(self.axs[1].xaxis.get_majorticklabels(), rotation=45, ha='right')
            
            # 添加统计信息到图表
            if balances:
                start_balance = balances[0]
                end_balance = balances[-1]
                max_balance = max(balances)
                min_balance = min(balances)
                return_rate = percentage_changes[-1] if percentage_changes else 0
                
                stats_text = f"起始资金: {start_balance:,.0f}元\n"
                stats_text += f"最终资金: {end_balance:,.0f}元\n"
                stats_text += f"最高资金: {max_balance:,.0f}元\n"
                stats_text += f"最低资金: {min_balance:,.0f}元\n"
                stats_text += f"总收益率: {return_rate:+.2f}%"
                
                self.axs[0].text(0.02, 0.98, stats_text, transform=self.axs[0].transAxes,
                               verticalalignment='top', bbox=dict(boxstyle='round',
                               facecolor='wheat', alpha=0.8), fontsize=10)
                
                # 更新状态栏统计信息
                stats_summary = f"账户: {account} | 起始: {start_balance:,.0f}元 | 最终: {end_balance:,.0f}元 | 收益率: {return_rate:+.2f}%"
                self.stats_label.config(text=stats_summary)
            
            # 调整布局
            self.fig.tight_layout()
            
            # 更新画布
            self.canvas.draw()
            
            self.status_var.set(f"账户分析完成: {account}")
            
        except Exception as e:
            messagebox.showerror("错误", f"账户分析失败: {str(e)}")
            self.status_var.set("账户分析失败")
    
    def export_chart(self):
        """导出图表"""
        if not self.data:
            messagebox.showwarning("警告", "没有可导出的图表")
            return
        
        # 根据当前模式设置默认文件名
        mode = self.current_mode.get()
        if mode == "分账户分析" and self.selected_account.get():
            default_name = f"{self.selected_account.get()}_资金曲线"
        else:
            default_name = "总体资金曲线分析"
        
        file_path = filedialog.asksaveasfilename(
            title="导出图表",
            defaultextension=".png",
            initialfile=default_name,
            filetypes=[("PNG files", "*.png"), ("JPG files", "*.jpg"),
                      ("PDF files", "*.pdf"), ("SVG files", "*.svg")]
        )
        
        if file_path:
            try:
                self.fig.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
                messagebox.showinfo("成功", f"图表已导出到:\n{file_path}")
                self.status_var.set(f"图表已导出: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")
    
    def clear_chart(self):
        """清除图表"""
        self.axs[0].clear()
        self.axs[1].clear()
        self.canvas.draw()
        self.stats_label.config(text="")
        self.status_var.set("图表已清除")

def main():
    """主函数"""
    root = tk.Tk()
    app = ComprehensiveFundAnalyzer(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
    finally:
        plt.close('all')

if __name__ == "__main__":
    main()
