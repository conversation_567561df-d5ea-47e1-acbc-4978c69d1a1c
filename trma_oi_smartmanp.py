import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog

from time import sleep
import time
from strategyTRMA import trmastrategy
mylog.add('ema.log', encoding='utf-8')

if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    # symbol = 'SHFE.ag2103'
    symbol = 'CZCE.OI205'
    # symbol = 'DCE.pp2105'
    symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 60
    sklimit = 60
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="smartmanp,ftp123")

    trmastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
