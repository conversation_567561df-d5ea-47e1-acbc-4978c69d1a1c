from crontab import CronTab
import os
import copy
import getpass

# cron = CronTab(user=getpass.getuser())
cron = CronTab(user=True)
crons=copy.deepcopy(cron.crons)
for c in crons:
    print(c)

# cron.remove_all()
basedir=os.path.dirname(__file__)
filename='clock_alarm.py'
fufilename=os.path.join(basedir,filename)
#
# job = cron.new(command="python /home/<USER>/rsitrader/speak_test.py")
# job.minute.every(10)
# cron.write()
# #
job = cron.new(command="/usr/local/bin/python3 " + fufilename)
job.minute.every(3)
if job in crons:
    print('already done...')
else:


    cron.write()
# job.hour.on(20)
# # job.minute.on(30)
# # job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
# job.dow.during('mon', 'fri')
# # cron.remove_all()
# cron.write()



