from pytdx.hq import TdxHq_API
import pandas as pd
import time

# 创建通达信行情API实例
api = TdxHq_API()

# 期货合约配置（根据实际需要修改）
contracts = [
    {'market': 30, 'code': 'RB2310'},  # 上海期货交易所螺纹钢
    {'market': 30, 'code': 'AG2312'},  # 上海期货交易所白银
    {'market': 28, 'code': 'TA309'},  # 郑州商品交易所PTA
    {'market': 29, 'code': 'M2309'},  # 大连商品交易所豆粕
]

try:
    # 连接到服务器（IP和端口可能需要定期更新）
    if api.connect('**************', 7709):
        print("服务器连接成功")

        while True:
            for contract in contracts:
                market = contract['market']
                code = contract['code']

                # 获取单个合约行情
                data = api.get_instrument_quote(market, code)

                if data:
                    # 转换为DataFrame格式
                    df = api.to_df(data)

                    # 提取关键字段（根据返回字段调整）
                    print(f"\n合约：{code}")
                    print(f"时间：{df['datetime'][0]}")
                    print(f"最新价：{df['price'][0]}")
                    print(f"买一价：{df['bid1'][0]}")
                    print(f"卖一价：{df['ask1'][0]}")
                    print(f"成交量：{df['vol'][0]}手")
                else:
                    print(f"⚠️ 未获取到 {code} 数据")

                time.sleep(1)  # 间隔防止请求过快

            print("\n" + "=" * 50 + " 等待更新...")
            time.sleep(5)  # 整体更新间隔

    else:
        print("服务器连接失败")

except Exception as e:
    print(f"程序异常：{e}")

finally:
    api.disconnect()
    print("连接已关闭")