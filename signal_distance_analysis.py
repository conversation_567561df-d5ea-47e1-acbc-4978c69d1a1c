import pickle
import os
import sys

from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tqsdk.tafunc import time_to_str
from MyTT import CROSS, REF, EMA
from myfunction import be_apart_from
def cal_distlist(siglist):
    distlist = []
    count = 0
    for i in range(len(siglist)):
        if not siglist[i]:
            count += 1
        elif siglist[i]:
            distlist.append(count)
            count = 0

    return distlist

def MaCrossCaculate(bars, period=13, printable=False):
    C = bars.close
    SYMBOL = bars.symbol.iloc[0]
    interval = bars.duration.iloc[0]

    trmac = ma(C, period)
    ups = crossup(C, trmac)
    dns = crossdown(C, trmac)
    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)
    if bkdist is None or skdist is None:
        print('please check...')
        cont = input('continue or not?')
        if cont == 'Y':
            return 0
        else:
            sys.exit(0)

    if bkdist > skdist:
        signalnow = 'SK'
        distnow = skdist
    else:
        signalnow = 'BK'
        distnow = bkdist
    updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    signalprice = C.iloc[-distnow]
    # sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
    # print('时间:', updatetime, SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:',
    #       C.iloc[-1], '信号盈亏:', sigfloatprofit)
    uplist = ups.tolist()
    average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))

    if printable:
        print(SYMBOL, interval, period, '平均信号距离：', average_signal_distance, '最大信号距离:',max(cal_distlist(uplist)))

    # print(max(cal_distlist(uplist)),'upsignal:  ', ups.iloc[-40:].tolist())
    # print(max(cal_distlist(dnslist)),'downsignal:', dns.iloc[-40:].tolist())


    # updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    # signal = {'合约': SYMBOL, '周期': interval, '时间': updatetime, '当前信号': signalnow,
    #           '持续周期': distnow, '信号价格': signalprice, '现价': C.iloc[-1],
    #           '信号盈亏': sigfloatprofit}

    return average_signal_distance



def zjtj(bars, period=8, printable=True):
    CLOSE = bars.close
    SYMBOL = bars.iloc[0].symbol
    interval = bars.iloc[0].duration

    VAR1 = EMA(EMA(CLOSE, period), period)
    trendline = (VAR1 - REF(VAR1, 1)) / REF(VAR1, 1) * 1000
    # A10=CROSS(trendline,0)

    upslist = list(CROSS(trendline, 0))

    dnslist = list(CROSS(0, trendline))
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)

    if bkdist > skdist:
        signalnow = 'SK'
        distnow = skdist
    else:
        signalnow = 'BK'
        distnow = bkdist

    signalprice = CLOSE.iloc[-distnow]
    sigfloatprofit = CLOSE.iloc[-1] - signalprice if signalnow == 'BK' else signalprice - CLOSE.iloc[-1]
    # print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', CLOSE.iloc[-1], '信号盈亏:', sigfloatprofit)
    average_signal_distance = int(len(upslist) / (upslist.count(1) * 2))
    # print('平均信号距离：', average_signal_distance)
    # print('upsignal:  ', upslist[-40:])
    # print('downsignal:', dnslist[-40:])
    #
    # signal = {'合约': SYMBOL, '周期': interval,'时间': time_to_str(bars.datetime.iloc[-1]), '当前信号': signalnow,
    #           '持续周期': distnow, '信号价格': signalprice, '现价': CLOSE.iloc[-1],
    #           '信号盈亏': sigfloatprofit}
    #
    # average_signal_distance = int(len(upslist) / (upslist.count(1) * 2))
    if printable:
        print(SYMBOL, interval, period, '平均信号距离：', average_signal_distance)


    return average_signal_distance






def main():
    files = os.listdir()
    deadproductid = ['bb', 'PM', 'JR', 'RI', 'LR', 'RS', 'wr', 'WH', 'ZC']

    for f in files:
        if f.endswith('pkl') and len(f.split('barsdata')[0]) <= 2:
            # print(f.split('barsdata'))
            if f.split('barsdata')[0] in deadproductid:
                continue

            else:
                with open(f, 'rb') as barfile:
                    data = pickle.load(barfile)
                    distancelist = []
                    for bars in data:
                        # distance = MaCrossCaculate(bars, 17, False)
                        distance = zjtj(bars)
                        distancelist.append(distance)

                    print(bars.symbol.iloc[0], min(distancelist), max(distancelist), max(distancelist)-min(distancelist),
                          )


if __name__ == "__main__":
    with open('OIbarsdata.pkl', 'rb') as f:
        data = pickle.load(f)
        for bars in data:
            distance = MaCrossCaculate(bars, 13, True)
            # zjtj(bars, period=60)
    # main()