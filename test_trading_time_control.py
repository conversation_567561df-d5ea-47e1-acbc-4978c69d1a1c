"""
测试交易时间控制功能
"""

import sys
import time
from datetime import datetime, time as dt_time
from TimeRoseMA_cross_ag_MultiTimeFrames import TradingTimeController


def test_time_functions():
    """测试时间判断函数"""
    print("=" * 50)
    print("测试时间判断函数")
    print("=" * 50)
    
    controller = TradingTimeController()
    
    # 获取当前时间
    now = datetime.now()
    current_time = now.time()
    current_weekday = now.weekday()
    
    print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"星期: {['周一', '周二', '周三', '周四', '周五', '周六', '周日'][current_weekday]}")
    print(f"是否交易日: {controller.is_trading_day()}")
    print(f"是否应该启动: {controller.should_start_trading()}")
    print(f"是否应该停止: {controller.should_stop_trading()}")
    print(f"是否在交易时段: {controller.is_in_trading_session()}")
    
    # 测试特定时间点
    test_times = [
        dt_time(20, 50),  # 启动前
        dt_time(20, 55),  # 启动时间
        dt_time(21, 0),   # 夜盘开始
        dt_time(23, 30),  # 夜盘中
        dt_time(2, 30),   # 夜盘结束
        dt_time(9, 0),    # 日盘开始
        dt_time(12, 0),   # 日盘中
        dt_time(15, 0),   # 日盘结束
        dt_time(15, 5),   # 停止时间
        dt_time(16, 0),   # 收盘后
    ]
    
    print(f"\n时间点测试:")
    for test_time in test_times:
        # 临时修改当前时间进行测试
        original_time = datetime.now().time()
        
        print(f"  {test_time.strftime('%H:%M')} - ", end="")
        
        # 这里只是模拟，实际需要修改系统时间或使用mock
        if test_time >= dt_time(20, 55) and test_time <= dt_time(21, 0):
            print("应该启动")
        elif test_time >= dt_time(15, 5) and test_time <= dt_time(15, 10):
            print("应该停止")
        elif (test_time >= dt_time(21, 0) or test_time <= dt_time(2, 30) or 
              (test_time >= dt_time(9, 0) and test_time <= dt_time(15, 0))):
            print("交易时段")
        else:
            print("非交易时段")


def test_mock_strategy():
    """模拟策略函数"""
    print("模拟策略开始运行...")
    start_time = time.time()
    
    try:
        # 模拟策略运行（运行30秒后停止）
        while time.time() - start_time < 30:
            print(f"策略运行中... {time.time() - start_time:.1f}秒")
            time.sleep(5)
    except KeyboardInterrupt:
        print("策略被中断")
    
    print("模拟策略结束")


def test_controller_basic():
    """测试控制器基本功能"""
    print("=" * 50)
    print("测试控制器基本功能")
    print("=" * 50)
    
    controller = TradingTimeController()
    
    print("1. 测试策略启动...")
    controller.start_strategy(test_mock_strategy)
    
    # 等待一段时间
    time.sleep(10)
    
    print("2. 测试策略停止...")
    controller.stop_strategy()
    
    time.sleep(2)
    print("基本功能测试完成")


def test_schedule_setup():
    """测试调度设置"""
    print("=" * 50)
    print("测试调度设置")
    print("=" * 50)
    
    controller = TradingTimeController()
    
    print("设置调度任务...")
    controller.setup_schedule(test_mock_strategy)
    
    print("调度设置完成，运行5分钟...")
    
    # 运行调度器5分钟
    start_time = time.time()
    try:
        while time.time() - start_time < 300:  # 5分钟
            import schedule
            schedule.run_pending()
            time.sleep(30)
            print(f"调度器运行中... {(time.time() - start_time)/60:.1f}分钟")
    except KeyboardInterrupt:
        print("调度器被中断")
    
    print("调度测试完成")


def test_command_line():
    """测试命令行调用"""
    print("=" * 50)
    print("测试命令行调用")
    print("=" * 50)
    
    import subprocess
    
    test_commands = [
        ["python", "TimeRoseMA_cross_ag_MultiTimeFrames.py", "ag", "schedule"],
        ["python", "TimeRoseMA_cross_ag_MultiTimeFrames.py", "rb", "schedule"],
    ]
    
    for cmd in test_commands:
        print(f"测试命令: {' '.join(cmd)}")
        try:
            # 启动进程
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 等待5秒
            time.sleep(5)
            
            # 终止进程
            process.terminate()
            
            # 获取输出
            stdout, stderr = process.communicate(timeout=5)
            
            print("输出:")
            print(stdout[:300] + "..." if len(stdout) > 300 else stdout)
            
            if stderr:
                print("错误:")
                print(stderr[:300] + "..." if len(stderr) > 300 else stderr)
                
        except Exception as e:
            print(f"测试失败: {e}")
            if 'process' in locals():
                process.kill()


def show_usage_examples():
    """显示使用示例"""
    print("=" * 50)
    print("交易时间控制使用示例")
    print("=" * 50)
    
    examples = [
        "# 启动定时策略（20:55启动，15:05关闭）",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py ag schedule",
        "",
        "# 启动不同品种的定时策略",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py rb schedule",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py cu schedule",
        "",
        "# 使用自定义认证的定时策略",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py ag schedule \"user,pass\"",
        "",
        "# 立即启动策略（不等待定时）",
        "python TimeRoseMA_cross_ag_MultiTimeFrames.py ag independent",
        "",
        "# 查看日志文件",
        "tail -f trading_schedule.log",
    ]
    
    for example in examples:
        print(example)
    
    print("\n时间设置:")
    print("- 启动时间: 每个交易日 20:55")
    print("- 停止时间: 每个交易日 15:05")
    print("- 交易时段: 21:00-02:30 (夜盘), 09:00-15:00 (日盘)")
    print("- 非交易日: 周六、周日自动跳过")


def main():
    """主测试函数"""
    print("交易时间控制功能测试")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        
        if test_type == "time":
            test_time_functions()
        elif test_type == "basic":
            test_controller_basic()
        elif test_type == "schedule":
            test_schedule_setup()
        elif test_type == "cmd":
            test_command_line()
        elif test_type == "usage":
            show_usage_examples()
        else:
            print("可用测试:")
            print("  python test_trading_time_control.py time      # 时间判断测试")
            print("  python test_trading_time_control.py basic     # 基本功能测试")
            print("  python test_trading_time_control.py schedule  # 调度测试")
            print("  python test_trading_time_control.py cmd       # 命令行测试")
            print("  python test_trading_time_control.py usage     # 使用示例")
    else:
        # 默认运行所有测试
        show_usage_examples()
        test_time_functions()
        
        choice = input("\n是否继续进行功能测试? [y/N]: ").strip().lower()
        if choice in ['y', 'yes']:
            test_controller_basic()


if __name__ == "__main__":
    main()
