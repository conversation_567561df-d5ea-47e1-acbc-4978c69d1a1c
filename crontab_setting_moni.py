import os
import getpass
from crontab import CronTab
import copy

username = getpass.getuser()
basedir = os.path.dirname(__file__)

cron = CronTab(user=username)
crons = copy.deepcopy(cron.crons)

# cron.remove_all()
# cron.write()

#
# job = cron.new(command="python /home/<USER>/rsitrader/speak_test.py")
# job.minute.every(10)
# cron.write()
# #
# job = cron.new(command="python /home/<USER>/rsitrader/clock_alarm.py")
# job.minute.every(15)
# job.hour.on(20)
# # job.minute.on(30)
# # job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
# job.dow.during('mon', 'fri')
# # cron.remove_all()
# cron.write()

programs = ['TimeRoseMA_cross_oi_1m_speak.py',
            'TimeRoseMA_cross__oi_3m_speak.py',
            'TimeRoseMA_cross__oi_5m_speak.py',
            'TimeRoseMA_cross__rb_5m_speak.py',
            ]

for p in programs:
    prejob = "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9 "
    # jobstr = prejob + '; python3 ' + os.path.join(basedir, p)
    jobpro = cron.new(command=prejob)

    jobpro.hour.on(20)
    jobpro.minute.on(40)
    jobpro.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    if jobpro in crons:
        continue
    else:
        cron.write()

    jobpro.hour.on(8)
    jobpro.minute.on(40)
    jobpro.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    if jobpro in crons:
        continue
    else:
        cron.write()


    jobstr = "/home/<USER>/miniconda3/bin/python " + os.path.join(basedir, p)
    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.hour.on(8)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    if job in crons:
        continue
    else:
        cron.write()

    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.hour.on(8)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    if job in crons:
        print('8.45 already done. pass')
        continue
    else:
        print('write to the file.')
        cron.write()


    # job = cron.new(command=jobstr)
    # job.hour.on(8)
    # job.minute.on(45)
    # job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    # # job.dow.on()
    # cron.remove_all()
    # cron.write()
#
# for p in programs:
#     prejob = "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
#     jobstr = prejob + '; python ' + os.path.join(basedir, p)
#     job = cron.new(command=jobstr)
#     # job.minute.every(15)
#     job.hour.on(8)
#     job.minute.on(45)
#     job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
#     # job.dow.on()
#     # cron.remove_all()
#     cron.write()

for p in programs:
    prejob = "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = "/usr/local/bin/python3 " + os.path.join(basedir, p)
    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.every_reboot()
    if not job in crons:
        print('not done, write the job.')
        cron.write()
    else:
        print('already done, pass.')

# deleter cron item test
# cron = CronTab(user=getpass.getuser())
# itercron=cron.find_command('tgjysss')
# for job in itercron:
#     print(job)
#     cron.remove(job)
# cron.write()
