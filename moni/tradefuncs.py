# coding: utf-8
# 交易相关的函数
from loguru import logger as mylog
mylog.add('tradefunc' + '.log', encoding='utf-8')

def BK(api, symbol, order_price, volume):
    print(symbol, volume)
    acct = api.get_account()
    order_price * volume
    if acct.available >6000:
        orderid = api.insert_order(symbol, direction='BUY', offset='OPEN', limit_price=order_price, volume=volume)
        print('tradefunc:', 'BK', symbol, order_price, volume)
        return orderid
    else:
        print('not enough fund.')
        return None


def SP(api, symbol, order_price, volume, today=False):
    offset = 'CLOSE'
    orderid = api.insert_order(symbol, direction='SELL', offset=offset, limit_price=order_price, volume=volume)
    # exchangeid=symbol.split('.')[0]
    #
    # position = api.get_position(symbol)
    # bkvol = position.pos_long
    # bkvol_yd = position.pos_long_his
    # bkvol_td = position.pos_long_today
    # bkfreeze = position.volume_long_frozen
    # orderid = 0
    # bkvolavailable =bkvol - bkfreeze
    #
    #
    # if volume >= bkvolavailable and bkvolavailable>0:
    #     mylog.info(['平仓数量大于持仓数量。','bkvol:', bkvol, 'bkfreeze:', bkfreeze])
    #     offset = 'CLOSETODAY'
    #     orderid = api.insert_order(symbol, direction='SELL', offset=offset, limit_price=order_price, volume=bkvolavailable)
    #     return orderid
    #
    # if today:
    #     if bkvol_td-bkfreeze >= volume:
    #         offset = 'CLOSETODAY'
    #         orderid = api.insert_order(symbol, direction='SELL', offset=offset, limit_price=order_price, volume=volume)
    #
    #     elif bkvol_td-bkfreeze < volume and bkvol_td > 0:
    #         if bkvol_td-bkfreeze <=0:
    #             return
    #
    #         offset = 'CLOSETODAY'
    #         orderid = api.insert_order(symbol, direction='SELL', offset=offset, limit_price=order_price, volume=bkvol_td-bkfreeze)
    #
    #         if bkvol_yd >= volume - bkvol_td:
    #             offset = 'CLOSE'
    #             orderid = api.insert_order(symbol, direction='SELL', offset=offset, limit_price=order_price, volume=bkvol_td)
    #
    #         elif bkvol_yd > 0:
    #             offset = 'CLOSE'
    #             orderid = api.insert_order(symbol, direction='SELL', offset=offset, limit_price=order_price, volume=bkvol_yd)
    #
    #         else:
    #             print(['平仓数量超过持仓数量。。。', 'bkvol:',bkvol,'freeze:', bkfreeze,  'volume:',volume])
    #             mylog.info(['平仓数量超过持仓数量。。。', 'bkvol:',bkvol,'freeze:', bkfreeze,  'volume:',volume])
    #
    # else:
    #
    #     if bkvol_yd >= volume:
    #         offset = 'CLOSE'
    #         orderid = api.insert_order(symbol, direction='SELL', offset=offset, limit_price=order_price, volume=volume)
    #
    #     else:
    #         offset = 'CLOSE'
    #         orderid = api.insert_order(symbol, direction='SELL', offset=offset, limit_price=order_price, volume=volume)
    #
    #         print(['平仓数量超过持仓数量。。。', 'bkvol:', bkvol, 'freeze:', bkfreeze, 'volume:', volume])
    #         mylog.info(['平仓数量超过持仓数量。。。', 'bkvol:', bkvol, 'freeze:', bkfreeze, 'volume:', volume])
    #         # print('平仓数量超过持仓数量。。。')
    #
    # print('tradefunc:', 'SP', symbol, order_price, volume, bkvol)
    # mylog.info(['tradefunc:', 'SP', symbol, order_price, volume, bkvol])
    #
    # if orderid:
    #     return orderid
    # else:
    #     return 0


def SK(api, symbol, order_price, volume):
    print(symbol, volume)
    acct = api.get_account()
    if acct.available>10000:
        orderid = api.insert_order(symbol, direction='SELL', offset='OPEN', limit_price=order_price, volume=volume)
        print('tradefunc:', 'SK', symbol, order_price, volume)
        return orderid
    else:
        print('not enough fund.')
        return None

def BP(api, symbol, order_price, volume, today=False):

    offset = 'CLOSE'
    orderid = api.insert_order(symbol, direction='BUY', offset=offset, limit_price=order_price, volume=volume)

    # position = api.get_position(symbol)
    # skvol = position.pos_short
    # skvol_yd = position.pos_short_his
    # skvol_td = position.pos_short_today
    # skfreeze = position.volume_short_frozen
    # orderid = 0
    # skvol_available = skvol - skfreeze
    #
    # if volume > skvol_available:
    #     print('可平数量：', skvol_available, '平仓数量:', volume, '平仓数量大于持仓数量，无法平仓。')
    #     return None
    #
    # if today:
    #     if skvol_td >= volume:
    #         offset = 'CLOSETODAY'
    #         orderid = api.insert_order(symbol, direction='BUY', offset=offset, limit_price=order_price, volume=volume)
    #
    #     elif skvol_td < volume and skvol_td > 0:
    #         offset = 'CLOSETODAY'
    #         orderid = api.insert_order(symbol, direction='BUY', offset=offset, limit_price=order_price, volume=skvol_td)
    #
    #         if skvol_yd >= volume - skvol_td:
    #             offset = 'CLOSE'
    #             orderid = api.insert_order(symbol, direction='BUY', offset=offset, limit_price=order_price, volume=skvol_td)
    #
    #         elif skvol_yd > 0:
    #             offset = 'CLOSE'
    #             orderid = api.insert_order(symbol, direction='BUY', offset=offset, limit_price=order_price, volume=skvol_yd)
    #
    #         else:
    #             print('可平数量：', skvol_available, '平仓数量:', volume, '平仓数量大于持仓数量，无法平仓。')
    #             print('平仓数量超过持仓数量。。。')
    # else:
    #
    #     if skvol_yd >= volume:
    #         offset = 'CLOSE'
    #         orderid = api.insert_order(symbol, direction='BUY', offset=offset, limit_price=order_price, volume=volume)
    #
    #     else:
    #         if skvol_td >= volume:
    #             offset = 'CLOSETODAY'
    #             orderid = api.insert_order(symbol, direction='BUY', offset=offset, limit_price=order_price, volume=volume)
    #
    #         else:
    #             offset = 'CLOSETODAY'
    #             orderid = api.insert_order(symbol, direction='BUY', offset=offset, limit_price=order_price, volume=skvol_td)
    #
    #         print('平仓数量超过持仓数量。。。')
    #         mylog.info('平仓数量超过持仓数量。。。')
    #
    # print('tradefunc:', 'BP', symbol, order_price, volume)
    # mylog.info(['tradefunc:', 'BP', symbol, order_price, volume])
    # if orderid:
    #     return orderid
    # else:
    #     return 0


def SPK(api, symbol, order_price, volume, today=False):
    acct = api.get_account()
    pos = api.get_position(symbol)
    bkvoltd = api.get_position(symbol).pos_long_today
    bkvolyd = api.get_position(symbol).pos_long_his
    orderid = None

    if today and bkvoltd >= volume:
        api.insert_order(symbol, direction='SELL', offset='CLOSETODAY', limit_price=order_price, volume=volume)
    elif bkvolyd > volume:
        api.insert_order(symbol, direction='SELL', offset='CLOSE', limit_price=order_price, volume=volume)
    if acct.available > 30000:
        print(acct.available)
        orderid = api.insert_order(symbol, direction='SELL', offset='OPEN', limit_price=order_price, volume=volume)

    return orderid


def BPK(api, symbol, order_price, volume, today=True):
    # order_price = quote.last_price
    acct = api.get_account()
    skvoltd = api.get_position(symbol).pos_short_today
    skvolyd = api.get_position(symbol).pos_short_his
    orderid = None
    if today and skvoltd >= volume:
        api.insert_order(symbol, direction='BUY', offset='CLOSETODAY', limit_price=order_price, volume=volume)
    elif skvolyd > volume:
        api.insert_order(symbol, direction='BUY', offset='CLOSE', limit_price=order_price, volume=volume)
    if acct.available > 30000:
        orderid = api.insert_order(symbol, direction='BUY', offset='OPEN', limit_price=order_price, volume=volume)

    if orderid:
        return orderid
    else:
        return None


def BARSSK():
    pass


def BARSBK():
    pass


def BARSBP():
    pass


def BARSSP():
    pass


def getLastSig():
    pass


def display_acct_pos_info(api, symbol):
    position = api.get_position(symbol)
    account = api.get_account()
    print(account.user_id, symbol, "净值: %.2f,可用: %.2f, 今多头: %d 手, 今空头: %d 手" % (account.balance, account.available, position.volume_long, position.volume_short))


def display_all_positon(api):
    position = api.get_position()
    account = api.get_account()
    userid = account.user_id
    for pos in position:
        # print(pos)
        if pos.split('.')[0] in ('CZCE', 'SHFE', 'DCE'):
            # print(position[pos])
            print(userid, pos, '昨多:', position[pos]['pos_long_his'], '今多:', position[pos]['pos_long_today'])
            print(userid, pos, '昨空:', position[pos]['pos_short_his'], '今空:', position[pos]['pos_short_today'])


# 判断是否为工作日,工作日返回1，非工作日返回0
def workDay():
    import datetime
    import time
    workTime = ['08:30:00', '15:15:00']
    dayOfWeek = datetime.datetime.now().weekday()
    # dayOfWeek = datetime.today().weekday()
    beginWork = datetime.datetime.now().strftime("%Y-%m-%d") + ' ' + workTime[0]
    endWork = datetime.datetime.now().strftime("%Y-%m-%d") + ' ' + workTime[1]
    beginWorkSeconds = time.time() - time.mktime(time.strptime(beginWork, '%Y-%m-%d %H:%M:%S'))
    endWorkSeconds = time.time() - time.mktime(time.strptime(endWork, '%Y-%m-%d %H:%M:%S'))
    if (int(dayOfWeek) in range(5)) and int(beginWorkSeconds) > 0 and int(endWorkSeconds) < 0:
        return True
    else:
        return False
