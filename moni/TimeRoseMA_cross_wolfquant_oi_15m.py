<<<<<<< HEAD
import copy
import sys

from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tradefuncs import *
from speaktextng import speak_text
from loguru import logger as mylog

from time import sleep
import time

from strategies.TimeRoseMA_cross import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount
    from datetime import date
    from accounts_zjy import cyzjy as acct


    product = 'CZCE.OI209'
    symbol=product
    interval = 60*15
    bklimit = 15
    sklimit = 15
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, product, interval, single_volume, bklimit, sklimit)


runstrategy()
=======
import copy
import sys

from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tradefuncs import *
from speaktextng import speak_text
from loguru import logger as mylog

from time import sleep
import time

from strategies.TimeRoseMA_cross import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount
    from datetime import date
    from accounts_zjy import cyzjy as acct


    product = 'CZCE.OI209'
    symbol=product
    interval = 60*15
    bklimit = 15
    sklimit = 15
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, product, interval, single_volume, bklimit, sklimit)


runstrategy()
>>>>>>> 5b12b45 (add moni)
