"""
LLT策略运行脚本
提供多种运行模式：参数优化、回测、实时交易
"""

import sys
import argparse
import logging
from llt_strategy_refactored import *
from llt_config import get_config, load_config, save_config


def run_optimization(config: TradingConfig):
    """运行参数优化"""
    logger = logging.getLogger(__name__)
    logger.info("开始参数优化模式")
    
    # 初始化数据管理器
    data_manager = DataManager(config)
    
    try:
        # 初始化API和数据
        api = data_manager.initialize_api()
        klines = data_manager.load_historical_data()
        
        # 运行优化
        optimizer = ParameterOptimizer(klines)
        results = optimizer.optimize_d_value(range(*config.optimize_d_value_range))
        
        if results:
            logger.info("\n=== 参数优化结果 ===")
            logger.info("排名 | D_VALUE | 交易次数 | 累计盈亏 | 胜率 | 收益率")
            logger.info("-" * 60)
            
            for i, result in enumerate(results, 1):
                logger.info(
                    f"{i:4d} | {result['D_VALUE']:7d} | {result['total_trades']:8d} | "
                    f"{result['total_pnl']:8.2f} | {result['win_rate']:5.1f}% | "
                    f"{result['return_rate']:6.2f}%"
                )
            
            # 保存最优配置
            best_result = results[0]
            config.d_value = best_result['D_VALUE']
            save_config(config, "llt_config_optimized.json")
            logger.info(f"\n最优参数已保存: D_VALUE={config.d_value}")
            
            return best_result
        else:
            logger.warning("优化未找到有效结果")
            return None
            
    except Exception as e:
        logger.error(f"参数优化失败: {e}")
        return None
    finally:
        if 'api' in locals():
            api.close()


def run_backtest(config: TradingConfig):
    """运行回测"""
    logger = logging.getLogger(__name__)
    logger.info("开始回测模式")
    
    data_manager = DataManager(config)
    
    try:
        # 初始化API和数据
        api = data_manager.initialize_api()
        klines = data_manager.load_historical_data()
        
        # 运行回测
        optimizer = ParameterOptimizer(klines)
        result = optimizer._run_backtest(config.alpha)
        
        logger.info("\n=== 回测结果 ===")
        logger.info(f"D_VALUE: {config.d_value}")
        logger.info(f"ALPHA: {config.alpha:.6f}")
        logger.info(f"总交易次数: {len(result['trades'])}")
        logger.info(f"累计盈亏: {result['total_pnl']:.2f}")
        logger.info(f"胜率: {result['win_rate']:.1f}%")
        logger.info(f"收益率: {result['return_rate']:.2f}%")
        logger.info(f"盈利交易: {result['winning_trades']}")
        logger.info(f"亏损交易: {result['losing_trades']}")
        
        return result
        
    except Exception as e:
        logger.error(f"回测失败: {e}")
        return None
    finally:
        if 'api' in locals():
            api.close()


def run_live_trading(config: TradingConfig):
    """运行实时交易"""
    logger = logging.getLogger(__name__)
    logger.info("开始实时交易模式")
    
    # 初始化所有组件
    data_manager = DataManager(config)
    position_manager = PositionManager(config.max_position)
    risk_manager = RiskManager(config.max_daily_loss, config.max_drawdown)
    trade_logger = TradeLogger(config.trade_log_file)
    
    try:
        # 初始化API和数据
        api = data_manager.initialize_api()
        klines = data_manager.load_historical_data()
        
        logger.info(f"使用参数: D_VALUE={config.d_value}, ALPHA={config.alpha:.6f}")
        logger.info("实时交易开始，按 Ctrl+C 停止...")
        
        # 创建并运行实时策略
        live_strategy = LLTLiveStrategy(config, api, position_manager, risk_manager, trade_logger)
        live_strategy.run()
        
    except Exception as e:
        logger.error(f"实时交易失败: {e}")
    finally:
        if 'api' in locals():
            api.close()
        trade_logger.save_trades()
        
        # 显示最终统计
        stats = trade_logger.get_statistics()
        if stats:
            logger.info("\n=== 最终统计 ===")
            logger.info(f"总交易次数: {stats['total_trades']}")
            logger.info(f"盈利交易: {stats['winning_trades']}")
            logger.info(f"亏损交易: {stats['losing_trades']}")
            logger.info(f"胜率: {stats['win_rate']:.1f}%")
            logger.info(f"累计盈亏: {stats['total_pnl']:.2f}")
            if stats['winning_trades'] > 0:
                logger.info(f"平均盈利: {stats['avg_win']:.2f}")
            if stats['losing_trades'] > 0:
                logger.info(f"平均亏损: {stats['avg_loss']:.2f}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LLT策略运行器")
    parser.add_argument('mode', choices=['optimize', 'backtest', 'live'], 
                       help='运行模式: optimize(参数优化), backtest(回测), live(实时交易)')
    parser.add_argument('--config', '-c', default='default', 
                       help='配置名称或配置文件路径')
    parser.add_argument('--symbol', '-s', help='交易合约')
    parser.add_argument('--d-value', '-d', type=int, help='D_VALUE参数')
    parser.add_argument('--volume', '-v', type=int, help='交易手数')
    parser.add_argument('--max-position', '-p', type=int, help='最大持仓')
    parser.add_argument('--log-level', '-l', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 加载配置
    if args.config.endswith('.json'):
        config = load_config(args.config)
    else:
        config = get_config(args.config)
    
    # 覆盖命令行参数
    if args.symbol:
        config.symbol = args.symbol
    if args.d_value:
        config.d_value = args.d_value
    if args.volume:
        config.volume = args.volume
    if args.max_position:
        config.max_position = args.max_position
    
    # 设置日志
    config.log_level = args.log_level
    logger = setup_logging(config.log_level)
    
    logger.info(f"LLT策略启动 - 模式: {args.mode}")
    logger.info(f"配置: {config}")
    
    # 运行对应模式
    try:
        if args.mode == 'optimize':
            run_optimization(config)
        elif args.mode == 'backtest':
            run_backtest(config)
        elif args.mode == 'live':
            run_live_trading(config)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
