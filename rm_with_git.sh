#!/bin/bash

# List of files to be removed
FILES_TO_DELETE=(
    "CZCE.OI405180.pkl"
    "CZCE.OI40560.pkl"
    "strategies/CZCE.OI309_15.pkl"
    "strategies/CZCE.OI311_15.pkl"
    "strategies/CZCE.OI40115.pkl"
    "strategies/CZCE.OI401300.pkl"
    "strategies/CZCE.OI40160.pkl"
    "strategies/CZCE.OI401900.pkl"
    "strategies/CZCE.OI401_15.pkl"
    "strategies/CZCE.OI405_15.pkl"
    "strategies/DCE.v240160.pkl"
    "strategies/barsdata60.pkl"
    "strategies/bkprices.pkl"
    "strategies/skprices.pkl"
)

# Loop through the list and remove each file using git rm
for FILE in "${FILES_TO_DELETE[@]}"; do
    git rm "$FILE" --quiet
done

echo "The specified files have been removed from the repository."


