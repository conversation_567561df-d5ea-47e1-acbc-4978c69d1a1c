from strategies.TimeRoseMA_cross_speak import ma_cross
from tqsdk import Tq<PERSON><PERSON>, TqAccount, TqKq
from datetime import date
import threading


def run_strategy_for_interval(api, symbol, interval, single_volume, bklimit, sklimit):
    """Runs the strategy for a specific interval in a separate thread."""
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


def runstrategy():
    product = 'OI'
    intervals = [60, 180, 300, 900]
    bklimit = 10
    sklimit = 10
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    # api = TqApi(TqKq(), auth="wolfquant,ftp123")
    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")  # Creating the API instance once
    symbol = api.query_cont_quotes(product_id=product)[0]  # Get the symbol here

    threads = []
    for interval in intervals:
        thread = threading.Thread(target=run_strategy_for_interval, args=(api, symbol, interval, single_volume, bklimit, sklimit))
        threads.append(thread)
        thread.start()

    # Wait for all threads to complete (optional)
    for thread in threads:
        thread.join()  # Remove if you want non-blocking strategy execution

    print("All strategies started.") # print after all thread started

if __name__ == '__main__':
    runstrategy()