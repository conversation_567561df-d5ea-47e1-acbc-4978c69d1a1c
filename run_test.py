import time
from datetime import datetime
from tqsdk import TqApi, TqKq
from strategies.TimeRoseMA_cross_speak import ma_cross

productid = 'OI'
api = TqApi(TqKq(), auth='walkquant,ftp123')
symbol = api.query_cont_quotes(product_id=productid)[0]
symbol_info = api.query_symbol_info(symbol)

tradingTime = symbol_info.trading_time_day.tolist()[0]
tradingTimeNight = symbol_info.trading_time_night.tolist()

if tradingTimeNight[0] is not None:
    tradingTimeNight = tradingTimeNight[0][0]
    tradingTime.append(tradingTimeNight)


def tradingtime(timeintervals: list):
    timenow = time.time()
    for interval in range(len(timeintervals)):
        starttime = datetime.now().strftime("%Y-%m-%d") + ' ' + tradingTime[interval][0]
        endtimet = datetime.now().strftime("%Y-%m-%d") + ' ' + tradingTime[interval][1]
        sttime = time.mktime(time.strptime(starttime, '%Y-%m-%d %H:%M:%S'))
        endtime = time.mktime(time.strptime(endtimet, '%Y-%m-%d %H:%M:%S'))
        if sttime < timenow and timenow < endtime:
            return True

    return False


def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date
    # from accounts import cyzjy as acct

    product = 'OI'
    symbol = product
    interval = 60
    bklimit = 10
    sklimit = 100
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    api = TqApi(TqKq(), auth="walkquant,ftp123")
    # api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")
    symbol = api.query_cont_quotes(product_id=product)[0]
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)
    api.close()


if __name__ == '__main__':
    import pandas as pd

    tradingtimelist = pd.read_pickle('tradingtime.pkl')

    while True:
        if tradingtime(tradingtimelist):
            print('trading time...')
            runstrategy()

        else:
            print('no trading time, sleep')

            time.sleep(60)
