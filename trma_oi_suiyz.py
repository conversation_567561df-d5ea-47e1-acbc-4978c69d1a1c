from loguru import logger as mylog
from strategyTRMA_fixed_volume_suiyz import trmastrategy

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    config = {
        'symbol': 'CZCE.OI509',
        'interval': 60,
        'bklimit': 700,
        'sklimit': 70,
        'single_volume': 1
    }

    # 交易账号设置
    api = TqApi(TqKq(), auth="suiyz,131399@21", disable_print=True)
    symbol = api.query_cont_quotes(product_id='OI')[0]
    trmastrategy(api, symbol=config['symbol'], interval=config['interval'], single_volume=config['single_volume'], bklimit=config['bklimit'], sklimit=config['sklimit'])
