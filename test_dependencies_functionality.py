#!/usr/bin/env python3
"""
依赖功能测试脚本
测试各个关键包的基本功能是否正常
"""

import sys
import traceback
from typing import Dict, List, Tuple


def test_tqsdk():
    """测试TqSDK功能"""
    try:
        from tqsdk import TqApi, TqKq
        print("✅ TqSDK导入成功")
        
        # 测试基本功能（不连接真实服务器）
        print("   - 基本类可以实例化")
        return True, "TqSDK功能正常"
    except Exception as e:
        return False, f"TqSDK测试失败: {e}"


def test_pandas_numpy():
    """测试Pandas和Numpy功能"""
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        data = np.random.randn(100, 4)
        df = pd.DataFrame(data, columns=['open', 'high', 'low', 'close'])
        
        # 基本操作
        df['ma5'] = df['close'].rolling(5).mean()
        result = df.describe()
        
        print("✅ Pandas/Numpy数据处理功能正常")
        print(f"   - 创建DataFrame: {df.shape}")
        print(f"   - 计算移动平均线: {len(df['ma5'].dropna())} 个有效值")
        return True, "数据处理功能正常"
    except Exception as e:
        return False, f"数据处理测试失败: {e}"


def test_loguru():
    """测试Loguru日志功能"""
    try:
        from loguru import logger
        
        # 测试日志记录
        logger.info("测试日志记录功能")
        logger.debug("调试信息")
        logger.warning("警告信息")
        
        print("✅ Loguru日志功能正常")
        return True, "日志功能正常"
    except Exception as e:
        return False, f"日志测试失败: {e}"


def test_zmq():
    """测试ZeroMQ功能"""
    try:
        import zmq
        
        # 创建context和socket
        context = zmq.Context()
        socket = context.socket(zmq.REQ)
        
        # 测试基本功能
        print("✅ ZeroMQ功能正常")
        print(f"   - ZMQ版本: {zmq.zmq_version()}")
        print(f"   - PyZMQ版本: {zmq.pyzmq_version()}")
        
        # 清理
        socket.close()
        context.term()
        
        return True, "ZeroMQ功能正常"
    except Exception as e:
        return False, f"ZeroMQ测试失败: {e}"


def test_websockets():
    """测试WebSockets功能"""
    try:
        import websockets
        
        print("✅ WebSockets功能正常")
        print(f"   - WebSockets版本: {websockets.__version__}")
        return True, "WebSockets功能正常"
    except Exception as e:
        return False, f"WebSockets测试失败: {e}"


def test_speech():
    """测试语音功能"""
    try:
        import pyttsx3
        
        # 初始化语音引擎
        engine = pyttsx3.init()
        
        # 获取语音属性
        voices = engine.getProperty('voices')
        rate = engine.getProperty('rate')
        
        print("✅ 语音合成功能正常")
        print(f"   - 可用语音: {len(voices) if voices else 0} 个")
        print(f"   - 语音速度: {rate}")
        
        # 测试语音（不实际播放）
        engine.say("测试语音功能")
        # engine.runAndWait()  # 注释掉避免实际播放
        
        return True, "语音功能正常"
    except Exception as e:
        return False, f"语音测试失败: {e}"


def test_crontab():
    """测试Crontab功能"""
    try:
        from crontab import CronTab
        
        # 创建crontab对象（不实际修改系统crontab）
        cron = CronTab(tab='')  # 空的crontab
        
        # 测试基本功能
        job = cron.new(command='echo test')
        job.minute.every(5)
        
        print("✅ Crontab功能正常")
        print(f"   - 创建任务: {job.command}")
        print(f"   - 调度规则: {job}")
        return True, "任务调度功能正常"
    except Exception as e:
        return False, f"Crontab测试失败: {e}"


def test_addict():
    """测试Addict功能"""
    try:
        from addict import Dict
        
        # 测试字典增强功能
        config = Dict()
        config.trading.symbol = 'SHFE.rb2501'
        config.trading.volume = 1
        config.strategy.ma_period = 13
        
        print("✅ Addict字典增强功能正常")
        print(f"   - 嵌套访问: {config.trading.symbol}")
        print(f"   - 动态创建: {config.strategy.ma_period}")
        return True, "字典增强功能正常"
    except Exception as e:
        return False, f"Addict测试失败: {e}"


def test_cloudpickle():
    """测试CloudPickle功能"""
    try:
        import cloudpickle
        import pickle
        
        # 测试序列化功能
        data = {'test': 'data', 'numbers': [1, 2, 3]}
        
        # 序列化
        serialized = cloudpickle.dumps(data)
        
        # 反序列化
        deserialized = cloudpickle.loads(serialized)
        
        assert data == deserialized
        
        print("✅ CloudPickle序列化功能正常")
        print(f"   - 原始数据: {data}")
        print(f"   - 序列化大小: {len(serialized)} 字节")
        return True, "序列化功能正常"
    except Exception as e:
        return False, f"CloudPickle测试失败: {e}"


def test_optional_packages():
    """测试可选包功能"""
    results = []
    
    # 测试matplotlib
    try:
        import matplotlib.pyplot as plt
        import numpy as np
        
        # 创建简单图表（不显示）
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        plt.figure(figsize=(6, 4))
        plt.plot(x, y)
        plt.close()  # 关闭图表避免显示
        
        results.append("✅ Matplotlib图表功能正常")
    except Exception as e:
        results.append(f"⚠️ Matplotlib不可用: {e}")
    
    # 测试PyQt5
    try:
        from PyQt5 import QtCore, QtWidgets
        results.append("✅ PyQt5图形界面功能正常")
    except Exception as e:
        results.append(f"⚠️ PyQt5不可用: {e}")
    
    return results


def main():
    """主测试函数"""
    print("🧪 开始测试依赖包功能...")
    print("=" * 60)
    
    # 定义测试函数
    tests = [
        ("TqSDK交易接口", test_tqsdk),
        ("数据处理", test_pandas_numpy),
        ("日志记录", test_loguru),
        ("ZeroMQ通信", test_zmq),
        ("WebSocket通信", test_websockets),
        ("语音合成", test_speech),
        ("任务调度", test_crontab),
        ("字典增强", test_addict),
        ("对象序列化", test_cloudpickle),
    ]
    
    # 运行测试
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试 {test_name}...")
        try:
            success, message = test_func()
            if success:
                passed += 1
                print(f"   {message}")
            else:
                failed += 1
                print(f"   ❌ {message}")
        except Exception as e:
            failed += 1
            print(f"   ❌ 测试异常: {e}")
            print(f"   详细错误: {traceback.format_exc()}")
    
    # 测试可选包
    print(f"\n🔍 测试可选包...")
    optional_results = test_optional_packages()
    for result in optional_results:
        print(f"   {result}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📦 总计: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 所有核心功能测试通过！")
        print("💡 建议:")
        print("   1. 运行实际的策略测试")
        print("   2. 检查TqSDK认证配置")
        print("   3. 测试网络连接")
        return True
    else:
        print(f"\n⚠️ 有 {failed} 个功能测试失败")
        print("💡 建议:")
        print("   1. 检查失败的包是否正确安装")
        print("   2. 查看详细错误信息")
        print("   3. 重新安装相关依赖")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
