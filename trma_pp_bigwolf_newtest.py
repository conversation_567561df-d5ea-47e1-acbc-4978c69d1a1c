import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMA import trmastrategy
from time import sleep
import time

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    config = {
        'symbol': 'DCE.pp2105',
        'interval': 15,
        'bklimit': 3000000,
        'sklimit': 3000000,
        'single_volume': 1000
    }

    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123")

    trmastrategy(api, symbol=config['symbol'], interval=config['interval'], single_volume=config['single_volume'], bklimit=config['bklimit'], sklimit=config['sklimit'])
