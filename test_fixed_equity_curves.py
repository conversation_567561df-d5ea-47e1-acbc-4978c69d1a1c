"""
测试修复后的资金曲线功能
验证真实权益曲线数据的获取和绘制
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from loguru import logger
from llt_multi_contract_analyzer import (
    ContractConfig, ContractAnalyzer, ContractAnalysisResult,
    MultiContractAnalyzer, create_analysis_config
)

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    n_points = 300
    
    # 创建有趋势的价格数据
    base_price = 100
    trend = np.linspace(0, 15, n_points)  # 上升趋势
    noise = np.random.randn(n_points) * 1.5  # 噪声
    prices = base_price + trend + noise
    
    # 确保价格为正
    prices = np.maximum(prices, 50)
    
    dates = pd.date_range('2024-01-01', periods=n_points, freq='5min')
    
    klines_data = pd.DataFrame({
        'datetime': dates,
        'open': prices + np.random.randn(n_points) * 0.1,
        'high': prices + np.abs(np.random.randn(n_points)) * 0.5,
        'low': prices - np.abs(np.random.randn(n_points)) * 0.5,
        'close': prices,
        'volume': np.random.randint(100, 1000, n_points)
    })
    
    return klines_data


def test_single_contract_equity_curve():
    """测试单个合约的权益曲线生成"""
    print("=" * 60)
    print("测试单个合约权益曲线生成")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_test_data()
        print(f"测试数据: {len(klines_data)}条K线")
        
        # 创建合约配置
        contract = ContractConfig("TEST.symbol", "测试合约", "TEST")
        
        # 创建分析器
        analyzer = ContractAnalyzer(contract, klines_data, "profit_only")
        
        # 测试回测
        d_value = 20
        alpha = 2 / (d_value + 1)
        
        print(f"测试参数: D_VALUE={d_value}, ALPHA={alpha:.6f}")
        
        # 运行回测
        backtest_result = analyzer._run_backtest(alpha)
        
        print(f"\n📊 回测结果:")
        print(f"收益率: {backtest_result['return_rate']:.2f}%")
        print(f"交易次数: {backtest_result['total_trades']}")
        print(f"胜率: {backtest_result['win_rate']:.1f}%")
        
        # 检查权益曲线
        equity_curve = backtest_result.get('equity_curve', [])
        print(f"\n📈 权益曲线数据:")
        print(f"数据点数: {len(equity_curve)}")
        
        if len(equity_curve) > 0:
            print(f"起始值: {equity_curve[0]:.2f}")
            print(f"结束值: {equity_curve[-1]:.2f}")
            print(f"最大值: {max(equity_curve):.2f}")
            print(f"最小值: {min(equity_curve):.2f}")
            print(f"前10个点: {[f'{x:.2f}' for x in equity_curve[:10]]}")
            print("✅ 权益曲线数据正常")
        else:
            print("❌ 权益曲线数据为空")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_analysis_result_with_equity_curve():
    """测试分析结果包含权益曲线"""
    print("=" * 60)
    print("测试分析结果包含权益曲线")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_test_data()
        contract = ContractConfig("TEST.symbol", "测试合约", "TEST")
        
        # 创建分析器
        analyzer = ContractAnalyzer(contract, klines_data, "profit_only")
        
        # 运行参数优化
        print("🔄 开始参数优化...")
        optimization_results = analyzer.optimize_parameters(range(15, 26))  # 测试11个参数
        
        if optimization_results:
            print(f"✅ 优化完成，获得 {len(optimization_results)} 个结果")
            
            # 生成分析结果
            analysis_result = analyzer.generate_analysis_result(optimization_results)
            
            if analysis_result:
                print(f"\n📊 分析结果:")
                print(f"最优D_VALUE: {analysis_result.best_d_value}")
                print(f"收益率: {analysis_result.return_rate:.2f}%")
                print(f"交易次数: {analysis_result.total_trades}")
                
                # 检查权益曲线数据
                print(f"\n📈 权益曲线检查:")
                print(f"权益曲线点数: {len(analysis_result.equity_curve)}")
                print(f"时间点数: {len(analysis_result.equity_dates)}")
                
                if len(analysis_result.equity_curve) > 0:
                    print(f"权益曲线范围: {min(analysis_result.equity_curve):.2f} ~ {max(analysis_result.equity_curve):.2f}")
                    print("✅ 分析结果包含权益曲线数据")
                else:
                    print("❌ 分析结果缺少权益曲线数据")
                    return False
                
                if len(analysis_result.equity_dates) > 0:
                    print(f"时间范围: {analysis_result.equity_dates[0]} ~ {analysis_result.equity_dates[-1]}")
                    print("✅ 分析结果包含时间数据")
                else:
                    print("❌ 分析结果缺少时间数据")
                    return False
                
                return True
            else:
                print("❌ 生成分析结果失败")
                return False
        else:
            print("❌ 参数优化失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multi_contract_analysis():
    """测试多合约分析的权益曲线"""
    print("=" * 60)
    print("测试多合约分析权益曲线")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建多个测试合约
        contracts = [
            ContractConfig("TEST.contract1", "测试合约1", "TEST"),
            ContractConfig("TEST.contract2", "测试合约2", "TEST"),
            ContractConfig("TEST.contract3", "测试合约3", "TEST"),
        ]
        
        # 创建分析配置
        config = create_analysis_config(
            custom_contracts=contracts,
            d_value_range=(20, 26),  # 小范围测试
            kline_data_length=200,   # 少量数据
            output_dir="test_equity_curves"
        )
        
        print(f"🔄 开始多合约分析...")
        print(f"合约数量: {len(config.contracts)}")
        
        # 注意：这里不能真正运行完整分析，因为需要真实的API连接
        # 但我们可以验证配置是否正确
        
        analyzer = MultiContractAnalyzer(config, "profit_only")
        print(f"✅ 多合约分析器创建成功")
        print(f"回测模式: {analyzer.backtest_mode}")
        
        # 模拟分析结果
        mock_results = []
        for i, contract in enumerate(contracts):
            # 创建模拟的权益曲线数据
            equity_curve = [0.0] + [10.0 * (i+1) * j for j in range(1, 51)]  # 50个点
            equity_dates = [datetime.now() + timedelta(minutes=5*j) for j in range(51)]
            
            result = ContractAnalysisResult(
                contract=contract,
                best_d_value=20 + i,
                best_alpha=0.05,
                total_trades=30 + i*10,
                total_pnl=500.0 + i*100,
                win_rate=80.0 + i*5,
                return_rate=5.0 + i*2,
                winning_trades=25 + i*8,
                losing_trades=5 + i*2,
                avg_win=20.0,
                avg_loss=-10.0,
                profit_factor=2.0,
                max_drawdown=100.0,
                sharpe_ratio=1.0,
                analysis_period_days=30,
                data_quality_score=95.0,
                equity_curve=equity_curve,
                equity_dates=equity_dates
            )
            
            mock_results.append(result)
        
        print(f"\n📊 模拟分析结果:")
        for i, result in enumerate(mock_results, 1):
            print(f"  {i}. {result.contract.symbol}: {result.return_rate:+.2f}% "
                 f"(权益点数: {len(result.equity_curve)})")
        
        # 测试绘制权益曲线
        print(f"\n📈 测试权益曲线绘制...")
        
        # 使用模拟结果测试绘图功能
        from llt_multi_contract_analyzer import plot_equity_curves_from_results
        plot_equity_curves_from_results(mock_results, "test_equity_output", top_n=3)
        
        print(f"✅ 权益曲线绘制完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("修复后资金曲线功能测试")
    print("=" * 80)
    
    tests = [
        ("单合约权益曲线生成", test_single_contract_equity_curve),
        ("分析结果包含权益曲线", test_analysis_result_with_equity_curve),
        ("多合约分析权益曲线", test_multi_contract_analysis),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:25}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 修复后资金曲线功能测试成功！")
        print("\n📋 现在可以获取真实的权益曲线数据:")
        print("# 分析时自动绘制真实权益曲线")
        print("python llt_multi_contract_analyzer.py --mode quick --plot-curves")
        print("")
        print("# 权益曲线将显示真实的交易盈亏变化")
        print("# 包含准确的时间轴和盈亏数据")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
