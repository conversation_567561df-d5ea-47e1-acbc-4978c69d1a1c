from strategies.TimeRoseMA_cross_speak import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date
    # from accounts import cyzjy as acct


    product = 'OI'
    # symbol='CZCE.OI30'
    interval = 60
<<<<<<< HEAD
    bklimit = 120
    sklimit = 120
=======
    bklimit = 20
    sklimit = 20
>>>>>>> 99e74d98e8652462f279e99724c448e9de4b5273
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    # api = TqApi(TqKq(), auth="wolfquant,ftp123")
    api = TqApi(TqKq(), auth="wolfquant2023,Qiai1301")
    symbol = api.query_cont_quotes(product_id=product)[0]
    symbol = "CZCE.OI501"
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


runstrategy()
