from strategies.TimeRoseMA_cross_speak import ma_cross
import pandas as pd
from binance.spot import Spot
import copy
import sys
import os
import pandas as pd
# from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from signalSend import socket, sendsignal

from time import sleep
import time
# from .paths import Paths

from MyTT import CROSS, MA

client = Spot()


# Get server timestamp
# print(client.time())
# Get klines of BTCUSDT at 1m interval
# print(client.klines("BTCUSDT", "1m"))

# Get last 10 klines of BNBUSDT at 1h interval
# print(client.klines("BTCUSDT", "1h", limit=300))
# klines = client.klines("BTCUSDT", "15m")
# df = pd.DataFrame(klines,
#                   columns=['open_time', 'open', 'high', 'low', 'close', 'volume', 'close_time', 'quote_asset_volume',
#                            'number_of_trades', 'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume',
#                            'Can be ignored'])

# df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
# df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
#
# print(df)
# new_klines = df[['open_time', 'open', 'high', 'low', 'close']]
#
# print(new_klines)


def get_ohlc(symbol):
    klines = client.klines(symbol=symbol, interval='15m')
    df = pd.DataFrame(klines, columns=['open_time', 'open', 'high', 'low', 'close', 'volume', 'close_time',
                                       'quote_asset_volume', 'number_of_trades', 'taker_buy_base_asset_volume',
                                       'taker_buy_quote_asset_volume', 'Can be ignored'])
    df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
    df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
    df[['open', 'high', 'low', 'close']] = df[['open', 'high', 'low', 'close']].astype(float)
    ohlc = df[['open_time', 'open', 'high', 'low', 'close']]
    return ohlc


def MaCrossCaculate(bars, period):
    C = bars.close
    SYMBOL = 'test'
    interval = 15

    trmac = ma(C, period)

    ups = crossup(C, trmac)
    dns = crossdown(C, trmac)
    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)
    if bkdist > skdist:
        signalnow = '空'
        distnow = skdist
    else:
        signalnow = '多'
        distnow = bkdist
    updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    signalprice = C.iloc[-distnow]

    sigfloatprofit = C.iloc[-1] - signalprice if signalnow == '多' else signalprice - C.iloc[-1]
    print('时间:', updatetime, SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice,
          '现价:',
          C.iloc[-1], '信号盈亏:', sigfloatprofit)
    uplist = ups.tolist()
    average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
    print('平均信号距离：', average_signal_distance)
    print('upsignal:  ', ups.iloc[-40:].tolist())
    print('downsignal:', dns.iloc[-40:].tolist())

    updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    signal = {'合约': SYMBOL, '周期': interval, '时间': updatetime, '当前信号': signalnow,
              '持续周期': distnow, '信号价格': signalprice, '现价': C.iloc[-1],
              '信号盈亏': sigfloatprofit}

    return signal

    # def runstrategy():
    # from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date
    from accounts_zjy import cyzjy as acct
    #
    # tqaccount="quant_tyc,Qiai1301"
    # product = 'ag'
    # symbol=product
    # interval = 60*5
    # bklimit = 6
    # sklimit = 6
    # single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    # api = TqApi(TqKq(), auth=tqaccount)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    # ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


# runstrategy()

def get_Signals_Ma(bars, period=13):
    from MyTT import CROSS, MA
    CLOSE = bars.close.values
    OPEN = bars.open.values
    HIGH = bars.high.values
    LOW = bars.low.values
    N = period

    ups = list(CROSS(CLOSE, MA(CLOSE, N)))
    dns = list(CROSS(MA(CLOSE, N), CLOSE))

    # dnsn = [-1 if x == 1 else x for x in dns]
    signals = []

    for i in range(len(ups)):
        if ups[i] == 0 and dns[i] == 0:
            signals.append(0)

        else:
            if ups[i] == 1:
                signals.append(1)
            if dns[i] == 1:
                signals.append(-1)

    # bsigs = ups.count(True)
    # ssigs = dns.count(True)
    # avgdistance = len(ups) / (bsigs + ssigs)
    return ups, dns, signals


while True:
    symbol = 'BTCUSDT'
    bars = get_ohlc(symbol)
    print(symbol)
    ups, dns, signals = get_Signals_Ma(bars, 13)
    print(ups[-40:])
    print(dns[-40:])
    print(signals[-40:])

    if signals[-1] == 1 or signals[-1] == -1:
        speak_text(symbol + '发出交易信号')
        print('信号价格:', bars.close.iloc[-1])

    symbol = 'DOGEUSDT'
    bars = get_ohlc(symbol)
    print(symbol)

    ups, dns, signals = get_Signals_Ma(bars, 13)
    print(ups[-40:])
    print(dns[-40:])
    print(signals[-40:])

    if signals[-1] == 1 or signals[-1] == -1:
        speak_text(symbol + '发出交易信号')
        print('信号价格:', bars.close.iloc[-1])

    time.sleep(60)
# MaCrossCaculate(bars, 15)
