import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog

from time import sleep
import time
from strategyEMA import emastrategy
mylog.add('ema.log', encoding='utf-8')

if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    product ='pp'
    interval = 15
    bklimit = 180
    sklimit = 180
    single_volume = 1



    # 交易账号设置
    api = TqApi(TqKq(), auth="smartmanp,ftp123")
    symbol = api.query_cont_quotes(product_id=product)[0]
    emastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
