import pandas as pd
from tqsdk import TqApi, TqAuth, TqBacktest, TqAccount
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from tqsdk import BacktestFinished

# 1. 核心LLT计算函数 (与之前相同)
def cal_LLT(price: pd.Series, alpha: float):
    """
    计算低延迟趋势线 (LLT)
    """
    LLT = []
    price_value = price.values
    if len(price_value) < 2:
        return [float('nan')] * len(price_value)

    LLT.append(price_value[0])
    LLT.append(price_value[1])

    for i, e in enumerate(price_value):
        if i > 1:
            v = (alpha - alpha ** 2 / 4) * e + (alpha ** 2 / 2) * price_value[i - 1] - \
                (alpha - 3 * (alpha ** 2) / 4) * price_value[i - 2] + 2 * \
                (1 - alpha) * LLT[i - 1] - (1 - alpha) ** 2 * LLT[i - 2]
            LLT.append(v)

    return LLT


def run_llt_backtest(symbol, kline_period, start_dt, end_dt):
    """
    执行LLT策略的回测并绘制PnL曲线。

    :param symbol: str, 合约代码 (例如 "SHFE.rb2509")
    :param kline_period: int, K线周期，单位为秒 (例如 24*60*60 表示日线)
    :param start_dt: str, 回测开始日期 (例如 "2023-01-01")
    :param end_dt: str, 回测结束日期 (例如 "2024-01-01")
    """
    # 策略参数
    D_VALUE = 60
    ALPHA = 2 / (D_VALUE + 1)
    VOLUME = 1

    # 初始化 TqApi 进行回测
    # TqAccount() 创建一个初始资金为1000万的模拟账户
    from datetime import date
    from tqsdk import TqApi, TqAuth, TqSim, TqBacktest

    api = TqApi(TqSim(), backtest=TqBacktest(start_dt=date(2018, 5, 1), end_dt=date(2025, 7, 1)),
                auth=TqAuth("walkquant", "ftp123"))
    # api = TqApi(account=TqAccount(init_balance=********),
    #             backtest=TqBacktest(start_dt=start_dt, end_dt=end_dt),
    #             auth=TqAuth("bigwolf", "ftp123"))

    print(f"开始回测: 合约={symbol}, 周期={kline_period}秒, 时间={start_dt} to {end_dt}")

    # 获取指定时间范围内的K线数据
    # 注意：data_length要足够大以覆盖整个回测期间的K线
    klines = api.get_kline_serial(symbol, duration_seconds=kline_period, data_length=5000)

    # 用于存储每个时间点的账户权益
    pnl_records = []

    # 主循环，等待API更新
    while True:
        api.wait_update()

        # 当有新的K线完成时，执行策略逻辑
        if api.is_changing(klines.iloc[-1], "datetime"):
            # 记录当前账户权益
            print(klines.iloc[-1].datetime)
            account = api.get_account()

            current_time = pd.to_datetime(klines.iloc[-1]["datetime"])
            pnl_records.append({
                "datetime": current_time,
                "pnl": account.static_balance  # 使用静态权益
            })

            # --- 交易逻辑开始 ---
            close_prices = pd.Series(klines.close)
            llt_series = cal_LLT(close_prices, ALPHA)

            if len(llt_series) < 2:
                continue

            if llt_series[-1] > llt_series[-2]:
                signal = 1
                # print(signal)
            elif llt_series[-1] < llt_series[-2]:
                signal = -1
                # print(signal)
            else:
                signal = 0
            print(signal)

            position = api.get_position(symbol)

            if signal == 1:
                if position.pos_short > 0:
                    api.close_position(symbol, offset="CLOSE", volume=position.pos_short)
                if position.pos_long == 0:
                    api.insert_order(symbol=symbol, direction="BUY", offset="OPEN", volume=VOLUME)
            elif signal == -1:
                if position.pos_long > 0:
                    api.close_position(symbol, offset="CLOSE", volume=position.pos_long)
                if position.pos_short == 0:
                    api.insert_order(symbol=symbol, direction="SELL", offset="OPEN", volume=VOLUME)
            # --- 交易逻辑结束 ---

        # 判断回测是否结束
        if BacktestFinished(api):
            break

    api.close()
    print("回测完成！")

    # 绘制PnL曲线
    if not pnl_records:
        print("没有生成PnL数据，无法绘制曲线。")
        return

    pnl_df = pd.DataFrame(pnl_records)

    # 设置中文字体，以防图表标题乱码
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    plt.figure(figsize=(15, 7))
    plt.plot(pnl_df['datetime'], pnl_df['pnl'], label='账户静态权益')
    plt.title(f'"{symbol}" PnL 曲线 ({start_dt} to {end_dt})')
    plt.xlabel("日期")
    plt.ylabel("账户静态权益 (元)")

    # 格式化X轴日期显示
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    plt.gca().xaxis.set_major_locator(mdates.MonthLocator(interval=3))  # 每3个月显示一个主刻度
    plt.gcf().autofmt_xdate()  # 自动旋转日期标签

    plt.grid(True)
    plt.legend()

    # 保存图表到文件
    pnl_chart_path = "pnl_curve.png"
    plt.savefig(pnl_chart_path)
    print(f"PnL曲线图已保存至: {pnl_chart_path}")
    # plt.show() # 如果在本地运行，可以取消此行注释以直接显示图表


# --- 回测参数设置 ---
if __name__ == "__main__":
    # 合约代码 (例如: 螺纹钢主连)
    # SYMBOL = "<EMAIL>"
    SYMBOL = "CZCE.OI509"
    # K线周期 (秒), 例如: 5分钟=5*60, 1小时=60*60, 日线=24*60*60
    KLINE_PERIOD = 60
    # 回测开始和结束日期
    START_DATE = "2025-01-01"
    END_DATE = "2025-07-01"

    # 运行回测
    run_llt_backtest(
        symbol=SYMBOL,
        kline_period=KLINE_PERIOD,
        start_dt=START_DATE,
        end_dt=END_DATE
    )