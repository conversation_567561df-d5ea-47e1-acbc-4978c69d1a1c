
from strategies.TimeRoseMA_cross import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date


    product = 'CZCE.OI501'
    symbol=product
    interval = 180
    bklimit = 17
    sklimit = 17
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="smartmanp,ftp123", disable_print=True)
    # symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, product, interval, single_volume, bklimit, sklimit)


runstrategy()
