import time

def runstrategy():
    from tqsdk import TqApi, TqKq, TqBacktest, TqAuth
    from datetime import date
    product = 'OI'
    interval = 60
    bklimit = 200
    sklimit = 200
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth=("bigwolf,ftp123"), disable_print=True)
    symbol = api.query_cont_quotes(product_id=product)[0]
    quote = api.get_quote(symbol)

    while True:
        current_time = time.localtime()
        # print(quote)
        # print(f"ask1:{quote.ask_price1}, bid1:{quote.bid_price1}, open_interest:{quote.open_interest}")
        print(f"ask1:{quote.ask_price1}, bid1:{quote.bid_price1}, open_interest:{quote.open_interest}, last_price:{quote.last_price}")
        if (current_time.tm_hour == 20 and current_time.tm_min > 58 and current_time.tm_sec > 55):
            # 计算价格
            short_price = quote.bid_price1 + 20
            long_price = quote.ask_price1 - 20

            # 下空单
            api.insert_order(symbol=symbol, direction="SELL", offset="OPEN", volume=single_volume, limit_price=short_price)
            print(f"下空单: 价格 {short_price}, 数量 {single_volume}")

            # 下多单
            api.insert_order(symbol=symbol, direction="BUY", offset="OPEN", volume=single_volume, limit_price=long_price)
            print(f"下多单: 价格 {long_price}, 数量 {single_volume}")

            print("当前时间大于20:58，退出循环")
            break

        if quote and quote.volume > 0:
            # 在这里添加你的交易逻辑
            print(f"ask1:{quote.ask_price1}, bid1:{quote.bid_price1}, open_interest:{quote.open_interest}, last_price:{quote.last_price}")

        time.sleep(1)
    api.close()

runstrategy()

'''
{'datetime': '2025-05-16 19:14:57.000000', 'ask_price1': nan, 'ask_volume1': 0, 'bid_price1': nan, 'bid_volume1': 0, 'ask_price2': nan, 'ask_volume2': 0, 'bid_price2': nan, 'bid_volume2': 0, 'ask_price3': nan, 'ask_volume3': 0, 'bid_price3': nan, 'bid_volume3': 0, 'ask_price4': nan, 'ask_volume4': 0, 'bid_price4': nan, 'bid_volume4': 0, 'ask_price5': nan, 'ask_volume5': 0, 'bid_price5': nan, 'bid_volume5': 0, 'last_price': nan, 'highest': nan, 'lowest': nan, 'open': nan, 'close': nan, 'average': nan, 'volume': 0, 'amount': 0, 'open_interest': 287993, 'settlement': nan, 'upper_limit': 10011, 'lower_limit': 8527, 'pre_open_interest': 287993, 'pre_settlement': 9269, 'pre_close': 9277, 'price_tick': 1.0, 'price_decs': 0, 'volume_multiple': 10.0, 'max_limit_order_volume': 1000, 'max_market_order_volume': 200, 'min_limit_order_volume': 1, 'min_market_order_volume': 1, 'open_max_market_order_volume': 200, 'open_max_limit_order_volume': 1000, 'open_min_market_order_volume': 1, 'open_min_limit_order_volume': 1, 'underlying_symbol': '', 'strike_price': nan, 'ins_class': 'FUTURE', 'instrument_id': 'CZCE.OI509', 'instrument_name': '菜油2509', 'exchange_id': 'CZCE', 'expired': False, 'trading_time': {"day": [["09:00:00", "10:15:00"], ["10:30:00", "11:30:00"], ["13:30:00", "15:00:00"]], "night": [["21:00:00", "23:00:00"]]}, 'expire_datetime': 1757660400.0, 'delivery_year': 2025, 'delivery_month': 9, 'last_exercise_datetime': nan, 'exercise_year': 0, 'exercise_month': 0, 'option_class': '', 'exercise_type': '', 'product_id': 'OI', 'iopv': nan, 'public_float_share_quantity': 0, 'stock_dividend_ratio': [], 'cash_dividend_ratio': [], 'expire_rest_days': 119, 'categories': [{'id': 'GREASE', 'name': '油脂油料'}], 'position_limit': 28799, 'commission': 2.0, 'margin': 4634.5}Traceback (most recent call last):
'''