"""
测试最终改进
验证************合约和首次绘制功能
"""

import sys
import os

def test_contract_correction():
    """测试合约代码修正"""
    print("=" * 60)
    print("测试合约代码修正")
    print("=" * 60)
    
    try:
        # 检查程序文件
        program_file = "波动交易GUI.py"
        with open(program_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查默认合约设置
        if 'self.symbol = "<EMAIL>"' in content:
            print("✓ 默认合约已更新为 <EMAIL>")
        else:
            print("✗ 默认合约未正确更新")
            return False
        
        # 检查界面默认值
        if 'self.symbol_var = tk.StringVar(value="<EMAIL>")' in content:
            print("✓ 界面默认合约已更新为 <EMAIL>")
        else:
            print("✗ 界面默认合约未正确更新")
            return False
        
        # 检查常用合约列表
        if '"<EMAIL>"' in content and 'contracts = [' in content:
            print("✓ 常用合约列表已包含 <EMAIL>")
        else:
            print("✗ 常用合约列表未正确更新")
            return False
        
        print("✓ 合约代码修正验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 合约代码测试失败: {e}")
        return False

def test_initial_plot_feature():
    """测试首次绘制功能"""
    print("=" * 60)
    print("测试首次绘制功能")
    print("=" * 60)
    
    try:
        # 检查程序文件
        program_file = "波动交易GUI.py"
        with open(program_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查initial_plot方法
        if 'def initial_plot(self, df):' in content:
            print("✓ initial_plot 方法已添加")
        else:
            print("✗ initial_plot 方法未找到")
            return False
        
        # 检查首次绘制调用
        if 'self.initial_plot(test_data)' in content:
            print("✓ 首次绘制调用已添加")
        else:
            print("✗ 首次绘制调用未找到")
            return False
        
        # 检查首次绘制相关日志
        initial_plot_logs = [
            '开始首次绘制图表',
            '计算技术指标',
            '绘制K线图',
            '绘制技术指标',
            '首次图表绘制完成'
        ]
        
        for log_msg in initial_plot_logs:
            if log_msg in content:
                print(f"✓ 首次绘制日志存在: {log_msg}")
            else:
                print(f"✗ 首次绘制日志缺失: {log_msg}")
                return False
        
        print("✓ 首次绘制功能验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 首次绘制功能测试失败: {e}")
        return False

def test_log_optimization():
    """测试日志优化"""
    print("=" * 60)
    print("测试日志优化")
    print("=" * 60)
    
    try:
        # 检查程序文件
        program_file = "波动交易GUI.py"
        with open(program_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查日志频率控制
        frequency_controls = [
            'frame % 20 == 0',
            '减少日志频率',
            '减少警告频率',
            '减少频率'
        ]
        
        found_controls = 0
        for control in frequency_controls:
            if control in content:
                found_controls += 1
                print(f"✓ 日志频率控制: {control}")
        
        if found_controls >= 2:
            print("✓ 日志频率控制已实现")
        else:
            print("✗ 日志频率控制不足")
            return False
        
        # 检查简化的绘制方法
        simplified_features = [
            '减少调试日志',
            '只在首次或偶尔记录',
            'return'  # 简化的返回逻辑
        ]
        
        for feature in simplified_features[:2]:  # 检查前两个
            if feature in content:
                print(f"✓ 日志简化: {feature}")
            else:
                print(f"✗ 日志简化缺失: {feature}")
        
        print("✓ 日志优化验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 日志优化测试失败: {e}")
        return False

def test_contract_format_explanation():
    """测试合约格式说明"""
    print("=" * 60)
    print("合约格式说明")
    print("=" * 60)
    
    print("<EMAIL> 格式解析:")
    print("• KQ: 快期数据源")
    print("• i: 铁矿石品种代码")
    print("• @: 分隔符")
    print("• CZCE: 郑州商品交易所")
    print("• OI: 菜籽油品种")
    print()
    
    print("对比分析:")
    contracts = [
        ("KQ.i@OI", "❌ 缺少交易所信息", "无法获取数据"),
        ("<EMAIL>", "✅ 完整格式", "可以正常获取数据"),
        ("CZCE.OI509", "✅ 直接合约", "可以获取特定月份数据")
    ]
    
    print(f"{'合约代码':<15} {'格式状态':<15} {'数据获取'}")
    print("-" * 50)
    
    for contract, status, result in contracts:
        print(f"{contract:<15} {status:<15} {result}")
    
    print()
    print("推荐使用:")
    print("1. <EMAIL> - 快期指数，包含完整交易所信息")
    print("2. CZCE.OI509 - 直接合约，2025年9月菜籽油")
    
    return True

def show_workflow_explanation():
    """显示工作流程说明"""
    print("=" * 60)
    print("优化后的工作流程")
    print("=" * 60)
    
    print("启动分析流程:")
    print("1. 用户点击'开始分析'")
    print("2. 验证参数设置")
    print("3. 创建数据分析器")
    print("4. 连接TQ API")
    print("5. 测试数据获取")
    print("6. 🆕 首次绘制图表 (新增)")
    print("7. 启动实时更新")
    print("8. 开始动画循环")
    print()
    
    print("首次绘制优势:")
    print("• 立即显示图表，无需等待")
    print("• 用户可以马上看到数据")
    print("• 验证数据和指标计算")
    print("• 提供即时反馈")
    print()
    
    print("实时更新优化:")
    print("• 减少日志输出频率 (每20帧记录一次)")
    print("• 简化绘制过程日志")
    print("• 保留重要错误信息")
    print("• 提高界面响应速度")
    print()
    
    print("日志级别建议:")
    print("• 开发调试: 选择'所有'或'DEBUG'")
    print("• 正常使用: 选择'INFO'")
    print("• 问题排查: 选择'WARNING'或'ERROR'")

def main():
    """主测试函数"""
    print("最终改进测试")
    print("=" * 80)
    
    tests = [
        ("合约代码修正", test_contract_correction),
        ("首次绘制功能", test_initial_plot_feature),
        ("日志优化", test_log_optimization),
        ("合约格式说明", test_contract_format_explanation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示工作流程说明
    show_workflow_explanation()
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 最终改进完成！")
        print("\n主要改进:")
        print("✓ 修正合约代码为 <EMAIL>")
        print("✓ 添加首次绘制功能")
        print("✓ 优化日志输出频率")
        print("✓ 简化绘制过程日志")
        print("✓ 提高界面响应速度")
        print("\n现在程序特点:")
        print("• 使用正确的合约格式")
        print("• 数据获取后立即显示图表")
        print("• 实时更新时减少日志干扰")
        print("• 保持重要信息的可见性")
        print("• 提供流畅的用户体验")
        print("\n使用建议:")
        print("1. 启动程序: python 波动交易GUI.py")
        print("2. 确认合约: <EMAIL>")
        print("3. 点击开始分析")
        print("4. 观察首次图表显示")
        print("5. 监控实时更新")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
