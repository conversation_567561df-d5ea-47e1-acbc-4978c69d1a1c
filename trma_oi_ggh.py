from loguru import logger as mylog
from strategyTRMA_fixed_volume import trmastrategy

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    config = {
        'symbol': 'CZCE.OI301',
        'interval': 60,
        'bklimit': 300,
        'sklimit': 300,
        'single_volume': 1
    }

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301", disable_print=True)
    symbol = api.query_cont_quotes(product_id='OI')[0]
    trmastrategy(api, symbol=symbol, interval=config['interval'], single_volume=config['single_volume'], bklimit=config['bklimit'], sklimit=config['sklimit'])
