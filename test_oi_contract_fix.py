"""
测试菜籽油主力合约获取修复效果
"""

from multi_contract_config import MainContractFetcher, get_current_main_contracts
from loguru import logger

def test_oi_contract():
    """专门测试菜籽油合约获取"""
    print("=" * 60)
    print("测试菜籽油主力合约获取")
    print("=" * 60)
    
    # 设置详细日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='DEBUG', colorize=True)
    
    try:
        with MainContractFetcher() as fetcher:
            print("\n1. 测试单独获取菜籽油主力合约:")
            
            # 测试菜籽油
            oi_contract = fetcher.get_main_contract("CZCE", "OI")
            if oi_contract:
                print(f"✅ 菜籽油主力合约: {oi_contract}")
            else:
                print("❌ 菜籽油主力合约获取失败")
            
            print("\n2. 测试沪铜主力合约（对比）:")
            
            # 测试沪铜（对比）
            cu_contract = fetcher.get_main_contract("SHFE", "cu")
            if cu_contract:
                print(f"✅ 沪铜主力合约: {cu_contract}")
            else:
                print("❌ 沪铜主力合约获取失败")
                
            print("\n3. 测试豆粕主力合约:")
            
            # 测试豆粕
            m_contract = fetcher.get_main_contract("DCE", "m")
            if m_contract:
                print(f"✅ 豆粕主力合约: {m_contract}")
            else:
                print("❌ 豆粕主力合约获取失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_agricultural_contracts():
    """测试所有农产品主力合约"""
    print("=" * 60)
    print("测试所有农产品主力合约")
    print("=" * 60)
    
    # 设置INFO级别日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        print("\n获取所有农产品主力合约:")
        main_contracts = get_current_main_contracts(['agricultural'])
        
        print(f"\n✅ 成功获取 {len(main_contracts)} 个农产品主力合约:")
        print("-" * 40)
        
        # 按品种分类显示
        czce_contracts = []
        dce_contracts = []
        
        for key, symbol in main_contracts.items():
            exchange, product_id = key.split('.')
            if exchange == "CZCE":
                czce_contracts.append((product_id, symbol))
            elif exchange == "DCE":
                dce_contracts.append((product_id, symbol))
        
        if czce_contracts:
            print("郑商所 (CZCE):")
            for product_id, symbol in czce_contracts:
                print(f"  {product_id:4}: {symbol}")
        
        if dce_contracts:
            print("大商所 (DCE):")
            for product_id, symbol in dce_contracts:
                print(f"  {product_id:4}: {symbol}")
        
        # 特别检查菜籽油
        if 'CZCE.OI' in main_contracts:
            oi_contract = main_contracts['CZCE.OI']
            print(f"\n🎯 菜籽油主力合约: {oi_contract}")
            
            # 检查是否是通过API获取的
            if "推断" in str(oi_contract):
                print("⚠️  这是通过推断获取的，不是API查询结果")
            else:
                print("✅ 这是通过API查询获取的真实主力合约")
        else:
            print("\n❌ 未找到菜籽油主力合约")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_response_format():
    """测试API响应格式"""
    print("=" * 60)
    print("测试API响应格式")
    print("=" * 60)
    
    # 设置DEBUG级别日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='DEBUG', colorize=True)
    
    try:
        with MainContractFetcher() as fetcher:
            print("\n测试不同品种的API响应格式:")
            
            test_products = [
                ("CZCE", "OI", "菜籽油"),
                ("SHFE", "cu", "沪铜"),
                ("DCE", "m", "豆粕"),
            ]
            
            for exchange, product_id, name in test_products:
                print(f"\n--- {name} ({exchange}.{product_id}) ---")
                
                cont_symbol = f"{exchange}.{product_id}@{exchange}.{product_id}"
                print(f"查询: {cont_symbol}")
                
                try:
                    quotes = fetcher.api.query_cont_quotes(cont_symbol)
                    print(f"响应类型: {type(quotes)}")
                    print(f"响应长度: {len(quotes) if quotes else 0}")
                    
                    if quotes and len(quotes) > 0:
                        first_quote = quotes[0]
                        print(f"第一个元素类型: {type(first_quote)}")
                        print(f"第一个元素内容: {first_quote}")
                        
                        if hasattr(first_quote, 'keys'):
                            print(f"可用字段: {list(first_quote.keys())}")
                        elif hasattr(first_quote, '__dict__'):
                            print(f"对象属性: {list(first_quote.__dict__.keys())}")
                    else:
                        print("响应为空")
                        
                except Exception as e:
                    print(f"查询异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("菜籽油主力合约获取修复测试")
    print("=" * 80)
    
    tests = [
        ("菜籽油合约获取", test_oi_contract),
        ("农产品合约批量获取", test_agricultural_contracts),
        ("API响应格式测试", test_api_response_format),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:25}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 菜籽油主力合约获取修复成功！")
    else:
        print("⚠️  部分测试失败，需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
