# TimeRoseMA_cross_speak 多合约多时间周期重构

## 概述

本项目将原有的 `TimeRoseMA_cross_speak.py` 重构为支持多个合约、每个合约多个时间周期的统一策略系统。实现了更高的灵活性、可扩展性和资源利用效率。

## 重构成果

### 📁 新增文件

1. **`strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py`** - 核心策略文件
   - 支持多合约多时间周期
   - 模块化设计，易于扩展
   - 统一API管理

2. **`example_MultiSymbol_MultiTimeFrame.py`** - 使用示例
   - 7种不同的使用场景
   - 详细的配置示例

3. **`test_MultiSymbol_MultiTimeFrame.py`** - 测试脚本
   - 9项测试全部通过 ✅
   - 验证功能正确性

4. **`README_MultiSymbol_MultiTimeFrame.md`** - 本说明文档

### 🚀 核心特性

#### 1. **多合约支持**
- 同时处理任意数量的合约
- 每个合约独立的配置参数
- 统一的信号管理和交易执行

#### 2. **多时间周期支持**
- 每个合约可配置多个时间周期
- 独立的信号计算和交易逻辑
- 实时更新和监控

#### 3. **统一API管理**
- 所有合约和时间周期共享一个API连接
- 减少资源消耗
- 统一的错误处理和日志记录

#### 4. **模块化设计**
- `SymbolTimeFrameConfig`: 配置管理
- `SymbolTimeFrameStrategy`: 单合约单时间周期策略
- `MultiSymbolMultiTimeFrameManager`: 多合约多时间周期管理器
- `SignalInfo`: 信号信息封装

## 使用方法

### 1. 基本用法

```python
from strategies.TimeRoseMA_cross_MultiSymbol_MultiTimeFrame import (
    run_multi_symbol_multi_timeframe_strategy
)

# 使用默认配置运行（SH, OI, ag 三个合约，每个4个时间周期）
run_multi_symbol_multi_timeframe_strategy()
```

### 2. 单合约多时间周期

```python
from strategies.TimeRoseMA_cross_MultiSymbol_MultiTimeFrame import (
    run_single_symbol_multi_timeframe
)

# 运行SH合约的多个时间周期
run_single_symbol_multi_timeframe(
    symbol='SH',
    timeframes=[60, 300, 900],  # 1分钟、5分钟、15分钟
    bklimit=2,
    sklimit=2,
    single_volume=1
)
```

### 3. 多合约单时间周期

```python
from strategies.TimeRoseMA_cross_MultiSymbol_MultiTimeFrame import (
    run_multi_symbol_single_timeframe
)

# 运行多个合约的5分钟策略
run_multi_symbol_single_timeframe(
    symbols=['SH', 'OI', 'ag', 'rb'],
    interval=300,  # 5分钟
    bklimit=1,
    sklimit=1
)
```

### 4. 自定义配置

```python
from strategies.TimeRoseMA_cross_MultiSymbol_MultiTimeFrame import (
    SymbolTimeFrameConfig,
    run_multi_symbol_multi_timeframe_strategy
)

# 创建自定义配置
configs = [
    SymbolTimeFrameConfig(
        symbol='SH',
        interval=60,
        bklimit=3,
        sklimit=3,
        single_volume=2,
        period=13
    ),
    SymbolTimeFrameConfig(
        symbol='OI',
        interval=300,
        bklimit=2,
        sklimit=2,
        single_volume=1,
        period=20
    ),
]

run_multi_symbol_multi_timeframe_strategy(configs)
```

### 5. 命令行使用

```bash
# 默认配置
python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py default

# 单合约多时间周期
python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py single_symbol SH

# 多合约单时间周期
python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py multi_symbol SH OI ag

# 自定义配置
python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py custom
```

### 6. 兼容原有用法

```python
from strategies.TimeRoseMA_cross_MultiSymbol_MultiTimeFrame import ma_cross_multi
from tqsdk import TqApi, TqKq

api = TqApi(TqKq(), auth="your_auth")

# 兼容原有ma_cross函数的用法
symbol_configs = [
    ('SH', 60),    # SH 1分钟
    ('SH', 300),   # SH 5分钟
    ('OI', 180),   # OI 3分钟
]

ma_cross_multi(
    api=api,
    symbol_configs=symbol_configs,
    single_volume=1,
    bklimit=1,
    sklimit=1,
    period=13
)
```

## 配置参数说明

### SymbolTimeFrameConfig 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| symbol | str | - | 合约代码（如'SH', 'OI', 'ag'） |
| interval | int | - | 时间间隔（秒，如60=1分钟） |
| bklimit | int | 1 | 多单持仓限制 |
| sklimit | int | 1 | 空单持仓限制 |
| single_volume | int | 1 | 单次交易量 |
| period | int | 13 | MA周期 |
| enabled | bool | True | 是否启用该配置 |

### 常用时间周期

| 时间周期 | 秒数 | 说明 |
|----------|------|------|
| 1分钟 | 60 | 高频交易 |
| 3分钟 | 180 | 短期趋势 |
| 5分钟 | 300 | 常用周期 |
| 15分钟 | 900 | 中期趋势 |
| 30分钟 | 1800 | 长期趋势 |

## 重构对比

| 方面 | 重构前 | 重构后 |
|------|--------|--------|
| 合约支持 | 单个合约 | 多个合约 |
| 时间周期 | 单个周期 | 每个合约多个周期 |
| API连接 | 每次调用独立API | 统一API管理 |
| 配置管理 | 硬编码参数 | 配置类管理 |
| 扩展性 | 需要复制代码 | 添加配置即可 |
| 资源使用 | 多个API连接 | 单个API连接 |
| 代码维护 | 分散在多个文件 | 统一管理 |

## 优势

### 1. **灵活性**
- 支持任意合约和时间周期组合
- 每个配置独立的参数设置
- 可以动态启用/禁用特定配置

### 2. **效率**
- 统一API连接，减少资源消耗
- 并行处理多个合约和时间周期
- 优化的信号计算和交易执行

### 3. **可维护性**
- 模块化设计，职责清晰
- 统一的错误处理和日志记录
- 易于测试和调试

### 4. **可扩展性**
- 添加新合约只需要增加配置
- 支持自定义策略参数
- 兼容原有函数接口

## 测试验证

运行测试脚本验证功能：

```bash
python test_MultiSymbol_MultiTimeFrame.py
```

测试结果：
- ✅ 配置管理测试通过
- ✅ 信号信息测试通过
- ✅ 默认配置创建测试通过
- ✅ 自定义配置创建测试通过
- ✅ 配置ID生成测试通过
- ✅ 时间周期名称生成测试通过
- ✅ 配置分组测试通过
- ✅ 依赖导入测试通过
- ✅ 基本功能测试通过

**总计: 9项测试全部通过 🎉**

## 使用示例

查看详细的使用示例：

```bash
python example_MultiSymbol_MultiTimeFrame.py help
```

包含7个不同场景的示例：
1. 默认配置运行
2. 单合约多时间周期
3. 多合约单时间周期
4. 自定义配置
5. 从字典创建配置
6. 兼容原有用法
7. 手动管理策略

## 注意事项

1. **网络连接**：确保网络连接稳定，避免API连接中断
2. **认证信息**：使用有效的TqSDK认证信息
3. **资源监控**：监控内存和CPU使用情况
4. **测试环境**：建议先在模拟环境测试
5. **持仓管理**：根据资金情况合理设置持仓限制

## 迁移指南

### 从原有ma_cross函数迁移

```python
# 原有用法
ma_cross(api, symbol, interval, single_volume, bklimit, sklimit, period)

# 新的用法
configs = [SymbolTimeFrameConfig(symbol, interval, bklimit, sklimit, single_volume, period)]
run_multi_symbol_multi_timeframe_strategy(configs)

# 或使用兼容函数
ma_cross_multi(api, [(symbol, interval)], single_volume, bklimit, sklimit, period)
```

重构完成！现在您可以使用统一的API同时处理多个合约，每个合约支持多个时间周期。
