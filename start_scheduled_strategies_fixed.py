"""
修复版批量启动定时策略脚本
支持多品种同时运行定时策略
"""

import subprocess
import sys
import time
import os
import json
import psutil
from datetime import datetime


class StrategyManager:
    """策略管理器"""
    
    def __init__(self):
        self.processes = {}
        self.log_dir = "logs"
        self.state_file = "strategy_manager_state.json"
        
        # 创建日志目录
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        # 加载之前的状态
        self.load_state()
    
    def load_state(self):
        """加载策略状态"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                print(f"加载状态文件，找到 {len(state_data)} 个策略记录")
                
                # 验证进程是否仍在运行
                for product, info in state_data.items():
                    pid = info.get('pid')
                    if pid and self.is_process_running(pid):
                        # 重建进程对象
                        try:
                            process = psutil.Process(pid)
                            self.processes[product] = {
                                'process': process,
                                'start_time': datetime.fromisoformat(info['start_time']),
                                'log_file': info['log_file'],
                                'pid': pid
                            }
                            print(f"  恢复策略: {product} (PID: {pid})")
                        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                            print(f"  进程 {pid} 不可访问: {e}")
                    else:
                        print(f"  进程 {pid} 未运行，跳过 {product}")
        except Exception as e:
            print(f"加载状态失败: {e}")
    
    def save_state(self):
        """保存策略状态"""
        try:
            state_data = {}
            for product, info in self.processes.items():
                if hasattr(info['process'], 'pid'):
                    pid = info['process'].pid
                else:
                    pid = info.get('pid')
                
                state_data[product] = {
                    'pid': pid,
                    'start_time': info['start_time'].isoformat(),
                    'log_file': info['log_file']
                }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
            
            print(f"状态已保存到 {self.state_file}")
        except Exception as e:
            print(f"保存状态失败: {e}")
    
    def is_process_running(self, pid):
        """检查进程是否在运行"""
        try:
            process = psutil.Process(pid)
            return process.is_running()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False
    
    def start_strategy(self, product: str, auth: str = "quant_ggh,Qiai1301"):
        """启动单个策略"""
        if product in self.processes:
            print(f"策略 {product} 已在运行中")
            return False
        
        try:
            # 构建命令 - 使用默认的共享API模式
            cmd = [
                sys.executable,
                "TimeRoseMA_cross_ag_MultiTimeFrames.py",
                product,
                auth
            ]
            
            print(f"启动命令: {' '.join(cmd)}")
            
            # 日志文件
            log_file = os.path.join(self.log_dir, f"{product}_strategy.log")
            
            # 启动进程
            print(f"启动 {product} 策略...")
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # 等待一小段时间确保进程启动
            time.sleep(2)
            
            # 检查进程是否立即退出
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                print(f"✗ 策略 {product} 启动失败 (返回码: {process.returncode})")
                print("输出:")
                print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
                return False
            
            self.processes[product] = {
                'process': process,
                'start_time': datetime.now(),
                'log_file': log_file,
                'pid': process.pid
            }
            
            # 保存状态
            self.save_state()
            
            print(f"✓ 策略 {product} 启动成功 (PID: {process.pid})")
            print(f"  日志文件: {log_file}")
            return True
            
        except Exception as e:
            print(f"✗ 策略 {product} 启动失败: {e}")
            return False
    
    def stop_strategy(self, product: str):
        """停止单个策略"""
        if product not in self.processes:
            print(f"策略 {product} 未在运行")
            return False
        
        try:
            process_info = self.processes[product]
            process = process_info['process']
            
            print(f"停止策略 {product}...")
            
            # 终止进程
            if hasattr(process, 'terminate'):
                # subprocess.Popen 对象
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    print(f"  强制终止 {product}")
                    process.kill()
                    process.wait()
            else:
                # psutil.Process 对象
                process.terminate()
                try:
                    process.wait(timeout=10)
                except psutil.TimeoutExpired:
                    print(f"  强制终止 {product}")
                    process.kill()
            
            # 计算运行时间
            run_time = datetime.now() - process_info['start_time']
            
            print(f"✓ 策略 {product} 已停止 (运行时间: {run_time})")
            
            del self.processes[product]
            
            # 保存状态
            self.save_state()
            
            return True
            
        except Exception as e:
            print(f"✗ 停止策略 {product} 失败: {e}")
            return False
    
    def stop_all_strategies(self):
        """停止所有策略"""
        print("停止所有策略...")
        products = list(self.processes.keys())
        
        for product in products:
            self.stop_strategy(product)
    
    def list_strategies(self):
        """列出所有运行中的策略"""
        if not self.processes:
            print("当前没有运行中的策略")
            return
        
        print("运行中的策略:")
        for product, info in self.processes.items():
            process = info['process']
            start_time = info['start_time']
            run_time = datetime.now() - start_time
            
            # 检查进程状态
            try:
                if hasattr(process, 'poll'):
                    # subprocess.Popen 对象
                    if process.poll() is None:
                        status = "运行中"
                        pid = process.pid
                    else:
                        status = f"已退出 (返回码: {process.returncode})"
                        pid = info.get('pid', 'N/A')
                else:
                    # psutil.Process 对象
                    if process.is_running():
                        status = "运行中"
                        pid = process.pid
                    else:
                        status = "已退出"
                        pid = info.get('pid', 'N/A')
            except Exception as e:
                status = f"状态未知 ({e})"
                pid = info.get('pid', 'N/A')
            
            print(f"  {product}: PID {pid}, {status}, 运行时间: {run_time}")
    
    def check_strategies(self):
        """检查策略状态"""
        dead_processes = []
        
        for product, info in self.processes.items():
            process = info['process']
            try:
                if hasattr(process, 'poll'):
                    if process.poll() is not None:
                        print(f"⚠️  策略 {product} 已退出 (返回码: {process.returncode})")
                        dead_processes.append(product)
                else:
                    if not process.is_running():
                        print(f"⚠️  策略 {product} 已退出")
                        dead_processes.append(product)
            except Exception as e:
                print(f"⚠️  策略 {product} 状态检查失败: {e}")
                dead_processes.append(product)
        
        # 清理已退出的进程
        for product in dead_processes:
            del self.processes[product]
        
        # 保存状态
        if dead_processes:
            self.save_state()
        
        return len(dead_processes) == 0
    
    def cleanup_state(self):
        """清理状态文件"""
        try:
            if os.path.exists(self.state_file):
                os.remove(self.state_file)
                print("状态文件已清理")
        except Exception as e:
            print(f"清理状态文件失败: {e}")


def main():
    """主函数"""
    manager = StrategyManager()
    
    # 默认策略配置
    default_strategies = ["ag", "rb", "cu"]
    
    print("=" * 60)
    print("定时策略批量管理器 (修复版)")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "start":
            # 启动指定策略或所有默认策略
            if len(sys.argv) > 2:
                products = sys.argv[2:]
            else:
                products = default_strategies
            
            print(f"启动策略: {', '.join(products)}")
            
            success_count = 0
            for product in products:
                if manager.start_strategy(product):
                    success_count += 1
                time.sleep(1)  # 间隔启动
            
            print(f"\n成功启动 {success_count}/{len(products)} 个策略")
            
        elif command == "stop":
            # 停止指定策略或所有策略
            if len(sys.argv) > 2:
                products = sys.argv[2:]
                for product in products:
                    manager.stop_strategy(product)
            else:
                manager.stop_all_strategies()
                
        elif command == "list":
            # 列出运行中的策略
            manager.list_strategies()
            
        elif command == "check":
            # 检查策略状态
            if manager.check_strategies():
                print("所有策略运行正常")
            else:
                print("发现异常退出的策略")
        
        elif command == "cleanup":
            # 清理状态
            manager.cleanup_state()
                
        else:
            print("用法:")
            print("  python start_scheduled_strategies_fixed.py <command> [args]")
            print("")
            print("命令:")
            print("  start [products...]    启动策略 (默认: ag rb cu)")
            print("  stop [products...]     停止策略 (无参数则停止所有)")
            print("  list                   列出运行中的策略")
            print("  check                  检查策略状态")
            print("  cleanup                清理状态文件")
    else:
        print("用法:")
        print("  python start_scheduled_strategies_fixed.py <command> [args]")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)
