import subprocess

def check_if_process_running(process_name):
    try:
        process = subprocess.check_output('pgrep -f %s' % process_name, shell=True)
        process_status = process.decode("utf-8").strip()

        if process_status:
            print('运行中的进程id为: ', process_status)
            return True
        else:
            print(process_name, '进程未运行')
            return False
    except Exception as e:
        print(e)
        return False

check_if_process_running('Pythonwhoisonthebaot.py')
