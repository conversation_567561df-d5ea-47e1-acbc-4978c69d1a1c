# # This is a sample Python script.
#
# # Press Shift+F10 to execute it or replace it with your code.
# # Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.
# #
# # from tqapi import api
# #
# # symbol_info=api.query_symbol_info('CZCE.OI501')
# # print(symbol_info)
# # api.close()
from datetime import datetime, time
from tqsdk import TqApi
from tqapi import api

from datetime import datetime, time
from tqsdk import TqApi
import pandas as pd


def is_trading_time(api: TqApi, symbol: str):
    # 查询合约信息
    symbol_info = api.query_symbol_info(symbol)

    # 获取交易时间
    trading_time_day = symbol_info['trading_time_day'].to_list()[0]  # 转换为Python列表
    print(trading_time_day, type(trading_time_day))
    trading_time_night = symbol_info['trading_time_night'].to_list()[0]

    # 转换为Python列表

    # 合并日间和夜间交易时间
    trading_periods = trading_time_day + trading_time_night

    # 获取当前时间
    now = datetime.now().time()

    # 检查当前时间是否在任何一个交易时间段内
    for period in trading_periods:
        start_time = time.fromisoformat(period[0])
        end_time = time.fromisoformat(period[1])

        # 处理跨午夜的情况
        if start_time <= end_time:
            if start_time <= now <= end_time:
                return True
        else:
            if now >= start_time or now <= end_time:
                return True

    return False



if __name__ == "__main__":
    from tqapi import api
    symbol = "SHFE.cu2309"  # 以上海期货交易所的铜期货为例

    try:
        result = is_trading_time(api, symbol)
        print(f"{symbol} 当前是否在交易时间: {result}")
    finally:
        api.close()


# from tqapi import api
# account = api.get_account()
# account_balance = account.balance
# account_available = account.available
# print(account_balance)
# print(account_available)
# api.close()

