
from strategies.TimeRoseMA_cross import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date


    product = 'CZCE.OI305'
    symbol=product
    interval = 900
    bklimit = 50
    sklimit = 100
    single_volume = 10

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_zcn,Qiai1301", disable_print=True)
    # symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, product, interval, single_volume, bklimit, sklimit)


runstrategy()
