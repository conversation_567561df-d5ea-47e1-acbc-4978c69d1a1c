import json
import os
from datetime import datetime
from tqsdk import TqA<PERSON>, TqAuth,TqKq
from tqsdk.ta import MA
# from tqapi import myapi as api
import copy
from loguru import logger as log
import pandas as pd
class MultiTimeframeStrategy:
    def __init__(self, api, symbol, timeframes, max_positions, positions_file='positions.json', history_length=1000):
        self.api = api
        self.symbol = symbol
        self.timeframes = timeframes
        self.max_positions = max_positions
        self.userid = self.api.get_account().user_id
        self.positions_file = self.userid + self.symbol + '_' + positions_file
        self.history_length = history_length
        self.klines = {}
        self.last_update_time = {}
        self.log = log.info
        self.load_positions()

        for tf in self.timeframes:
            # 获取初始历史数据
            self.klines[tf] = copy.deepcopy(self.api.get_kline_serial(self.symbol, tf, data_length=self.history_length))
            self.last_update_time[tf] = self.klines[tf].iloc[-1].datetime

    def load_positions(self):
        if os.path.exists(self.positions_file):
            with open(self.positions_file, 'r') as f:
                self.positions = json.load(f)
                print(self.positions)
            self.log("Loaded positions from file.")
        else:
            self.positions = {str(tf): {'long': 0, 'short': 0} for tf in self.timeframes}
            self.log("Initialized new positions.")
            self.save_positions()

    def save_positions(self):
        with open(self.positions_file, 'w') as f:
            json.dump(self.positions, f)
            print(self.positions)
        self.log("Saved positions to file.")

    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")

    def check_crossover(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] > ma.iloc[-1] and close.iloc[-2] <= ma.iloc[-2]

    def check_crossunder(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] < ma.iloc[-1] and close.iloc[-2] >= ma.iloc[-2]

    def get_position_profit(self, position_type):
        position = self.api.get_position(self.symbol)
        if position_type == 'long':
            return position.float_profit_long
        else:
            return position.float_profit_short


    def execute_trade(self, timeframe, action, position_type):
        timeframe_str = str(timeframe)
        if action == 'open':
            print(self.positions)
            if self.positions[timeframe_str][position_type] < self.max_positions[timeframe][position_type]:
                volume = 1 if position_type == 'long' else -1
                self.api.insert_order(symbol=self.symbol, direction="BUY" if position_type == 'long' else "SELL",
                                      offset="OPEN", volume=abs(volume),
                                      limit_price=self.klines[timeframe].iloc[-1].close)
                self.log(f"{timeframe}秒周期: 开{position_type}单")
                self.positions[timeframe_str][position_type] += 1
                self.save_positions()
            else:
                self.log(f"{timeframe}秒周期,{position_type}:持仓限额已到,不开仓.")
        elif action == 'close':
            if self.positions[timeframe_str][position_type] > 0:
                profit = self.get_position_profit(position_type)
                if profit > 30:
                    # volume_to_close = self.positions[timeframe_str][position_type]
                    volume_to_close = 1
                    direction = "SELL" if position_type == 'long' else "BUY"

                    if self.symbol.startswith("SHFE"):
                        try:
                            # 获取持仓信息
                            position_info = self.api.get_position(self.symbol)

                            # 根据持仓方向获取对应的持仓数量
                            if position_type == 'long':
                                hist_pos = position_info.volume_long_his
                                today_pos = position_info.volume_long_today
                            else:
                                hist_pos = position_info.volume_short_his
                                today_pos = position_info.volume_short_today

                            # 先平历史仓位
                            if hist_pos > 0:
                                hist_volume_to_close = min(hist_pos, volume_to_close)
                                self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE",
                                                      volume=hist_volume_to_close,
                                                      limit_price=self.klines[timeframe].iloc[-1].close)
                                volume_to_close -= hist_volume_to_close
                                self.log(f"{timeframe}秒周期: 平{position_type}历史仓位 {hist_volume_to_close} 手")

                            # 如果还有剩余需要平仓的量，平今日仓位
                            if volume_to_close > 0:
                                today_volume_to_close = min(today_pos, volume_to_close)
                                self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSETODAY",
                                                      volume=today_volume_to_close,
                                                      limit_price=self.klines[timeframe].iloc[-1].close)
                                self.log(f"{timeframe}秒周期: 平{position_type}今日仓位 {today_volume_to_close} 手")
                        except AttributeError as e:
                            self.log(f"获取SHFE合约位置时出错: {str(e)}")
                            return  # 出错时退出函数，不执行后续操作
                        except Exception as e:
                            self.log(f"平仓SHFE合约时出错: {str(e)}")
                            return  # 出错时退出函数，不执行后续操作
                    else:
                        # 非SHFE合约，直接平仓
                        self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE",
                                              volume=volume_to_close, limit_price=self.klines[timeframe].iloc[-1].close)
                        self.log(f"{timeframe}秒周期: 平{position_type}单 {volume_to_close} 手")

                    self.positions[timeframe_str][position_type] -= volume_to_close
                    self.save_positions()
                else:
                    self.log(f"{timeframe}秒周期: {position_type}单亏损,不平仓")

    def update_klines(self, timeframe):
        # 获取最新的k线数据
        new_klines = self.api.get_kline_serial(self.symbol, timeframe, data_length=2)  # 获取最新的两根k线
        # print(new_klines)
        # new_klines=new_klines.set_index("datetime")
        last_complete_kline = new_klines.iloc[-2]  # 倒数第二根是最新的完整k线
        # print(last_complete_kline)
        # 检查是否有新的完整k线
        if last_complete_kline.datetime > self.last_update_time[timeframe]:
            # 更新klines数据
            # self.klines[timeframe] = pd.concat([self.klines[timeframe],pd.DataFrame(last_complete_kline.T)], ignore_index=True)
            self.klines[timeframe] = self.klines[timeframe]._append(pd.Series(last_complete_kline), ignore_index=True)
            self.last_update_time[timeframe] = last_complete_kline.datetime
            # print(self.klines[timeframe].iloc[:5])
            # print(self.klines[timeframe].iloc[-5:])
            print(f"更新{timeframe}秒周期的k线数据")
            return True
        return False

    def run(self, timeframe):
        # 检查并更新k线数据
        if self.update_klines(timeframe):
            kline = self.klines[timeframe]
            ma13 = kline.close.rolling(13).mean()
            self.position= self.api.get_position(self.symbol)
            print(f"{self.symbol}:{timeframe}秒周期: 当前持仓: 多单:{self.position.pos_long}, 空单:{self.position.pos_short}")

            if self.check_crossover(kline.close, ma13):
                self.log(f"{self.symbol}:{timeframe}秒周期: 收盘价大于13日均线, 发出开多信号")
                self.execute_trade(timeframe, 'open', 'long')
                self.execute_trade(timeframe, 'close', 'short')
            elif self.check_crossunder(kline.close, ma13):
                self.log(f"{self.symbol}:{timeframe}秒周期: 收盘价小于13日均线, 发出开空信号")
                self.execute_trade(timeframe, 'open', 'short')
                self.execute_trade(timeframe, 'close', 'long')

    def update(self):
        for timeframe in self.timeframes:
            self.run(timeframe)

if __name__ == "__main__":
    from tqsdk import TqAccount
    from accounts_zjy import cyzjy as acct

    while True:# 主程序
        try:
            api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
            product_id = 'OI'
            # product_id = 'ag'
            # product_id = 'SA'
            # api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
            symbol = api.query_cont_quotes(product_id=product_id)[0]
            symbol = 'CZCE.OI505'
            print(f"交易的合约是: {symbol}")

            # 定义交易周期（以秒为单位）和每个周期的持仓限制
            # timeframes = [60, 180, 800, 900]  # 1分钟, 3分钟, 13分20秒, 15分钟
            max_positions = {
                15: {'long': 1, 'short': 1},
                60: {'long': 1, 'short': 1},
                180: {'long': 0, 'short': 0},
                300: {'long': 0, 'short': 0},
                900: {'long': 0, 'short': 0}
            }

            timeframes = list(max_positions.keys())
            strategy = MultiTimeframeStrategy(api, symbol, timeframes, max_positions)

            while True:
                api.wait_update()
                strategy.update()

        except KeyboardInterrupt:
            print("策略已停止")
        finally:
            api.close()
