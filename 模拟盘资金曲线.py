import json
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 加载 JSON 文件
with open('simulatedaysummary_new.json', 'r', encoding='utf-8') as file:
    data = json.load(file)

# 计算每个交易日的资金合计
dates = []
total_balances = []

for date, users in data.items():
    total_balance = sum(user["balance"] for user in users)
    dates.append(date)
    total_balances.append(total_balance)

# 计算百分比变化
base_value = total_balances[0]
percentage_changes = [(balance / base_value - 1) * 100 for balance in total_balances]

# 绘制图形
fig, axs = plt.subplots(2, 1, figsize=(10, 12), sharex=True)

# 上方资金曲线
axs[0].plot(dates, total_balances, marker='o', label='资金曲线')
axs[0].set_ylabel('资金合计')
axs[0].set_title('所有账号资金曲线')
axs[0].legend()
axs[0].grid()

# 下方百分比变化曲线
axs[1].plot(dates, percentage_changes, marker='o', color='orange', label='百分比变化')
axs[1].set_xlabel('交易日')
axs[1].set_ylabel('百分比变化 (%)')
axs[1].set_title('资金百分比变化曲线')
axs[1].legend()
axs[1].grid()

plt.xticks(rotation=45)
plt.tight_layout()
plt.show()