from strategies.TimeRoseMA_cross_singlevolume_NoSendSignal import ma_cross

def runstrategy():
    from tqsdk import TqA<PERSON>, TqAccount, TqKq
    from datetime import date
    # from accounts import cyzjy as acct


    product = 'jm'
    symbol=product
    interval = 60
    bklimit = 20
    sklimit = 20
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    api = TqApi(TqKq(), auth="quant_zcn,Qiai1301")
    # api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")
    symbol = api.query_cont_quotes(product_id=product)[0]
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)

if __name__=='__main__':
    runstrategy()
