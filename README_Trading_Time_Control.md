# 交易时间控制功能使用指南

## 🎯 功能概述

已成功为 `TimeRoseMA_cross_ag_MultiTimeFrames.py` 添加了交易时间控制功能，实现了：
- **自动启动**: 每个交易日晚上20:55自动启动策略
- **自动关闭**: 每个交易日下午15:05自动关闭策略
- **交易时段检测**: 智能识别夜盘和日盘交易时间
- **非交易日跳过**: 自动跳过周末等非交易日

## 📝 新增功能

### 1. TradingTimeController 类
- **时间判断**: 自动判断交易日、交易时段
- **策略控制**: 自动启动和停止策略
- **日志记录**: 详细的操作日志
- **调度管理**: 基于 schedule 库的定时任务

### 2. 新增命令行参数
- **schedule 模式**: 启用定时控制功能
- **时间配置**: 可自定义启动和停止时间

### 3. 智能启动逻辑
- 如果在启动时间窗口内，立即启动
- 如果在交易时段内，立即启动
- 否则等待下一个启动时间

## 🚀 使用方法

### 基本语法
```bash
python TimeRoseMA_cross_ag_MultiTimeFrames.py <product> schedule [auth]
```

### 实际示例
```bash
# 启动ag定时策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag schedule

# 启动rb定时策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py rb schedule

# 使用自定义认证
python TimeRoseMA_cross_ag_MultiTimeFrames.py cu schedule "user,pass"
```

## ⏰ 时间设置

### 默认时间配置
- **启动时间**: 20:55 (每个交易日)
- **停止时间**: 15:05 (每个交易日)
- **检查频率**: 每30秒检查一次

### 交易时段定义
- **夜盘**: 21:00 - 02:30 (次日)
- **日盘**: 09:00 - 15:00
- **非交易日**: 周六、周日自动跳过

### 启动逻辑
1. **20:55-21:00**: 启动时间窗口
2. **交易时段内**: 立即启动
3. **其他时间**: 等待下一个启动时间

## 📊 运行状态

### 启动场景
```bash
启动 AG 定时策略 (20:55启动，15:05关闭)
当前在交易时段内，立即启动策略...
=== 多时间周期策略启动 ===
产品: ag
运行模式: independent
认证信息: quant_ggh,Qiai1301
时间周期: ['1m', '3m', '5m', '15m']
```

### 等待场景
```bash
启动 AG 定时策略 (20:55启动，15:05关闭)
等待交易时间...
设置交易时间调度...
启动时间: 20:55:00
停止时间: 15:05:00
启动交易时间调度器...
```

## 📋 日志功能

### 日志文件
- **文件名**: `trading_schedule.log`
- **编码**: UTF-8
- **内容**: 启动、停止、错误等操作记录

### 日志示例
```
2025-06-19 20:55:00 - INFO - 启动交易策略...
2025-06-19 21:00:00 - INFO - 策略运行中，当前在夜盘时段
2025-06-19 15:05:00 - INFO - 停止交易策略...
2025-06-19 15:06:00 - INFO - 策略已停止
```

### 查看日志
```bash
# 实时查看日志
tail -f trading_schedule.log

# 查看最近100行
tail -n 100 trading_schedule.log

# 搜索特定内容
grep "启动" trading_schedule.log
```

## 🔧 高级配置

### 自定义时间设置
如需修改启动和停止时间，可以编辑 `TradingTimeController` 类：

```python
class TradingTimeController:
    def __init__(self):
        self.start_time = dt_time(20, 55)  # 修改启动时间
        self.end_time = dt_time(15, 5)     # 修改停止时间
```

### 交易时段调整
```python
def is_in_trading_session(self) -> bool:
    # 夜盘时间调整
    night_start = dt_time(21, 0)
    night_end = dt_time(2, 30)
    
    # 日盘时间调整
    day_start = dt_time(9, 0)
    day_end = dt_time(15, 0)
```

## 🛠️ 测试验证

### 运行测试脚本
```bash
# 测试时间判断功能
python test_trading_time_control.py time

# 测试基本功能
python test_trading_time_control.py basic

# 测试调度功能
python test_trading_time_control.py schedule

# 查看使用示例
python test_trading_time_control.py usage
```

### 测试结果示例
```
当前时间: 2025-06-19 21:40:42
星期: 周四
是否交易日: True
是否应该启动: False
是否应该停止: False
是否在交易时段: True
```

## 🎯 实际应用场景

### 1. 生产环境部署
```bash
# 启动定时策略，无需人工干预
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag schedule

# 后台运行
nohup python TimeRoseMA_cross_ag_MultiTimeFrames.py ag schedule > ag_schedule.log 2>&1 &
```

### 2. 多品种自动化
```bash
# 创建启动脚本
echo "python TimeRoseMA_cross_ag_MultiTimeFrames.py ag schedule" > start_ag_schedule.sh
echo "python TimeRoseMA_cross_ag_MultiTimeFrames.py rb schedule" > start_rb_schedule.sh
echo "python TimeRoseMA_cross_ag_MultiTimeFrames.py cu schedule" > start_cu_schedule.sh

# 批量启动
chmod +x *.sh
./start_ag_schedule.sh &
./start_rb_schedule.sh &
./start_cu_schedule.sh &
```

### 3. 系统服务集成
```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/trading-ag.service

[Unit]
Description=AG Trading Strategy
After=network.target

[Service]
Type=simple
User=trader
WorkingDirectory=/path/to/strategy
ExecStart=/usr/bin/python3 TimeRoseMA_cross_ag_MultiTimeFrames.py ag schedule
Restart=always

[Install]
WantedBy=multi-user.target

# 启用服务
sudo systemctl enable trading-ag.service
sudo systemctl start trading-ag.service
```

## ⚠️ 注意事项

### 1. 系统要求
- **Python 3.7+**: 支持 datetime 和 schedule 库
- **稳定网络**: 确保TqSDK连接稳定
- **系统时间**: 确保系统时间准确

### 2. 资源管理
- **内存使用**: 定时模式会额外占用少量内存
- **日志文件**: 定期清理日志文件避免占用过多空间
- **进程管理**: 确保策略进程正常退出

### 3. 风险控制
- **认证有效性**: 确保TqSDK认证信息有效
- **网络中断**: 网络中断时策略会自动重连
- **异常处理**: 程序异常时会记录日志并尝试恢复

### 4. 监控建议
- **日志监控**: 定期检查日志文件
- **进程监控**: 监控策略进程状态
- **性能监控**: 监控系统资源使用情况

## 🔍 故障排除

### 常见问题

#### 1. 策略未在预定时间启动
- 检查系统时间是否准确
- 检查是否为交易日
- 查看日志文件了解详细信息

#### 2. 策略未正常停止
- 检查网络连接
- 查看进程是否仍在运行
- 手动终止相关进程

#### 3. 日志文件过大
```bash
# 清理日志文件
> trading_schedule.log

# 或者备份后清理
mv trading_schedule.log trading_schedule_backup.log
touch trading_schedule.log
```

## 🎉 总结

交易时间控制功能为策略程序提供了：

✅ **自动化运行**: 无需人工干预的定时启动和停止  
✅ **智能时间管理**: 准确识别交易时段和非交易日  
✅ **完善的日志记录**: 详细的操作记录便于监控  
✅ **灵活的配置**: 可根据需要调整时间设置  
✅ **稳定的运行**: 异常处理和自动恢复机制  

这使得策略程序更加适合生产环境的自动化部署和运行！
