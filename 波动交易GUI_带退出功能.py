#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波动交易GUI程序 - 带退出功能版本
基于波动交易_2.py实现的完整GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.animation import FuncAnimation
import matplotlib
import platform
import threading
import time
from datetime import datetime

# 设置matplotlib中文字体
if platform.system() == 'Windows':
    matplotlib.rcParams['font.sans-serif'] = ['SimHei']
elif platform.system() == 'Darwin':  # macOS
    matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS']
else:  # Linux
    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']

matplotlib.rcParams['axes.unicode_minus'] = False

class FuturesDataAnalyzer:
    """期货数据分析器核心类"""
    
    def __init__(self, symbol, duration_seconds=60, data_length=200):
        self.symbol = symbol
        self.duration_seconds = duration_seconds
        self.data_length = data_length
        self.api = None
        self.is_connected = False
        
    def connect_api(self):
        """连接TQ API"""
        try:
            print("正在导入TQ SDK...")
            from tqsdk import TqApi, TqAuth, TqKq
            print("TQ SDK导入成功")

            print("正在创建API连接...")
            self.api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
            print("API连接创建成功")

            self.is_connected = True
            return True
        except ImportError as e:
            print(f"TQ SDK导入失败: {e}")
            print("请确保已安装tqsdk: pip install tqsdk")
            return False
        except Exception as e:
            print(f"API连接失败: {e}")
            return False
    
    def disconnect_api(self):
        """断开API连接"""
        if self.api:
            try:
                self.api.close()
                self.is_connected = False
            except:
                pass
    
    def fetch_data(self):
        """获取期货行情数据"""
        if not self.is_connected or not self.api:
            print("API未连接")
            return None

        try:
            print(f"正在获取数据: {self.symbol}, 周期: {self.duration_seconds}秒, 长度: {self.data_length}")
            df = self.api.get_kline_serial(
                self.symbol,
                duration_seconds=self.duration_seconds,
                data_length=self.data_length
            )

            if df is not None and len(df) > 0:
                print(f"数据获取成功: {len(df)} 条记录")
                print(f"数据时间范围: {df.index[0]} 到 {df.index[-1]}")
                print(f"最新价格: {df['close'].iloc[-1]:.2f}")
                
                # 计算技术指标
                df = self.calculate_indicators(df)
            else:
                print("获取的数据为空")

            return df
        except Exception as e:
            print(f"获取数据失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def calculate_indicators(self, df):
        """计算技术指标"""
        try:
            # 基础指标计算
            df['HH1'] = df['high'].rolling(window=20).max()
            df['LL1'] = df['low'].rolling(window=20).min()
            df['HH2'] = df['high'].rolling(window=10).max()
            df['LL2'] = df['low'].rolling(window=10).min()
            
            # 波动交易指标
            df['K1'] = np.where(
                df['close'] > df['HH2'], -3,
                np.where(df['close'] < df['LL2'], 1, 0)
            )
            df['K2'] = self.VALUEWHEN(df['K1'] != 0, df['K1'])
            
            df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
            df['G1'] = df['G'].iloc[-1] if len(df) > 0 else 0
            
            # 其他指标
            df['W1'] = df['K2']
            df['W2'] = df['open'] - df['close']
            df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
            df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])
            
            return df
        except Exception as e:
            print(f"计算指标失败: {e}")
            return df
    
    def VALUEWHEN(self, condition, value):
        """VALUEWHEN函数实现"""
        result = pd.Series(index=condition.index, dtype=float)
        last_value = 0
        
        for i in range(len(condition)):
            if condition.iloc[i]:
                last_value = value.iloc[i]
            result.iloc[i] = last_value
            
        return result

class VolatilityTradingGUI:
    """波动交易GUI主程序"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("波动交易分析系统")
        self.setup_window()
        
        # 数据分析器
        self.analyzer = None
        self.is_running = False
        self.animation = None

        # 日志系统（必须在setup_ui之前初始化）
        self.log_messages = []

        # 设置界面
        self.setup_ui()

        # 默认参数
        self.symbol = "<EMAIL>"
        self.duration_seconds = 60
        self.data_length = 8964
        self.update_interval = 1000
        
    def setup_window(self):
        """设置窗口"""
        # 窗口大小和居中
        window_width = 1600
        window_height = 1000
        
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        center_x = int(screen_width / 2 - window_width / 2)
        center_y = int(screen_height / 2 - window_height / 2)
        
        self.root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')
        self.root.minsize(1200, 800)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 控制面板
        self.setup_control_panel(main_frame)

        # 创建水平分割的主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 图表区域（左侧，占70%宽度）
        self.setup_chart_area(content_frame)

        # 日志区域（右侧，占30%宽度）
        self.setup_log_area(content_frame)

        # 状态栏
        self.setup_status_bar(main_frame)
    
    def setup_control_panel(self, parent):
        """设置控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：合约和参数设置
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="合约代码:").pack(side=tk.LEFT, padx=(0, 5))
        self.symbol_var = tk.StringVar(value="<EMAIL>")
        symbol_entry = ttk.Entry(row1, textvariable=self.symbol_var, width=15)
        symbol_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(row1, text="周期(秒):").pack(side=tk.LEFT, padx=(0, 5))
        self.duration_var = tk.StringVar(value="60")
        duration_combo = ttk.Combobox(row1, textvariable=self.duration_var,
                                     values=["60", "180", "300", "900", "3600"], width=8, state="readonly")
        duration_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(row1, text="数据长度:").pack(side=tk.LEFT, padx=(0, 5))
        self.length_var = tk.StringVar(value="8964")
        length_entry = ttk.Entry(row1, textvariable=self.length_var, width=8)
        length_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(row1, text="更新间隔(ms):").pack(side=tk.LEFT, padx=(0, 5))
        self.interval_var = tk.StringVar(value="1000")
        interval_combo = ttk.Combobox(row1, textvariable=self.interval_var,
                                     values=["500", "1000", "2000", "5000"], width=8, state="readonly")
        interval_combo.pack(side=tk.LEFT)
        
        # 第二行：控制按钮
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, pady=(5, 0))
        
        self.start_btn = ttk.Button(row2, text="🚀 开始分析", command=self.start_analysis, width=12)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(row2, text="⏹️ 停止分析", command=self.stop_analysis, 
                                  width=12, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(row2, text="🔄 刷新数据", command=self.refresh_data, width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="💾 保存图表", command=self.save_chart, width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="⚙️ 设置", command=self.show_settings, width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="❌ 退出程序", command=self.exit_program, width=12).pack(side=tk.LEFT, padx=(0, 5))
        
        # 实时信息显示
        info_frame = ttk.Frame(row2)
        info_frame.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.price_label = ttk.Label(info_frame, text="当前价格: --", foreground="blue", font=("Arial", 10, "bold"))
        self.price_label.pack(side=tk.TOP)
        
        self.time_label = ttk.Label(info_frame, text="更新时间: --", foreground="gray", font=("Arial", 9))
        self.time_label.pack(side=tk.TOP)
    
    def setup_chart_area(self, parent):
        """设置图表区域"""
        chart_frame = ttk.LabelFrame(parent, text="实时K线图表", padding="5")
        chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 创建matplotlib图形
        plt.style.use('dark_background')
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        self.fig.patch.set_facecolor('#2E2E2E')

        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, master=chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加工具栏
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()

    def setup_log_area(self, parent):
        """设置日志区域"""
        log_frame = ttk.LabelFrame(parent, text="执行日志", padding="5")
        log_frame.pack(side=tk.RIGHT, fill=tk.BOTH, padx=(5, 0))

        # 设置日志区域的固定宽度
        log_frame.configure(width=400)
        log_frame.pack_propagate(False)

        # 日志文本框
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True)

        # 创建文本框和滚动条
        self.log_text = tk.Text(log_text_frame, wrap=tk.WORD, font=("Consolas", 9),
                               bg='#1E1E1E', fg='#FFFFFF', insertbackground='white')

        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 日志控制按钮
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(log_control_frame, text="清除日志", command=self.clear_log, width=10).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_control_frame, text="保存日志", command=self.save_log, width=10).pack(side=tk.LEFT, padx=(0, 5))

        # 日志级别选择
        ttk.Label(log_control_frame, text="级别:").pack(side=tk.LEFT, padx=(10, 2))
        self.log_level_var = tk.StringVar(value="所有")
        log_level_combo = ttk.Combobox(log_control_frame, textvariable=self.log_level_var,
                                      values=["所有", "DEBUG", "INFO", "WARNING", "ERROR"],
                                      width=8, state="readonly")
        log_level_combo.pack(side=tk.LEFT)
        log_level_combo.bind('<<ComboboxSelected>>', self.on_log_level_change)

        # 添加初始日志
        self.add_log("INFO", "日志系统已初始化")
    
    def setup_status_bar(self, parent):
        """设置状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 请设置参数并开始分析")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.pack(fill=tk.X)

    def add_log(self, level, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"

        # 添加到日志列表
        self.log_messages.append(log_entry)

        # 保持日志数量在合理范围内
        if len(self.log_messages) > 1000:
            self.log_messages = self.log_messages[-500:]

        # 检查日志级别过滤
        current_filter = getattr(self, 'log_level_var', None)
        if current_filter and hasattr(current_filter, 'get'):
            filter_level = current_filter.get()

            # 定义日志级别优先级
            level_priority = {
                "DEBUG": 0,
                "INFO": 1,
                "WARNING": 2,
                "ERROR": 3
            }

            # 如果选择了"所有"或者当前日志级别符合过滤条件，则显示
            should_display = (filter_level == "所有" or
                            level_priority.get(level, 1) >= level_priority.get(filter_level, 1))
        else:
            should_display = True

        # 显示在文本框中
        if should_display and hasattr(self, 'log_text'):
            # 根据日志级别设置颜色
            color_map = {
                "DEBUG": "#888888",    # 灰色
                "INFO": "#FFFFFF",     # 白色
                "WARNING": "#FFA500",  # 橙色
                "ERROR": "#FF4444"     # 红色
            }

            color = color_map.get(level, "#FFFFFF")

            # 插入带颜色的日志
            self.log_text.insert(tk.END, log_entry + "\n")

            # 设置颜色标签
            start_line = self.log_text.index(tk.END + "-2l")
            end_line = self.log_text.index(tk.END + "-1l")
            tag_name = f"level_{level}"

            if tag_name not in self.log_text.tag_names():
                self.log_text.tag_configure(tag_name, foreground=color)

            self.log_text.tag_add(tag_name, start_line, end_line)
            self.log_text.see(tk.END)

        # 同时输出到控制台
        print(log_entry)

    def on_log_level_change(self, event=None):
        """日志级别改变时的处理"""
        self.refresh_log_display()

    def refresh_log_display(self):
        """刷新日志显示"""
        if not hasattr(self, 'log_text'):
            return

        # 清除当前显示
        self.log_text.delete(1.0, tk.END)

        # 获取当前过滤级别
        filter_level = self.log_level_var.get()

        # 定义日志级别优先级
        level_priority = {
            "DEBUG": 0,
            "INFO": 1,
            "WARNING": 2,
            "ERROR": 3
        }

        # 重新显示符合条件的日志
        for log_entry in self.log_messages:
            # 解析日志级别
            try:
                level = log_entry.split("] ")[1].split(":")[0]
            except:
                level = "INFO"

            # 检查是否应该显示
            should_display = (filter_level == "所有" or
                            level_priority.get(level, 1) >= level_priority.get(filter_level, 1))

            if should_display:
                # 根据日志级别设置颜色
                color_map = {
                    "DEBUG": "#888888",
                    "INFO": "#FFFFFF",
                    "WARNING": "#FFA500",
                    "ERROR": "#FF4444"
                }

                color = color_map.get(level, "#FFFFFF")

                # 插入日志
                self.log_text.insert(tk.END, log_entry + "\n")

                # 设置颜色
                start_line = self.log_text.index(tk.END + "-2l")
                end_line = self.log_text.index(tk.END + "-1l")
                tag_name = f"level_{level}"

                if tag_name not in self.log_text.tag_names():
                    self.log_text.tag_configure(tag_name, foreground=color)

                self.log_text.tag_add(tag_name, start_line, end_line)

        # 滚动到底部
        self.log_text.see(tk.END)

    def clear_log(self):
        """清除日志"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
        self.log_messages.clear()
        self.add_log("INFO", "日志已清除")

    def save_log(self):
        """保存日志到文件"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="保存日志",
                defaultextension=".txt",
                initialfile=f"波动交易日志_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(self.log_messages))
                self.add_log("INFO", f"日志已保存到: {filename}")

        except Exception as e:
            self.add_log("ERROR", f"保存日志失败: {str(e)}")

    def start_analysis(self):
        """开始分析"""
        try:
            self.add_log("INFO", "开始启动分析...")

            # 获取参数
            self.symbol = self.symbol_var.get().strip()
            self.duration_seconds = int(self.duration_var.get())
            self.data_length = int(self.length_var.get())
            self.update_interval = int(self.interval_var.get())

            self.add_log("INFO", f"参数设置 - 合约: {self.symbol}, 周期: {self.duration_seconds}秒, 数据长度: {self.data_length}, 更新间隔: {self.update_interval}ms")

            if not self.symbol:
                self.add_log("ERROR", "合约代码为空")
                messagebox.showerror("错误", "请输入合约代码")
                return

            # 创建分析器
            self.add_log("INFO", "创建数据分析器...")
            self.analyzer = FuturesDataAnalyzer(self.symbol, self.duration_seconds, self.data_length)

            # 连接API
            self.add_log("INFO", "正在连接TQ API...")
            self.status_var.set("正在连接API...")
            self.root.update()

            if not self.analyzer.connect_api():
                self.add_log("ERROR", "API连接失败")
                messagebox.showerror("错误", "API连接失败，请检查网络和认证信息")
                return

            self.add_log("INFO", "API连接成功")

            # 测试数据获取
            self.add_log("INFO", "测试数据获取...")
            test_data = self.analyzer.fetch_data()
            if test_data is None:
                self.add_log("ERROR", "无法获取数据")
                messagebox.showerror("错误", "无法获取数据，请检查合约代码是否正确")
                return

            self.add_log("INFO", f"成功获取 {len(test_data)} 条数据")

            # 首次绘制图表
            self.add_log("INFO", "首次绘制图表...")
            self.initial_plot(test_data)

            # 启动实时更新
            self.is_running = True
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")

            self.add_log("INFO", "启动实时更新...")

            # 启动动画
            self.animation = FuncAnimation(
                self.fig,
                self.update_plot,
                interval=self.update_interval,
                cache_frame_data=False
            )

            self.status_var.set(f"分析中 - 合约: {self.symbol}, 周期: {self.duration_seconds}秒")
            self.add_log("INFO", "分析已启动，开始实时更新图表")

        except ValueError as e:
            self.add_log("ERROR", f"参数错误: {str(e)}")
            messagebox.showerror("错误", f"参数设置错误: {str(e)}")
            self.stop_analysis()
        except Exception as e:
            self.add_log("ERROR", f"启动分析失败: {str(e)}")
            messagebox.showerror("错误", f"启动分析失败: {str(e)}")
            self.stop_analysis()
    
    def stop_analysis(self):
        """停止分析"""
        self.add_log("INFO", "正在停止分析...")
        self.is_running = False

        if self.animation:
            self.animation.event_source.stop()
            self.animation = None
            self.add_log("INFO", "动画更新已停止")

        if self.analyzer:
            self.analyzer.disconnect_api()
            self.analyzer = None
            self.add_log("INFO", "API连接已断开")

        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_var.set("已停止分析")
        self.add_log("INFO", "分析已完全停止")

    def refresh_data(self):
        """刷新数据"""
        if self.analyzer and self.analyzer.is_connected:
            self.add_log("INFO", "手动刷新数据...")
            data = self.analyzer.fetch_data()
            if data is not None:
                self.initial_plot(data)
                self.add_log("INFO", "数据刷新完成")
            else:
                self.add_log("ERROR", "数据刷新失败")
        else:
            self.add_log("WARNING", "请先开始分析")

    def initial_plot(self, df):
        """首次绘制图表"""
        try:
            self.add_log("INFO", "开始首次绘制图表...")

            if df is None or len(df) == 0:
                self.add_log("ERROR", "数据为空，无法绘制图表")
                return

            # 只显示最后100条数据
            if len(df) > 100:
                df_display = df.tail(100).copy()
                self.add_log("INFO", f"显示最新100条数据（总数据：{len(df)}条）")
            else:
                df_display = df.copy()
                self.add_log("INFO", f"显示全部{len(df)}条数据")

            # 清除图表
            self.ax.clear()
            self.ax.set_title(f'期货行情: {self.symbol}', color='white', fontweight='bold', fontsize=14)

            # 绘制K线图
            self.add_log("INFO", "绘制K线图...")
            self.plot_candlestick(df_display)

            # 绘制技术指标
            self.add_log("INFO", "绘制技术指标...")
            self.plot_indicators(df_display)

            # 更新实时信息
            if len(df) > 0:
                current_price = df['close'].iloc[-1]
                self.price_label.config(text=f"当前价格: {current_price:.2f}")
                self.time_label.config(text=f"更新时间: {datetime.now().strftime('%H:%M:%S')}")
                self.add_log("INFO", f"当前价格: {current_price:.2f}")

            # 刷新画布
            self.add_log("INFO", "刷新画布...")
            self.canvas.draw()

            self.add_log("INFO", "首次图表绘制完成")

        except Exception as e:
            self.add_log("ERROR", f"首次绘制图表失败: {str(e)}")
            import traceback
            self.add_log("ERROR", f"首次绘制错误详情: {traceback.format_exc()}")

    def update_plot(self, frame):
        """更新图表（动画回调函数）"""
        if not self.is_running or not self.analyzer:
            return

        try:
            # 获取最新数据
            df = self.analyzer.fetch_data()
            if df is None or len(df) == 0:
                if frame % 20 == 0:
                    self.add_log("WARNING", "获取数据为空")
                return

            # 只显示最后100条数据
            if len(df) > 100:
                df_display = df.tail(100).copy()
                if frame % 20 == 0:
                    self.add_log("DEBUG", f"显示最新100条数据（总数据：{len(df)}条）")
            else:
                df_display = df.copy()
                if frame % 20 == 0:
                    self.add_log("DEBUG", f"显示全部{len(df)}条数据")

            # 清除图表
            self.ax.clear()
            self.ax.set_title(f'期货行情: {self.symbol}', color='white', fontweight='bold', fontsize=14)

            # 绘制K线图
            self.plot_candlestick(df_display)

            # 绘制技术指标
            self.plot_indicators(df_display)

            # 更新实时信息
            if len(df) > 0:
                current_price = df['close'].iloc[-1]
                self.price_label.config(text=f"当前价格: {current_price:.2f}")
                self.time_label.config(text=f"更新时间: {datetime.now().strftime('%H:%M:%S')}")

                # 记录价格更新（减少频率）
                if frame % 20 == 0:
                    self.add_log("DEBUG", f"价格更新: {current_price:.2f}")

            # 刷新画布
            self.canvas.draw()

        except Exception as e:
            if frame % 20 == 0:
                self.add_log("ERROR", f"更新图表失败: {str(e)}")

    def plot_candlestick(self, df):
        """绘制K线图"""
        if df is None or len(df) == 0:
            return

        try:
            # 完全按照原版波动交易_2.py的逻辑绘制K线
            width = 0.6
            width2 = 0.05

            up = df[df.close > df.open]
            down = df[df.close < df.open]
            equal = df[df.close == df.open]

            # 上涨K线 - 红色
            self.ax.bar(up.index, up.close - up.open, width, bottom=up.open, color='red')
            self.ax.vlines(up.index, up.low, up.high, color='red', linewidth=1)

            # 下跌K线 - 绿色
            self.ax.bar(down.index, down.close - down.open, width, bottom=down.open, color='green')
            self.ax.vlines(down.index, down.low, down.high, color='green', linewidth=1)

            # 开盘价等于收盘价的K线
            self.ax.bar(equal.index, width2, width, bottom=equal.open - width2 / 2, color='cyan')
            self.ax.vlines(equal.index, equal.low, equal.high, color='cyan', linewidth=1)

        except Exception as e:
            self.add_log("ERROR", f"绘制K线图失败: {str(e)}")
            import traceback
            self.add_log("ERROR", f"K线绘制错误详情: {traceback.format_exc()}")


    def plot_indicators(self, df):
        """绘制技术指标"""
        if df is None or len(df) == 0:
            return

        try:
            # 完全按照原版波动交易_2.py的逻辑绘制指标
            for i in range(len(df) - 1):
                if df['W1'].iloc[i] == 1:
                    self.ax.plot(
                        [df.index[i], df.index[i]],
                        [df['low'].iloc[i], df['LT'].iloc[i]],
                        color='cyan'
                    )
                    self.ax.plot(
                        [df.index[i], df.index[i]],
                        [df['high'].iloc[i], df['HT'].iloc[i]],
                        color='cyan'
                    )
                elif df['W1'].iloc[i] == -3:
                    self.ax.plot(
                        [df.index[i], df.index[i]],
                        [df['low'].iloc[i], df['LT'].iloc[i]],
                        color='red'
                    )
                    self.ax.plot(
                        [df.index[i], df.index[i]],
                        [df['high'].iloc[i], df['HT'].iloc[i]],
                        color='red'
                    )

                if df['W1'].iloc[i] == 1 and df['W1'].iloc[i - 1] == 1:
                    self.ax.plot(
                        [df.index[i - 1], df.index[i]],
                        [df['G'].iloc[i - 1], df['G'].iloc[i]],
                        color='limegreen'
                    )
                elif df['W1'].iloc[i] == -3 and df['W1'].iloc[i - 1] == -3:
                    self.ax.plot(
                        [df.index[i - 1], df.index[i]],
                        [df['G'].iloc[i - 1], df['G'].iloc[i]],
                        color='yellow'
                    )

            # 在最后一个点绘制G1的值
            self.ax.text(
                df.index[-1],
                df['G1'].iloc[-1],
                f"{df['G1'].iloc[-1]:.2f}",
                color='cyan',
                fontweight='bold'
            )

            # 设置图表样式（与原版一致）
            self.ax.grid(True, color='gray', linestyle=':', alpha=0.5)
            self.ax.tick_params(axis='x', colors='white')
            self.ax.tick_params(axis='y', colors='white')

        except Exception as e:
            self.add_log("ERROR", f"绘制技术指标失败: {str(e)}")
            import traceback
            self.add_log("ERROR", f"指标绘制错误详情: {traceback.format_exc()}")
            print(f"绘制指标失败: {e}")

    def save_chart(self):
        """保存图表"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="保存图表",
                defaultextension=".png",
                initialfile=f"波动交易_{self.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                filetypes=[("PNG files", "*.png"), ("JPG files", "*.jpg"), ("PDF files", "*.pdf")]
            )
            
            if filename:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight', facecolor='#2E2E2E')
                messagebox.showinfo("成功", f"图表已保存到: {filename}")
                self.add_log("INFO", f"图表已保存到: {filename}")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")
            self.add_log("ERROR", f"保存图表失败: {str(e)}")
    
    def show_settings(self):
        """显示设置对话框"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("设置")
        settings_window.geometry("400x300")
        settings_window.transient(self.root)
        settings_window.grab_set()
        
        # 居中显示
        settings_window.update_idletasks()
        x = (settings_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (settings_window.winfo_screenheight() // 2) - (300 // 2)
        settings_window.geometry(f"400x300+{x}+{y}")
        
        # 设置内容
        ttk.Label(settings_window, text="程序设置", font=("Arial", 14, "bold")).pack(pady=10)
        
        # 常用合约
        contracts_frame = ttk.LabelFrame(settings_window, text="常用合约", padding="10")
        contracts_frame.pack(fill=tk.X, padx=10, pady=5)
        
        contracts = ["<EMAIL>", "CZCE.OI509", "SHFE.ag2512", "SHFE.cu2512", "DCE.i2512", "CZCE.CF512"]
        for contract in contracts:
            btn = ttk.Button(contracts_frame, text=contract, 
                           command=lambda c=contract: self.symbol_var.set(c))
            btn.pack(side=tk.LEFT, padx=2)
        
        # 关于信息
        about_frame = ttk.LabelFrame(settings_window, text="关于", padding="10")
        about_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        about_text = """
波动交易分析系统 v1.0

功能特点:
• 实时期货K线图显示
• 波动交易技术指标
• 自定义参数设置
• 图表保存功能
• 安全退出功能

使用说明:
1. 输入合约代码
2. 设置分析参数
3. 点击开始分析
4. 观察实时图表
5. 使用退出按钮安全关闭
        """
        
        ttk.Label(about_frame, text=about_text, justify=tk.LEFT).pack()
        
        ttk.Button(settings_window, text="关闭", command=settings_window.destroy).pack(pady=10)

    def exit_program(self):
        """退出程序"""
        try:
            # 询问用户确认
            if messagebox.askyesno("确认退出", "确定要退出波动交易分析系统吗？"):
                self.add_log("INFO", "用户请求退出程序")
                
                # 停止分析
                if self.is_running:
                    self.add_log("INFO", "正在停止分析...")
                    self.stop_analysis()
                
                # 保存日志
                try:
                    log_filename = f"波动交易日志_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                    with open(log_filename, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(self.log_messages))
                    self.add_log("INFO", f"日志已自动保存到: {log_filename}")
                except Exception as e:
                    self.add_log("WARNING", f"日志保存失败: {str(e)}")
                
                # 关闭窗口
                self.add_log("INFO", "程序正常退出")
                self.root.quit()
                self.root.destroy()
                
        except Exception as e:
            self.add_log("ERROR", f"退出程序时发生错误: {str(e)}")
            # 强制退出
            self.root.quit()
            self.root.destroy()

def main():
    """主函数"""
    root = tk.Tk()
    app = VolatilityTradingGUI(root)
    
    def on_closing():
        """关闭程序时的处理"""
        try:
            app.add_log("INFO", "检测到窗口关闭事件")
            if app.is_running:
                app.add_log("INFO", "分析正在运行，正在停止...")
                app.stop_analysis()
            
            # 自动保存日志
            try:
                log_filename = f"波动交易日志_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                with open(log_filename, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(app.log_messages))
                app.add_log("INFO", f"日志已自动保存到: {log_filename}")
            except:
                pass
            
            app.add_log("INFO", "程序正常关闭")
        except:
            pass
        finally:
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
    finally:
        if app.is_running:
            app.stop_analysis()

if __name__ == "__main__":
    main()
