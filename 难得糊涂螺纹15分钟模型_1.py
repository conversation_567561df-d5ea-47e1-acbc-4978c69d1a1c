import pandas as pd
import numpy as np
from typing import List, Tuple
import matplotlib.pyplot as plt


class TradingStrategy:
    def __init__(self):
        self.positions = 0
        self.money_total = 0
        self.last_signal = 0
        self.trade_history = []
        self.ss = 0
        self.tt = 0
        self.bk_price = 0
        self.sk_price = 0

    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate all technical indicators used in the strategy"""
        # 首先创建last_signal列并初始化为0
        df['last_signal'] = 0

        # 基础价格数据
        df['ma_255'] = df['close'].ewm(span=255, adjust=False).mean()
        df['ma_109'] = df['close'].ewm(span=109, adjust=False).mean()

        # 价格波动指标
        df['oc_sum'] = df['open'] + df['close']
        df['tmp'] = df['oc_sum'].rolling(93).std() / 2

        # 日期变化检测
        df['date'] = pd.to_datetime(df['datetime']).dt.date
        df['date_change'] = df['date'] != df['date'].shift(1)

        # 计算OO和CC (日线开盘价和收盘价)
        df['oo'] = np.where(df['date_change'], df['open'], np.nan)
        df['cc'] = np.where(df['date_change'], df['close'], np.nan)
        df['oo'] = df['oo'].ffill()
        df['cc'] = df['cc'].ffill()

        # 计算价格通道
        df['bb'] = df[['oo', 'cc']].max(axis=1) + 0.1 * 5 * df['tmp']
        df['dd'] = df[['oo', 'cc']].min(axis=1) - 0.1 * 5 * df['tmp']

        # K指标计算
        df['low_63'] = df['low'].rolling(63).min()
        df['high_63'] = df['high'].rolling(63).max()
        df['rsv'] = (df['close'] - df['low_63']) / (df['high_63'] - df['low_63']) * 100
        df['k'] = df['rsv'].rolling(18).mean()

        # 短周期RSV计算
        df['low_3'] = df['low'].rolling(3).min()
        df['high_3'] = df['high'].rolling(3).max()
        df['rsv2'] = (df['close'] - df['low_3']) / (df['high_3'] - df['low_3']) * 100
        df['kk'] = df['rsv2'].ewm(span=59, adjust=False).mean()
        df['dd2'] = df['kk'].ewm(span=105, adjust=False).mean()

        # 移动平均线
        df['ma_85'] = df['close'].rolling(85).mean()
        df['ma_190'] = df['close'].rolling(190).mean()

        # 价格动量指标
        df['rc'] = df['close'] / df['close'].shift(1)
        df['arc'] = df['rc'].shift(1).rolling(177).mean()

        # ZL和SH指标
        df['var1'] = (2 * df['close'] + df['high'] + df['low']) / 4
        df['var2'] = df['low'].rolling(63).min()
        df['var3'] = df['high'].rolling(63).max()
        df['zl'] = ((df['var1'] - df['var2']) / (df['var3'] - df['var2']) * 100).ewm(span=373, adjust=False).mean()
        df['sh'] = (0.7 * df['zl'].shift(1) + 0.3 * df['zl']).ewm(span=118, adjust=False).mean()

        # 初始化交易信号记录
        df['signal'] = 0  # 0: 无信号, 1: 买入信号, -1: 卖出信号

        return df

    def check_buy_conditions(self, row: pd.Series) -> bool:
        """检查所有买入条件"""
        if pd.isna(row['k']) or pd.isna(row['ma_255']):  # 检查必要指标是否已经计算出来
            return False

        conditions = []

        # 条件1: 基础趋势条件
        trend_condition = (row['close'] > row['ma_255'] and row['k'] < 48)
        conditions.append(trend_condition)

        # 条件2: 均线交叉条件
        ma_cross_condition = (
                not pd.isna(row['ma_85']) and
                not pd.isna(row['ma_190']) and
                row['ma_85'] > row['ma_190'] and
                row['arc'] > 1
        )
        conditions.append(ma_cross_condition)

        # 条件3: ZL/SH交叉条件
        if not (pd.isna(row['zl']) or pd.isna(row['sh'])):
            zl_cross_condition = ((row['zl'] > row['sh'] and row['sh'] > 54) or
                                  (row['zl'] > row['sh'] and row['sh'] < 48))
            conditions.append(zl_cross_condition)

        # 条件4: KK/DD交叉条件
        if not (pd.isna(row['kk']) or pd.isna(row['dd2']) or pd.isna(row['ma_109'])):
            kk_cross_condition = (row['kk'] > row['dd2'] and row['dd2'] < 1 and
                                  row['ma_109'] > row['ma_109'].shift(1))
            conditions.append(kk_cross_condition)

        return any(conditions)  # 任意条件满足即可

    def check_sell_conditions(self, row: pd.Series) -> bool:
        """检查所有卖出条件"""
        if pd.isna(row['k']) or pd.isna(row['ma_255']):  # 检查必要指标是否已经计算出来
            return False

        conditions = []

        # 条件1: 基础趋势条件
        trend_condition = (row['close'] < row['ma_255'] and row['k'] > 39)
        conditions.append(trend_condition)

        # 条件2: 均线交叉条件
        if not (pd.isna(row['ma_85']) or pd.isna(row['ma_190'])):
            ma_cross_condition = (row['ma_85'] < row['ma_190'] and row['arc'] < 1)
            conditions.append(ma_cross_condition)

        # 条件3: ZL/SH交叉条件
        if not (pd.isna(row['zl']) or pd.isna(row['sh'])):
            zl_cross_condition = ((row['sh'] > row['zl'] and row['zl'] < 45) or
                                  (row['sh'] > row['zl'] and row['zl'] > 72))
            conditions.append(zl_cross_condition)

        # 止损条件
        if self.bk_price > 0:  # 只在有买入价格时检查止损
            stop_loss = row['close'] < (1 - 0.001 * 34) * self.bk_price
            conditions.append(stop_loss)

        # 止盈条件
        if self.bk_price > 0:  # 只在有买入价格时检查止盈
            take_profit = row['close'] > (1 + 0.001 * 272) * self.bk_price
            conditions.append(take_profit)

        return any(conditions)  # 任意条件满足即可

    def backtest(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List]:
        """运行回测"""
        df = self.calculate_indicators(df)

        # 跳过前面的数据，直到足够的历史数据来计算指标
        start_index = max(255, 190, 373, 105)  # 使用最大的指标周期作为起始点

        for i in range(start_index, len(df)):
            row = df.iloc[i]

            if self.positions == 0:  # 无持仓
                if self.check_buy_conditions(row):
                    self.positions = 1
                    self.last_signal = 1
                    self.bk_price = row['close']
                    df.at[i, 'signal'] = 1
                    df.at[i, 'last_signal'] = 1
                    self.trade_history.append({
                        'datetime': row['datetime'],
                        'type': 'buy',
                        'price': row['close'],
                        'size': 1
                    })
            else:  # 有持仓
                if self.check_sell_conditions(row):
                    self.positions = 0
                    self.last_signal = -1
                    self.sk_price = row['close']
                    df.at[i, 'signal'] = -1
                    df.at[i, 'last_signal'] = -1
                    self.trade_history.append({
                        'datetime': row['datetime'],
                        'type': 'sell',
                        'price': row['close'],
                        'size': 1
                    })

        return df, self.trade_history


def analyze_trades(trades: List[dict]) -> None:
    """分析交易结果"""
    if not trades:
        print("没有交易记录")
        return

    trades_df = pd.DataFrame(trades)
    trades_df['datetime'] = pd.to_datetime(trades_df['datetime'])

    # 计算基础统计
    total_trades = len(trades_df)
    buy_trades = trades_df[trades_df['type'] == 'buy']['price'].values
    sell_trades = trades_df[trades_df['type'] == 'sell']['price'].values

    if len(buy_trades) > 0 and len(sell_trades) > 0:
        # 确保买卖数量相等进行配对
        min_trades = min(len(buy_trades), len(sell_trades))
        profits = sell_trades[:min_trades] - buy_trades[:min_trades]

        win_rate = (profits > 0).mean()
        avg_profit = profits.mean()
        max_profit = profits.max()
        max_loss = profits.min()

        print(f"总交易次数: {total_trades}")
        print(f"完整交易次数: {min_trades}")
        print(f"胜率: {win_rate:.2%}")
        print(f"平均收益: {avg_profit:.2f}")
        print(f"最大收益: {max_profit:.2f}")
        print(f"最大亏损: {max_loss:.2f}")


def run_backtest(csv_file: str) -> None:
    """主函数"""
    # 读取数据
    df = pd.read_csv(csv_file)

    # 转换时间格式
    df['datetime'] = pd.to_datetime(df['datetime'], unit='ns')

    # 初始化策略
    strategy = TradingStrategy()

    # 运行回测
    result_df, trades = strategy.backtest(df)

    # 分析交易结果
    analyze_trades(trades)

    # 绘制结果
    plt.figure(figsize=(15, 7))
    plt.plot(result_df['datetime'], result_df['close'], label='Price')
    plt.plot(result_df['datetime'], result_df['ma_255'], label='MA255')
    plt.plot(result_df['datetime'], result_df['bb'], label='Upper Band', linestyle='--')
    plt.plot(result_df['datetime'], result_df['dd'], label='Lower Band', linestyle='--')

    if trades:
        trades_df = pd.DataFrame(trades)
        trades_df['datetime'] = pd.to_datetime(trades_df['datetime'])
        plt.scatter(trades_df[trades_df['type'] == 'buy']['datetime'],
                    trades_df[trades_df['type'] == 'buy']['price'],
                    marker='^', color='g', label='Buy', s=100)
        plt.scatter(trades_df[trades_df['type'] == 'sell']['datetime'],
                    trades_df[trades_df['type'] == 'sell']['price'],
                    marker='v', color='r', label='Sell', s=100)

    plt.legend()
    plt.title('Backtest Results')
    plt.grid(True)
    plt.show()


if __name__ == "__main__":
    run_backtest('data_rb_15.csv')