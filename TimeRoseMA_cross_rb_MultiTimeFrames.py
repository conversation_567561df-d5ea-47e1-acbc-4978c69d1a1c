"""
TimeRoseMA_cross_SH 多时间周期统一策略
整合 1m, 3m, 5m, 15m 时间周期，共用一个API连接
"""

import copy
import time
from typing import Dict, List
from tqsdk import TqApi, TqKq
from strategies.TimeRoseMA_cross_speak import ma_cross
import threading
from concurrent.futures import ThreadPoolExecutor
import queue


class TimeFrameConfig:
    """时间周期配置类"""
    def __init__(self, interval: int, bklimit: int = 1, sklimit: int = 1, single_volume: int = 1):
        self.interval = interval  # 时间间隔（秒）
        self.bklimit = bklimit    # 多单限制
        self.sklimit = sklimit    # 空单限制
        self.single_volume = single_volume  # 单次交易量
        self.name = f"{interval//60}m"  # 时间周期名称


class MultiTimeFrameStrategy:
    """多时间周期策略管理器（多线程模式）"""

    def __init__(self, api: TqApi, symbol: str, timeframe_configs: List[TimeFrameConfig], auth: str = None):
        self.symbol = symbol
        self.timeframe_configs = timeframe_configs
        self.auth = auth or "quant_ggh,Qiai1301"
        self.running = False
        self.threads = []
        self.apis = {}  # 为每个线程创建独立的API

    def run_single_timeframe(self, config: TimeFrameConfig):
        """运行单个时间周期的策略（独立API）"""
        try:
            print(f"启动 {config.name} 时间周期策略...")

            # 为每个时间周期创建独立的API连接
            api = TqApi(TqKq(), auth=self.auth, disable_print=True)
            self.apis[config.name] = api

            # 获取合约
            if len(self.symbol) <= 3:
                symbol = api.query_cont_quotes(product_id=self.symbol)[0]
            else:
                symbol = self.symbol

            print(f"{config.name} 策略使用合约: {symbol}")

            ma_cross(
                api=api,
                symbol=symbol,
                interval=config.interval,
                single_volume=config.single_volume,
                bklimit=config.bklimit,
                sklimit=config.sklimit
            )
        except Exception as e:
            print(f"{config.name} 时间周期策略出错: {e}")
        finally:
            # 清理API连接
            if config.name in self.apis:
                self.apis[config.name].close()
                del self.apis[config.name]

    def run_strategy(self):
        """启动所有时间周期策略"""
        self.running = True

        print("=== 启动多线程多时间周期策略 ===")

        # 为每个时间周期创建独立的线程
        for config in self.timeframe_configs:
            thread = threading.Thread(
                target=self.run_single_timeframe,
                args=(config,),
                name=f"TimeFrame_{config.name}",
                daemon=True
            )
            self.threads.append(thread)
            thread.start()

        print(f"已启动 {len(self.timeframe_configs)} 个时间周期策略线程")

        # 等待所有线程
        try:
            for thread in self.threads:
                thread.join()
        except KeyboardInterrupt:
            print("策略被用户中断")
            self.stop()

    def stop(self):
        """停止所有策略"""
        self.running = False
        print("正在停止所有时间周期策略...")

        # 关闭所有API连接
        for name, api in self.apis.items():
            try:
                api.close()
                print(f"已关闭 {name} 的API连接")
            except:
                pass


class OptimizedMultiTimeFrameStrategy:
    """优化的多时间周期策略（单API，多时间周期并行处理）"""

    def __init__(self, symbol: str, timeframe_configs: List[TimeFrameConfig], auth: str):
        self.symbol = symbol
        self.timeframe_configs = timeframe_configs
        self.auth = auth
        self.api = None
        self.running = False
        self.strategy_threads = {}

    def initialize_api(self):
        """初始化API连接"""
        self.api = TqApi(TqKq(), auth=self.auth, disable_print=True)
        # 如果symbol是产品代码，获取主力合约
        if len(self.symbol) <= 3:
            self.symbol = self.api.query_cont_quotes(product_id=self.symbol)[0]
        print(f"交易合约: {self.symbol}")

    def run_strategy(self):
        """运行策略主循环 - 顺序执行方案"""
        self.initialize_api()
        self.running = True

        print("=== 开始运行多时间周期策略 ===")

        try:
            while self.running:
                # 顺序处理每个时间周期
                for config in self.timeframe_configs:
                    if not self.running:
                        break
                    self.process_timeframe(config)

                # 等待API更新
                self.api.wait_update()

        except KeyboardInterrupt:
            print("策略被用户中断")
        except Exception as e:
            print(f"策略执行出错: {e}")
        finally:
            self.cleanup()

    def process_timeframe(self, config: TimeFrameConfig):
        """处理单个时间周期"""
        try:
            # 这里可以添加时间周期特定的逻辑
            # 由于ma_cross函数是阻塞的，我们需要修改调用方式
            pass
        except Exception as e:
            print(f"{config.name} 处理出错: {e}")

    def cleanup(self):
        """清理资源"""
        self.running = False
        if self.api:
            self.api.close()
            print("API连接已关闭")


class SimpleMultiTimeFrameStrategy:
    """简化的多时间周期策略（独立API方案）"""

    def __init__(self, symbol: str, timeframe_configs: List[TimeFrameConfig], auth: str):
        self.symbol = symbol
        self.timeframe_configs = timeframe_configs
        self.auth = auth
        self.processes = []

    def run_strategy(self):
        """运行策略 - 每个时间周期独立进程"""
        print("=== 启动多时间周期策略（独立API模式）===")

        try:
            # 为每个时间周期创建独立的进程
            for config in self.timeframe_configs:
                self.start_timeframe_process(config)

            # 等待用户中断
            while True:
                time.sleep(1)

        except KeyboardInterrupt:
            print("策略被用户中断")
        finally:
            self.cleanup()

    def start_timeframe_process(self, config: TimeFrameConfig):
        """启动单个时间周期的独立进程"""
        import subprocess
        import sys

        # 创建独立的Python脚本来运行单个时间周期
        script_content = f'''
from strategies.TimeRoseMA_cross_speak import ma_cross
from tqsdk import TqApi, TqKq

def run_single_timeframe():
    product = "{self.symbol}"
    interval = {config.interval}
    bklimit = {config.bklimit}
    sklimit = {config.sklimit}
    single_volume = {config.single_volume}

    api = TqApi(TqKq(), auth="{self.auth}")
    if len(product) <= 3:
        symbol = api.query_cont_quotes(product_id=product)[0]
    else:
        symbol = product

    print(f"启动 {config.name} 时间周期策略，合约: {{symbol}}")
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)

if __name__ == "__main__":
    run_single_timeframe()
'''

        # 写入临时脚本文件
        script_filename = f"temp_strategy_{config.name}.py"
        with open(script_filename, 'w', encoding='utf-8') as f:
            f.write(script_content)

        # 启动子进程
        process = subprocess.Popen([sys.executable, script_filename])
        self.processes.append((process, script_filename))
        print(f"已启动 {config.name} 策略进程 (PID: {process.pid})")

    def cleanup(self):
        """清理资源"""
        import os

        # 终止所有子进程
        for process, script_file in self.processes:
            if process.poll() is None:  # 进程还在运行
                process.terminate()
                print(f"已终止进程 {process.pid}")

            # 删除临时脚本文件
            try:
                os.remove(script_file)
            except:
                pass


def create_sh_timeframe_configs() -> List[TimeFrameConfig]:
    """创建SH的时间周期配置"""
    return [
        TimeFrameConfig(interval=60, bklimit=1, sklimit=1, single_volume=1),      # 1分钟
        TimeFrameConfig(interval=180, bklimit=1, sklimit=1, single_volume=1),     # 3分钟
        TimeFrameConfig(interval=300, bklimit=1, sklimit=1, single_volume=1),     # 5分钟
        TimeFrameConfig(interval=900, bklimit=1, sklimit=1, single_volume=1),     # 15分钟
    ]


def run_sh_multi_timeframe_strategy(mode: str = "independent"):
    """
    运行SH多时间周期策略的主函数

    Args:
        mode: 运行模式
            - "independent": 独立API模式（推荐，稳定性好）
            - "optimized": 优化模式（单API，需要修改ma_cross函数）
            - "threaded": 多线程模式（可能有API冲突）
    """

    # 配置参数
    product = 'rb'
    auth = "quant_ggh,Qiai1301"

    # 创建时间周期配置
    timeframe_configs = create_sh_timeframe_configs()

    print("=== SH多时间周期策略启动 ===")
    print(f"产品: {product}")
    print(f"运行模式: {mode}")
    print(f"时间周期: {[config.name for config in timeframe_configs]}")

    # 根据模式选择策略类
    if mode == "independent":
        strategy = SimpleMultiTimeFrameStrategy(
            symbol=product,
            timeframe_configs=timeframe_configs,
            auth=auth
        )
    elif mode == "optimized":
        strategy = OptimizedMultiTimeFrameStrategy(
            symbol=product,
            timeframe_configs=timeframe_configs,
            auth=auth
        )
    elif mode == "threaded":
        strategy = MultiTimeFrameStrategy(
            api=None,  # 将在内部创建
            symbol=product,
            timeframe_configs=timeframe_configs,
            auth=auth
        )
    else:
        raise ValueError(f"不支持的运行模式: {mode}")

    try:
        strategy.run_strategy()
    except KeyboardInterrupt:
        print("策略已停止")
    except Exception as e:
        print(f"策略运行出错: {e}")


def run_single_timeframe(timeframe: str):
    """运行单个时间周期策略（兼容原有文件）"""

    timeframe_map = {
        "1m": 60,
        "3m": 180,
        "5m": 300,
        "15m": 900
    }

    if timeframe not in timeframe_map:
        raise ValueError(f"不支持的时间周期: {timeframe}")

    from strategies.TimeRoseMA_cross_speak import ma_cross
    from tqsdk import TqApi, TqKq

    product = 'rb'
    interval = timeframe_map[timeframe]
    bklimit = 1
    sklimit = 1
    single_volume = 1

    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")
    symbol = api.query_cont_quotes(product_id=product)[0]

    print(f"运行 SH {timeframe} 策略，合约: {symbol}")
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


if __name__ == "__main__":
    import sys

    # 支持命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg in ["1m", "3m", "5m", "15m"]:
            # 运行单个时间周期
            run_single_timeframe(arg)
        elif arg in ["independent", "optimized", "threaded"]:
            # 运行多时间周期
            run_sh_multi_timeframe_strategy(arg)
        else:
            print("用法:")
            print("  python TimeRoseMA_cross_SH_MultiTimeFrames.py [1m|3m|5m|15m]  # 单时间周期")
            print("  python TimeRoseMA_cross_SH_MultiTimeFrames.py [independent|optimized|threaded]  # 多时间周期")
    else:
        # 默认运行独立API模式的多时间周期策略
        run_sh_multi_timeframe_strategy("independent")
        # run_sh_multi_timeframe_strategy("optimized")
        # run_sh_multi_timeframe_strategy("threaded")