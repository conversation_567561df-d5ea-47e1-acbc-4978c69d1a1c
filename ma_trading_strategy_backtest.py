import pandas as pd
import numpy as np
from datetime import datetime


def calculate_improved_ma(series, window=13):
    """计算改进后的移动平均线"""
    ma = series.rolling(window=window, min_periods=1).mean()
    return ma


def trading_strategy(df):
    # 计算改进后的13周期均线
    df['MA'] = calculate_improved_ma(df['close'], window=13)

    # 初始化变量
    current_position = 0
    trades = []
    portfolio_value = 100000  # 初始资金100000元
    contract_multiplier = 10  # 合约乘数
    signal_profits = []  # 记录每个信号结束后的盈亏
    entry_price = 0  # 记录入场价格
    current_quantity = 1  # 当前下单数量
    consecutive_losses = 0  # 连续亏损次数

    # 遍历数据
    for i in range(1, len(df)):  # 从第二个数据点开始，因为我们需要比较前一个点的均线
        row = df.iloc[i]
        prev_row = df.iloc[i - 1]

        # 生成交易信号
        if row['close'] > row['MA'] and prev_row['close'] <= prev_row['MA']:
            signal = 1  # 买入信号
        elif row['close'] < row['MA'] and prev_row['close'] >= prev_row['MA']:
            signal = -1  # 卖出信号
        else:
            signal = 0  # 无信号

        if signal != 0:
            if current_position == 0:
                # 开仓
                current_position = signal
                entry_price = row['close']

                trades.append({
                    'datetime': row.name,
                    'price': entry_price,
                    'action': '开多单' if signal == 1 else '开空单',
                    'quantity': current_quantity,
                    'symbol': row['symbol'],
                    'portfolio_value': portfolio_value
                })
            else:
                # 平仓
                exit_price = row['close']
                price_diff = exit_price - entry_price if current_position > 0 else entry_price - exit_price
                profit = price_diff * contract_multiplier * current_quantity
                portfolio_value += profit

                trades.append({
                    'datetime': row.name,
                    'price': exit_price,
                    'action': '平多单' if current_position > 0 else '平空单',
                    'quantity': current_quantity,
                    'symbol': row['symbol'],
                    'profit': profit,
                    'portfolio_value': portfolio_value
                })

                # 记录本次信号的盈亏
                signal_profits.append(profit)

                # 更新连续亏损次数和下单数量
                if profit < 0:
                    consecutive_losses += 1
                    current_quantity = min(current_quantity + 1, 10)  # 增加下单数量，但不超过10
                else:
                    consecutive_losses = 0
                    current_quantity = 1  # 盈利后重置为1

                # 反向开仓
                current_position = signal
                entry_price = exit_price

                trades.append({
                    'datetime': row.name,
                    'price': entry_price,
                    'action': '开多单' if signal == 1 else '开空单',
                    'quantity': current_quantity,
                    'symbol': row['symbol'],
                    'portfolio_value': portfolio_value
                })

        df.at[df.index[i], 'position'] = current_position

    return df, trades, signal_profits


# 读取CSV文件
df = pd.read_csv('data5.csv')

# 将datetime列转换为datetime类型并设置为索引
df['datetime'] = pd.to_datetime(df['datetime'], unit='ns')
df.set_index('datetime', inplace=True)
df.sort_index(inplace=True)

# 运行交易策略
df, trades, signal_profits = trading_strategy(df)

# 打印交易记录
for trade in trades:
    print(f"时间: {trade['datetime']}, 动作: {trade['action']}, 价格: {trade['price']}, 数量: {trade['quantity']}, 交易品种: {trade['symbol']}")
    if 'profit' in trade:
        print(f"利润: {trade['profit']:.2f}")
    print(f"组合价值: {trade['portfolio_value']:.2f}")
    print("---")

# 计算总盈亏
total_profit = sum(trade['profit'] for trade in trades if 'profit' in trade)
print(f"总盈亏: {total_profit:.2f}")

# 计算胜率
winning_trades = sum(1 for trade in trades if 'profit' in trade and trade['profit'] > 0)
total_trades = sum(1 for trade in trades if 'profit' in trade)
win_rate = winning_trades / total_trades if total_trades > 0 else 0
print(f"胜率: {win_rate:.2%}")

# 计算最终组合价值
final_portfolio_value = trades[-1]['portfolio_value'] if trades else 100000
print(f"最终组合价值: {final_portfolio_value:.2f}")

# 打印每个信号结束后的盈亏
print("每个信号结束后的盈亏:")
# for i, profit in enumerate(signal_profits, 1):
#     print(f"信号 {i}: {profit:.2f}")