
import sys
print("Python版本:", sys.version)
print("参数:", sys.argv)

try:
    from tqsdk import TqApi, TqKq
    print("✓ TqSDK导入成功")
    
    # 尝试创建API连接
    if len(sys.argv) > 1:
        auth = sys.argv[1]
        print(f"使用认证: {auth}")
        api = TqApi(TqKq(), auth=auth, disable_print=True)
        print("✓ API连接成功")
        api.close()
        print("✓ API关闭成功")
    else:
        print("未提供认证参数")
        
except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback
    traceback.print_exc()

print("测试脚本执行完成")
