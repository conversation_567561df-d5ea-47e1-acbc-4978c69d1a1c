
# 主程序
from tqsdk import Tq<PERSON><PERSON>, TqAuth, TqKq
from multiTimeFrames_base import MultiTimeframeStrategy
from utils.utils import parse_time, get_time_period, is_trading_time
import time


if __name__ == "__main__":
    from tqsdk import TqAccount
    from accounts_zjy import tgjyzjy as acct

    try:
        # product_id = 'OI'
        product_id = 'rb'
        # product_id = 'SA'
        api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
        symbol = api.query_cont_quotes(product_id=product_id)[0]
        # symbol = 'CZCE.OI505'
        print(f"交易的合约是: {symbol}")
        time_periods = get_time_period(api, symbol)

        # 定义交易周期（以秒为单位）和每个周期的持仓限制
        # timeframes = [60, 180, 800, 900]  # 1分钟, 3分钟, 13分20秒, 15分钟
        max_positions = {
            15: {'long': 0, 'short': 0},
            60: {'long': 1, 'short': 1},
            180: {'long': 1, 'short': 1},
            300: {'long': 1, 'short': 1},
            800: {'long': 0, 'short': 0},
            900: {'long': 0, 'short': 0}
        }

        while True:

            if is_trading_time(time_periods):
                try:
                    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
                    strategy = MultiTimeframeStrategy(api, symbol, max_positions)
                    while True:
                        api.wait_update()
                        strategy.update()
                except Exception as e:
                    print(e)
                    time.sleep(10)
            else:
                print('非交易时间:', time.asctime())
                time.sleep(1)
                # continue

    except KeyboardInterrupt:
        print("策略已停止")

    finally:
        api.close()
