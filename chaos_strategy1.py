#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于混沌理论的交易策略
使用李亚普诺夫指数来检测市场是否处于混沌状态，并结合RSI进行交易决策
"""

from tqsdk import TqApi, TqAuth, TargetPosTask
from tqsdk.ta import RSI
import numpy as np
import datetime
import time

# 配置参数
SYMBOL = "SHFE.au2106"  # 交易的合约代码
LYAPUNOV_WINDOW = 50  # 李亚普诺夫指数计算窗口
RSI_WINDOW = 14  # RSI计算窗口
RSI_OVERBOUGHT = 70  # RSI超买阈值
RSI_OVERSOLD = 30  # RSI超卖阈值
POSITION_RATE = 0.5  # 仓位比例


def calculate_lyapunov_exponent(price_series, m=2, tau=1, eps=0.01, max_iterations=20):
    """
    计算李亚普诺夫指数，用于判断时间序列是否呈现混沌特性

    参数:
    price_series: 价格序列
    m: 嵌入维度
    tau: 时间延迟
    eps: 邻近点距离阈值
    max_iterations: 最大迭代次数

    返回:
    lyapunov_exp: 李亚普诺夫指数
    """
    n = len(price_series)
    if n < m * tau + max_iterations:
        return 0

    # 构建相空间
    phase_space = []
    for i in range(n - (m - 1) * tau):
        phase_point = [price_series[i + j * tau] for j in range(m)]
        phase_space.append(phase_point)

    # 计算李亚普诺夫指数
    divergence_sum = 0
    divergence_count = 0

    for i in range(len(phase_space) - max_iterations):
        # 寻找临近点
        min_dist = float('inf')
        nearest_index = -1

        for j in range(len(phase_space)):
            if i != j:
                dist = np.linalg.norm(np.array(phase_space[i]) - np.array(phase_space[j]))
                if dist < min_dist and dist > 0:
                    min_dist = dist
                    nearest_index = j

        if nearest_index != -1 and min_dist < eps:
            # 计算轨道分离
            divergence = 0
            valid_iterations = 0

            for k in range(1, max_iterations + 1):
                if i + k < len(phase_space) and nearest_index + k < len(phase_space):
                    d_k = np.linalg.norm(np.array(phase_space[i + k]) - np.array(phase_space[nearest_index + k]))
                    if d_k > 0:
                        divergence += np.log(d_k / min_dist)
                        valid_iterations += 1

            if valid_iterations > 0:
                divergence_sum += divergence / valid_iterations
                divergence_count += 1

    if divergence_count > 0:
        lyapunov_exp = divergence_sum / divergence_count
        return lyapunov_exp
    else:
        return 0


def is_chaotic(prices, threshold=0.01):
    """
    判断市场是否处于混沌状态

    参数:
    prices: 价格序列
    threshold: 混沌阈值

    返回:
    is_chaotic: 布尔值，True表示处于混沌状态
    """
    lyapunov_exp = calculate_lyapunov_exponent(prices)
    return lyapunov_exp > threshold, lyapunov_exp


def main():
    """主函数"""
    print("正在启动混沌交易系统...")

    # 创建API实例
    api = TqApi(auth=TqAuth("您的用户名", "您的密码"))

    # 订阅行情
    quote = api.get_quote(SYMBOL)
    klines = api.get_kline_serial(SYMBOL, 60 * 60)  # 1小时K线

    # 创建 目标持仓 任务
    target_pos = TargetPosTask(api, SYMBOL)

    # 策略状态变量
    position = 0  # 当前持仓方向，1为多，-1为空，0为空仓

    print(f"开始交易 {SYMBOL}，混沌窗口: {LYAPUNOV_WINDOW}，RSI窗口: {RSI_WINDOW}")

    try:
        while True:
            # 等待K线更新
            api.wait_update()

            if api.is_changing(klines.iloc[-1], "datetime"):
                # 计算指标
                prices = klines.close.tolist()

                # 检查数据是否足够
                if len(prices) < max(LYAPUNOV_WINDOW, RSI_WINDOW):
                    continue

                # 计算混沌状态
                chaos_flag, lyapunov = is_chaotic(prices[-LYAPUNOV_WINDOW:])

                # 计算RSI
                rsi_values = RSI(klines, RSI_WINDOW)
                current_rsi = rsi_values.iloc[-1]

                # 获取当前价格
                current_price = klines.close.iloc[-1]

                # 交易逻辑
                if chaos_flag:  # 市场处于混沌状态
                    print(f"检测到混沌状态，李亚普诺夫指数: {lyapunov:.4f}, RSI: {current_rsi:.2f}")

                    # 在混沌状态下使用RSI进行交易决策
                    if current_rsi < RSI_OVERSOLD and position <= 0:
                        # RSI超卖，做多
                        print(f"混沌+RSI超卖信号，开多仓，时间: {datetime.datetime.now()}, 价格: {current_price}")
                        position = 1
                        # 计算目标持仓量
                        target_volume = int(quote.volume_multiple * POSITION_RATE)
                        target_pos.set_target_volume(target_volume)

                    elif current_rsi > RSI_OVERBOUGHT and position >= 0:
                        # RSI超买，做空
                        print(f"混沌+RSI超买信号，开空仓，时间: {datetime.datetime.now()}, 价格: {current_price}")
                        position = -1
                        # 计算目标持仓量
                        target_volume = -int(quote.volume_multiple * POSITION_RATE)
                        target_pos.set_target_volume(target_volume)

                else:  # 市场不处于混沌状态
                    # 如果有持仓且市场不再混沌，考虑平仓
                    if position != 0:
                        print(f"市场退出混沌状态，平仓，时间: {datetime.datetime.now()}, 价格: {current_price}")
                        position = 0
                        target_pos.set_target_volume(0)

                # 输出状态信息
                print(f"当前状态 - 时间: {datetime.datetime.now()}, 价格: {current_price}, "
                      f"混沌: {'是' if chaos_flag else '否'}, 李亚普诺夫指数: {lyapunov:.4f}, RSI: {current_rsi:.2f}, "
                      f"持仓: {position}")

            # 防止请求过于频繁
            time.sleep(3)

    except KeyboardInterrupt:
        print("策略已手动停止")
    finally:
        # 关闭API
        api.close()
        print("策略已结束")


if __name__ == "__main__":
    main()