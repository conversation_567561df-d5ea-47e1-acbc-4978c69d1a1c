"""
测试新的主力合约获取方法
使用 api.query_cont_quotes() 不传参数
"""

from multi_contract_config import MainContractFetcher, get_current_main_contracts
from loguru import logger

def test_api_query_cont_quotes():
    """测试直接调用 api.query_cont_quotes()"""
    print("=" * 60)
    print("测试 api.query_cont_quotes() 直接调用")
    print("=" * 60)
    
    # 设置详细日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='DEBUG', colorize=True)
    
    try:
        with MainContractFetcher() as fetcher:
            print("\n1. 直接调用 api.query_cont_quotes():")
            
            # 直接调用API
            all_quotes = fetcher.api.query_cont_quotes()
            
            print(f"返回数据类型: {type(all_quotes)}")
            print(f"返回数据长度: {len(all_quotes) if all_quotes else 0}")
            
            if all_quotes and len(all_quotes) > 0:
                print(f"\n前5个合约数据:")
                for i, quote in enumerate(all_quotes[:5]):
                    print(f"  {i+1}. 类型: {type(quote)}, 内容: {quote}")
                    
                    # 尝试获取字段
                    if hasattr(quote, 'instrument_id'):
                        print(f"     instrument_id: {quote.instrument_id}")
                    if hasattr(quote, 'symbol'):
                        print(f"     symbol: {quote.symbol}")
                    if hasattr(quote, '__dict__'):
                        print(f"     属性: {list(quote.__dict__.keys())}")
                    elif isinstance(quote, dict):
                        print(f"     字段: {list(quote.keys())}")
                
                if len(all_quotes) > 5:
                    print(f"  ... 还有 {len(all_quotes) - 5} 个合约")
            else:
                print("返回数据为空")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_new_main_contract_fetcher():
    """测试新的主力合约获取器"""
    print("=" * 60)
    print("测试新的主力合约获取器")
    print("=" * 60)
    
    # 设置INFO级别日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        with MainContractFetcher() as fetcher:
            print("\n2. 测试获取特定品种主力合约:")
            
            # 测试几个主要品种
            test_products = [
                ("CZCE", "OI", "菜籽油"),
                ("SHFE", "cu", "沪铜"),
                ("DCE", "m", "豆粕"),
                ("SHFE", "au", "沪金"),
                ("DCE", "i", "铁矿石"),
            ]
            
            results = {}
            
            for exchange, product_id, name in test_products:
                print(f"\n--- 测试 {name} ({exchange}.{product_id}) ---")
                
                main_contract = fetcher.get_main_contract(exchange, product_id)
                results[f"{exchange}.{product_id}"] = main_contract
                
                if main_contract:
                    print(f"✅ {name} 主力合约: {main_contract}")
                else:
                    print(f"❌ {name} 主力合约获取失败")
            
            print(f"\n📊 获取结果汇总:")
            success_count = sum(1 for v in results.values() if v)
            print(f"成功: {success_count}/{len(results)}")
            
            for key, symbol in results.items():
                status = "✅" if symbol else "❌"
                print(f"  {status} {key}: {symbol or '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_batch_main_contracts():
    """测试批量获取主力合约"""
    print("=" * 60)
    print("测试批量获取主力合约")
    print("=" * 60)
    
    # 设置INFO级别日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        print("\n3. 测试批量获取农产品主力合约:")
        
        agricultural_contracts = get_current_main_contracts(['agricultural'])
        
        print(f"\n✅ 成功获取 {len(agricultural_contracts)} 个农产品主力合约:")
        print("-" * 40)
        
        # 按交易所分类显示
        czce_contracts = []
        dce_contracts = []
        
        for key, symbol in agricultural_contracts.items():
            exchange, product_id = key.split('.')
            if exchange == "CZCE":
                czce_contracts.append((product_id, symbol))
            elif exchange == "DCE":
                dce_contracts.append((product_id, symbol))
        
        if czce_contracts:
            print("郑商所 (CZCE):")
            for product_id, symbol in czce_contracts:
                print(f"  {product_id:4}: {symbol}")
        
        if dce_contracts:
            print("大商所 (DCE):")
            for product_id, symbol in dce_contracts:
                print(f"  {product_id:4}: {symbol}")
        
        # 特别检查菜籽油
        if 'CZCE.OI' in agricultural_contracts:
            oi_contract = agricultural_contracts['CZCE.OI']
            print(f"\n🎯 菜籽油主力合约: {oi_contract}")
            print("✅ 菜籽油已成功包含在分析结果中！")
        else:
            print("\n❌ 菜籽油仍未找到")
        
        print(f"\n4. 测试金属主力合约:")
        
        metals_contracts = get_current_main_contracts(['metals'])
        
        print(f"\n✅ 成功获取 {len(metals_contracts)} 个金属主力合约:")
        for key, symbol in metals_contracts.items():
            exchange, product_id = key.split('.')
            print(f"  {product_id:4}: {symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_all_product_groups():
    """测试所有品种组"""
    print("=" * 60)
    print("测试所有品种组")
    print("=" * 60)
    
    # 设置INFO级别日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        print("\n5. 测试所有品种组:")
        
        all_groups = ['agricultural', 'metals', 'chemicals', 'ferrous', 'energy']
        total_contracts = 0
        
        for group in all_groups:
            print(f"\n--- {group.upper()} ---")
            
            try:
                contracts = get_current_main_contracts([group])
                print(f"✅ {group}: {len(contracts)} 个主力合约")
                
                # 显示前3个
                for i, (key, symbol) in enumerate(list(contracts.items())[:3]):
                    exchange, product_id = key.split('.')
                    print(f"  {product_id:4}: {symbol}")
                
                if len(contracts) > 3:
                    print(f"  ... 还有 {len(contracts) - 3} 个")
                
                total_contracts += len(contracts)
                
            except Exception as e:
                print(f"❌ {group}: 获取失败 - {e}")
        
        print(f"\n📊 总计获取 {total_contracts} 个主力合约")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("新主力合约获取方法测试")
    print("=" * 80)
    
    tests = [
        ("API直接调用测试", test_api_query_cont_quotes),
        ("新主力合约获取器", test_new_main_contract_fetcher),
        ("批量获取测试", test_batch_main_contracts),
        ("所有品种组测试", test_all_product_groups),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:25}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 新主力合约获取方法测试成功！")
        print("✅ 菜籽油等所有品种都应该能正确获取主力合约了")
    else:
        print("⚠️  部分测试失败，需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
