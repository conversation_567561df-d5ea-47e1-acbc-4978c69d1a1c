# 多时间周期策略命令行参数使用指南

## 🎯 功能概述

已成功将 `TimeRoseMA_cross_ag_MultiTimeFrames.py` 程序修改为支持命令行参数，现在可以通过命令行指定不同的合约代码，而不再局限于硬编码的 `ag`。

## 📝 修改内容

### 主要改动
1. **函数重命名**：
   - `run_sh_multi_timeframe_strategy()` → `run_multi_timeframe_strategy()`
   - `create_sh_timeframe_configs()` → `create_default_timeframe_configs()`

2. **参数化支持**：
   - 合约代码作为函数参数传入
   - 认证信息可自定义
   - 运行模式可选择

3. **命令行解析**：
   - 完整的参数解析逻辑
   - 错误处理和帮助信息
   - 多种使用模式支持

## 🚀 使用方法

### 基本语法
```bash
python TimeRoseMA_cross_ag_MultiTimeFrames.py <product> [timeframe|mode] [auth]
```

### 参数说明
- **product**: 合约代码（必需）
  - 支持：ag, rb, cu, au, ni, zn, al, pb, sn 等
- **timeframe**: 单时间周期模式（可选）
  - 选项：1m, 3m, 5m, 15m
- **mode**: 多时间周期模式（可选）
  - 选项：independent, optimized, threaded
- **auth**: TqSDK认证信息（可选）
  - 格式：username,password
  - 默认：quant_ggh,Qiai1301

## 📋 使用示例

### 1. 多时间周期策略

#### 默认模式（推荐）
```bash
# 运行ag多时间周期策略（默认独立API模式）
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag

# 运行rb多时间周期策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py rb

# 运行cu多时间周期策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py cu
```

#### 指定运行模式
```bash
# 独立API模式（推荐，稳定性好）
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag independent

# 优化模式（单API，需要修改ma_cross函数）
python TimeRoseMA_cross_ag_MultiTimeFrames.py rb optimized

# 多线程模式（可能有API冲突）
python TimeRoseMA_cross_ag_MultiTimeFrames.py cu threaded
```

### 2. 单时间周期策略

```bash
# 运行ag 1分钟策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag 1m

# 运行rb 5分钟策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py rb 5m

# 运行cu 15分钟策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py cu 15m

# 运行au 3分钟策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py au 3m
```

### 3. 自定义认证

```bash
# 使用自定义认证信息运行ag策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag 1m "username,password"

# 使用自定义认证运行多时间周期策略
python TimeRoseMA_cross_ag_MultiTimeFrames.py rb independent "user,pass"
```

## 🔧 支持的合约代码

### 有色金属
- **ag**: 白银
- **cu**: 铜
- **al**: 铝
- **zn**: 锌
- **pb**: 铅
- **sn**: 锡
- **ni**: 镍

### 黑色金属
- **rb**: 螺纹钢
- **hc**: 热轧卷板
- **i**: 铁矿石
- **j**: 焦炭
- **jm**: 焦煤

### 贵金属
- **au**: 黄金
- **ag**: 白银

### 能源化工
- **fu**: 燃料油
- **sc**: 原油
- **ma**: 甲醇
- **pp**: 聚丙烯

## ⚙️ 运行模式说明

### 1. Independent（独立API模式）- 推荐
- 每个时间周期使用独立的API连接
- 稳定性最好，不会相互干扰
- 资源消耗稍高

### 2. Optimized（优化模式）
- 所有时间周期共用一个API连接
- 需要修改ma_cross函数以支持共享API
- 资源消耗最低

### 3. Threaded（多线程模式）
- 使用多线程并发运行
- 可能存在API冲突问题
- 适合测试和开发

## 🛠️ 错误处理

### 常见错误及解决方案

#### 1. 缺少合约代码参数
```bash
错误: 缺少合约代码参数
```
**解决**: 必须提供合约代码作为第一个参数

#### 2. 不支持的参数
```bash
错误: 不支持的参数 'invalid_timeframe'
```
**解决**: 检查时间周期或模式参数是否正确

#### 3. TqSDK认证失败
```bash
TqSDK认证失败
```
**解决**: 检查认证信息是否正确，或使用默认认证

## 📊 测试验证

### 运行测试脚本
```bash
python test_multi_timeframe_args.py
```

测试脚本会验证：
- ✅ 帮助信息显示
- ✅ 参数解析正确性
- ✅ 错误处理机制
- ✅ 各种使用模式

## 🎯 实际使用建议

### 1. 生产环境推荐
```bash
# 使用独立API模式，稳定性最好
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag independent
```

### 2. 开发测试推荐
```bash
# 使用单时间周期，便于调试
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag 5m
```

### 3. 资源受限环境
```bash
# 使用优化模式，资源消耗最低
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag optimized
```

## 📈 使用场景

### 1. 多品种策略
```bash
# 同时运行多个品种（需要多个终端）
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag &
python TimeRoseMA_cross_ag_MultiTimeFrames.py rb &
python TimeRoseMA_cross_ag_MultiTimeFrames.py cu &
```

### 2. 不同时间周期测试
```bash
# 测试不同时间周期的效果
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag 1m
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag 5m
python TimeRoseMA_cross_ag_MultiTimeFrames.py ag 15m
```

### 3. 批量部署
```bash
# 创建批处理脚本
echo "python TimeRoseMA_cross_ag_MultiTimeFrames.py ag" > start_ag.bat
echo "python TimeRoseMA_cross_ag_MultiTimeFrames.py rb" > start_rb.bat
echo "python TimeRoseMA_cross_ag_MultiTimeFrames.py cu" > start_cu.bat
```

## 🔍 注意事项

1. **认证信息**: 确保TqSDK认证信息有效
2. **网络连接**: 需要稳定的网络连接
3. **资源管理**: 多个策略同时运行时注意资源消耗
4. **风险控制**: 实盘交易前请充分测试
5. **日志监控**: 建议配置日志文件监控策略运行状态

## 🎉 总结

通过这次修改，`TimeRoseMA_cross_ag_MultiTimeFrames.py` 程序现在具备了：

✅ **灵活的合约选择**: 支持任意合约代码  
✅ **多种运行模式**: 适应不同使用场景  
✅ **完善的参数处理**: 错误检查和帮助信息  
✅ **向后兼容**: 保持原有功能不变  
✅ **易于使用**: 清晰的命令行接口  

这使得策略程序更加通用和实用，可以轻松应用于不同的交易品种！
