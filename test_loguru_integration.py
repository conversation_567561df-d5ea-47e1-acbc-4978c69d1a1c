"""
测试loguru集成效果
验证日志输出是否正常工作
"""

import sys
import time
from loguru import logger
from llt_strategy_refactored import setup_logging, LLTIndicator, TradingConfig
import pandas as pd
import numpy as np


def test_loguru_basic():
    """测试基本的loguru功能"""
    print("=" * 60)
    print("测试基本loguru功能")
    print("=" * 60)
    
    # 设置日志
    setup_logging("INFO")
    
    # 测试不同级别的日志
    logger.debug("这是DEBUG级别日志")
    logger.info("这是INFO级别日志")
    logger.success("这是SUCCESS级别日志")
    logger.warning("这是WARNING级别日志")
    logger.error("这是ERROR级别日志")
    
    # 测试带参数的日志
    name = "LLT策略"
    version = "2.0"
    logger.info(f"程序: {name}, 版本: {version}")
    
    # 测试emoji和特殊字符
    logger.info("🎯 开始测试")
    logger.success("✅ 测试成功")
    logger.warning("⚠️  警告信息")
    logger.error("❌ 错误信息")
    
    print("基本loguru功能测试完成")
    return True


def test_llt_with_loguru():
    """测试LLT指标计算的日志输出"""
    print("=" * 60)
    print("测试LLT指标计算日志")
    print("=" * 60)
    
    try:
        # 创建测试数据
        np.random.seed(42)
        prices = np.random.randn(50).cumsum() + 100
        price_series = pd.Series(prices)
        
        logger.info(f"创建测试数据: {len(price_series)}个价格点")
        
        # 测试LLT计算
        alpha = 0.1
        logger.info(f"开始LLT计算，alpha={alpha}")
        
        llt_values = LLTIndicator.calculate(price_series, alpha)
        
        logger.success(f"LLT计算完成，结果长度: {len(llt_values)}")
        
        # 测试信号生成
        signals = LLTIndicator.generate_signals(llt_values)
        
        logger.info(f"信号统计:")
        logger.info(f"  买入信号: {signals.count(1)}个")
        logger.info(f"  卖出信号: {signals.count(-1)}个")
        logger.info(f"  无信号: {signals.count(0)}个")
        
        return True
        
    except Exception as e:
        logger.error(f"LLT测试失败: {e}")
        return False


def test_timing_with_loguru():
    """测试时间统计功能的日志输出"""
    print("=" * 60)
    print("测试时间统计日志")
    print("=" * 60)
    
    from llt_strategy_refactored import ExecutionTimer, timing_decorator
    
    try:
        # 测试ExecutionTimer
        logger.info("测试ExecutionTimer...")
        
        with ExecutionTimer("模拟计算任务"):
            time.sleep(0.1)  # 模拟计算
            logger.info("正在执行模拟计算...")
        
        # 测试timing_decorator
        @timing_decorator("装饰器测试")
        def test_function():
            logger.info("装饰器函数内部日志")
            time.sleep(0.05)
            return "测试结果"
        
        logger.info("测试timing_decorator...")
        result = test_function()
        logger.info(f"函数返回结果: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"时间统计测试失败: {e}")
        return False


def test_config_with_loguru():
    """测试配置相关的日志输出"""
    print("=" * 60)
    print("测试配置日志")
    print("=" * 60)
    
    try:
        # 创建配置
        config = TradingConfig()
        
        logger.info("交易配置信息:")
        logger.info(f"  合约: {config.symbol}")
        logger.info(f"  D_VALUE: {config.d_value}")
        logger.info(f"  ALPHA: {config.alpha:.6f}")
        logger.info(f"  交易量: {config.volume}")
        logger.info(f"  最大持仓: {config.max_position}")
        
        # 测试配置修改
        old_d_value = config.d_value
        config.d_value = 45
        
        logger.warning(f"配置已修改: D_VALUE {old_d_value} -> {config.d_value}")
        logger.info(f"新的ALPHA值: {config.alpha:.6f}")
        
        return True
        
    except Exception as e:
        logger.error(f"配置测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理的日志输出"""
    print("=" * 60)
    print("测试错误处理日志")
    print("=" * 60)
    
    try:
        # 故意触发一个错误
        logger.info("准备触发测试错误...")
        
        try:
            # 除零错误
            result = 1 / 0
        except ZeroDivisionError as e:
            logger.error(f"捕获到预期错误: {e}")
            logger.warning("这是一个测试错误，程序继续运行")
        
        # 测试异常日志格式
        try:
            # 类型错误
            invalid_data = "字符串" + 123
        except TypeError as e:
            logger.error(f"类型错误: {e}")
            import traceback
            logger.debug(f"详细错误信息:\n{traceback.format_exc()}")
        
        logger.success("错误处理测试完成")
        return True
        
    except Exception as e:
        logger.error(f"错误处理测试失败: {e}")
        return False


def test_log_levels():
    """测试不同日志级别"""
    print("=" * 60)
    print("测试日志级别")
    print("=" * 60)
    
    # 测试不同级别
    levels = ["DEBUG", "INFO", "WARNING", "ERROR"]
    
    for level in levels:
        print(f"\n--- 测试 {level} 级别 ---")
        
        # 重新配置日志级别
        setup_logging(level)
        
        logger.debug(f"DEBUG消息 (级别: {level})")
        logger.info(f"INFO消息 (级别: {level})")
        logger.success(f"SUCCESS消息 (级别: {level})")
        logger.warning(f"WARNING消息 (级别: {level})")
        logger.error(f"ERROR消息 (级别: {level})")
    
    # 恢复INFO级别
    setup_logging("INFO")
    logger.info("日志级别测试完成，已恢复INFO级别")
    
    return True


def main():
    """主测试函数"""
    print("Loguru集成测试")
    print("=" * 80)
    
    # 首先安装loguru（如果需要）
    try:
        from loguru import logger
        print("✅ loguru已安装")
    except ImportError:
        print("❌ loguru未安装，请运行: pip install loguru")
        return False
    
    # 运行测试
    tests = [
        ("基本loguru功能", test_loguru_basic),
        ("LLT指标日志", test_llt_with_loguru),
        ("时间统计日志", test_timing_with_loguru),
        ("配置相关日志", test_config_with_loguru),
        ("错误处理日志", test_error_handling),
        ("日志级别测试", test_log_levels),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！loguru集成成功")
        print("\n📋 loguru功能特点:")
        print("  • 彩色输出，更易阅读")
        print("  • 自动文件轮转")
        print("  • 更简洁的API")
        print("  • 更好的异常处理")
        print("  • 支持结构化日志")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
