import os
import time
from datetime import datetime, timedelta

# 设置日志目录路径
log_dir = os.path.join(os.getcwd(), '.tqsdk', 'logs')

# 计算两天前的时间戳
two_days_ago = datetime.now() - timedelta(days=2)
two_days_ago_timestamp = two_days_ago.timestamp()

# 确保日志目录存在
if not os.path.exists(log_dir):
    print("日志目录不存在:", log_dir)
    exit()

# 遍历日志目录中的文件
for filename in os.listdir(log_dir):
    file_path = os.path.join(log_dir, filename)

    # 确保是文件而非目录
    if os.path.isfile(file_path):
        # 获取文件的修改时间
        file_mtime = os.path.getmtime(file_path)

        # 如果文件修改时间早于两天前，删除文件
        if file_mtime < two_days_ago_timestamp:
            try:
                os.remove(file_path)
                print(f"已删除: {file_path}")
            except Exception as e:
                print(f"删除 {file_path} 失败: {e}")

print("清理完成！")