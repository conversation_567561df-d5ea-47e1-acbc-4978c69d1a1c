"""
多合约多时间周期策略使用示例
展示如何使用重构后的TimeRoseMA_cross_speak.py
"""

from strategies.TimeRoseMA_cross_MultiSymbol_MultiTimeFrame import (
    SymbolTimeFrameConfig,
    MultiSymbolMultiTimeFrameManager,
    create_default_configs,
    create_custom_configs,
    run_multi_symbol_multi_timeframe_strategy,
    run_single_symbol_multi_timeframe,
    run_multi_symbol_single_timeframe,
    ma_cross_multi
)
from tqsdk import TqApi, TqKq


def example_1_default_config():
    """示例1: 使用默认配置运行多合约多时间周期策略"""
    print("=== 示例1: 默认配置 ===")
    print("运行 SH, OI, ag 三个合约，每个合约 1m, 3m, 5m, 15m 四个时间周期")
    
    run_multi_symbol_multi_timeframe_strategy()


def example_2_single_symbol_multi_timeframe():
    """示例2: 单个合约多时间周期"""
    print("=== 示例2: 单个合约多时间周期 ===")
    print("运行 SH 合约的多个时间周期")
    
    run_single_symbol_multi_timeframe(
        symbol='SH',
        timeframes=[60, 300, 900],  # 1分钟、5分钟、15分钟
        bklimit=2,
        sklimit=2,
        single_volume=1
    )


def example_3_multi_symbol_single_timeframe():
    """示例3: 多个合约单时间周期"""
    print("=== 示例3: 多个合约单时间周期 ===")
    print("运行多个合约的5分钟策略")
    
    run_multi_symbol_single_timeframe(
        symbols=['SH', 'OI', 'ag', 'rb'],
        interval=300,  # 5分钟
        bklimit=1,
        sklimit=1,
        single_volume=1
    )


def example_4_custom_config():
    """示例4: 自定义配置"""
    print("=== 示例4: 自定义配置 ===")
    
    # 创建自定义配置
    custom_configs = [
        # SH合约：1分钟和15分钟，较大的持仓限制
        SymbolTimeFrameConfig(
            symbol='SH',
            interval=60,
            bklimit=3,
            sklimit=3,
            single_volume=2,
            period=13
        ),
        SymbolTimeFrameConfig(
            symbol='SH',
            interval=900,
            bklimit=5,
            sklimit=5,
            single_volume=3,
            period=13
        ),
        
        # OI合约：3分钟和5分钟
        SymbolTimeFrameConfig(
            symbol='OI',
            interval=180,
            bklimit=2,
            sklimit=2,
            single_volume=1,
            period=20  # 使用20周期MA
        ),
        SymbolTimeFrameConfig(
            symbol='OI',
            interval=300,
            bklimit=2,
            sklimit=2,
            single_volume=1,
            period=20
        ),
        
        # ag合约：仅5分钟
        SymbolTimeFrameConfig(
            symbol='ag',
            interval=300,
            bklimit=1,
            sklimit=1,
            single_volume=1,
            period=13
        ),
    ]
    
    run_multi_symbol_multi_timeframe_strategy(custom_configs)


def example_5_config_from_dict():
    """示例5: 从字典创建配置"""
    print("=== 示例5: 从字典创建配置 ===")
    
    # 定义合约和时间周期的映射
    symbol_timeframe_map = {
        'SH': [60, 180, 300],      # SH: 1分钟、3分钟、5分钟
        'OI': [300, 900],          # OI: 5分钟、15分钟
        'ag': [180],               # ag: 3分钟
        'rb': [60, 900],           # rb: 1分钟、15分钟
    }
    
    # 默认参数
    default_params = {
        'bklimit': 2,
        'sklimit': 2,
        'single_volume': 1,
        'period': 13,
        'enabled': True
    }
    
    configs = create_custom_configs(symbol_timeframe_map, default_params)
    run_multi_symbol_multi_timeframe_strategy(configs)


def example_6_compatible_with_original():
    """示例6: 兼容原有ma_cross函数的用法"""
    print("=== 示例6: 兼容原有用法 ===")
    
    # 创建API连接
    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301", disable_print=True)
    
    try:
        # 定义合约和时间周期配置
        symbol_configs = [
            ('SH', 60),    # SH 1分钟
            ('SH', 300),   # SH 5分钟
            ('OI', 180),   # OI 3分钟
            ('OI', 900),   # OI 15分钟
        ]
        
        # 使用兼容函数
        ma_cross_multi(
            api=api,
            symbol_configs=symbol_configs,
            single_volume=1,
            bklimit=1,
            sklimit=1,
            period=13
        )
        
    except KeyboardInterrupt:
        print("策略被用户中断")
    finally:
        api.close()


def example_7_manual_management():
    """示例7: 手动管理策略"""
    print("=== 示例7: 手动管理 ===")
    
    # 创建API连接
    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301", disable_print=True)
    
    try:
        # 创建配置
        configs = [
            SymbolTimeFrameConfig(symbol='SH', interval=60),
            SymbolTimeFrameConfig(symbol='SH', interval=300),
            SymbolTimeFrameConfig(symbol='OI', interval=180),
        ]
        
        # 创建管理器
        manager = MultiSymbolMultiTimeFrameManager(api, configs)
        
        # 显示初始状态
        manager.print_status()
        
        # 手动运行几次更新
        for i in range(10):
            api.wait_update()
            manager._process_updates()
            
            # 每5次更新显示一次状态
            if i % 5 == 0:
                print(f"\n--- 更新 {i+1} ---")
                signals = manager.get_all_signals()
                for strategy_id, signal in signals.items():
                    print(f"{strategy_id}: {signal.signal_type} "
                          f"(盈亏: {signal.signal_profit:.2f})")
        
        # 获取特定合约的信号
        sh_signals = manager.get_symbol_signals('SH')
        print(f"\nSH合约信号: {len(sh_signals)} 个时间周期")
        
    except KeyboardInterrupt:
        print("策略被用户中断")
    finally:
        api.close()


def show_usage_help():
    """显示使用帮助"""
    print("""
=== 多合约多时间周期策略使用指南 ===

1. 基本用法:
   - 默认配置: python example_MultiSymbol_MultiTimeFrame.py 1
   - 单合约多周期: python example_MultiSymbol_MultiTimeFrame.py 2
   - 多合约单周期: python example_MultiSymbol_MultiTimeFrame.py 3
   - 自定义配置: python example_MultiSymbol_MultiTimeFrame.py 4

2. 命令行运行:
   python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py default
   python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py single_symbol SH
   python strategies/TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py multi_symbol SH OI ag

3. 配置说明:
   - symbol: 合约代码（如'SH', 'OI', 'ag'等）
   - interval: 时间间隔（秒，如60=1分钟, 300=5分钟）
   - bklimit/sklimit: 多空单持仓限制
   - single_volume: 单次交易量
   - period: MA周期

4. 优势:
   - 统一API管理，减少连接数
   - 支持任意合约和时间周期组合
   - 模块化设计，易于扩展
   - 兼容原有ma_cross函数

5. 注意事项:
   - 确保网络连接稳定
   - 建议先在模拟环境测试
   - 监控策略性能和资源使用
   - 根据需要调整持仓限制
""")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        example_num = sys.argv[1]
        
        if example_num == "1":
            example_1_default_config()
        elif example_num == "2":
            example_2_single_symbol_multi_timeframe()
        elif example_num == "3":
            example_3_multi_symbol_single_timeframe()
        elif example_num == "4":
            example_4_custom_config()
        elif example_num == "5":
            example_5_config_from_dict()
        elif example_num == "6":
            example_6_compatible_with_original()
        elif example_num == "7":
            example_7_manual_management()
        elif example_num == "help":
            show_usage_help()
        else:
            print("可用示例: 1, 2, 3, 4, 5, 6, 7, help")
    else:
        print("使用方法: python example_MultiSymbol_MultiTimeFrame.py [1-7|help]")
        print("运行 'python example_MultiSymbol_MultiTimeFrame.py help' 查看详细说明")
