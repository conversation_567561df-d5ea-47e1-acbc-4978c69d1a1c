import pandas as pd
import numpy as np

# 假设您有一个包含OHLCV数据的DataFrame, 例如:
# df = pd.DataFrame({
# 'open': [10, 10.2, 10.1, 10.5, 10.3],
# 'high': [10.3, 10.4, 10.6, 10.6, 10.5],
# 'low': [9.9, 10.1, 10.0, 10.2, 10.1],
# 'close': [10.2, 10.1, 10.5, 10.3, 10.2],
# 'volume': [1000, 1200, 800, 1500, 900],
# 'opi': [5000, 5100, 4900, 5200, 5000] # 持仓量示例
# })
# # 通常K线数据会有DatetimeIndex
# df.index = pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05'])


def REF(series_x: pd.Series, n: int) -> pd.Series:
    """
    引用X在N个周期前的值.
    文华说明: N为有效值，但当前k线数不足N根，返回空值；N为0时返回当前X值.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")
    if n < 0:
        # 文华REF通常不直接支持负数n作为pandas.shift的直接参数含义
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.shift(n)

def SUM(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的总和.
    文华说明: N包含当前k线. 若N为0则从第一个有效值开始算起.
              当N为有效值，但当前的k线数不足N根，按照实际的根数计算.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n < 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    if n == 0:  # 从第一个有效值开始算起 (expanding sum)
        return series_x.expanding(min_periods=1).sum()
    else:  # 滚动求和，min_periods=1 表示不足N根时按实际根数计算
        return series_x.rolling(window=n, min_periods=1).sum()

def MA(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的简单移动平均.
    文华说明: N包含当前k线. 不足N根，返回空值.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    # min_periods=n 确保在窗口数据不足n时结果为NaN (空值)
    return series_x.rolling(window=n, min_periods=n).mean()

def EMA(series_x: pd.Series, n: int) -> pd.Series:
    """
    求N周期X值的指数加权移动平均.
    文华说明: N包含当前k线. 当N为有效值，但当前的k线数不足N根，按N根计算.
              (这通常意味着EMA从早期数据点开始计算, adjust=False, min_periods 默认或较小)
              EMAWH则明确说明不足N根返回空值, 此时min_periods=n.
              此处采用更通用的EMA定义, adjust=False, min_periods=1 开始计算.
              如果严格要求不足N根返回空值，则应使用 min_periods=n.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.ewm(span=n, adjust=False, min_periods=1).mean() # min_periods=n 如果需要严格空值

def HHV(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的最高值.
    文华说明: N包含当前k线. 若N为0则从第一个有效值开始算起.
              不足N根，按照实际的根数计算.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")
    if n < 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    if n == 0:
        return series_x.expanding(min_periods=1).max()
    else:
        return series_x.rolling(window=n, min_periods=1).max()

def LLV(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的最小值.
    文华说明: N包含当前k线. 若N为0则从第一个有效值开始算起.
              不足N根，按照实际的根数计算.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n < 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    if n == 0:
        return series_x.expanding(min_periods=1).min()
    else:
        return series_x.rolling(window=n, min_periods=1).min()

def STD(series_x: pd.Series, n: int) -> pd.Series:
    """
    求X在N个周期内的样本标准差.
    文华说明: N包含当前k线. 不足N根，返回空值.
    """
    if not isinstance(series_x, pd.Series):
        raise TypeError("Input 'series_x' must be a pandas Series.")
    if not isinstance(n, int):
        raise TypeError("Parameter 'n' must be an integer.")

    if n <= 1: # 样本标准差至少需要2个点
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.rolling(window=n, min_periods=n).std(ddof=1)

def IFELSE(condition: pd.Series, series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """
    若COND条件成立，则返回A，否则返回B.
    确保A和B与condition有相同的索引.
    """
    if not (condition.index.equals(series_a.index) and condition.index.equals(series_b.index)):
        # Attempt to align if possible, or raise error
        # This simplified version assumes they are already aligned or Series a/b are scalars
        pass
    return pd.Series(np.where(condition, series_a, series_b), index=condition.index)

# (O, H, L, C, V, OPI - 这些通常直接从DataFrame列获取，如 df['open'], df['close']等)
# OPEN = df['open']
# HIGH = df['high']
# LOW = df['low']
# CLOSE = df['close']
# VOL = df['volume']
# OPI = df['opi'] # 持仓量


# (辅助函数已在上方定义)

def ABS(series_x: pd.Series) -> pd.Series:
    """
    ABS(X)：取的X的绝对值.
    例: ABS(CLOSE-10); ABS(C-O);
    """
    return series_x.abs()


def ACOS(series_x: pd.Series) -> pd.Series:
    """
    ACOS(X)：返回X的反余弦值.
    注: X取值范围[-1, 1]. 若X不在取值范围，返回值为空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = (series_x >= -1) & (series_x <= 1)
    result[mask] = np.arccos(series_x[mask])
    return result


def ASIN(series_x: pd.Series) -> pd.Series:
    """
    ASIN(X)：返回X的反正弦值.
    注: X取值范围[-1, 1]. 若X不在取值范围，返回值为空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = (series_x >= -1) & (series_x <= 1)
    result[mask] = np.arcsin(series_x[mask])
    return result


def ATAN(series_x: pd.Series) -> pd.Series:
    """
    ATAN(X)：返回X的反正切值.
    注: X的取值为R（实数集）.
    """
    return np.arctan(series_x)


def AVEDEV(series_x: pd.Series, n: int) -> pd.Series:
    """
    AVEDEV(X,N)：返回X在N周期内的平均绝对偏差.
    算法举例: AVEDEV(C,3) = (ABS(C-MA(C,3)) + ABS(REF(C,1)-MA(C,3)) + ABS(REF(C,2)-MA(C,3)))/3
    (注意: 算法举例中的 MA(C,3) 是指这3个值的均值, 而不是滚动MA)
     Pandas rolling.apply 可以更通用地实现:
    注: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)

    def avedev_calc(window_series):
        return (window_series - window_series.mean()).abs().mean()

    return series_x.rolling(window=n, min_periods=n).apply(avedev_calc, raw=True)


def AVPRICE(df_ohlc: pd.DataFrame) -> pd.Series:
    """
    AVPRICE 取得K线图的均价.
    注: 表示单根K线内的均价. 日线周期上收盘后与SETTLE函数一样取得当日的结算价.
    (简单实现可能为 (O+H+L+C)/4 或 (H+L+C)/3, 或 (H+L)/2. 具体算法未明确给出.
     如果指 VWAP (成交量加权平均价), 则需要成交额和成交量数据.
     此处假设为 (H+L+C)/3 作为一种常见近似.)
    """
    if all(col in df_ohlc.columns for col in ['high', 'low', 'close']):
        return (df_ohlc['high'] + df_ohlc['low'] + df_ohlc['close']) / 3
    else:
        # 或返回 (df_ohlc['high'] + df_ohlc['low'])/2
        # 或 (df_ohlc['open'] + df_ohlc['high'] + df_ohlc['low'] + df_ohlc['close']) / 4
        # 实际含义需参照文华平台具体算法，这里提供一个简单版本
        print("AVPRICE requires 'high', 'low', 'close' columns. Definition used: (H+L+C)/3")
        return pd.Series(np.nan, index=df_ohlc.index)


def BETWEEN(series_x: pd.Series, series_y: pd.Series, series_z: pd.Series) -> pd.Series:
    """
    BETWEEN(X,Y,Z) 表示X是否处于Y和Z之间，成立返回1(Yes)，否则返回0(No).
    注: 其中若X=Y、X=Z、或X=Y且Y=Z时函数返回值为1.
    这意味着 Y 和 Z 的顺序不重要.
    """
    lower_bound = pd.Series(np.minimum(series_y, series_z), index=series_x.index)
    upper_bound = pd.Series(np.maximum(series_y, series_z), index=series_x.index)
    return ((series_x >= lower_bound) & (series_x <= upper_bound)).astype(int)


def CEILING(series_x: pd.Series, series_y: pd.Series = None) -> pd.Series:
    """
    CEILING(X,Y): 返回指定实数(X)在沿绝对值增大的方向上第一个能整除基数(Y)的值.
    CEILING(A): (如果只有一个参数，类似 FLOOR(A) 的向上取整版本，即 np.ceil(A))
    文华说明中的Y是基数。如果Y省略，则行为可能不同。
    这里实现CEILING(A) 即 np.ceil(A).
    对于 CEILING(X,Y), 文华的描述比较复杂，涉及到X,Y符号。
    例1：CEILING(2.1,1); 求得3。
    例2：CEILING(-8.8,-2); 求得-10。
    例3: CEILING(-7,2); 求得-6. (向0舍入)
    这个行为不完全是标准的数学 ceiling(x/y)*y.
    实现一个简化版本 CEILING(X) = np.ceil(X).
    对于双参数版本，需要更精确的规则复现，此处省略。
    """
    if series_y is None:
        return np.ceil(series_x)
    else:
        # 文华的CEILING(X,Y) 行为特殊，这里不提供完整实现，仅提示
        print("Warning: Two-argument CEILING(X,Y) has complex rules in WenHua not fully implemented here. Defaulting to CEILING(X).")
        return np.ceil(series_x)


def COS(series_x: pd.Series) -> pd.Series:
    """COS(X)：返回X的余弦值."""
    return np.cos(series_x)


def CUBE(series_x: pd.Series) -> pd.Series:
    """CUBE(X)：返回X的三次方."""
    return series_x ** 3


def DEVSQ(series_x: pd.Series, n: int) -> pd.Series:
    """
    DEVSQ(X,N)： 计算数据X的N个周期的数据偏差平方和.
    算法: SUM(SQUARE(X - MA(X,N))) over N periods.
    注: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值. N不支持为变量.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)

    mean_x_n = series_x.rolling(window=n, min_periods=n).mean()
    squared_dev = (series_x - mean_x_n) ** 2

    # The sum is over the *same N periods* for which MA was calculated,
    # but DEVSQ is SUM OF (X_i - MA_of_window_ending_at_i)^2 over the window
    # This is effectively: n * VARP(X,N) or (n-1) * VAR(X,N)
    # 文华的算法举例：DEVSQ(C,3) = SQUARE(C-MA(C,3)) + SQUARE(REF(C,1)-MA(C,3)) + SQUARE(REF(C,2)-MA(C,3))
    # MA(C,3) here refers to the mean of C, REF(C,1), REF(C,2).
    # This means for each window, calculate mean, then sum of squared diffs.
    def devsq_calc(window):
        return ((window - window.mean()) ** 2).sum()

    return series_x.rolling(window=n, min_periods=n).apply(devsq_calc, raw=True)


def EXP(series_x: pd.Series) -> pd.Series:
    """EXP(X)：求e的X次幂."""
    return np.exp(series_x)


def FLOOR(series_x: pd.Series) -> pd.Series:
    """FLOOR(A)：向数值减小方向舍入 (取整)."""
    return np.floor(series_x)


def INTPART(series_x: pd.Series) -> pd.Series:
    """INTPART(X)：取X的整数部分 (向0截断)."""
    return np.trunc(series_x)  # or series_x.astype(int) if appropriate for positive/negative


def LN(series_x: pd.Series) -> pd.Series:
    """
    LN(X)：求X的自然对数.
    注: X取值范围为正数. X为0或负数，返回值为空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = series_x > 0
    result[mask] = np.log(series_x[mask])
    return result


def LOG(series_x: pd.Series, series_base: pd.Series) -> pd.Series:
    """
    LOG(X,Y) 求以Y为底X的对数值. (LOG(X, Base))
    注: X>0, Base>0, Base!=1. 不满足则空值.
    """
    # log_base(x) = log_e(x) / log_e(base)
    log_x = LN(series_x)  # Handles x <= 0
    log_base_val = LN(series_base)  # Handles base <= 0

    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    # Ensure base is not 1 (log_base_val is not 0) and both logs are valid
    valid_mask = pd.notna(log_x) & pd.notna(log_base_val) & (series_base != 1)
    result[valid_mask] = log_x[valid_mask] / log_base_val[valid_mask]
    return result


def LOG10(series_x: pd.Series) -> pd.Series:
    """
    LOG10(X) 求X的常用对数值 (以10为底).
    注: X>0. X为0或负数，返回值为空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = series_x > 0
    result[mask] = np.log10(series_x[mask])
    return result


def MAX(series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """MAX(A,B)：取最大值。取A，B中较大者."""
    return np.maximum(series_a, series_b)


def MAX1(*series_list) -> pd.Series:
    """
    MAX1(A1,...,A30) 在A1到A30中取最大值.
    支持2-30个序列.
    """
    if not (2 <= len(series_list) <= 30):
        raise ValueError("MAX1 supports between 2 and 30 series arguments.")
    # Ensure all are pd.Series and align them if necessary (assuming same index for simplicity)
    df_temp = pd.concat(series_list, axis=1)
    return df_temp.max(axis=1)


def MIN(series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """MIN(A,B)：取最小值。取A，B中较小者."""
    return np.minimum(series_a, series_b)


def MIN1(*series_list) -> pd.Series:
    """
    MIN1(A1,...,A30) 在A1到A30中取最小值.
    支持2-30个序列.
    """
    if not (2 <= len(series_list) <= 30):
        raise ValueError("MIN1 supports between 2 and 30 series arguments.")
    df_temp = pd.concat(series_list, axis=1)
    return df_temp.min(axis=1)


def MOD(series_a: pd.Series, series_b: pd.Series) -> pd.Series:
    """
    MOD(A,B)：取模。返回A对B求模.
    注: 第二个参数最多支持四位小数 (this implies series_b might not always be integer).
    np.mod or % operator.
    """
    return np.mod(series_a, series_b)  # or series_a % series_b


def NOT(condition: pd.Series) -> pd.Series:
    """NOT(X)：取非。当X＝0时返回1，否则返回0."""
    # Assuming X is a boolean-like series (0 for False, non-zero for True)
    return (~condition.astype(bool)).astype(int)


def POW(series_x: pd.Series, series_y: pd.Series) -> pd.Series:
    """
    POW(X,Y)：求X的Y次幂.
    注: 当X为负数时，Y必须为整数 (否则空值).
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    # Condition where power is well-defined or Y is integer if X is negative
    # (X < 0 and Y is integer) OR (X >= 0)
    # Y is integer: series_y % 1 == 0 (approx for floats)
    is_y_integer = np.isclose(series_y, np.round(series_y))
    valid_mask = (series_x >= 0) | ((series_x < 0) & is_y_integer)

    # Calculate power only for valid conditions to avoid warnings/errors with np.power
    # For fractional powers of negative numbers, result is complex or NaN.
    # np.power handles this by returning NaN for complex results if dtype is float.
    # We can use a loop for precision or rely on numpy's behavior.
    # A simpler approach for now, relying on numpy's NaN for undefined real results:
    # result[valid_mask] = np.power(series_x[valid_mask], series_y[valid_mask])
    # return result
    # Or, more robustly:
    temp_result = np.power(series_x.astype(float), series_y.astype(float))  # Allow complex intermediates
    # if result is complex where X<0 and Y is not int, it should be NaN
    result[valid_mask] = temp_result[valid_mask].real  # take real part if it was valid
    # if original X < 0 and Y was not integer, result should be NaN even if temp_result had a real part (e.g. (-8)^(1/3)=-2)
    # This detail might need closer look at platform behavior.
    # The current valid_mask should make result NaN where it should be.
    # Let's re-evaluate np.power's direct handling:
    # For x < 0, y not integer, np.power(x, y) usually gives nan for float output.
    return np.power(series_x, series_y)  # Simpler, relies on numpy's handling. Add explicit masking if issues.


def REVERSE(series_x: pd.Series) -> pd.Series:
    """REVERSE(X)：取相反值，返回－X."""
    return -series_x


def ROUND(series_x: pd.Series, m: int) -> pd.Series:
    """
    ROUND(N,M) 对数字N进行位数为M的四舍五入.
    M>0: 小数点后M位. M=0: 整数. M<0: 小数点左侧M位.
    """
    return series_x.round(decimals=m)


def SGN(series_x: pd.Series) -> pd.Series:
    """SGN(X)：取符号。若X>0返回1,若X<0返回-1,否则返回0."""
    return np.sign(series_x).astype(int)


def SIN(series_x: pd.Series) -> pd.Series:
    """SIN(X)：求X的正弦值."""
    return np.sin(series_x)


def SQRT(series_x: pd.Series) -> pd.Series:
    """
    SQRT(X)：求X的平方根.
    注: X的取值为正数. X为负数时返回空值.
    """
    result = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    mask = series_x >= 0
    result[mask] = np.sqrt(series_x[mask])
    return result


def SQUARE(series_x: pd.Series) -> pd.Series:
    """SQUARE(X)求X的平方."""
    return series_x ** 2


def TAN(series_x: pd.Series) -> pd.Series:
    """TAN(X)：返回X的正切值."""
    return np.tan(series_x)


def STDP(series_x: pd.Series, n: int) -> pd.Series:
    """
    STDP(X,N)：为X的N周期总体标准差.
    文华说明: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.rolling(window=n, min_periods=n).std(ddof=0)  # ddof=0 for population std


def VAR(series_x: pd.Series, n: int) -> pd.Series:
    """
    VAR(X,N)求X在N周期内的样本方差.
    文华说明: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 1:  # 样本方差至少需要2个点
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.rolling(window=n, min_periods=n).var(ddof=1)  # ddof=1 for sample variance


def VARP(series_x: pd.Series, n: int) -> pd.Series:
    """
    VARP(X,N)：为X的N周期总体方差.
    文华说明: N包含当前k线. 不足N根，返回空值. N为0或空值，返回空值.
    """
    if n <= 0:
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)
    return series_x.rolling(window=n, min_periods=n).var(ddof=0)  # ddof=0 for population variance


# (辅助函数已在 Part 1 或上方定义)

def BARSCOUNT(series_x: pd.Series) -> pd.Series:
    """
    BARSCOUNT(COND) 第一个有效周期到当前的周期数. (COND is the series X here)
    文华说明: 返回值为COND从第一个有效周期开始计算，到现在为止的周期数.
              条件第一次成立的当根k线上BARSCOUNT(COND)的返回值为0.
              (This implies COND is a series, and we count from its first non-NaN value)
    """
    # Find the first valid (non-NaN) index
    first_valid_idx_pos = series_x.first_valid_index()
    if first_valid_idx_pos is None:  # All NaN
        return pd.Series(np.nan, index=series_x.index, name=series_x.name)

    # Get integer position of first valid index
    first_valid_iloc = series_x.index.get_loc(first_valid_idx_pos)

    barscount_values = pd.Series(np.nan, index=series_x.index, name=series_x.name)
    for i in range(first_valid_iloc, len(series_x)):
        barscount_values.iloc[i] = i - first_valid_iloc

    return barscount_values


def BARSLAST(condition: pd.Series) -> pd.Series:
    """
    BARSLAST(COND)：上一次条件COND成立到当前的周期数.
    文华说明: 条件成立的当根k线上BARSLAST(COND)的返回值为0.
              如果之前从未成立，则为空值.
    """
    condition = condition.astype(bool)  # Ensure boolean
    out = pd.Series(np.nan, index=condition.index, name="BARSLAST")
    last_true_iloc = -1

    for i in range(len(condition)):
        if condition.iloc[i]:
            last_true_iloc = i
            out.iloc[i] = 0
        elif last_true_iloc != -1:  # If a True has occurred before
            out.iloc[i] = i - last_true_iloc
    return out


def BARSLASTCOUNT(condition: pd.Series) -> pd.Series:
    """
    BARSLASTCOUNT(COND) 从当前周期向前计算，统计连续满足条件的周期数.
    文华说明: 条件第一次成立的当根k线上BARSLASTCOUNT(COND)的返回值为1.
    """
    condition = condition.astype(bool)
    # Invert condition, group by blocks of Falses, then cumsum for Trues
    # A True resets the count for the group of (~condition) after it.
    # This counts consecutive Trues.
    # (~condition).cumsum() creates groups separated by Trues.
    # We want to count Trues within these groups (actually, between Falses).

    # Alternative: group by changes from True to False or vice-versa
    consecutive_trues = condition.groupby((condition != condition.shift()).cumsum()).cumsum()
    # Reset to 0 where condition is False
    consecutive_trues[~condition] = 0
    return consecutive_trues


def BARSSINCE(condition: pd.Series) -> pd.Series:
    """
    BARSSINCE(COND) 第一个条件成立到当前的周期数.
    文华说明: 条件第一次成立的当根k线上BARSSINCE(COND)的返回值为0.
              如果从未成立，则为空值.
    """
    condition = condition.astype(bool)
    out = pd.Series(np.nan, index=condition.index, name="BARSSINCE")

    first_true_iloc = -1
    try:  # Find first True if it exists
        first_true_iloc = condition[condition].index[0]  # Gets the index label
        first_true_iloc = condition.index.get_loc(first_true_iloc)  # Gets integer position
    except IndexError:  # No True in condition
        return out  # All NaNs

    for i in range(first_true_iloc, len(condition)):
        out.iloc[i] = i - first_true_iloc

    return out


def BARSSINCEN(condition: pd.Series, n: int) -> pd.Series:
    """
    BARSSINCEN(COND,N) 统计N周期内第一次条件成立到当前的周期数.
    注: N包含当前k线. 若N为0返回无效值. N可以为变量.
        当N为有效值，但当前的k线数不足N根，按照实际的根数计算.
    """
    if n == 0:  # 文华说N=0返回无效值
        return pd.Series(np.nan, index=condition.index)
    if n < 0:  # Assuming N should be positive for window length
        return pd.Series(np.nan, index=condition.index)

    condition = condition.astype(bool)
    out = pd.Series(np.nan, index=condition.index, name="BARSSINCEN")

    for i in range(len(condition)):
        # Determine the actual window size for this iteration
        current_n = min(n, i + 1)  # "不足N根，按照实际的根数计算"
        window = condition.iloc[i - current_n + 1: i + 1]

        first_true_in_window_relative_idx = -1
        try:
            first_true_in_window_relative_idx = window[window].index[0]  # Label
            first_true_in_window_relative_idx = window.index.get_loc(first_true_in_window_relative_idx)  # Pos in window
        except IndexError:  # No true in window
            continue  # out.iloc[i] remains NaN

        # Distance from that first true in window to end of window (which is current i)
        # Window length is current_n. first_true_in_window_relative_idx is 0-indexed within window.
        # The "current" point in the window is at index current_n - 1.
        out.iloc[i] = (current_n - 1) - first_true_in_window_relative_idx

    return out


def BARPOS(series_x: pd.Series) -> pd.Series:  # Input series_x is just for index and length
    """
    BARPOS，返回从第一根K线开始到当前的周期数.
    注: 本机已有的第一根K线上返回值为1.
    """
    if series_x.empty:
        return pd.Series(dtype=float, name="BARPOS")
    return pd.Series(np.arange(1, len(series_x) + 1), index=series_x.index, name="BARPOS", dtype=float)


# --- Time and Date related (assuming df.index is DatetimeIndex) ---

def DATE(idx: pd.DatetimeIndex) -> pd.Series:
    """
    DATE,返回某周期的日期数. YYMMDD.
    文华夜盘: 2013/7/8 21:00 -> DATE is 130709 (next day).
    This requires knowledge of trading sessions. Pandas dt.strftime('%y%m%d') gives current day.
    For simplicity, this returns the K-bar's date. Adjust for night sessions if needed.
    """
    return pd.Series(idx.strftime('%y%m%d').astype(int), index=idx, name="DATE")


def DATE1(idx: pd.DatetimeIndex) -> pd.Series:
    """
    DATE1：返回某周期的日期数.
    2000年以前 YYMMDD, 2000年以后 1YYMMDD.
    """
    year = idx.year
    date_str = idx.strftime('%y%m%d')
    return pd.Series(np.where(year >= 2000, '1' + date_str, date_str).astype(int), index=idx, name="DATE1")


def DAY(idx: pd.DatetimeIndex) -> pd.Series:
    """DAY,返回某一周期的日数 (1-31)."""
    return pd.Series(idx.day, index=idx, name="DAY")


def HOUR(idx: pd.DatetimeIndex) -> pd.Series:
    """HOUR，返回某周期的小时数 (0-23)."""
    return pd.Series(idx.hour, index=idx, name="HOUR")


def MINUTE(idx: pd.DatetimeIndex) -> pd.Series:
    """MINUTE,返回某个周期的分钟数 (0-59)."""
    return pd.Series(idx.minute, index=idx, name="MINUTE")


def MONTH(idx: pd.DatetimeIndex) -> pd.Series:
    """MONTH，返回某个周期的月份 (1-12)."""
    return pd.Series(idx.month, index=idx, name="MONTH")


def PERIOD(df_index: pd.Index) -> int:  # Or str
    """
    PERIOD，返回当前技术分析图表的周期.
    This is more about the environment/chart setting.
    In Python, you'd know the period of your df (e.g., by df.index.freqstr).
    This function can't determine it from data alone if df has irregular times.
    If df.index has a freq, use it. Otherwise, it's context-dependent.
    Returns a numerical code as per WenHua. Here, we return a string representation if available.
    """
    if isinstance(df_index, pd.DatetimeIndex) and df_index.freqstr:
        # This is a simplistic mapping, WenHua codes are specific.
        freq = df_index.freqstr
        mapping = {
            'T': 1, 'min': 1,  # 1 minute
            '3T': 2, '3min': 2,
            '5T': 3, '5min': 3,
            '15T': 5, '15min': 5,
            '30T': 6, '30min': 6,
            'H': 7, 'h': 7,  # 1 hour
            'D': 8, 'd': 8,  # 1 day
            'W': 9, 'w': 9,  # 1 week
            'ME': 10,  # Month End
            'YE': 11  # Year End
            # Add more mappings for S, custom periods, etc.
        }
        # A more direct approach for common pandas frequencies:
        if 'min' in freq or 'T' in freq: return mapping.get(freq, 12)  # Custom Min
        if 'H' in freq: return mapping.get(freq, 13)  # Custom Hour
        if 'D' in freq: return mapping.get(freq, 14)  # Custom Day
        # etc.
        return f"Pandas Freq: {freq} (WenHua code unknown)"
    return "Unknown/Irregular"  # Or a default WenHua code for unknown


def QUARTER(idx: pd.DatetimeIndex) -> pd.Series:
    """QUARTER,返回某周期的季度数 (1-4)."""
    return pd.Series(idx.quarter, index=idx, name="QUARTER")


def TIME(idx: pd.DatetimeIndex) -> pd.Series:
    """
    TIME，取K线时间. HHMM or HHMMSS (for seconds period).
    文华说明: K线走完后返回K线的起始时间. SIGCHECK模型中返回当时分钟时间.
    """
    # Assuming index is start of bar for historical data.
    # Check if index has second resolution
    if hasattr(idx, 'second') and (idx.second != 0).any():
        return pd.Series(idx.strftime('%H%M%S').astype(int), index=idx, name="TIME")
    return pd.Series(idx.strftime('%H%M').astype(int), index=idx, name="TIME")


def WEEKDAY(idx: pd.DatetimeIndex) -> pd.Series:
    """
    WEEKDAY,取得星期数.
    文华: 0-6 (Monday=0 or Sunday=0? - Pandas: Monday=0, Sunday=6)
    文华例: ...WEEKDAY=5... (implies 0-6, perhaps Sunday=0, Friday=5, Sat=6 or Mon=1..Fri=5)
    Pandas weekday is Monday=0 to Sunday=6. If WenHua is different, adjust.
    Assuming WenHua aligns with Python's datetime.weekday() (Mon=0..Sun=6) for now.
    """
    return pd.Series(idx.weekday, index=idx, name="WEEKDAY")  # Monday=0, ..., Sunday=6


def YEAR(idx: pd.DatetimeIndex) -> pd.Series:
    """YEAR，取得年份."""
    return pd.Series(idx.year, index=idx, name="YEAR")


# --- Informational / Utility Functions ---
def MINPRICE(context_specific_min_tick_size: float) -> float:
    """
    MINPRICE 取数据合约的最小变动价位.
    This is context-specific (depends on the contract loaded).
    In Python, you'd typically have this information from your data provider or contract specs.
    """
    # This would be a lookup or a known value for the specific instrument.
    # Example:
    # if instrument == 'IF': return 0.2
    # if instrument == ' populaires': return 0.01
    return context_specific_min_tick_size  # Placeholder


def MINPRICE1(context_specific_min_tick_size_trade: float) -> float:
    """MINPRICE1  取交易合约的最小变动价位."""
    return context_specific_min_tick_size_trade  # Placeholder, similar to MINPRICE


def UNIT(context_specific_contract_unit: int) -> int:
    """UNIT 取数据合约的交易单位."""
    return context_specific_contract_unit  # Placeholder


def UNIT1(context_specific_contract_unit_trade: int) -> int:
    """UNIT1  取交易合约的交易单位."""
    return context_specific_contract_unit_trade  # Placeholder


def STKTYPE(context_specific_market_type_code: int) -> int:
    """
    STKTYPE 取市场类型.
    文华返回值: 1国内股票, 2美国股票, 6外汇, 7国内期货, 8国内期权, 9外盘, 5其它.
    This is context-dependent.
    """
    return context_specific_market_type_code  # Placeholder


def COUNT(condition: pd.Series, n: int) -> pd.Series:
    """
    COUNT(COND,N)：统计N周期中满足COND条件的周期数.
    注: N包含当前k线. 若N为0则从第一个有效值算起.
        不足N根，从第一根统计到当前周期.
    """
    if n < 0: return pd.Series(np.nan, index=condition.index)

    condition_int = condition.astype(int)  # Assuming COND is boolean-like (True=1, False=0)
    if n == 0:  # 从第一个有效值开始算起 (expanding sum of trues)
        return condition_int.expanding(min_periods=1).sum()
    else:  # 滚动求和，不足N根时按实际根数计算 (min_periods=1)
        return condition_int.rolling(window=n, min_periods=1).sum()


def EVERY(condition: pd.Series, n: int) -> pd.Series:
    """
    EVERY(COND,N)，判断N周期内，是否一直满足COND条件. 若满足返回1,不满足返回0.
    注: N包含当前k线. 若N是有效数值，但前面没有那么多K线,或者N为空值，代表条件不满足，返回0.
    """
    if n <= 0: return pd.Series(0, index=condition.index, dtype=int)  # Or NaN if "empty value" for N means that

    # Rolling sum of condition (as int). If sum == N, then all were True in window.
    # min_periods=n ensures that if window is not full, result is NaN.
    # Then we fill NaN with 0 (condition not met if window not full) and convert to int.
    all_true_in_window = (condition.astype(int).rolling(window=n, min_periods=n).sum() == n)
    # If min_periods=n results in NaN for shorter windows, this should be 0.
    # A more direct way:
    # Rolling product: if any is False (0), product is 0. If all True (1), product is 1.
    # Ensure NaNs at start if window isn't full to match "no那么多K线...返回0"
    # A rolling window where all elements are True.
    # A rolling window of size 'n'. If all are true, the min is true.
    # If any is false, the min is false.
    result_is_true = condition.astype(bool).rolling(window=n, min_periods=n).min()
    # result_is_true will be NaN if not enough periods. Fill NaN with False.
    return result_is_true.fillna(False).astype(int)


def EXIST(condition: pd.Series, n: int) -> pd.Series:
    """
    EXIST(COND,N) 判断N个周期内是否有满足COND的条件. (True=1, False=0)
    注: N包含当前k线. 若N是有效数值，但前面没有那么多K线，按实际周期数计算.
    """
    if n <= 0:  # Assuming N must be positive for a window
        # 文华文档对此情况未明确，若N=0从头算起，则用expanding().max()
        # 若N<=0无意义，则返回空或0.
        return pd.Series(0, index=condition.index, dtype=int)

    # Rolling sum of condition (as int). If sum > 0, then at least one True in window.
    # min_periods=1 ensures calculation even if window is not full.
    any_true_in_window = (condition.astype(int).rolling(window=n, min_periods=1).sum() > 0)
    return any_true_in_window.astype(int)


def FILTER(condition: pd.Series, n_filter_out: int) -> pd.Series:
    """
    FILTER(COND,N) 当COND条件成立，将其后N周期内的数据设置为0. (N is filter duration, not window)
    This means if COND is true at time `t`, then the *output* of this FILTER function
    for time `t` is 1 (or the original COND value), but for `t+1` to `t+N`,
    even if COND is true, the *output* should be 0.
    This implies a stateful filtering based on prior COND trigger.

    文华的描述 "将其后N周期内的数据设置为0" 可能指一个信号系列，
    如果COND为真，则该信号为真，但接下来N个周期内即使COND再为真，信号也为假。
    This is a common way to prevent re-entry too soon.

    Output: A boolean series, True where COND is True AND not filtered.
    N does not include the bar where COND is true.
    """
    if n_filter_out < 0: n_filter_out = 0  # No negative filtering

    condition_true = condition.astype(bool)
    output_signal = pd.Series(False, index=condition.index)
    filter_until_iloc = -1  # Integer location until which signals are filtered

    for i in range(len(condition)):
        if i <= filter_until_iloc:  # If current bar is within a filter period
            output_signal.iloc[i] = False
        elif condition_true.iloc[i]:  # Condition is true and not currently filtered
            output_signal.iloc[i] = True
            filter_until_iloc = i + n_filter_out  # Filter for the NEXT N bars
        else:  # Condition is false and not filtered
            output_signal.iloc[i] = False

    return output_signal.astype(int)  # Return as 0 or 1


def LONGCROSS(series_a: pd.Series, series_b: pd.Series, n_sustain: int) -> pd.Series:
    """
    LONGCROSS(A,B,N) 表示A在N个周期内都小于B，本周期A从下向上穿越B.
    (A_prev[N-1] < B_prev[N-1]) AND ... AND (A_prev[0] < B_prev[0]) AND (A_curr > B_curr)
    Note: "本周期A从下向上穿越B" implies A_prev[0] <= B_prev[0] as well for the cross.
    So, for N periods *before current cross*, A < B.
    And current bar is A > B, previous bar was A <= B.
    """
    if n_sustain <= 0:
        return pd.Series(False, index=series_a.index).astype(int)

    is_crossing = CROSS(series_a, series_b).astype(bool)  # Current bar is a cross

    sustained_below = pd.Series(True, index=series_a.index)
    for k in range(1, n_sustain + 1):  # Check N periods *before* the current bar
        # For the cross to happen on current bar, A.shift(k) must be < B.shift(k)
        # for k=1 to N_sustain (these are bars t-1 down to t-N)
        # The cross definition itself takes care of A.shift(1) <= B.shift(1)
        # So, the N periods are t-1, t-2, ..., t-N
        # If n_sustain=1, it means A.shift(1) < B.shift(1) and CROSS.
        # This seems to mean N previous bars (not including current, but before previous of cross)
        # "A在N个周期内都小于B" - let's assume these are N periods ending at REF(A,1)
        sustained_below = sustained_below & (REF(series_a, k) < REF(series_b, k))

    return (is_crossing & sustained_below).astype(int)


def RANGE(series_x: pd.Series, series_lower: pd.Series, series_upper: pd.Series) -> pd.Series:
    """
    RANGE(X,Y,Z)：介于某个范围之内。表示X大于Y同时小于Z时返回1，否则返回0.
    (X > Y AND X < Z)
    """
    return ((series_x > series_lower) & (series_x < series_upper)).astype(int)


