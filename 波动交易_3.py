import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from tqsdk import TqApi, TqAuth, TqKq
import threading
import queue


class FuturesDataAnalyzer:
    def __init__(self, symbol, duration_seconds=15, data_length=200):
        """
        期货数据分析器初始化

        Args:
            symbol (str): 期货合约代码
            duration_seconds (int): K线周期(秒)
            data_length (int): 数据长度
        """
        self.symbol = symbol
        self.duration_seconds = duration_seconds
        self.data_length = data_length

        # 创建API实例
        self.api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)

        # 获取K线数据源
        self.klines = self.api.get_kline_serial(
            symbol,
            duration_seconds=duration_seconds,
            data_length=data_length
        )

        # 数据队列，用于线程间通信
        self.data_queue = queue.Queue()

        # 初始化图表
        plt.style.use('dark_background')
        self.fig, self.ax = plt.subplots(figsize=(15, 10))
        self.fig.suptitle(f'期货行情: {symbol}', fontsize=16)

        # 初始化数据和指标
        self.df = self.klines.copy()
        self.df = self.calculate_indicators(self.df)

    def data_update_thread(self):
        """
        数据更新线程
        持续监听数据变化并触发更新
        """
        while True:
            try:
                # 等待数据更新
                self.api.wait_update()

                # 检查最新K线是否发生变化
                if self.api.is_changing(self.klines.iloc[-1], "datetime"):
                    # 创建当前数据副本并计算指标
                    current_df = self.klines.copy()
                    current_df = self.calculate_indicators(current_df)

                    # 将更新后的数据放入队列
                    self.data_queue.put(current_df)

            except Exception as e:
                print(f"数据更新线程错误: {e}")
                break

    def start_data_thread(self):
        """
        启动数据更新线程
        """
        self.update_thread = threading.Thread(target=self.data_update_thread, daemon=True)
        self.update_thread.start()

    @staticmethod
    def MA(series, n):
        """计算移动平均线"""
        return series.rolling(window=n).mean()

    @staticmethod
    def VALUEWHEN(condition, value):
        """条件取值"""
        return value[condition].reindex(value.index).ffill()

    @staticmethod
    def REFX1(series, n):
        """平移"""
        return series.shift(n)

    def calculate_indicators(self, df):
        """
        计算技术指标

        Args:
            df (pandas.DataFrame): 原始数据

        Returns:
            pandas.DataFrame: 添加指标后的数据
        """
        # 移动平均线
        df['MA1'] = self.MA(df['close'], 5)

        # 高点和低点指标
        df['HH1'] = np.where(
            (df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
            df['high'].shift(2), 0
        )
        df['HH2'] = self.VALUEWHEN(df['HH1'] > 0, df['HH1'])

        df['LL1'] = np.where(
            (df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)),
            df['low'].shift(2), 0
        )
        df['LL2'] = self.VALUEWHEN(df['LL1'] > 0, df['LL1'])

        # 趋势判断指标
        df['K1'] = np.where(
            df['close'] > df['HH2'], -3,
            np.where(df['close'] < df['LL2'], 1, 0)
        )
        df['K2'] = self.VALUEWHEN(df['K1'] != 0, df['K1'])

        df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
        df['G1'] = df['G'].iloc[-1]

        # 其他指标
        df['W1'] = df['K2']
        df['W2'] = df['open'] - df['close']
        df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
        df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])

        df['LLL'] = np.minimum(df['open'].shift(1), df['close'].shift(1))
        df['HHH'] = np.maximum(df['open'].shift(1), df['close'].shift(1))

        df['CCCC'] = self.REFX1(df['close'], 1000)
        df['LLLL'] = self.REFX1(df['LLL'], 1000)
        df['HHHH'] = self.REFX1(df['HHH'], 1000)

        return df

    def plot_candlestick(self, df):
        """
        绘制K线图

        Args:
            df (pandas.DataFrame): 期货数据
        """
        width = 0.6
        width2 = 0.05

        up = df[df.close > df.open]
        down = df[df.close < df.open]
        equal = df[df.close == df.open]

        # 上涨K线 - 红色
        self.ax.bar(up.index, up.close - up.open, width, bottom=up.open, color='red')
        self.ax.vlines(up.index, up.low, up.high, color='red', linewidth=1)

        # 下跌K线 - 绿色
        self.ax.bar(down.index, down.close - down.open, width, bottom=down.open, color='green')
        self.ax.vlines(down.index, down.low, down.high, color='green', linewidth=1)

        # 开盘价等于收盘价的K线
        self.ax.bar(equal.index, width2, width, bottom=equal.open - width2 / 2, color='cyan')
        self.ax.vlines(equal.index, equal.low, equal.high, color='cyan', linewidth=1)

    def plot_indicators(self, df):
        """
        绘制技术指标

        Args:
            df (pandas.DataFrame): 期货数据
        """
        for i in range(len(df) - 1):
            if df['W1'].iloc[i] == 1:
                plt.plot(
                    [df.index[i], df.index[i]],
                    [df['low'].iloc[i], df['LT'].iloc[i]],
                    color='cyan'
                )
                plt.plot(
                    [df.index[i], df.index[i]],
                    [df['high'].iloc[i], df['HT'].iloc[i]],
                    color='cyan'
                )
            elif df['W1'].iloc[i] == -3:
                plt.plot(
                    [df.index[i], df.index[i]],
                    [df['low'].iloc[i], df['LT'].iloc[i]],
                    color='red'
                )
                plt.plot(
                    [df.index[i], df.index[i]],
                    [df['high'].iloc[i], df['HT'].iloc[i]],
                    color='red'
                )

            if df['W1'].iloc[i] == 1 and df['W1'].iloc[i - 1] == 1:
                plt.plot(
                    [df.index[i - 1], df.index[i]],
                    [df['G'].iloc[i - 1], df['G'].iloc[i]],
                    color='limegreen'
                )
            elif df['W1'].iloc[i] == -3 and df['W1'].iloc[i - 1] == -3:
                plt.plot(
                    [df.index[i - 1], df.index[i]],
                    [df['G'].iloc[i - 1], df['G'].iloc[i]],
                    color='yellow'
                )

        # 在最后一个点绘制G1的值
        plt.text(
            df.index[-1],
            df['G1'].iloc[-1],
            f"{df['G1'].iloc[-1]:.2f}",
            color='cyan',
            fontweight='bold'
        )

        plt.grid(True, color='gray', linestyle=':', alpha=0.5)
        plt.tick_params(axis='x', colors='white')
        plt.tick_params(axis='y', colors='white')

    def update_plot(self, frame):
        """
        实时更新图表

        Args:
            frame (int): 动画帧
        """
        try:
            # 检查是否有新数据
            if not self.data_queue.empty():
                # 获取最新数据
                self.df = self.data_queue.get()

                # 清除之前的图像
                self.ax.clear()
                self.ax.set_title(f'期货行情: {self.symbol}', color='white', fontweight='bold')
                plt.xlabel('Date', color='white')
                plt.ylabel('Price', color='white')

                # 绘制K线和指标
                self.plot_candlestick(self.df)
                self.plot_indicators(self.df)

        except Exception as e:
            print(f"图表更新错误: {e}")

    def start_visualization(self):
        """
        启动实时可视化
        """
        # 启动数据更新线程
        self.start_data_thread()

        # 启动图表动画
        self.anim = FuncAnimation(
            self.fig,
            self.update_plot,
            interval=1000,  # 每秒检查一次
            cache_frame_data=False
        )

        plt.tight_layout()
        plt.show()

    def __del__(self):
        """
        关闭API连接
        """
        if hasattr(self, 'api'):
            self.api.close()


def main():
    # 选择期货合约
    api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
    symbol = api.query_cont_quotes(product_id='OI')[0]
    api.close()

    # 创建和启动可视化
    analyzer = FuturesDataAnalyzer(symbol)
    analyzer.start_visualization()


if __name__ == '__main__':
    main()