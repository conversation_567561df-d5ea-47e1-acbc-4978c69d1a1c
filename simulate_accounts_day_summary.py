from tqsdk import *
import math
import numpy as np
import pandas as pd
import json
from accountSimulate_registerbook import account_simulate
# 保存到文件

import json
import datetime
import os

# 文件路径
file_path = 'simulatedaysummary_new.json'

# 直接访问所有实例
account_list = account_simulate.all_instances
day_account_info=[]
for account in account_list:
    # print(account.tqacc)
    name=account.tqacc.split(',')[0]
    api=TqApi(TqKq(), auth=account.tqacc)
    account=api.get_account()

    # Manually extract the attributes into a dictionary
    account_info = {
        'user_name': name,
        'day_pnl': account.balance-account.pre_balance,
        'pre_balance':account.pre_balance,
        'balance': account.balance,
        'close_profit': account.close_profit,
        'commission': account.commission,
        'available': account.available,
        'user_id': account.user_id,
        'float_profit': account.float_profit,
        'frozen_commission': account.frozen_commission,
        'frozen_margin': account.frozen_margin,
        'margin': account.margin,
        'risk_ratio': account.risk_ratio
    }

    print(account_info)
    print('当日盈亏: ',account_info['balance']-account_info['pre_balance'])
    day_account_info.append(account_info)
    api.close()

print(day_account_info)

# 定义一个自定义编码函数来处理nan和特殊对象
class NumpyJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.float32) or isinstance(obj, np.float64):
            return float(obj)
        if isinstance(obj, np.int32) or isinstance(obj, np.int64):
            return int(obj)
        if math.isnan(obj) if isinstance(obj, float) else False:
            return "NaN"
        # 处理tqsdk.objs.Account对象
        if str(type(obj)).find("tqsdk.objs.Account") > -1:
            # 转换为字典而不是字符串
            try:
                # 首先尝试使用obj.__dict__
                return obj.__dict__
            except:
                try:
                    # 如果没有__dict__，尝试使用vars()
                    return vars(obj)
                except:
                    try:
                        # 如果对象是可迭代的，转换为字典
                        return dict(obj)
                    except:
                        # 最后尝试返回对象的字符串表示
                        return str(obj)
        return super(NumpyJSONEncoder, self).default(obj)

# 添加或更新当前日期
current_date = datetime.datetime.now().strftime('%Y-%m-%d')
# 检查文件是否存在
if os.path.exists(file_path):
    # 读取现有文件
    with open(file_path, 'r', encoding='utf-8') as f:
        all_data = json.load(f)
else:
    # 创建新字典
    all_data = {}

# 将当前日期作为键，day_account_info 数组作为值
all_data[current_date] = day_account_info


# 保存回文件
with open(file_path, 'w', encoding='utf-8') as f:
    json.dump(all_data, f, ensure_ascii=False, indent=4, cls=NumpyJSONEncoder)

print(f"数据已更新并保存到{file_path}，当前日期：{current_date}")

