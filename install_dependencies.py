#!/usr/bin/env python3
"""
依赖安装脚本
自动检测和安装项目所需的Python包
"""

import subprocess
import sys
import os
import importlib
from typing import List, <PERSON><PERSON>


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)
    else:
        print(f"✅ Python版本检查通过: {sys.version}")


def run_command(command: List[str]) -> Tuple[bool, str]:
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr


def check_package_installed(package_name: str) -> bool:
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False


def install_package(package: str) -> bool:
    """安装单个包"""
    print(f"📦 正在安装 {package}...")
    success, output = run_command([sys.executable, "-m", "pip", "install", package])
    
    if success:
        print(f"✅ {package} 安装成功")
        return True
    else:
        print(f"❌ {package} 安装失败: {output}")
        return False


def install_from_requirements(requirements_file: str = "requirements.txt") -> bool:
    """从requirements文件安装依赖"""
    if not os.path.exists(requirements_file):
        print(f"❌ 找不到 {requirements_file} 文件")
        return False
    
    print(f"📋 从 {requirements_file} 安装依赖...")
    success, output = run_command([
        sys.executable, "-m", "pip", "install", "-r", requirements_file
    ])
    
    if success:
        print(f"✅ 从 {requirements_file} 安装成功")
        return True
    else:
        print(f"❌ 从 {requirements_file} 安装失败: {output}")
        return False


def upgrade_pip():
    """升级pip"""
    print("🔄 升级pip...")
    success, output = run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
    
    if success:
        print("✅ pip升级成功")
    else:
        print(f"⚠️ pip升级失败: {output}")


def check_critical_packages():
    """检查关键包是否正确安装"""
    critical_packages = {
        'tqsdk': 'TqSDK交易接口',
        'pandas': '数据处理',
        'numpy': '数值计算',
        'loguru': '日志记录',
        'zmq': 'ZeroMQ通信',
    }
    
    print("\n🔍 检查关键包安装状态...")
    all_good = True
    
    for package, description in critical_packages.items():
        if check_package_installed(package):
            print(f"✅ {package} ({description}) - 已安装")
        else:
            print(f"❌ {package} ({description}) - 未安装")
            all_good = False
    
    return all_good


def install_optional_packages():
    """安装可选包"""
    optional_packages = {
        'PyQt5': '图形界面支持',
        'matplotlib': '图表绘制',
        'jupyter': '开发环境',
    }
    
    print("\n📦 安装可选包...")
    for package, description in optional_packages.items():
        if not check_package_installed(package.lower()):
            choice = input(f"是否安装 {package} ({description})? [y/N]: ").lower()
            if choice in ['y', 'yes']:
                install_package(package)


def create_virtual_environment():
    """创建虚拟环境"""
    venv_path = "venv"
    
    if os.path.exists(venv_path):
        print(f"📁 虚拟环境 {venv_path} 已存在")
        return True
    
    choice = input("是否创建虚拟环境? [y/N]: ").lower()
    if choice not in ['y', 'yes']:
        return False
    
    print(f"🏗️ 创建虚拟环境 {venv_path}...")
    success, output = run_command([sys.executable, "-m", "venv", venv_path])
    
    if success:
        print(f"✅ 虚拟环境创建成功")
        print(f"💡 激活虚拟环境:")
        if os.name == 'nt':  # Windows
            print(f"   {venv_path}\\Scripts\\activate")
        else:  # Unix/Linux/macOS
            print(f"   source {venv_path}/bin/activate")
        return True
    else:
        print(f"❌ 虚拟环境创建失败: {output}")
        return False


def main():
    """主函数"""
    print("🚀 开始安装项目依赖...")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 询问是否创建虚拟环境
    create_virtual_environment()
    
    # 升级pip
    upgrade_pip()
    
    # 安装基础依赖
    if not install_from_requirements("requirements.txt"):
        print("❌ 基础依赖安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 检查关键包
    if not check_critical_packages():
        print("⚠️ 部分关键包未正确安装，请检查错误信息")
    
    # 询问是否安装开发依赖
    if os.path.exists("requirements-dev.txt"):
        choice = input("\n是否安装开发环境依赖? [y/N]: ").lower()
        if choice in ['y', 'yes']:
            install_from_requirements("requirements-dev.txt")
    
    # 安装可选包
    install_optional_packages()
    
    print("\n" + "=" * 50)
    print("🎉 依赖安装完成!")
    print("\n📝 下一步:")
    print("1. 配置TqSDK认证信息")
    print("2. 运行测试脚本验证安装")
    print("3. 查看README文档了解使用方法")
    
    # 显示已安装的包
    print("\n📋 已安装的包列表:")
    success, output = run_command([sys.executable, "-m", "pip", "list"])
    if success:
        print(output)


if __name__ == "__main__":
    main()
