'''
VAR1:=REF(CLOSE,1);
VAR2:=SUM(VOL,2)/((HHV(HIGH,2)-LLV(LOW,2))*100);
VAR3:=(CLOSE-VAR1)*VAR2;
VAR4:=SUM(VAR3,0);
VAR5:=SMA(VAR4,5,1);
VAR6:=SMA(VAR4,13,1);
DB:VAR5-VAR6,COLORWHITE,LINETHICK2;
CB:MA(DB,4),COLORRED,LINETHICK2;
STICKLINE(DB>0,0,DB,10,1),COLORRED;
STICKLINE(VAR5-VAR6<0,0,VAR5-VAR6,10,1),COLORGREEN;
STICKLINE(DB>CB,DB,CB,3.6,0),COLORRED;
STICKLINE(DB<CB,DB,CB,3.6,0),COLORCYAN;
QS:=CROSS(DB,CB),COLORRED;
RS:=CROSS(CB,DB),COLORGREEN;
BJ:0,COLORBLUE,LINETHICK3;
DRAWICON(QS,CB,1),VALIGN0;
DRAWICON(RS,DB,2),VALIGN2;
'''