"""
多时间周期统一策略 - 支持命令行参数指定合约
整合 1m, 3m, 5m, 15m 时间周期，支持多种运行模式
支持通过命令行参数指定合约代码（如 ag, rb, cu 等）
支持交易时间控制：晚上20:55启动，下午15:05关闭
"""

import copy
import time
import schedule
from datetime import datetime, time as dt_time, timedelta
from typing import Dict, List
from tqsdk import TqApi, TqKq
from strategies.TimeRoseMA_cross_speak import ma_cross
import threading
from concurrent.futures import ThreadPoolExecutor
import queue
import logging


class TradingTimeController:
    """交易时间控制器"""

    def __init__(self):
        self.start_time = dt_time(20, 55)  # 晚上20:55启动
        self.end_time = dt_time(15, 5)     # 下午15:05关闭
        self.is_trading_time = False
        self.strategy_instance = None

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('trading_schedule.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def is_trading_day(self) -> bool:
        """判断是否为交易日（简化版本，排除周末）"""
        today = datetime.now().weekday()
        # 0=周一, 1=周二, ..., 6=周日
        # 排除周六(5)和周日(6)
        return today < 5

    def should_start_trading(self) -> bool:
        """判断是否应该启动交易"""
        if not self.is_trading_day():
            return False

        now = datetime.now().time()

        # 检查是否在启动时间窗口内（20:55-21:00）
        start_window_end = dt_time(21, 0)
        return self.start_time <= now <= start_window_end

    def should_stop_trading(self) -> bool:
        """判断是否应该停止交易"""
        now = datetime.now().time()

        # 检查是否在停止时间窗口内（15:05-15:10）
        stop_window_end = dt_time(15, 10)
        return self.end_time <= now <= stop_window_end

    def is_in_trading_session(self) -> bool:
        """判断是否在交易时段内"""
        if not self.is_trading_day():
            return False

        now = datetime.now().time()

        # 夜盘：21:00-02:30（次日）
        night_start = dt_time(21, 0)
        night_end = dt_time(2, 30)

        # 日盘：09:00-15:00
        day_start = dt_time(9, 0)
        day_end = dt_time(15, 0)

        # 检查是否在交易时段
        if night_start <= now or now <= night_end:
            return True  # 夜盘时段
        elif day_start <= now <= day_end:
            return True  # 日盘时段
        else:
            return False

    def start_strategy(self, strategy_func, *args, **kwargs):
        """启动策略"""
        if self.strategy_instance is not None:
            self.logger.warning("策略已在运行中")
            return

        self.logger.info("启动交易策略...")
        self.is_trading_time = True

        # 在新线程中运行策略
        strategy_thread = threading.Thread(
            target=self._run_strategy_wrapper,
            args=(strategy_func, args, kwargs),
            daemon=True
        )
        strategy_thread.start()
        self.strategy_instance = strategy_thread

    def _run_strategy_wrapper(self, strategy_func, args, kwargs):
        """策略运行包装器"""
        try:
            strategy_func(*args, **kwargs)
        except Exception as e:
            self.logger.error(f"策略运行出错: {e}")
        finally:
            self.strategy_instance = None
            self.is_trading_time = False

    def stop_strategy(self):
        """停止策略"""
        if self.strategy_instance is None:
            self.logger.warning("没有运行中的策略")
            return

        self.logger.info("停止交易策略...")
        self.is_trading_time = False

        # 这里可以添加更优雅的停止逻辑
        # 由于策略通常在无限循环中，可能需要设置停止标志

    def setup_schedule(self, strategy_func, *args, **kwargs):
        """设置定时任务"""
        self.logger.info("设置交易时间调度...")

        # 每天20:55检查是否启动
        schedule.every().day.at("20:55").do(
            self._check_and_start, strategy_func, *args, **kwargs
        )

        # 每天15:05检查是否停止
        schedule.every().day.at("15:05").do(self._check_and_stop)

        # 每分钟检查交易状态
        schedule.every().minute.do(self._check_trading_status)

        self.logger.info("交易调度设置完成")
        self.logger.info(f"启动时间: {self.start_time}")
        self.logger.info(f"停止时间: {self.end_time}")

    def _check_and_start(self, strategy_func, *args, **kwargs):
        """检查并启动策略"""
        if self.should_start_trading() and not self.is_trading_time:
            self.start_strategy(strategy_func, *args, **kwargs)
        else:
            self.logger.info("不满足启动条件或策略已在运行")

    def _check_and_stop(self):
        """检查并停止策略"""
        if self.should_stop_trading() and self.is_trading_time:
            self.stop_strategy()
        else:
            self.logger.info("不满足停止条件或策略未在运行")

    def _check_trading_status(self):
        """检查交易状态"""
        if self.is_trading_time:
            if not self.is_in_trading_session():
                self.logger.warning("当前不在交易时段，但策略仍在运行")

    def run_scheduler(self):
        """运行调度器"""
        self.logger.info("启动交易时间调度器...")

        try:
            while True:
                schedule.run_pending()
                time.sleep(30)  # 每30秒检查一次
        except KeyboardInterrupt:
            self.logger.info("调度器被用户中断")
            if self.is_trading_time:
                self.stop_strategy()


class TimeFrameConfig:
    """时间周期配置类"""
    def __init__(self, interval: int, bklimit: int = 1, sklimit: int = 1, single_volume: int = 1):
        self.interval = interval  # 时间间隔（秒）
        self.bklimit = bklimit    # 多单限制
        self.sklimit = sklimit    # 空单限制
        self.single_volume = single_volume  # 单次交易量
        self.name = f"{interval//60}m"  # 时间周期名称


class MultiTimeFrameStrategy:
    """多时间周期策略管理器（多线程模式）"""

    def __init__(self, api: TqApi, symbol: str, timeframe_configs: List[TimeFrameConfig], auth: str = None):
        self.symbol = symbol
        self.timeframe_configs = timeframe_configs
        self.auth = auth or "quant_ggh,Qiai1301"
        self.running = False
        self.threads = []
        self.apis = {}  # 为每个线程创建独立的API

    def run_single_timeframe(self, config: TimeFrameConfig):
        """运行单个时间周期的策略（独立API）"""
        try:
            print(f"启动 {config.name} 时间周期策略...")

            # 为每个时间周期创建独立的API连接
            api = TqApi(TqKq(), auth=self.auth, disable_print=True)
            self.apis[config.name] = api

            # 获取合约
            if len(self.symbol) <= 3:
                symbol = api.query_cont_quotes(product_id=self.symbol)[0]
            else:
                symbol = self.symbol

            print(f"{config.name} 策略使用合约: {symbol}")

            ma_cross(
                api=api,
                symbol=symbol,
                interval=config.interval,
                single_volume=config.single_volume,
                bklimit=config.bklimit,
                sklimit=config.sklimit
            )
        except Exception as e:
            print(f"{config.name} 时间周期策略出错: {e}")
        finally:
            # 清理API连接
            if config.name in self.apis:
                self.apis[config.name].close()
                del self.apis[config.name]

    def run_strategy(self):
        """启动所有时间周期策略"""
        self.running = True

        print("=== 启动多线程多时间周期策略 ===")

        # 为每个时间周期创建独立的线程
        for config in self.timeframe_configs:
            thread = threading.Thread(
                target=self.run_single_timeframe,
                args=(config,),
                name=f"TimeFrame_{config.name}",
                daemon=True
            )
            self.threads.append(thread)
            thread.start()

        print(f"已启动 {len(self.timeframe_configs)} 个时间周期策略线程")

        # 等待所有线程
        try:
            for thread in self.threads:
                thread.join()
        except KeyboardInterrupt:
            print("策略被用户中断")
            self.stop()

    def stop(self):
        """停止所有策略"""
        self.running = False
        print("正在停止所有时间周期策略...")

        # 关闭所有API连接
        for name, api in self.apis.items():
            try:
                api.close()
                print(f"已关闭 {name} 的API连接")
            except:
                pass


class OptimizedMultiTimeFrameStrategy:
    """优化的多时间周期策略（单API，多时间周期并行处理）"""

    def __init__(self, symbol: str, timeframe_configs: List[TimeFrameConfig], auth: str):
        self.symbol = symbol
        self.timeframe_configs = timeframe_configs
        self.auth = auth
        self.api = None
        self.running = False
        self.strategy_threads = {}
        self.klines = {}  # 存储各时间周期的K线数据
        self.quotes = {}  # 存储行情数据

    def initialize_api(self):
        """初始化API连接"""
        self.api = TqApi(TqKq(), auth=self.auth)
        # 如果symbol是产品代码，获取主力合约
        if len(self.symbol) <= 3:
            self.symbol = self.api.query_cont_quotes(product_id=self.symbol)[0]
        print(f"交易合约: {self.symbol}")

        # 初始化各时间周期的K线数据
        for config in self.timeframe_configs:
            self.klines[config.name] = self.api.get_kline_serial(self.symbol, config.interval)
            print(f"初始化 {config.name} K线数据")

        # 获取行情数据
        self.quotes[self.symbol] = self.api.get_quote(self.symbol)
        print(f"初始化行情数据: {self.symbol}")

    def run_strategy(self):
        """运行策略主循环 - 使用ma_cross函数"""
        self.initialize_api()
        self.running = True

        print("=== 开始运行多时间周期策略（共享API模式）===")

        # 导入ma_cross函数
        from strategies.TimeRoseMA_cross_speak import ma_cross

        try:
            # 为每个时间周期创建线程运行ma_cross
            threads = []
            for config in self.timeframe_configs:
                thread = threading.Thread(
                    target=self.run_timeframe_strategy,
                    args=(config,),
                    name=f"TimeFrame_{config.name}",
                    daemon=True
                )
                threads.append(thread)
                thread.start()
                print(f"启动 {config.name} 策略线程")

            print(f"已启动 {len(self.timeframe_configs)} 个时间周期策略线程")

            # 等待所有线程
            for thread in threads:
                thread.join()

        except KeyboardInterrupt:
            print("策略被用户中断")
        except Exception as e:
            print(f"策略执行出错: {e}")
        finally:
            self.cleanup()

    def run_timeframe_strategy(self, config: TimeFrameConfig):
        """运行单个时间周期的策略"""
        try:
            from strategies.TimeRoseMA_cross_speak import ma_cross

            print(f"启动 {config.name} 时间周期策略，合约: {self.symbol}")

            # 使用共享的API运行ma_cross
            ma_cross(
                api=self.api,
                symbol=self.symbol,
                interval=config.interval,
                single_volume=config.single_volume,
                bklimit=config.bklimit,
                sklimit=config.sklimit
            )
        except Exception as e:
            print(f"{config.name} 时间周期策略出错: {e}")



    def cleanup(self):
        """清理资源"""
        self.running = False
        if self.api:
            self.api.close()
            print("API连接已关闭")


class SimpleMultiTimeFrameStrategy:
    """简化的多时间周期策略（独立API方案）"""

    def __init__(self, symbol: str, timeframe_configs: List[TimeFrameConfig], auth: str):
        self.symbol = symbol
        self.timeframe_configs = timeframe_configs
        self.auth = auth
        self.processes = []

    def run_strategy(self):
        """运行策略 - 每个时间周期独立进程"""
        print("=== 启动多时间周期策略（独立API模式）===")

        try:
            # 为每个时间周期创建独立的进程
            for config in self.timeframe_configs:
                self.start_timeframe_process(config)

            # 等待用户中断
            while True:
                time.sleep(1)

        except KeyboardInterrupt:
            print("策略被用户中断")
        finally:
            self.cleanup()

    def start_timeframe_process(self, config: TimeFrameConfig):
        """启动单个时间周期的独立进程"""
        import subprocess
        import sys
        import os
        import tempfile
        from datetime import datetime

        # 创建独立的Python脚本来运行单个时间周期
        script_content = f'''
from strategies.TimeRoseMA_cross_speak import ma_cross
from tqsdk import TqApi, TqKq

def run_single_timeframe():
    product = "{self.symbol}"
    interval = {config.interval}
    bklimit = {config.bklimit}
    sklimit = {config.sklimit}
    single_volume = {config.single_volume}

    api = TqApi(TqKq(), auth="{self.auth}")
    if len(product) <= 3:
        symbol = api.query_cont_quotes(product_id=product)[0]
    else:
        symbol = product

    print(f"启动 {config.name} 时间周期策略，合约: {{symbol}}")
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)

if __name__ == "__main__":
    run_single_timeframe()
'''

        # 生成唯一的临时文件名（包含合约代码、时间周期、进程ID和时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pid = os.getpid()
        script_filename = f"temp_strategy_{self.symbol}_{config.name}_{pid}_{timestamp}.py"

        with open(script_filename, 'w', encoding='utf-8') as f:
            f.write(script_content)

        # 记录临时文件以便后续清理
        if not hasattr(self, 'temp_files'):
            self.temp_files = []
        self.temp_files.append(script_filename)

        # 启动子进程
        process = subprocess.Popen([sys.executable, script_filename])
        self.processes.append((process, script_filename))
        print(f"已启动 {config.name} 策略进程 (PID: {process.pid})")

    def cleanup(self):
        """清理资源"""
        import os

        print(f"清理 {self.symbol} 策略资源...")

        # 终止所有子进程
        for process, script_file in self.processes:
            if process.poll() is None:  # 进程还在运行
                process.terminate()
                print(f"  已终止进程 {process.pid}")

                # 等待进程结束
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    print(f"  强制终止进程 {process.pid}")

            # 删除临时脚本文件
            try:
                if os.path.exists(script_file):
                    os.remove(script_file)
                    print(f"  删除临时文件: {script_file}")
            except Exception as e:
                print(f"  删除临时文件失败 {script_file}: {e}")

        # 清理临时文件列表
        if hasattr(self, 'temp_files'):
            for temp_file in self.temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        print(f"  删除临时文件: {temp_file}")
                except Exception as e:
                    print(f"  删除临时文件失败 {temp_file}: {e}")
            self.temp_files.clear()

        self.processes.clear()
        print(f"  {self.symbol} 策略资源清理完成")

    def __del__(self):
        """析构函数，自动清理资源"""
        try:
            self.cleanup()
        except:
            pass  # 忽略析构时的错误


def create_default_timeframe_configs() -> List[TimeFrameConfig]:
    """创建默认的时间周期配置"""
    return [
        TimeFrameConfig(interval=60, bklimit=1, sklimit=1, single_volume=1),      # 1分钟
        TimeFrameConfig(interval=180, bklimit=1, sklimit=1, single_volume=1),     # 3分钟
        TimeFrameConfig(interval=300, bklimit=1, sklimit=1, single_volume=1),     # 5分钟
        TimeFrameConfig(interval=900, bklimit=1, sklimit=1, single_volume=1),     # 15分钟
    ]


def run_multi_timeframe_strategy(product: str = "ag", mode: str = "optimized", auth: str = "quant_ggh,Qiai1301"):
    """
    运行多时间周期策略的主函数

    Args:
        product: 合约代码（如 ag, rb, cu 等）
        mode: 运行模式
            - "optimized": 优化模式（默认，单API共享，资源消耗低）
            - "independent": 独立API模式（每个周期独立API，稳定性好）
            - "threaded": 多线程模式（可能有API冲突）
        auth: TqSDK认证信息
    """

    # 配置参数（现在从参数传入）
    # product = 'ag'  # 现在从参数传入
    # auth = "quant_ggh,Qiai1301"  # 现在从参数传入

    # 创建时间周期配置
    timeframe_configs = create_default_timeframe_configs()

    print("=== 多时间周期策略启动 ===")
    print(f"产品: {product}")
    print(f"运行模式: {mode}")
    print(f"认证信息: {auth}")
    print(f"时间周期: {[config.name for config in timeframe_configs]}")

    # 根据模式选择策略类
    if mode == "optimized":
        # 默认模式：共享API，资源消耗低
        strategy = OptimizedMultiTimeFrameStrategy(
            symbol=product,
            timeframe_configs=timeframe_configs,
            auth=auth
        )
    elif mode == "independent":
        # 独立API模式：每个周期独立API，稳定性好
        strategy = SimpleMultiTimeFrameStrategy(
            symbol=product,
            timeframe_configs=timeframe_configs,
            auth=auth
        )
    elif mode == "threaded":
        # 多线程模式：可能有API冲突
        strategy = MultiTimeFrameStrategy(
            api=None,  # 将在内部创建
            symbol=product,
            timeframe_configs=timeframe_configs,
            auth=auth
        )
    else:
        raise ValueError(f"不支持的运行模式: {mode}")

    try:
        strategy.run_strategy()
    except KeyboardInterrupt:
        print("策略已停止")
    except Exception as e:
        print(f"策略运行出错: {e}")


def run_single_timeframe(product: str, timeframe: str, auth: str = "quant_ggh,Qiai1301"):
    """运行单个时间周期策略（兼容原有文件）"""

    timeframe_map = {
        "1m": 60,
        "3m": 180,
        "5m": 300,
        "15m": 900
    }

    if timeframe not in timeframe_map:
        raise ValueError(f"不支持的时间周期: {timeframe}")

    from strategies.TimeRoseMA_cross_speak import ma_cross
    from tqsdk import TqApi, TqKq

    # product = 'ag'  # 现在从参数传入
    interval = timeframe_map[timeframe]
    bklimit = 1
    sklimit = 1
    single_volume = 1

    api = TqApi(TqKq(), auth=auth)
    symbol = api.query_cont_quotes(product_id=product)[0]

    print(f"运行 {product} {timeframe} 策略，合约: {symbol}")
    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


def run_with_time_control(product: str, mode: str = "optimized", auth: str = "quant_ggh,Qiai1301"):
    """带时间控制的策略运行"""
    controller = TradingTimeController()

    # 设置策略参数
    def strategy_wrapper():
        run_multi_timeframe_strategy(product, mode, auth)

    # 设置定时任务
    controller.setup_schedule(strategy_wrapper)

    # 检查是否立即启动
    if controller.should_start_trading():
        print("当前时间满足启动条件，立即启动策略...")
        controller.start_strategy(strategy_wrapper)
    elif controller.is_in_trading_session():
        print("当前在交易时段内，立即启动策略...")
        controller.start_strategy(strategy_wrapper)
    else:
        print("等待交易时间...")

    # 运行调度器
    controller.run_scheduler()


def print_usage():
    """打印使用说明"""
    print("用法:")
    print("  python TimeRoseMA_cross_ag_MultiTimeFrames.py <product> [timeframe|mode|schedule] [auth]")
    print("")
    print("参数说明:")
    print("  product: 合约代码 (如: ag, rb, cu, au, ni 等)")
    print("  timeframe: 单时间周期模式 [1m|3m|5m|15m]")
    print("  mode: 多时间周期模式 [optimized|independent|threaded]")
    print("    - optimized: 共享API模式（默认，资源消耗低）")
    print("    - independent: 独立API模式（稳定性好）")
    print("    - threaded: 多线程模式（可能有冲突）")
    print("  schedule: 定时模式 [schedule] - 20:55启动，15:05关闭")
    print("  auth: TqSDK认证信息 (可选，默认: quant_ggh,Qiai1301)")
    print("")
    print("示例:")
    print("  python TimeRoseMA_cross_ag_MultiTimeFrames.py ag                    # ag多时间周期策略（默认共享API）")
    print("  python TimeRoseMA_cross_ag_MultiTimeFrames.py rb independent        # rb独立API模式")
    print("  python TimeRoseMA_cross_ag_MultiTimeFrames.py cu optimized          # cu共享API模式")
    print("  python TimeRoseMA_cross_ag_MultiTimeFrames.py cu 5m                 # cu 5分钟策略")
    print("  python TimeRoseMA_cross_ag_MultiTimeFrames.py ag schedule           # ag定时策略（默认共享API）")
    print("  python TimeRoseMA_cross_ag_MultiTimeFrames.py ag 1m user,pass       # 自定义认证")


if __name__ == "__main__":
    import sys

    # 解析命令行参数
    if len(sys.argv) < 2:
        print("错误: 缺少合约代码参数")
        print_usage()
        sys.exit(1)

    # 获取合约代码
    product = sys.argv[1].lower()

    # 默认参数
    default_auth = "quant_ggh,Qiai1301"

    # 解析第二个参数（时间周期或模式）
    if len(sys.argv) > 2:
        second_arg = sys.argv[2]

        # 获取认证信息（如果提供）
        auth = sys.argv[3] if len(sys.argv) > 3 else default_auth

        if second_arg in ["1m", "3m", "5m", "15m"]:
            # 运行单个时间周期
            print(f"启动 {product.upper()} {second_arg} 单时间周期策略")
            try:
                run_single_timeframe(product, second_arg, auth)
            except KeyboardInterrupt:
                print("策略已停止")
            except Exception as e:
                print(f"策略运行出错: {e}")

        elif second_arg in ["optimized", "independent", "threaded"]:
            # 运行多时间周期
            print(f"启动 {product.upper()} 多时间周期策略 ({second_arg} 模式)")
            try:
                run_multi_timeframe_strategy(product, second_arg, auth)
            except KeyboardInterrupt:
                print("策略已停止")
            except Exception as e:
                print(f"策略运行出错: {e}")

        elif second_arg == "schedule":
            # 运行定时策略
            print(f"启动 {product.upper()} 定时策略 (20:55启动，15:05关闭)")
            try:
                run_with_time_control(product, "optimized", auth)
            except KeyboardInterrupt:
                print("定时策略已停止")
            except Exception as e:
                print(f"定时策略运行出错: {e}")

        else:
            print(f"错误: 不支持的参数 '{second_arg}'")
            print_usage()
            sys.exit(1)
    else:
        # 只提供了合约代码，默认运行多时间周期策略
        print(f"启动 {product.upper()} 多时间周期策略 (默认共享API模式)")
        try:
            run_multi_timeframe_strategy(product, "optimized", default_auth)
        except KeyboardInterrupt:
            print("策略已停止")
        except Exception as e:
            print(f"策略运行出错: {e}")
