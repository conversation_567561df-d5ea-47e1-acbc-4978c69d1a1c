"""
测试策略执行
验证不同策略是否正确执行
"""

import subprocess
import sys
import time
from loguru import logger

def test_strategy_execution():
    """测试策略执行"""
    print("=" * 60)
    print("测试策略执行")
    print("=" * 60)
    
    # 测试命令列表
    test_commands = [
        {
            "name": "LLT策略测试",
            "command": [
                sys.executable, "llt_multi_contract_analyzer.py",
                "--strategy", "LLT",
                "--d-range", "15", "21",
                "--mode", "quick",
                "--groups", "metals",
                "--output", "test_llt_output"
            ],
            "expected_indicators": ["LLT", "D_VALUE", "ALPHA"]
        },
        {
            "name": "MA策略测试", 
            "command": [
                sys.executable, "llt_multi_contract_analyzer.py",
                "--strategy", "MA",
                "--ma-range", "10", "16",
                "--mode", "quick",
                "--groups", "metals",
                "--output", "test_ma_output"
            ],
            "expected_indicators": ["MA", "MA_PERIOD", "移动平均"]
        },
        {
            "name": "双均线策略测试",
            "command": [
                sys.executable, "llt_multi_contract_analyzer.py", 
                "--strategy", "DualMA",
                "--fast-range", "5", "8",
                "--slow-range", "13", "16",
                "--mode", "quick",
                "--groups", "metals",
                "--output", "test_dual_ma_output"
            ],
            "expected_indicators": ["DualMA", "FAST_PERIOD", "SLOW_PERIOD", "双均线"]
        }
    ]
    
    results = []
    
    for test_case in test_commands:
        print(f"\n🔄 执行测试: {test_case['name']}")
        print(f"命令: {' '.join(test_case['command'])}")
        
        try:
            # 执行命令
            start_time = time.time()
            result = subprocess.run(
                test_case['command'],
                capture_output=True,
                text=True,
                timeout=120  # 2分钟超时
            )
            execution_time = time.time() - start_time
            
            print(f"⏱️  执行时间: {execution_time:.1f}秒")
            print(f"返回码: {result.returncode}")
            
            if result.returncode == 0:
                # 检查输出中是否包含预期的指标
                output_text = result.stdout + result.stderr
                
                found_indicators = []
                missing_indicators = []
                
                for indicator in test_case['expected_indicators']:
                    if indicator in output_text:
                        found_indicators.append(indicator)
                    else:
                        missing_indicators.append(indicator)
                
                print(f"✅ 找到指标: {found_indicators}")
                if missing_indicators:
                    print(f"⚠️  缺失指标: {missing_indicators}")
                
                # 检查是否有错误信息
                if "❌" in output_text or "ERROR" in output_text.upper():
                    print("⚠️  输出中包含错误信息")
                    success = False
                elif len(found_indicators) >= len(test_case['expected_indicators']) // 2:
                    print("✅ 策略执行成功")
                    success = True
                else:
                    print("❌ 策略执行可能有问题")
                    success = False
                
                results.append((test_case['name'], success))
                
            else:
                print(f"❌ 命令执行失败")
                print(f"错误输出: {result.stderr[:500]}")
                results.append((test_case['name'], False))
                
        except subprocess.TimeoutExpired:
            print("❌ 命令执行超时")
            results.append((test_case['name'], False))
        except Exception as e:
            print(f"❌ 执行异常: {e}")
            results.append((test_case['name'], False))
    
    return results


def test_strategy_config_validation():
    """测试策略配置验证"""
    print("=" * 60)
    print("测试策略配置验证")
    print("=" * 60)
    
    try:
        from llt_multi_contract_analyzer import create_strategy_config_from_args
        
        # 模拟不同的命令行参数
        test_cases = [
            {
                "name": "LLT策略配置",
                "args": type('Args', (), {
                    'strategy': 'LLT',
                    'd_range': [10, 31],
                    'ma_range': [5, 51],
                    'fast_range': [3, 11],
                    'slow_range': [10, 31]
                })(),
                "expected_type": "LLT",
                "expected_param": "d_value_range"
            },
            {
                "name": "MA策略配置",
                "args": type('Args', (), {
                    'strategy': 'MA',
                    'd_range': [20, 81],
                    'ma_range': [10, 21],
                    'fast_range': [3, 11],
                    'slow_range': [10, 31]
                })(),
                "expected_type": "MA",
                "expected_param": "period_range"
            },
            {
                "name": "双均线策略配置",
                "args": type('Args', (), {
                    'strategy': 'DualMA',
                    'd_range': [20, 81],
                    'ma_range': [5, 51],
                    'fast_range': [5, 11],
                    'slow_range': [13, 21]
                })(),
                "expected_type": "DualMA",
                "expected_param": "fast_range"
            }
        ]
        
        all_passed = True
        
        for test_case in test_cases:
            print(f"\n📊 测试: {test_case['name']}")
            
            config = create_strategy_config_from_args(test_case['args'])
            
            print(f"  策略类型: {config.strategy_type}")
            print(f"  参数: {config.parameters}")
            
            # 验证策略类型
            if config.strategy_type == test_case['expected_type']:
                print(f"  ✅ 策略类型正确: {config.strategy_type}")
            else:
                print(f"  ❌ 策略类型错误: 期望{test_case['expected_type']}, 实际{config.strategy_type}")
                all_passed = False
            
            # 验证参数
            if test_case['expected_param'] in config.parameters:
                print(f"  ✅ 参数包含: {test_case['expected_param']}")
            else:
                print(f"  ❌ 参数缺失: {test_case['expected_param']}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_debug_info():
    """显示调试信息"""
    print("=" * 60)
    print("调试信息")
    print("=" * 60)
    
    try:
        from llt_multi_contract_analyzer import (
            StrategyConfig, StrategyAnalyzer, 
            LLTIndicator, MAIndicator, DualMAIndicator
        )
        
        print("✅ 策略相关类导入成功:")
        print(f"  - StrategyConfig: {StrategyConfig}")
        print(f"  - StrategyAnalyzer: {StrategyAnalyzer}")
        print(f"  - LLTIndicator: {LLTIndicator}")
        print(f"  - MAIndicator: {MAIndicator}")
        print(f"  - DualMAIndicator: {DualMAIndicator}")
        
        # 测试策略分析器的指标类选择
        print(f"\n📊 测试指标类选择:")
        
        import pandas as pd
        import numpy as np
        
        # 创建虚拟数据
        dummy_data = pd.DataFrame({
            'datetime': pd.date_range('2024-01-01', periods=10, freq='5min'),
            'close': np.random.randn(10) + 100
        })
        
        from llt_multi_contract_analyzer import ContractConfig
        dummy_contract = ContractConfig("TEST", "测试", "TEST")
        
        # 测试不同策略的指标类
        strategies = [
            StrategyConfig("LLT", {}),
            StrategyConfig("MA", {}),
            StrategyConfig("DualMA", {})
        ]
        
        for strategy in strategies:
            analyzer = StrategyAnalyzer(dummy_contract, dummy_data, strategy)
            print(f"  {strategy.strategy_type}策略 -> {analyzer.indicator_class.__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试信息获取失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("策略执行测试")
    print("=" * 80)
    
    tests = [
        ("策略配置验证", test_strategy_config_validation),
        ("调试信息", show_debug_info),
        ("策略执行测试", test_strategy_execution),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 策略执行测试成功！")
        print("\n📋 现在可以正确使用不同策略:")
        print("python llt_multi_contract_analyzer.py --strategy MA --mode quick")
        print("python llt_multi_contract_analyzer.py --strategy DualMA --mode quick")
    else:
        print("⚠️  部分测试失败，策略选择可能还有问题")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
