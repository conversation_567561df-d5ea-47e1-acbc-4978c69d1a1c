import copy
import sys
import os
import pandas as pd
from myfunction import *
from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from signalSend_noblock import socket, sendsignal
from time import sleep
import time

try:
    from .paths import Paths
except:
    from paths import Paths
from MyTT import MA, CROSS
from utils.utils import tradingTime

speak_text('程序开始运行')


def disp_day_info(daymean, last3mean, hlmax, homean, olmean):
    print('最大价差:', hlmax, '平均价差:', daymean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:', int(olmean))


def disp_0Day_info(quote):
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    gapcover = False

    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    print(quote.datetime, quote.instrument_id, '缺口：', gap, '回补:', gapcover, '现价:', quote.last_price, 'BB:', BB, '今日价差：',
          dayvib, '上价差:', upvib, '下价差:', dnvib)


def process_symbol_data(api, symbol, interval, period=13):
    """初始化单个合约的数据"""
    data = {}

    data['quote'] = api.get_quote(symbol)
    SYMBOL = symbol
        # if 'KQ.i' in symbol or 'KQ.m' in symbol else data['quote'].underlying_symbol

    data['klines'] = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)

    data['klines_tmp'] = api.get_kline_serial(SYMBOL, interval, 10)
    data['position'] = api.get_position(SYMBOL)
    data['daybars'] = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    data['daybar'] = data['daybars'].dropna()

    productid = SYMBOL.split('.')[1]
    minutes = str(int(data['klines_tmp'].duration.iloc[0] / 60))
    soundmsg = ''.join([productid, '     ', minutes])

    data['sounds'] = {
        'open_long': soundmsg + '分钟发出做多信号',
        'open_short': soundmsg + '分钟发出做空信号',
        'close_long': soundmsg + '分钟发出平多单信号',
        'close_short': soundmsg + '分钟发出平空单信号'
    }

    return SYMBOL, data


def calculate_signals(klines, quote, period=13):
    """计算交易信号"""
    C = klines.close
    trmac = ma(C, period)
    ups = crossup(C, trmac)
    dns = crossdown(C, trmac)

    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)

    signalnow = '空' if bkdist > skdist else '多'
    distnow = skdist if bkdist > skdist else bkdist

    signalprice = C.iloc[-distnow]
    sigfloatprofit = quote.last_price - signalprice if signalnow == '多' else signalprice - quote.last_price

    return {
        'signalnow': signalnow,
        'distnow': distnow,
        'signalprice': signalprice,
        'sigfloatprofit': sigfloatprofit,
        'ups': ups,
        'dns': dns
    }


def handle_trading(api, symbol, signal_data, position, quote, single_volume, bklimit, sklimit):
    """处理交易逻辑"""
    if signal_data['ups'].iloc[-1]:
        bkprice = quote.last_price + 1
        if position.pos_long < bklimit:
            # BK(api, symbol=symbol, order_price=bkprice, volume=single_volume)
            mylog.info([symbol, '下多单:', '数量', single_volume, '价格', bkprice])

        if position.pos_short > 0 and position.float_profit_short > 5 * position.pos_short:
            # BP(api, symbol=symbol, order_price=bkprice, volume=single_volume)
            mylog.info([symbol, '平多单:', '数量', single_volume, '价格', bkprice])

    if signal_data['dns'].iloc[-1]:
        skprice = quote.last_price - 1
        if position.pos_short < sklimit:
            # SK(api, symbol=symbol, order_price=skprice, volume=single_volume)
            mylog.info([symbol, '下空单:', '数量', single_volume, '价格', skprice])

        if position.pos_long > 0 and position.float_profit_long > 5 * position.pos_long:
            # SP(api, symbol=symbol, order_price=skprice, volume=single_volume)
            mylog.info([symbol, '平空单:', '数量', single_volume, '价格', skprice])


def ma_cross_multi(api, symbols, interval, single_volume, bklimit, sklimit, period=13):
    """处理多个合约的MA交叉策略"""
    strategyname = 'ma_cross'
    symbols_data = {}

    # 初始化每个合约的数据
    for symbol in symbols:
        try:
            SYMBOL, data = process_symbol_data(api, symbol, interval, period)
            symbols_data[SYMBOL] = data
            mylog.info(f"Successfully initialized {SYMBOL}")
        except Exception as e:
            mylog.error(f"Error initializing {symbol}: {e}")
            continue

    # 主循环
    while True:
        api.wait_update()

        # 遍历所有合约处理更新
        for SYMBOL, data in symbols_data.items():
            try:
                if api.is_changing(data['quote']) or api.is_changing(data['klines_tmp'].iloc[-1], "datetime"):
                    # 计算信号
                    signal_data = calculate_signals(data['klines'], data['quote'], period)

                    # 更新信号
                    signal = {
                        '合约': SYMBOL,
                        '周期': interval,
                        '时间': data['quote'].datetime.split(' ')[1].split('.')[0],
                        '当前信号': signal_data['signalnow'],
                        '持续周期': signal_data['distnow'],
                        '信号价格': signal_data['signalprice'],
                        '现价': data['quote'].last_price,
                        '信号盈亏': signal_data['sigfloatprofit']
                    }

                    print(f"Symbol: {SYMBOL} - Signal:", signal)
                    sendsignal(socket, strategyname, signal)

                    # 处理交易
                    handle_trading(api, SYMBOL, signal_data, data['position'],
                                   data['quote'], single_volume, bklimit, sklimit)

            except Exception as e:
                mylog.error(f"Error processing {SYMBOL}: {e}")
                continue


def runstrategy():
    from tqsdk import TqApi, TqKq

    interval = 60
    bklimit = 2
    sklimit = 2
    single_volume = 1

    api = TqApi(TqKq(), auth="bigwolf,ftp123", disable_print=True, debug=None)

    try:
        # 获取所有活跃合约
        symbols = api.query_cont_quotes(exchange_id="CZCE")
        # 过滤需要交易的合约
        trading_symbols = [s for s in symbols if s.endswith('505')]
        trading_symbols = ['CZCE.OI509', 'CZCE.OI505','SHFE.rb2505','SHFE.rb2510','DCE.eb2505','DCE.eb2509']
        print("Trading symbols:", trading_symbols)
        ma_cross_multi(api, trading_symbols, interval, single_volume, bklimit, sklimit)

    except Exception as e:
        print("Strategy error:", e)
    finally:
        api.close()


if __name__ == "__main__":
    runstrategy()