from pydispatch import dispatcher

from tqsdk import TqApi, TqAccount, TqKq

signalquote = 'Quote'
signalbar = 'Bar'
sender = 'tianhm'

def on_quote(data, signal, sender):
    print('quote data handler.')
    if signal == signalquote:
        print(data)


def on_bar(data, signal, sender):
    print('bar data handler.')
    if signal == signalbar:
        print(data)


def on_order(event=None):
    print('order data handler.')
    print(event)


def on_trade(trade):
    print('trade data handler.')
    print(trade)


dispatcher.connect(on_quote, sender=sender, signal=signalquote)
dispatcher.connect(on_bar, sender=sender, signal=signalbar)

api = TqApi(TqKq(), auth="wolfquant,ftp123", disable_print=True, debug=None)

symbol = 'CZCE.OI305'
quote = api.get_quote(symbol)
bar = api.get_kline_serial(symbol, duration_seconds=15, data_length=10)
while True:
    api.wait_update()
    if api.is_changing(quote):
        dispatcher.send(data=quote, signal=signalquote, sender='tianhm')

    if api.is_changing(bar.iloc[-1], 'datetime'):
        dispatcher.send(data=bar, signal=signalbar, sender='tianhm')

api.close()
