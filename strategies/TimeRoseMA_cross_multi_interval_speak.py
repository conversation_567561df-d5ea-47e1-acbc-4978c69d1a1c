import copy
import sys
import os
import pandas as pd
from myfunction import *
from MyTT import CROSS, MA

from tqsdk import TqApi, TqKq, TqAccount, TqSim, TqBacktest, TqAuth
from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from signalSend import socket, sendsignal
from utils.utils import tradingTime
from time import sleep
import time
from datetime import date

# from .paths import Paths

speak_text('程序开始运行')


def disp_day_info(daymean, last3mean, hlmax, homean, olmean):
    print('最大价差:', hlmax, '平均价差:', daymean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:',
          int(olmean))


def Disp0DayInfo(quote):
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    gapcover = False

    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    print(quote.datetime, quote.instrument_id, '缺口：', gap, '回补:', gapcover, '现价:', quote.last_price, 'BB:', BB,
          '今日价差：',
          dayvib,
          '上价差:', upvib, '下价差:', dnvib)


def MaCrossCaculate(bars, period, quote):
    C = bars.close
    SYMBOL = bars.symbol.iloc[0]
    interval = bars.duration.iloc[0]

    trmac = ma(C, period)
    ups = crossup(C, trmac)
    dns = crossdown(C, trmac)
    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)
    if bkdist > skdist:
        signalnow = 'SK'
        distnow = skdist
    else:
        signalnow = 'BK'
        distnow = bkdist
    updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    signalprice = C.iloc[-distnow]
    sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
    print('时间:', updatetime, SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice,
          '现价:',
          C.iloc[-1], '信号盈亏:', sigfloatprofit)
    uplist = ups.tolist()
    average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
    print('平均信号距离：', average_signal_distance)
    print('upsignal:  ', ups.iloc[-40:].tolist())
    print('downsignal:', dns.iloc[-40:].tolist())

    updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    signal = {'合约': SYMBOL, '周期': interval, '时间': updatetime, '当前信号': signalnow,
              '持续周期': distnow, '信号价格': signalprice, '现价': C.iloc[-1],
              '信号盈亏': sigfloatprofit}

    return signal


def get_Signals_Ma(bars, period=13):
    CLOSE = bars.close.values
    N = period

    ups = list(CROSS(CLOSE, MA(CLOSE, N)))
    dns = list(CROSS(MA(CLOSE, N), CLOSE))

    # dnsn = [-1 if x == 1 else x for x in dns]
    signals = []

    for i in range(len(ups)):
        if ups[i] == 0 and dns[i] == 0:
            signals.append(0)

        else:
            if ups[i] == 1:
                signals.append(1)
            if dns[i] == 1:
                signals.append(-1)

    # bsigs = ups.count(True)
    # ssigs = dns.count(True)
    # avgdistance = len(ups) / (bsigs + ssigs)
    return signals


def disp_account(acct):
    acct_info = dict(acct)
    print(acct_info)

class timeRoseMultiInterval:
    def __init__(self, acct, symbol):
        print('系统开始初始化。')
        self.acct = acct
        self.symbol = symbol
        self.api = TqApi(TqKq(), auth=self.acct, disable_print=True)
        # self.api = TqApi(TqSim(), backtest=TqBacktest(start_dt=date(2022, 10, 1), end_dt=date(2022, 12, 1)), auth=TqAuth("follower","ftp123"))
        self.account = self.api.get_account()
        self.postion = self.api.get_position(self.symbol)
        self.quote = self.api.get_quote(self.symbol)
        self.bars1 = self.api.get_kline_serial(self.symbol, duration_seconds=60, data_length=8964).dropna()
        self.bars3 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 3, data_length=8964).dropna()
        self.bars5 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 5, data_length=8964).dropna()
        self.bars15 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 15, data_length=8964).dropna()

        self.barstmp1 = self.api.get_kline_serial(self.symbol, duration_seconds=60, data_length=8)
        self.barstmp3 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 3, data_length=8)
        self.barstmp5 = self.api.get_kline_serial(self.symbol, duration_seconds=60 * 5, data_length=8)
        self.barstmp15 = self.api.get_kline_serial(symbol, duration_seconds=60 * 15, data_length=8)

        self.orderlist = []

        self.barupdateinfo = 'Bar Data Updated...'
        print('初始化完成。。。')



    def initialization(self, acct, symbol):
        self.api = TqApi(TqKq(), acct, disable_print=True)
        # self.api = TqApi(TqSim(), backtest=TqBacktest(start_dt=date(2022, 10, 1), end_dt=date(2022, 12, 1)), auth=TqAuth("follower","ftp123"))
        self.account = self.api.get_account()
        self.postion = self.api.get_position(symbol)
        self.quote = self.api.get_quote(symbol)

        self.barstmp1 = self.api.get_kline_serial(symbol, duration_seconds=60, data_length=8)
        self.barstmp3 = self.api.get_kline_serial(symbol, duration_seconds=60 * 3, data_length=8)
        self.barstmp5 = self.api.get_kline_serial(symbol, duration_seconds=60 * 5, data_length=8)
        self.barstmp15 = self.api.get_kline_serial(symbol, duration_seconds=60 * 15, data_length=8)

    def onsignal(self, signal):
        if signal == 1:
            orderid = self.api.insert_order(self.symbol, direction='BUY', offset='OPEN', volume=1,
                                            limit_price=self.quote.last_price)
            self.orderlist.append(orderid)
            mylog.info('发出做多信号，下多单。')

        if signal == -1:
            orderid = self.api.insert_order(self.symbol, direction='SELL', offset='OPEN', volume=1,
                                            limit_price=self.quote.last_price)
            self.orderlist.append(orderid)
            mylog.info('发出做空信号， 下空单')

    def onbar(self, bars):
        symbol = bars.iloc[-1].symbol
        duration = bars.iloc[-1].duration
        signals = get_Signals_Ma(bars)

        symbolname = symbol.split('.')[1]
        interval = int(duration)

        print(symbol, duration, signals[-40:])

        if signals[-1] == 1:
            self.onsignal(signals[-1])
            loginfo = [symbol, duration, 'signal', signals[-1], self.quote.last_price]
            mylog.info(loginfo)
            speak_text(symbolname + str(interval) + '发出做多信号。')

        if signals[-1] == -1:
            self.onsignal(signals[-1])
            loginfo=[symbol, duration, 'signal', signals[-1], self.quote.last_price]
            mylog.info(loginfo)
            speak_text(symbolname + str(interval) + '发出做空信号。')
    def update_bars(self, bars, bar):
        barsduration=bars.iloc[0].duration
        tmpbarduration=bar.duration
        if barsduration == tmpbarduration:
            tt = bar.datetime
            print(time_to_str(bar.datetime), bar.symbol, bar.duration)
            # print(type(newk.duration))
            bdt = bars.datetime.tolist()
            if tt not in bdt:
                newktmp = bar.to_frame()
                newktmp = newktmp.T
                bars = pd.concat([bars, newktmp], ignore_index=True)
                print(bar.duration, self.barupdateinfo)
                # speak_text(str(newktmp.duration)+'updated.')
            else:
                print('发现重复数据，跳过。。。')

        else:
            print('k线周期不一致，跳过， 请检查程序和数据。')

        return bars


    def run(self):
        while True:
            if tradingTime():
                try:
                    print('交易时间...', time.asctime())
                    print(self.acct)
                    # del self.api
                    # self.api = TqApi(TqKq(), auth=self.acct, disable_print=True)
                    # self.initialization(self, self.acct, self.symbol)

                    print('api重新连接成功...')
                    while tradingTime():
                        self.api.wait_update()

                        if self.api.is_changing(self.barstmp1.iloc[-1], "datetime"):
                            newk = self.barstmp1.iloc[-2]
                            self.bars1 = self.update_bars(self.bars1, newk)
                            self.onbar(self.bars1)

                        if self.api.is_changing(self.barstmp3.iloc[-1], "datetime"):
                            newk = self.barstmp3.iloc[-2]
                            self.bars3 = self.update_bars(self.bars3, newk)
                            self.onbar(self.bars3)

                        if self.api.is_changing(self.barstmp5.iloc[-1], "datetime"):
                            newk = self.barstmp5.iloc[-2]
                            self.bars5 = self.update_bars(self.bars5, newk)
                            self.onbar(self.bars5)

                        if self.api.is_changing(self.barstmp15.iloc[-1], "datetime"):
                            newk = self.barstmp15.iloc[-2]
                            self.bars15 = self.update_bars(self.bars15, newk)
                            self.onbar(self.bars15)

                    else:
                        print('非交易时间，关闭api.', time.asctime())
                        self.api.close()


                except:
                    print('api connect failed, please check...', time.asctime())
                    time.sleep(60)


            else:
                print('not in the trading time...', time.asctime())
                self.api.close()
                print(self.api)
                time.sleep(60)
            #
            # for bars in [self.barstmp1, self.barstmp3, self.barstmp5, self.barstmp15]:
            #     # print(time_to_str(bars.iloc[-1].datetime), bars.iloc[-1].duration)
            #     if self.api.is_changing(bars.iloc[-1], "datetime"):
            #         newk = bars.iloc[-2]
            #
            #         for bars in [self.bars1, self.bars3, self.bars5, self.bars15]:
            #             barsduration = bars.iloc[0].duration
            #             if barsduration == newk.duration:
            #                 self.update_bars(bars, newk)
            #                 self.onbar(bars)

            #
                    # tt = newk.datetime
                    # print(time_to_str(newk.datetime), newk.symbol, newk.duration)
                    # # print(type(newk.duration))
                    # bdt = self.bars1.datetime.tolist()
                    #
                    # if newk.duration == 60:
                    #     bdt = self.bars1.datetime.tolist()
                    # if newk.duration == 180:
                    #     bdt = self.bars3.datetime.tolist()
                    #
                    # if newk.duration == 300:
                    #     bdt = self.bars5.datetime.tolist()
                    #
                    # if newk.duration == 900:
                    #     bdt = self.bars15.datetime.tolist()
                    #
                    # if tt in bdt:
                    #     mylog.info('发现重复数据, 跳过...', time_to_str(tt))
                    #
                    # else:
                    #
                    #     if newk.duration == 60:
                    #         newktmp = newk.to_frame()
                    #         newktmp = newktmp.T
                    #         self.bars1 = pd.concat([self.bars1, newktmp], ignore_index=True)
                    #         print(newk.duration, self.barupdateinfo)
                    #         # speak_text(str(newktmp.duration)+'updated.')
                    #         self.onbar(self.bars1)
                    #
                    #     if newk.duration == 180:
                    #         newktmp = newk.to_frame()
                    #         newktmp = newktmp.T
                    #         self.bars3 = pd.concat([self.bars3, newktmp], ignore_index=True)
                    #         print(newktmp.duration, self.barupdateinfo)
                    #         self.onbar(self.bars3)
                    #
                    #     if newk.duration == 300:
                    #         newktmp = newk.to_frame()
                    #         newktmp = newktmp.T
                    #         self.bars5 = pd.concat([self.bars5, newktmp], ignore_index=True)
                    #         print(newktmp.duration, self.barupdateinfo)
                    #         self.onbar(self.bars5)
                    #
                    #     if newk.duration == 900:
                    #         newktmp = newk.to_frame()
                    #         newktmp = newktmp.T
                    #         self.bars15 = pd.concat([self.bars15, newktmp], ignore_index=True)
                    #         print(newktmp.duration, self.barupdateinfo)
                    #         self.onbar(self.bars15)


if __name__ == "__main__":
    # runstrategy()

    api = TqApi(TqKq(), auth="wolfquant,ftp123", disable_print=True, debug=None)
    symbol = api.query_cont_quotes(product_id='OI')[0]
    api.close()

    strategy = timeRoseMultiInterval('walkquant,ftp123', symbol)
    strategy.run()
