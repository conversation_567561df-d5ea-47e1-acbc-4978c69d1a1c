'''
RB:=3;
HC:=2;
bs:=0;
DV:=1;
IK:=6;
X_1:=IF(HC=1 OR HC=3,HIGH,CLOSE);
X_2:=IF(HC=1 OR HC=3,LOW,CLOSE);
X_3:=IK=1 OR IK=5 OR IK=6;
X_4:=IK=2 OR IK=5 OR IK=6;
X_5:=IK=3 OR IK=6;
X_6:=IK=4 OR IK=6;
X_7:=1;
X_8:=IF(X_7=1,REF(X_7,1)-1,IF(X_7=(-1),REF(X_7,1)+1,X_7));
X_9:=IF(X_8<0,X_8+1,X_8-1);
X_10:=TROUGHBARS(3,RB,1);
X_11:=PEAKBARS(3,RB,1);
X_12:=IF(X_10=0,(-1),IF(X_11=0,1,0));
X_13:=IF(X_10=0,X_11,IF(X_11=0,-X_10,IF(X_10>X_11,X_11,-X_10)));
X_14:=1;
X_15:=BArslAST(X_14<0);
X_16:=BARSLAST(X_14>0);
X_17:=IF(X_15=0,(-1),IF(X_16=0,1,0));
X_18:=IF(X_15=0,X_16,IF(X_16=0,-X_15,IF(X_15>X_16,X_16,-X_15)));
X_19:=IF(HC=2,X_12,IF(HC=3,X_17,X_7));
X_20:=IF(HC=2,X_13,IF(HC=3,X_18,X_9));
X_21:=IF(X_20<0,-X_20,X_20);
周期:X_21,NODRAW,COLORWHITE;
X_22:=IF(X_20>0,100*(X_2/REF(X_1,X_21)-1),100*(X_1/REF(X_2,X_21)-1));
涨跌幅:X_22,NODRAW,COLORWHITE;
X_23:=INBLOCK(1)=1 AND NOT(NAMEINCLUDE(2));
X_24:=IF(BS=0,X_21,MIN(X_21,BS));
X_25:=IF(X_20>0,100*(X_2/REF(X_1,X_24)-1),100*(X_1/REF(X_2,X_24)-1));
X_26:=IF(X_23,AMOUNT/100000000,vol/100);
X_27:=SUM(X_26,X_24);
X_28:=X_27/X_25/DV;
速度指数:X_28,NODRAW,COLORWHITE;
DRAWLINE(X_3 AND X_19=(-1),X_2,X_3 AND X_19=1,X_1,0),DOTLINE,COLORYELLOW;
DRAWLINE(X_3 AND X_19=1,X_1,X_3 AND X_19=(-1),X_2,0),DOTLINE,COLORYELLOW;
DRAWNUMBER(X_4 AND X_19=1,HIGH,ROUND(X_28)),COLORWHITE,LINETHICK9;
DRAWNUMBER(X_4 AND X_19=(-1),LOW,ROUND(X_28)),COLORWHITE;
X_29:=BARSLAST(X_19=1);
X_30:=BARSLAST(X_19=(-1));
DRAWNUMBER(X_4 AND ISLASTBAR AND X_29>X_30,HIGH,ROUND(X_28)),COLORYELLOW,LINETHICK9;
DRAWNUMBER(X_4 AND ISLASTBAR AND X_29<X_30,LOW,ROUND(X_28)),COLORYELLOW;
X_31:=BARSLAST(X_19=1);
X_32:=BARSLAST(X_19=(-1));
X_33:=IF(X_31=0 OR X_32=0,DRAWNULL,(REF(X_1,X_31)+REF(X_2,X_32))/2);
R50%:IF(X_5,X_33,DRAWNULL),POINTDOT,COLORWHITE;
X_34:=IF(HIGH<REF(LOW,1),REF(LOW,1),HIGH);
X_35:=IF(LOW>REF(HIGH,1),REF(HIGH,1),LOW);
X_36:=HHV(X_34,3);
X_37:=LLV(X_35,3);
X_38:=BARSLAST(X_37<REF(X_37,1));
X_39:=BARSLAST(X_36>REF(X_36,1));
X_40:=IF(HHV(X_34,X_39+1)=X_34,1,0);
X_41:=IF(LLV(X_35,X_38+1)=X_35,1,0);
X_42:=BARSLAST(X_40);
X_43:=REF(LLV(X_35,3),X_42);
X_44:=BARSLAST(X_41);
X_45:=REF(HHV(X_34,3),X_44);
X_46:=VALUEWHEN(X_45>0,X_45);
X_47:=VALUEWHEN(X_43>0,X_43);
X_48:=IF(CLOSE>X_46,(-1),IF(CLOSE<X_47,1,0));
X_49:=VALUEWHEN(X_48!=0,X_48);
X_50:=BARSLAST(crOSS(0,X_49));
X_51:=BARSLAST(CROSS(X_49,0));
X_52:=IF(X_49=1,IF(LLV(X_46,X_51+1)=X_46,X_46,LLV(X_46,X_51+1)),X_46);
X_53:=IF(X_49=(-1),IF(HHV(X_47,X_50+1)=X_47,X_47,HHV(X_47,X_50+1)),X_47);
X_54:=IF(CLOSE>X_52,(-1),IF(CLOSE<X_53,1,0));
X_55:=VALUEWHEN(X_54!=0,X_54);
X_56:=BARSLAST(CROSS(0,X_54));
X_57:=BARSLAST(CROSS(X_54,0));
X_58:=IF(X_55=1,IF(LLV(X_52,X_57+1)=X_52,X_52,LLV(X_52,X_57+1)),IF(HHV(X_53,X_56+1)=X_53,X_53,HHV(X_53,X_56+1)));
多头止损:IF(X_6 AND X_55<0,X_58,DRAWNULL),DOTLINE,COLORMAGENTA;
空头止损:IF(X_6 AND X_55>0,X_58,DRAWNULL),DOTLINE,COLORCYAN;

//@version=4
study("Weis Wave Volume", shorttitle="WWV", overlay=false, resolution="")
method = input(defval="ATR", options=["ATR", "Traditional", "Part of Price"], title="Renko Assignment Method")
methodvalue = input(defval=14.0, type=input.float, minval=0, title="Value")
pricesource = input(defval="Close", options=["Close", "Open / Close", "High / Low"], title="Price Source")
useClose = pricesource == "Close"
useOpenClose = pricesource == "Open / Close" or useClose
useTrueRange = input(defval="Auto", options=["Always", "Auto", "Never"], title="Use True Range instead of Volume")
isOscillating = input(defval=false, type=input.bool, title="Oscillating")
normalize = input(defval=false, type=input.bool, title="Normalize")
vol = useTrueRange == "Always" or useTrueRange == "Auto" and na(volume) ? tr : volume
op = useClose ? close : open
hi = useOpenClose ? close >= op ? close : op : high
lo = useOpenClose ? close <= op ? close : op : low

if method == "ATR"
    methodvalue := atr(round(methodvalue))
if method == "Part of Price"
    methodvalue := close / methodvalue

currclose = float(na)
prevclose = nz(currclose[1])
prevhigh = prevclose + methodvalue
prevlow = prevclose - methodvalue
currclose := hi > prevhigh ? hi : lo < prevlow ? lo : prevclose

direction = int(na)
direction := currclose > prevclose ? 1 : currclose < prevclose ? -1 : nz(direction[1])
directionHasChanged = change(direction) != 0
directionIsUp = direction > 0
directionIsDown = direction < 0

barcount = 1
barcount := not directionHasChanged and normalize ? barcount[1] + barcount : barcount
vol := not directionHasChanged ? vol[1] + vol : vol
res = barcount > 1 ? vol / barcount : vol

plot(isOscillating and directionIsDown ? -res : res, style=plot.style_columns, color=directionIsUp ? color.green : color.red, transp=75, linewidth=3, title="Wave Volume")
Personally, I find it quite tricky to update 'currclose' value in the Dataframe, since 'prevclose' value doesn't get dynamically updated.

Thanks!

twopirllc and MateKristof reacted with thumbs up emoji
MateKristof reacted with heart emoji
@twopirllc twopirllc added enhancement New feature or requestgood first issue Good for newcomerslabels on Sep 17, 2021
@twopirllc twopirllc added this to To do in Indicator Requests via automation on Feb 18, 2022
@kaanguven
kaanguven commented on Nov 20, 2022 •
Hello,

It would be nice if this indicator gets integrated in the library (made by modhelius, TV):

//@version=4
study("Weis Wave Volume", shorttitle="WWV", overlay=false, resolution="")
method = input(defval="ATR", options=["ATR", "Traditional", "Part of Price"], title="Renko Assignment Method")
methodvalue = input(defval=14.0, type=input.float, minval=0, title="Value")
pricesource = input(defval="Close", options=["Close", "Open / Close", "High / Low"], title="Price Source")
useClose = pricesource == "Close"
useOpenClose = pricesource == "Open / Close" or useClose
useTrueRange = input(defval="Auto", options=["Always", "Auto", "Never"], title="Use True Range instead of Volume")
isOscillating = input(defval=false, type=input.bool, title="Oscillating")
normalize = input(defval=false, type=input.bool, title="Normalize")
vol = useTrueRange == "Always" or useTrueRange == "Auto" and na(volume) ? tr : volume
op = useClose ? close : open
hi = useOpenClose ? close >= op ? close : op : high
lo = useOpenClose ? close <= op ? close : op : low

if method == "ATR"
    methodvalue := atr(round(methodvalue))
if method == "Part of Price"
    methodvalue := close / methodvalue

currclose = float(na)
prevclose = nz(currclose[1])
prevhigh = prevclose + methodvalue
prevlow = prevclose - methodvalue
currclose := hi > prevhigh ? hi : lo < prevlow ? lo : prevclose

direction = int(na)
direction := currclose > prevclose ? 1 : currclose < prevclose ? -1 : nz(direction[1])
directionHasChanged = change(direction) != 0
directionIsUp = direction > 0
directionIsDown = direction < 0

barcount = 1
barcount := not directionHasChanged and normalize ? barcount[1] + barcount : barcount
vol := not directionHasChanged ? vol[1] + vol : vol
res = barcount > 1 ? vol / barcount : vol

plot(isOscillating and directionIsDown ? -res : res, style=plot.style_columns, color=directionIsUp ? color.green : color.red, transp=75, linewidth=3, title="Wave Volume")


'''

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 读取股票数据
df = pd.read_csv('CZCE.OI301.csv')

# 计算股票价格的变化率和交易量的变化率
df['price_change'] = df['close'].pct_change()
df['volume_change'] = df['volume'].pct_change()


# 定义积累、分配、跳空和跳空陷阱四种交易类型
def get_trading_type(price_change, volume_change):
    if price_change > 0 and volume_change > 0:
        return 'accumulation'
    elif price_change < 0 and volume_change < 0:
        return 'distribution'
    elif price_change > 0.05 and volume_change < 0:
        return 'spring'
    elif price_change < -0.05 and volume_change > 0:
        return 'upthrust'
    else:
        return 'no_trading_type'


# 对每日的股票价格变化率和交易量变化率进行分类
df['trading_type'] = df.apply(lambda x: get_trading_type(x['price_change'], x['volume_change']), axis=1)


# 统计不同交易类型的起始点和终止点
def get_trading_points(trading_type):
    start_points = []
    end_points = []
    trading_flag = False
    for i in range(len(trading_type)):
        if trading_type[i] != 'no_trading_type' and not trading_flag:
            start_points.append(i)
            trading_flag = True
        elif trading_type[i] == 'no_trading_type' and trading_flag:
            end_points.append(i - 1)
            trading_flag = False
    if trading_flag:
        end_points.append(len(trading_type) - 1)
    return start_points, end_points


# 计算交易类型对应的平均价格和交易量
def get_trading_features(df, start_points, end_points):
    trading_features = []
    for start, end in zip(start_points, end_points):
        trading_df = df[start:end + 1]
        trading_type = trading_df['trading_type'].iloc[0]
        price_mean = trading_df['close'].mean()
        volume_mean = trading_df['volume'].mean()
        trading_features.append((trading_type, price_mean, volume_mean))
    return trading_features


# 统计不同交易类型的起始点和终止点
start_points, end_points = get_trading_points(df['trading_type'])

# 计算交易类型对应的平均价格和交易量
trading_features = get_trading_features(df, start_points, end_points)

# 绘制股票价格图和交易类型标记
fig, ax = plt.subplots(figsize=(15, 8))
ax.plot(df['datetime'], df['close'])
for start,end in zip(start_points, end_points):
    trading_type = trading_features[start_points.index(start)][0]
    ax.axvspan(df['datetime'].iloc[start], df['datetime'].iloc[end], alpha=0.5,
           color='red' if trading_type == 'distribution' else 'green')
    plt.show()


# 简单的交易策略

def simple_strategy(trading_type, price_mean, volume_mean):


    if trading_type == 'accumulation' and price_mean > 100 and volume_mean > 1000000:
        return 'buy'
    elif trading_type == 'distribution' and price_mean < 90 and volume_mean > 1000000:
        return 'sell'
    else:
        return 'no_action'

    # 对每个交易区间执行交易策略

    for trading_type, price_mean, volume_mean in trading_features:
        action = simple_strategy(trading_type, price_mean, volume_mean)

    if action == 'buy':
        print('Buy at average price:', price_mean)
    elif action == 'sell':
        print('Sell at average price:', price_mean)
    else:
        print('No action for this trading type:', trading_type)
