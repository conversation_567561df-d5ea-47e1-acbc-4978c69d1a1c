import pandas as pd

from MyTT import *
# from tqsdk import TqA<PERSON>, TqAccount, TqKq
#
# product = 'OI'
# symbol = product
# interval = 60
# bklimit = 100
# sklimit = 100
# single_volume = 1
#
# # 交易账号设置
# # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
# # symbol = api.query_cont_quotes(product_id=product)[0]
# # api = TqApi(TqKq(), auth="wolfquant,ftp123")
# api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")
# symbol = api.query_cont_quotes(product_id=product)[0]
#
# klines = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964)
# api.close()
# pd.to_pickle(klines, 'klines.pkl')
klines = pd.read_pickle('klines.pkl')
high = klines.high
low = klines.low

HIGH = high
LOW=low

RB = 3
HC = 2
bs = 0
DV = 1
IK = 6
X_1 = high
X_2 = low
X_3 = 1
X_4 = 1
X_5 = 1
X_6 = 1
X_7 = 1
X_8 = IF(X_7 == 1, REF(X_7, 1) - 1, IF(X_7 == (-1), REF(X_7, 1) + 1, X_7))
X_9 = IF(X_8 < 0, X_8 + 1, X_8 - 1)


X_34 = IF(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
X_35 = IF(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)
X_36 = HHV(X_34, 3)
X_37 = LLV(X_35, 3)
X_38 = BARSLAST(X_37 < REF(X_37, 1))
X_39 = BARSLAST(X_36 > REF(X_36, 1))
X391 = X_39+1
X_39_1 = HHV(X_34, X391)

# X_40 = IF(HHV(X_34, X_39 + 1) == X_34, 1, 0)

X_40 = IF(X_39_1 == X_34, 1, 0)
X_41 = IF(LLV(X_35, X_38 + 1) == X_35, 1, 0)

X_42 = BARSLAST(X_40)
X_43 = REF(LLV(X_35, 3), X_42)
X_44 = BARSLAST(X_41)

X_45 = REF(HHV(X_34, 3), X_44)

X_46 = VALUEWHEN(X_45 > 0, X_45)
X_47 = VALUEWHEN(X_43 > 0, X_43)
X_48 = IF(CLOSE > X_46, (-1), IF(CLOSE < X_47, 1, 0))
X_49 = VALUEWHEN(X_48 != 0, X_48)
X_50 = BARSLAST(crOSS(0, X_49))
X_51 = BARSLAST(CROSS(X_49, 0))

X_52 = IF(X_49 == 1, IF(LLV(X_46, X_51 + 1) == X_46, X_46, LLV(X_46, X_51 + 1)), X_46)
X_53 = IF(X_49 == (-1), IF(HHV(X_47, X_50 + 1) == X_47, X_47, HHV(X_47, X_50 + 1)), X_47)

X_54 = IF(CLOSE > X_52, (-1), IF(CLOSE < X_53, 1, 0))
X_55 = VALUEWHEN(X_54 != 0, X_54)

X_56 = BARSLAST(CROSS(0, X_54))
X_57 = BARSLAST(CROSS(X_54, 0))
X_58 = IF(X_55 == 1, IF(LLV(X_52, X_57 + 1) == X_52, X_52, LLV(X_52, X_57 + 1)), IF(HHV(X_53, X_56 + 1) == X_53, X_53, HHV(X_53, X_56 + 1)))

多头止损 = IF(X_55 < 0, X_58, )
空头止损 = IF(X_55 > 0, X_58, )
