from tqsdk import TqApi, TqAccount, TqKq
api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")
def disp_bar_info(bars):

    bars = daybars.dropna()
    hl = bars.high - bars.low
    ho = bars.high - bars.open
    ol = bars.open - bars.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    print('最大价差:', hlmax, '平均价差:', daymean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:', int(olmean))

# symbol = 'CZCE.PK304'
# symbol = '<EMAIL>'
# symbol = '<EMAIL>'
# symbol = 'SSE.000300'
# symbol = 'CZCE.OI405'
symbol = '<EMAIL>'

daybars = api.get_kline_serial(symbol, duration_seconds=60 * 60 * 24, data_length=8964)
daybar = daybars.dropna()
disp_bar_info(daybar)
api.close()
