import time

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

from tqsdk import TqApi, TqKq

product = 'rb'
interval = 60 * 15
bklimit = 50
sklimit = 10
single_volume = 100

# 交易账号设置
api = TqApi(TqKq(), auth="bigwolf,ftp123", disable_print=True)
symbols = api.query_cont_quotes()


#
def get_trading_type(price_change, volume_change): # 定义积累、分配、跳空和跳空陷阱四种交易类型
    if price_change > 0 and volume_change > 0:
        return 'buy'
    elif price_change < 0 and volume_change < 0:
        return 'sell'
    elif price_change > 0.05 and volume_change < 0:
        return 'spring'
    elif price_change < -0.05 and volume_change > 0:
        return 'upthrust'
    else:
        return 'no_trading_type'


def weis_wave_trading_profitable_calculae(df):

    # 计算股票价格的变化率和交易量的变化率
    df['price_change'] = df['close'].pct_change()
    df['volume_change'] = df['volume'].pct_change()

    # 对每日的股票价格变化率和交易量变化率进行分类
    df['trading_type'] = df.apply(lambda x: get_trading_type(x['price_change'], x['volume_change']), axis=1)

    price_change = df.price_change.tolist()

    signals = df.trading_type.tolist()
    close_price = df.close.tolist()

    signalset = set(signals)
    # print(signals[-40:])
    buy_price_sum = 0
    sell_price_sum = 0

    for s in range(len(signals)):
        if signals[s] == 'buy':
            buy_price_sum += close_price[s]
        if signals[s] == 'sell':
            sell_price_sum += close_price[s]

    print(symbol, sell_price_sum - buy_price_sum)

for symbol in symbols:
    try:
        df = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964)
        weis_wave_trading_profitable_calculae(df)
    except:
        print(symbol, '获取数据失败...')


api.close()

