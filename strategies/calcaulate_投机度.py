
from tqsdk import TqApi, TqKq

def get_product_ids(api):
    productidlist = []

    symbols = api.query_cont_quotes()
    for s in symbols:
        exchangeid = s.split('.')[0]
        symbolid = ''.join(filter(str.isalpha, s.split('.')[1]))
        indexsymbolid = ''.join(['KQ.m@', exchangeid, '.', symbolid])
        productidlist.append(indexsymbolid)

    # with open('productids.pkl', 'wb') as f:
    #     pickle.dump(productidlist, f)

    return productidlist

api = TqApi(TqKq(), auth="follower,ftp123", disable_print=True, debug=False)
symbols = api.query_cont_quotes()

for symbol in symbols:
    quote=api.get_quote(symbol)
    try:
        toujidu = quote.volume/quote.open_interest
        if toujidu >1:
            print('投机度:', symbol, toujidu)

    except:
        print(symbol, 'dead symbol')

api.close()

# while True:
#     api.wait_update()
#     if api.is_changing(quote):
#         print('持仓量:', quote.open_interest)
#         print('成交量:', quote.volume)
#         print('投机度:', quote.volume/quote.open_interest)

