import numpy as np
import pandas as pd


def VALUEWHEN(COND, X):
    """
    When the condition COND is met, returns the current value of X.
    If the condition is not met, returns the value of X the last time CO<PERSON> was met.

    Parameters:
    -----------
    COND : array-like
        Boolean array representing the condition
    X : array-like
        Values to be returned when condition is met

    Returns:
    --------
    array-like
        Values of X when condition was last met
    """
    # Convert inputs to numpy arrays for consistent handling
    cond_array = np.array(COND, dtype=bool)
    x_array = np.array(X)

    # Initialize result array with NaN
    result = np.full_like(x_array, np.nan, dtype=float)

    # Initialize last valid value as NaN
    last_valid_value = np.nan

    # Iterate through the arrays
    for i in range(len(cond_array)):
        if cond_array[i]:
            # If condition is met, use current X value
            last_valid_value = x_array[i]

        # Store the value (either current if condition met, or last valid)
        result[i] = last_valid_value

    return result


def IFELSE(COND, A, B):
    """
    Returns A if COND is true, otherwise returns B.

    Parameters:
    -----------
    COND : array-like or scalar
        Boolean condition
    A : array-like or scalar
        Value to return if condition is true
    B : array-like or scalar
        Value to return if condition is false

    Returns:
    --------
    array-like or scalar
        Values of either A or B based on condition
    """
    # Convert inputs to numpy arrays for consistent handling
    if hasattr(COND, '__len__') and not isinstance(COND, (str, bytes)):
        cond_array = np.array(COND, dtype=bool)

        # Handle scalar A and B with array COND
        if not hasattr(A, '__len__') or isinstance(A, (str, bytes)):
            a_array = np.full_like(cond_array, A, dtype=float)
        else:
            a_array = np.array(A)

        if not hasattr(B, '__len__') or isinstance(B, (str, bytes)):
            b_array = np.full_like(cond_array, B, dtype=float)
        else:
            b_array = np.array(B)

        # Create result array
        result = np.where(cond_array, a_array, b_array)
    else:
        # Handle scalar COND
        result = A if COND else B

    return result


# Helper functions for technical indicators
def ISUP(df):
    """Returns True if candle is bullish (Close > Open)"""
    return df['CLOSE'] > df['OPEN']


def CROSS(series1, series2):
    """Returns True when series1 crosses above series2"""
    # Previous state: series1 was below series2
    prev_below = (series1.shift(1) < series2.shift(1))
    # Current state: series1 is above series2
    curr_above = (series1 > series2)
    # Cross occurs when previous was below and current is above
    return prev_below & curr_above


# Function to apply VALUEWHEN to a DataFrame
def apply_VALUEWHEN(df, condition_func, value_func):
    """
    Apply VALUEWHEN function to a pandas DataFrame.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing price data
    condition_func : function
        Function that takes df and returns a boolean Series (COND)
    value_func : function
        Function that takes df and returns a Series of values (X)

    Returns:
    --------
    pandas.Series
        Result of VALUEWHEN function
    """
    condition = condition_func(df)
    values = value_func(df)
    return pd.Series(VALUEWHEN(condition, values), index=df.index)


# Function to handle self-referential calculations
def calculate_with_self_reference(df, condition_func, true_value_func, false_value_func=None):
    """
    Calculate a value that can reference its own previous values.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing price data
    condition_func : function
        Function that takes df and current result series, returns a boolean Series (COND)
    true_value_func : function
        Function that takes df and current result series, returns a Series (A)
    false_value_func : function
        Function that takes df and current result series, returns a Series (B)

    Returns:
    --------
    pandas.Series
        Result of the calculation
    """
    # Initialize result series with NaN
    result = pd.Series(np.nan, index=df.index)

    # Iterate through the indices to allow for self-reference
    for i in range(len(df)):
        # Get current slice of the DataFrame up to index i
        current_df_slice = df.iloc[:i + 1]
        current_result = result.copy()

        # Evaluate condition for current position
        condition = condition_func(current_df_slice, current_result)
        if isinstance(condition, pd.Series):
            condition = condition.iloc[-1]

        # Evaluate true and false values for current position
        true_value = true_value_func(current_df_slice, current_result)
        if isinstance(true_value, pd.Series):
            true_value = true_value.iloc[-1]

        if false_value_func:
            false_value = false_value_func(current_df_slice, current_result)
            if isinstance(false_value, pd.Series):
                false_value = false_value.iloc[-1]
        else:
            # Default false value if not provided
            false_value = np.nan

        # Apply IFELSE logic
        result.iloc[i] = true_value if condition else false_value

    return result


if __name__ == "__main__":
    # Create a sample DataFrame for testing
    data = {
        'DATE': pd.date_range(start='2023-01-01', periods=20),
        'OPEN': [100, 102, 104, 103, 105, 107, 108, 106, 104, 105,
                 103, 101, 102, 104, 106, 105, 103, 104, 106, 108],
        'HIGH': [105, 106, 108, 106, 108, 110, 112, 109, 106, 108,
                 106, 104, 105, 108, 110, 108, 105, 107, 109, 112],
        'LOW': [98, 100, 102, 100, 103, 105, 106, 103, 102, 103,
                100, 98, 100, 102, 104, 102, 100, 102, 104, 106],
        'CLOSE': [102, 104, 106, 105, 107, 109, 110, 108, 105, 107,
                  104, 102, 103, 106, 108, 106, 104, 105, 107, 110]
    }

    # Convert DATE column to just the date part for day-based examples
    df = pd.DataFrame(data)
    df['DATE'] = df['DATE'].dt.date

    # Example 1 for VALUEWHEN: VALUEWHEN(HIGH>REF(HHV(HIGH,5),1),HIGH)
    print("\nVALUEWHEN Example 1:")
    # Calculate HHV(HIGH,5)
    hhv_high_5 = df['HIGH'].rolling(window=5).max()
    # Calculate REF(HHV(HIGH,5),1) - shift to get the previous value
    ref_hhv_high_5_1 = hhv_high_5.shift(1)
    # COND: HIGH > REF(HHV(HIGH,5),1)
    condition = df['HIGH'] > ref_hhv_high_5_1
    # Apply VALUEWHEN
    result1 = VALUEWHEN(condition, df['HIGH'])
    print(pd.DataFrame({
        'HIGH': df['HIGH'],
        'HHV(HIGH,5)': hhv_high_5,
        'REF(HHV(HIGH,5),1)': ref_hhv_high_5_1,
        'COND': condition,
        'RESULT': result1
    }))

    # Example 2 for VALUEWHEN: VALUEWHEN(DATE<>REF(DATE,1),OPEN)
    print("\nVALUEWHEN Example 2:")
    # COND: DATE <> REF(DATE,1)
    condition = df['DATE'] != df['DATE'].shift(1)
    # Apply VALUEWHEN
    result2 = VALUEWHEN(condition, df['OPEN'])
    print(pd.DataFrame({
        'DATE': df['DATE'],
        'OPEN': df['OPEN'],
        'COND': condition,
        'RESULT': result2
    }))

    # Example 3 for VALUEWHEN: VALUEWHEN(DATE<>REF(DATE,1),LOW>REF(HIGH,1))
    print("\nVALUEWHEN Example 3:")
    # COND: DATE <> REF(DATE,1)
    condition = df['DATE'] != df['DATE'].shift(1)
    # X: LOW > REF(HIGH,1)
    values = df['LOW'] > df['HIGH'].shift(1)
    # Apply VALUEWHEN
    result3 = VALUEWHEN(condition, values)
    print(pd.DataFrame({
        'DATE': df['DATE'],
        'LOW': df['LOW'],
        'REF(HIGH,1)': df['HIGH'].shift(1),
        'LOW>REF(HIGH,1)': values,
        'COND': condition,
        'RESULT': result3
    }))

    # Example 1 for IFELSE: IFELSE(ISUP,HIGH,LOW)
    print("\nIFELSE Example 1:")
    # COND: ISUP (CLOSE > OPEN)
    condition = df['CLOSE'] > df['OPEN']
    # Apply IFELSE
    result4 = IFELSE(condition, df['HIGH'], df['LOW'])
    print(pd.DataFrame({
        'OPEN': df['OPEN'],
        'CLOSE': df['CLOSE'],
        'HIGH': df['HIGH'],
        'LOW': df['LOW'],
        'ISUP': condition,
        'RESULT': result4
    }))

    # Example 2 for IFELSE: A:=IFELSE(MA5>MA10,CROSS(DIFF,DEA),IFELSE(CROSS(D,K),2,0))
    print("\nIFELSE Example 2:")
    # Calculate the required indicators
    # Moving averages
    ma5 = df['CLOSE'].rolling(window=5).mean()
    ma10 = df['CLOSE'].rolling(window=10).mean()

    # MACD components
    ema12 = df['CLOSE'].ewm(span=12).mean()
    ema26 = df['CLOSE'].ewm(span=26).mean()
    diff = ema12 - ema26
    dea = diff.ewm(span=9).mean()

    # Stochastic oscillator components
    low_9 = df['LOW'].rolling(window=9).min()
    high_9 = df['HIGH'].rolling(window=9).max()
    k = 100 * ((df['CLOSE'] - low_9) / (high_9 - low_9))
    d = k.rolling(window=3).mean()

    # Conditions
    cond1 = ma5 > ma10
    cond2 = CROSS(diff, dea)
    cond3 = CROSS(d, k)  # D crosses above K (K crosses below D)

    # Apply nested IFELSE
    inner_ifelse = IFELSE(cond3, 2, 0)
    result5 = IFELSE(cond1, cond2, inner_ifelse)

    print(pd.DataFrame({
        'CLOSE': df['CLOSE'],
        'MA5': ma5,
        'MA10': ma10,
        'MA5>MA10': cond1,
        'CROSS(DIFF,DEA)': cond2,
        'CROSS(D,K)': cond3,
        'RESULT': result5
    }))

    # Example for self-referential calculation: Y: IFELSE(COND,X,REF(Y,1))
    print("\nSelf-referential IFELSE Example:")
    # Define a simple condition and value
    cond = df['CLOSE'] > df['OPEN']
    x = df['HIGH']


    # Define the functions for self-reference calculation
    def condition_func(curr_df, curr_result):
        return curr_df['CLOSE'] > curr_df['OPEN']


    def true_value_func(curr_df, curr_result):
        return curr_df['HIGH']


    def false_value_func(curr_df, curr_result):
        if curr_df.shape[0] > 1:
            return curr_result.iloc[-2]  # Previous Y value
        else:
            return np.nan  # No previous value for the first element


    # Apply self-referential calculation
    result6 = calculate_with_self_reference(df, condition_func, true_value_func, false_value_func)

    print(pd.DataFrame({
        'OPEN': df['OPEN'],
        'CLOSE': df['CLOSE'],
        'HIGH': df['HIGH'],
        'CLOSE>OPEN': cond,
        'RESULT': result6
    }))