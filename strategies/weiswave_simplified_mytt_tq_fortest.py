import copy
from addict import Dict
from .MyTT import *
from .MyTT_plus import HHV, LLV, REF_plus

from tqsdk import Tq<PERSON><PERSON>, TqAccount, TqKq
from tqsdk.tafunc import time_to_str
from tradefuncs import *
from loguru import logger as mylog

mylog.add('weiswave' + '.log', encoding='utf-8')


def weiswave_calculate(bars):
    symbol = bars.symbol.iloc[-1]
    duration = bars.duration.iloc[-1]

    HIGH = bars.high
    LOW = bars.low
    CLOSE = bars.close

    X_34 = IF(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
    X_35 = IF(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)
    X_36 = HHV(X_34, 3)
    X_37 = LLV(X_35, 3)
    X_38 = BARSLAST(X_37 < REF(X_37, 1))
    X_39 = BARSLAST(X_36 > REF(X_36, 1))

    X_38_1 = X_38 + 1
    X_39_1 = X_39 + 1

    X_40 = IF(HHV(X_34, X_39 + 1) == X_34, 1, 0)
    X_41 = IF(LLV(X_35, X_38 + 1) == X_35, 1, 0)
    X_42 = BARSLAST(X_40)

    X_43 = REF_plus(LLV(X_35, 3), X_42)
    X_44 = BARSLAST(X_41)

    X_45 = REF_plus(HHV(X_34, 3), X_44)

    X_46 = VALUEWHEN(X_45 > 0, X_45)
    X_47 = VALUEWHEN(X_43 > 0, X_43)
    X_48 = IF(CLOSE > X_46, (-1), IF(CLOSE < X_47, 1, 0))
    X_49 = VALUEWHEN(X_48 != 0, X_48)
    X_50 = BARSLAST(CROSS(0, X_49))
    X_51 = BARSLAST(CROSS(X_49, 0))

    X_52 = IF(X_49 == 1, IF(LLV(X_46, X_51 + 1) == X_46, X_46, LLV(X_46, X_51 + 1)), X_46)
    X_53 = IF(X_49 == (-1), IF(HHV(X_47, X_50 + 1) == X_47, X_47, HHV(X_47, X_50 + 1)), X_47)

    X_54 = IF(CLOSE > X_52, (-1), IF(CLOSE < X_53, 1, 0))
    X_55 = VALUEWHEN(X_54 != 0, X_54)

    X_56 = BARSLAST(CROSS(0, X_54))
    X_57 = BARSLAST(CROSS(X_54, 0))
    X_58 = IF(X_55 == 1, IF(LLV(X_52, X_57 + 1) == X_52, X_52, LLV(X_52, X_57 + 1)), IF(HHV(X_53, X_56 + 1) == X_53, X_53, HHV(X_53, X_56 + 1)))

    多头止损 = IF(X_55 < 0, X_58, [0]).tolist()
    空头止损 = IF(X_55 > 0, X_58, [0]).tolist()

    print(多头止损[-30:])
    print(空头止损[-30:])

    if 多头止损[-1] > 0:
        print(symbol, '周期:', duration, '秒.', '当前信号多：', '止损:', 多头止损[-1])
    elif 空头止损[-1] > 0:
        print(symbol, '周期:', duration, '秒.', '当前信号空：', '止损:', 空头止损[-1])

    return 多头止损, 空头止损


def gen_WeisWave_signals(ups, dns, bars):
    signals_buy = []
    signals_sell = []

    for i in range(len(ups)):
        if i == 0:
            tmpsignal = 0

        else:
            # if i < len(ups):
            if ups[i - 1] == 0 and ups[i] > 0:
                tmpsignal = ['buy', bars.close.iloc[i], ups[i]]

            else:
                tmpsignal = 0
        signals_buy.append(tmpsignal)

    for i in range(len(dns)):
        if i == 0:
            tmpsignal = 0

        else:
            # if i < len(ups):
            if dns[i - 1] == 0 and dns[i] > 0:
                tmpsignal = ['sell', bars.close.iloc[i], dns[i]]
            else:
                tmpsignal = 0
        signals_sell.append(tmpsignal)

    return signals_buy, signals_sell


def be_apart_from(series):
    assert isinstance(series, list)
    assert series.__len__() > 0

    i = series.__len__() - 1

    while i >= 0:
        if series[i] == 1:
            return series.__len__() - i
        if series[i] is True:
            return series.__len__() - i
        if type(series[i]) is list:
            return series.__len__() - i

        i -= 1

    return None


if __name__ == "__main__":

    product = 'OI'
    symbol = product
    interval = 15
    bklimit = 100
    sklimit = 100
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    # api = TqApi(TqKq(), auth="wolfquant,ftp123")
    api = TqApi(TqKq(), auth="follower,ftp123")
    symbol = api.query_cont_quotes(product_id=product)[0]

    barsinit = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964)
    bartmp = api.get_kline_serial(symbol, duration_seconds=interval, data_length=10)
    # api.close()
    bars = copy.deepcopy(barsinit)
    del barsinit

    bars.to_pickle(symbol + str(interval) + '.pkl')
    # klines = pd.read_pickle('CZCE.OI40160.pkl')
    ups, dns = weiswave_calculate(bars)
    signalsBuy, signalsSell = gen_WeisWave_signals(ups, dns, bars)

    buydistance = be_apart_from(signalsBuy)
    selldistance = be_apart_from(signalsSell)

    if buydistance < selldistance:
        lastsignal = signalsBuy[-buydistance]
    else:
        lastsignal = signalsSell[-selldistance]

    print(lastsignal, '持续周期：', min(buydistance, selldistance), '当前价格：', bars.close.iloc[-1])
    current_sig_pos = Dict()
    current_sig_profit = 0
    while True:
        api.wait_update()
        if api.is_changing(bartmp.iloc[-1], "datetime"):

            newk = bartmp.iloc[-2]
            bdt = bars.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                newk = newk.to_frame()
                newk = newk.T
                bars = pd.concat([bars, newk], ignore_index=True)

            ups, dns = weiswave_calculate(bars)
            signalsBuy, signalsSell = gen_WeisWave_signals(ups, dns, bars)
            buydistance = be_apart_from(signalsBuy)
            selldistance = be_apart_from(signalsSell)

            if buydistance < selldistance:
                lastsignal = signalsBuy[-buydistance]
            else:
                lastsignal = signalsSell[-selldistance]

            if isinstance(signalsBuy[-1], list):
                print(signalsBuy[-1][0])
                orderprice = bars.close.iloc[-1]
                BK(api, symbol=symbol, order_price=bars.close.iloc[-1], volume=1)
                mylog.info('下多单。')
                if current_sig_pos.sigtype == 'sell' and current_sig_pos.sigprice - bars.close.iloc[-1] > 3:
                    BP(api, symbol, order_price=bars.close.iloc[-1], volume=1)
                    mylog.info('平空单。')
                else:
                    mylog.info(['持仓盈利不足，不平仓。', '信号：', current_sig_pos.sigtype, current_sig_pos.sigprice, 'now price', orderprice])

                current_sig_pos.sigtype = 'buy'
                current_sig_pos.sigprice = bars.close.iloc[-1]
                current_sig_pos.volume = 1

                print('buy signal')

            if isinstance(signalsSell[-1], list):
                print(signalsSell[-1][0])
                orderprice = bars.close.iloc[-1]
                SK(api, symbol=symbol, order_price=bars.close.iloc[-1], volume=1)
                mylog.info('下空单。')
                if current_sig_pos.sigtype == 'buy' and bars.close.iloc[-1] - current_sig_pos.sigprice > 3:
                    SP(api, symbol, order_price=bars.close.iloc[-1], volume=1)
                    mylog.info('平多单。')

                else:
                    mylog.info(['持仓盈利不足，不平仓。', '信号：', current_sig_pos.sigtype, current_sig_pos.sigprice, 'now price', orderprice])

                current_sig_pos.sigtype = 'sell'
                current_sig_pos.sigprice = bars.close.iloc[-1]
                current_sig_pos.volume = 1

            if lastsignal[0] == 'buy':
                current_sig_profit = bars.close.iloc[-1] - lastsignal[1]
            else:
                current_sig_profit = lastsignal[1] -bars.close.iloc[-1]

            print(lastsignal, '持续周期：', min(buydistance, selldistance), '当前价格：', bars.close.iloc[-1], '信号盈亏：', current_sig_profit)

