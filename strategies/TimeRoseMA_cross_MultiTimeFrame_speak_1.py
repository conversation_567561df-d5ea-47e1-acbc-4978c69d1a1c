import copy
import sys
import os
import pandas as pd
import time
from typing import List, Dict

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tqsdk import TqApi, TqAccount, TqKq
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from signalSend import socket, sendsignal

# signal={'合约': 'SHFE.sn2408', '周期': 90, '时间': '09:45:00', '当前信号': '空', '持续周期': 17, '信号价格': 274170.0, '现价': 266810.0, '信号盈亏': 7370.0}
# sendsignal(socket, 'test', signal)
# print('signal sent...')

from paths import Paths
from MyTT import CROSS, MA
from utils.utils import tradingTime


class MultiCycleTradingStrategy:
    def __init__(self, api, symbol, intervals: List[int], single_volume=1, bklimit=200, sklimit=200):
        """
                初始化多周期交易策略

                :param api: TqApi实例
                :param symbol: 交易合约
                :param intervals: 需要处理的时间周期列表（秒）
                :param single_volume: 单次交易量
                :param bklimit: 多头最大持仓量
                :param sklimit: 空头最大持仓量
                """
        self.api = api
        self.symbol = symbol
        self.intervals = sorted(intervals)
        self.single_volume = single_volume
        self.bklimit = bklimit
        self.sklimit = sklimit

        # 初始化各周期数据存储
        self.klines_dict = {}  # 存储每个周期的K线数据
        self.klines_serial_dict = {}  # 存储每个周期的K线序列
        self.last_bar_times = {}  # 存储每个周期最后一个K线的时间
        self.signals_dict = {}  # 存储每个周期的信号
        self.last_signals = {}  # 存储每个周期的最后信号

        self.init_data_storage()
        self.setup_logging()
        self.prepare_sound_messages()

    def init_data_storage(self):
        """
        初始化数据存储结构
        完整初始化每个周期的K线数据
        """
        self.quote = self.api.get_quote(self.symbol)
        for interval in self.intervals:
            # 获取K线序列
            klines_serial = self.api.get_kline_serial(
                self.symbol,
                duration_seconds=interval,
                data_length=8964  # 保留较长的历史数据
            )
            self.klines_serial_dict[interval] = klines_serial

            # 创建K线数据的深拷贝，用于后续处理
            klines = copy.deepcopy(klines_serial)
            self.klines_dict[interval] = klines

            # 初始化周期相关信息
            self.last_bar_times[interval] = klines.datetime.tolist()[-1]
            self.signals_dict[interval] = []
            self.last_signals[interval] = {
                'signalnow': None,
                'signalprice': None,
                'distnow': None
            }

            mylog.info(f'初始化 {interval} 秒周期 K线，总K线数: {len(klines)}')
            # 立即计算并输出初始信号情况
            self.calculate_and_print_initial_signal(klines, interval, self.quote)

    def calculate_and_print_initial_signal(self, klines, interval, quote):
        """
        计算并输出初始信号情况

        :param klines: K线数据
        :param interval: 时间周期
        :param quote: 行情数据
        """
        # 计算MA交叉信号
        C = klines.close
        period = 13
        trmac = ma(C, period)
        ups = crossup(C, trmac)
        dns = crossdown(C, trmac)
        upslist = ups.tolist()
        dnslist = dns.tolist()
        bkdist = self.be_apart_from(upslist)
        skdist = self.be_apart_from(dnslist)

        # 判断当前信号方向
        signalnow = '空' if bkdist > skdist else '多'
        distnow = skdist if bkdist > skdist else bkdist

        # 计算信号价格和盈亏
        signalprice = C.iloc[-distnow]
        sigfloatprofit = (quote.last_price - signalprice) if signalnow == '多' else (signalprice - quote.last_price)
        updatetime = time_to_str(klines.datetime.iloc[-1]).split(' ')[1].split('.')[0]

        # 计算平均信号距离
        uplist = ups.tolist()
        average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))

        # 输出信号信息
        print(f'时间: {updatetime} {self.symbol} {interval} 当前信号: {signalnow} 持续周期: {distnow} 信号价格: {signalprice} 现价: {quote.last_price} 信号盈亏: {sigfloatprofit}')
        print(f'平均信号距离： {average_signal_distance}')
        print(f'upsignal:   {ups.iloc[-40:].tolist()}')
        print(f'downsignal: {dns.iloc[-40:].tolist()}')

        # 构建信号字典
        signal = {
            '合约': self.symbol,
            '周期': interval,
            '时间': updatetime,
            '当前信号': signalnow,
            '持续周期': distnow,
            '信号价格': signalprice,
            '现价': quote.last_price,
            '信号盈亏': sigfloatprofit
        }
        print(signal)

    def update_klines(self, interval):
        """
        更新特定周期的K线数据

        :param interval: 需要更新的时间周期（秒）
        :return: 是否有新K线
        """
        klines_serial = self.klines_serial_dict[interval]
        klines = self.klines_dict[interval]

        # 检查是否有新K线
        current_bar_time = klines_serial.datetime.tolist()[-1]

        if current_bar_time != self.last_bar_times[interval]:
            # 有新K线
            self.last_bar_times[interval] = current_bar_time

            # 获取最新的K线并追加
            newk = klines_serial.iloc[-1].to_frame().T
            klines_updated = pd.concat([klines, newk], ignore_index=True)

            # 更新K线数据
            self.klines_dict[interval] = klines_updated

            mylog.info(f'更新 {interval} 秒周期 K线，当前K线数: {len(klines_updated)}')

            return True

        return False

    def setup_logging(self):
        """设置日志"""
        try:
            userid = self.api.get_account().user_id.split('-')[0]
        except:
            userid = 'moni'

        logfilename = f'{userid}_{self.symbol}_multicycle'
        logfilename = Paths.log(logfilename)
        mylog.add(logfilename + '.log', encoding='utf-8')
        mylog.info(f'策略初始化: {self.symbol}, 周期: {self.intervals}')

    def prepare_sound_messages(self):
        """准备语音提示消息"""
        productid = self.symbol.split('.')[1]
        self.sound_messages = {
            interval: {
                'openlong': f'{productid} {interval // 60}分钟发出做多信号',
                'openshort': f'{productid} {interval // 60}分钟发出做空信号',
                'closelong': f'{productid} {interval // 60}分钟发出平多单信号',
                'closeshort': f'{productid} {interval // 60}分钟发出平空单信号'
            } for interval in self.intervals
        }

    def check_gap_coverage(self, quote):
        """检查缺口是否回补"""
        gap = quote.open - quote.pre_close
        gapcover = False

        if gap > 0:
            if quote.lowest < quote.pre_close:
                gapcover = True
        else:
            if quote.highest > quote.pre_close:
                gapcover = True

        return gap, gapcover
    def be_apart_from(self, signal_list):
        """计算信号连续距离"""
        for i in range(len(signal_list) - 1, -1, -1):
            if signal_list[i] == 1:
                return len(signal_list) - 1 - i
        return len(signal_list)
    def trade_decision(self, quote, position, interval, signal_info, upslist, dnslist):
        """根据信号做交易决策"""
        signalnow = signal_info['当前信号']
        distnow = signal_info['持续周期']

        bkvol = position.pos_long
        skvol = position.pos_short

        # 做多信号
        if upslist[-1]:
            mylog.info(f'{self.symbol} {interval}分钟发出做多信号....')
            speak_text(self.sound_messages[interval]['openlong'])

            bkprice = quote.last_price + 1
            if bkvol < self.bklimit:
                orderVol = self.single_volume
                if orderVol > 0:
                    BK(self.api, symbol=self.symbol, order_price=bkprice, volume=orderVol)
                    mylog.info(f'{self.symbol} 下多单: 数量{orderVol}, 价格{bkprice}')

            # 平空单逻辑
            if skvol > 0:
                if position.float_profit_short > 5 * skvol:
                    BP(self.api, symbol=self.symbol, order_price=bkprice, volume=self.single_volume)
                    mylog.info(f'{self.symbol} 平空单, 数量{self.single_volume}, 价格{bkprice}')

        # 做空信号
        if dnslist[-1]:
            mylog.info(f'{self.symbol} {interval}分钟发出做空信号....')
            speak_text(self.sound_messages[interval]['openshort'])

            skprice = quote.last_price - 1
            if skvol < self.sklimit:
                orderVol = self.single_volume
                if orderVol > 0:
                    SK(self.api, symbol=self.symbol, order_price=skprice, volume=orderVol)
                    mylog.info(f'{self.symbol} 下空单: 数量{orderVol}, 价格{skprice}')

            # 平多单逻辑
            if bkvol > 0:
                if position.float_profit_long > 5 * bkvol:
                    SP(self.api, symbol=self.symbol, order_price=skprice, volume=self.single_volume, today_prefer=True)
                    mylog.info(f'{self.symbol} 平多单, 数量{self.single_volume}, 价格{skprice}')

    def generate_signal_info(self, bars, period, quote, interval):
        """生成信号信息"""
        C = bars.close
        SYMBOL = bars.symbol.iloc[0]

        trmac = ma(C, period)
        ups = crossup(C, trmac)
        dns = crossdown(C, trmac)
        upslist = ups.tolist()
        dnslist = dns.tolist()
        bkdist = self.be_apart_from(upslist)
        skdist = self.be_apart_from(dnslist)

        signalnow = '空' if bkdist > skdist else '多'
        distnow = skdist if bkdist > skdist else bkdist

        signalprice = C.iloc[-distnow]

        # 计算信号盈亏
        gap, gapcover = self.check_gap_coverage(quote)
        sigfloatprofit = (quote.last_price - signalprice) if signalnow == '多' else (signalprice - quote.last_price)

        updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]

        signal = {
            '合约': SYMBOL,
            '周期': interval,
            '时间': updatetime,
            '当前信号': signalnow,
            '持续周期': distnow,
            '信号价格': signalprice,
            '现价': quote.last_price,
            '信号盈亏': sigfloatprofit,
            '缺口': gap,
            '缺口回补': gapcover
        }

        # 保存最近的信号状态
        self.last_signals[interval] = {
            'signalnow': signalnow,
            'signalprice': signalprice,
            'distnow': distnow
        }

        return signal, upslist, dnslist, signalnow, distnow

    def run(self):
        """策略主运行函数"""
        speak_text('程序开始运行')
        quote = self.api.get_quote(self.symbol)
        position = self.api.get_position(self.symbol)

        while True:
            self.api.wait_update()

            # 处理每个时间周期的信号
            for interval in self.intervals:
                # 检查并更新K线
                if self.update_klines(interval):
                    klines_updated = self.klines_dict[interval]

                    # 生成信号
                    signal_info, upslist, dnslist, signalnow, distnow = self.generate_signal_info(
                        klines_updated, period=13, quote=quote, interval=interval
                    )

                    # 发送信号
                    sendsignal(socket, 'multicycle', signal_info)
                    print(f"{interval}分钟信号:", signal_info)

                    # 交易决策
                    self.trade_decision(quote, position, interval, signal_info, upslist, dnslist)

                klines = self.klines_dict[interval]

                # 检查是否有新K线
                if self.api.is_changing(klines.iloc[-1], "datetime"):
                    current_bar_time = klines.datetime.tolist()[-1]

                    if current_bar_time != self.last_bar_times[interval]:
                        self.last_bar_times[interval] = current_bar_time

                        # 更新K线
                        newk = klines.iloc[-2].to_frame().T
                        klines_updated = pd.concat([klines, newk], ignore_index=True)
                        self.klines_dict[interval] = klines_updated

                        # 生成信号
                        signal_info, upslist, dnslist, signalnow, distnow = self.generate_signal_info(
                            klines_updated, period=13, quote=quote, interval=interval
                        )

                        # 发送信号
                        sendsignal(socket, 'multicycle', signal_info)
                        print(f"{interval}分钟信号:", signal_info)

                        # 交易决策
                        self.trade_decision(quote, position, interval, signal_info, upslist, dnslist)

            # 实时quote变化处理
            if self.api.is_changing(quote):
                print('quote change...', self.last_signals.items())
                gap, gapcover = self.check_gap_coverage(quote)

                # 处理每个周期的最近信号
                for interval, last_signal in self.last_signals.items():
                    if last_signal['signalnow'] is not None:
                        sigfloatprofit = (quote.last_price - last_signal['signalprice']) if last_signal['signalnow'] == '多' else (last_signal['signalprice'] - quote.last_price)

                        updatetime = quote.datetime.split(' ')[1].split('.')[0]
                        real_time_signal = {
                            '合约': self.symbol,
                            '周期': interval,
                            '时间': updatetime,
                            '当前信号': last_signal['signalnow'],
                            '持续周期': last_signal['distnow'],
                            '信号价格': last_signal['signalprice'],
                            '现价': quote.last_price,
                            '信号盈亏': sigfloatprofit,
                            '缺口': gap,
                            '缺口回补': gapcover
                        }

                        # 实时发送信号
                        sendsignal(socket, 'multicycle_realtime', real_time_signal)
                        print(f"实时{interval}分钟信号:", real_time_signal)

    # 其他方法（be_apart_from, trade_decision等）保持不变
    # ...


def runstrategy():
    # 可配置的多周期列表
    intervals = [60, 180, 300, 900]  # 1, 3, 5, 15分钟

    product = 'OI'
    symbol_query = product
    single_volume = 1
    bklimit = 200
    sklimit = 200

    # 初始化API
    api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
    symbol = api.query_cont_quotes(product_id=product)[0]

    # 创建并运行策略
    strategy = MultiCycleTradingStrategy(
        api,
        symbol,
        intervals=intervals,
        single_volume=single_volume,
        bklimit=bklimit,
        sklimit=sklimit
    )
    strategy.run()


if __name__ == "__main__":
    runstrategy()