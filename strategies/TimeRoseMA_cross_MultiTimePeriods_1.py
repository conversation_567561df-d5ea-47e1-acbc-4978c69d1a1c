import copy
from dataclasses import dataclass
from typing import Dict, List, Optional
import pandas as pd
from tqsdk import TqApi, TqKq
from tqsdk.tafunc import ma, crossup, crossdown, time_to_str
from signalSend import socket, sendsignal


@dataclass
class SignalInfo:
    symbol: str
    period: int
    timestamp: str
    signal_type: str  # '多' or '空'
    duration: int
    signal_price: float
    current_price: float
    floating_profit: float
    gap_covered: bool = False


class PeriodStrategy:
    def __init__(self, period: int, symbol: str, strategy_name: str):
        self.period = period
        self.symbol = symbol
        self.strategy_name = strategy_name
        self.klines = None
        self.ma_period = 13
        self.gap = 0
        self.pre_close = 0

    def initialize(self, api: TqApi):
        """初始化K线数据"""
        self.klines = api.get_kline_serial(self.symbol, duration_seconds=self.period, data_length=8964)
        quote = api.get_quote(self.symbol)
        self.gap = quote.open - quote.pre_close
        self.pre_close = quote.pre_close

    def update_klines(self, new_bar):
        """更新K线数据"""
        if new_bar.datetime not in self.klines.datetime.tolist():
            new_bar = new_bar.to_frame().T
            self.klines = pd.concat([self.klines, new_bar], ignore_index=True)

    def calculate_signals(self, quote) -> SignalInfo:
        """计算当前周期的信号"""
        C = self.klines.close
        trmac = ma(C, self.ma_period)
        ups = crossup(C, trmac)
        dns = crossdown(C, trmac)

        upslist = ups.tolist()
        dnslist = dns.tolist()

        bk_dist = self._calculate_distance(upslist)
        sk_dist = self._calculate_distance(dnslist)

        if bk_dist > sk_dist:
            signal_type = '空'
            dist_now = sk_dist
        else:
            signal_type = '多'
            dist_now = bk_dist

        signal_price = C.iloc[-dist_now]
        floating_profit = quote.last_price - signal_price if signal_type == '多' else signal_price - quote.last_price

        return SignalInfo(
            symbol=self.symbol,
            period=self.period,
            timestamp=time_to_str(self.klines.datetime.iloc[-1]).split(' ')[1].split('.')[0],
            signal_type=signal_type,
            duration=dist_now,
            signal_price=signal_price,
            current_price=quote.last_price,
            floating_profit=floating_profit
        )

    def process_quote_update(self, quote) -> dict:
        """处理实时行情更新,保持原有的信号发送格式"""
        gap_covered = False
        if self.gap > 0:
            if quote.lowest < self.pre_close:
                gap_covered = True
        else:
            if quote.highest > self.pre_close:
                gap_covered = True

        # 获取当前信号
        signal_info = self.calculate_signals(quote)

        # 构造原格式的信号字典
        signal_dict = {
            '合约': self.symbol,
            '周期': self.period,
            '时间': quote.datetime.split(' ')[1].split('.')[0],
            '当前信号': signal_info.signal_type,
            '持续周期': signal_info.duration,
            '信号价格': signal_info.signal_price,
            '现价': quote.last_price,
            '信号盈亏': signal_info.floating_profit
        }

        # 发送信号
        print(signal_dict)
        sendsignal(socket, self.strategy_name, signal_dict)

        return signal_dict

    @staticmethod
    def _calculate_distance(signal_list: List[bool]) -> int:
        """计算信号持续距离"""
        for i, value in enumerate(reversed(signal_list)):
            if value:
                return i
        return len(signal_list)


class MultiPeriodManager:
    def __init__(self, symbol: str, periods: List[int], strategy_name: str):
        self.symbol = symbol
        self.periods = periods
        self.strategy_name = strategy_name
        self.strategies: Dict[int, PeriodStrategy] = {}

    def initialize(self, api: TqApi):
        """初始化所有周期的策略"""
        for period in self.periods:
            strategy = PeriodStrategy(period, self.symbol, self.strategy_name)
            strategy.initialize(api)
            self.strategies[period] = strategy

    def update_all(self, api: TqApi) -> Dict[int, dict]:
        """更新所有周期的信号"""
        quote = api.get_quote(self.symbol)
        signals = {}

        # 处理K线更新
        for period, strategy in self.strategies.items():
            klines_tmp = api.get_kline_serial(self.symbol, duration_seconds=period, data_length=10)
            if api.is_changing(klines_tmp.iloc[-1], "datetime"):
                strategy.update_klines(klines_tmp.iloc[-2])

        # 处理实时行情更新
        if api.is_changing(quote):
            print(f"quote update")
            for period, strategy in self.strategies.items():
                signals[period] = strategy.process_quote_update(quote)

        return signals


def run_multi_period_strategy():
    # 配置参数
    product = 'ag'
    periods = [60, 180, 300, 900]  # 1分钟、3分钟、5分钟、15分钟
    strategy_name = 'macross'

    # 初始化API
    api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True)
    symbol = api.query_cont_quotes(product_id=product)[0]

    # 创建多周期管理器
    manager = MultiPeriodManager(symbol, periods, strategy_name)
    manager.initialize(api)

    try:
        while True:
            api.wait_update()

            # 获取所有周期的信号
            signals = manager.update_all(api)

            # 输出信号信息
            for period, signal in signals.items():
                if signal:  # 只在有信号更新时打印
                    print(f"Period {period}s: {signal}")

    except KeyboardInterrupt:
        print("Strategy stopped by user")
        api.close()
    except Exception as e:
        print(f"Error occurred: {e}")
        api.close()


if __name__ == "__main__":
    run_multi_period_strategy()