try:
    from tdx_function import HHV, LLV, REF, IF, VALUEWHEN, BARSLAST
except ImportError:
    from .tdx_function import HHV, LLV, REF, IF, VALUEWHEN, BARSLAST

import json
import os
import pandas as pd
import numpy as np
from speaktext import speak_text

# 导入TqApi用于实际交易
try:
    from tqsdk import TqApi, TqAuth, TqKq
except ImportError:
    print("警告: 未安装tqsdk，无法进行实际交易")



class PositionManager:
    def __init__(self, symbol, positions_file=None):
        self.symbol = symbol
        self.positions_file = positions_file or f"positions_{symbol.replace('.', '_')}.json"
        self.positions = self.load_positions()

    def load_positions(self):
        """从文件加载持仓数据"""
        if os.path.exists(self.positions_file):
            try:
                with open(self.positions_file, 'r', encoding='utf-8') as f:
                    positions = json.load(f)
                    print(f"加载持仓数据: {positions}")
                    return positions
            except Exception as e:
                print(f"加载持仓文件失败: {e}")

        # 默认持仓
        default_positions = {
            'symbol': self.symbol,
            'long': 0,
            'short': 0,
            'last_update': ''
        }
        self.save_positions(default_positions)
        return default_positions

    def save_positions(self, positions=None):
        """保存持仓数据到文件"""
        if positions is None:
            positions = self.positions

        try:
            with open(self.positions_file, 'w', encoding='utf-8') as f:
                json.dump(positions, f, ensure_ascii=False, indent=2)
            print(f"保存持仓数据: {positions}")
        except Exception as e:
            print(f"保存持仓文件失败: {e}")

    def update_position(self, position_type, volume, action='open'):
        """更新持仓"""
        import datetime

        if action == 'open':
            self.positions[position_type] += volume
            print(f"开{position_type}单 {volume}手，当前持仓: {self.positions[position_type]}")
        elif action == 'close':
            self.positions[position_type] = max(0, self.positions[position_type] - volume)
            print(f"平{position_type}单 {volume}手，当前持仓: {self.positions[position_type]}")

        self.positions['last_update'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.save_positions()

    def can_open_position(self, position_type, volume, limit):
        """检查是否可以开仓"""
        current_pos = self.positions[position_type]
        if current_pos + volume > limit:
            print(f"{position_type}单持仓限制: 当前{current_pos} + 新增{volume} > 限额{limit}")
            return False
        return True

    def get_position(self, position_type):
        """获取当前持仓"""
        return self.positions[position_type]


def calculate_signals(results):
    """Calculate trading signals based on price action"""
    # 创建数据副本，避免修改原始K线数据
    results = results.copy()
    
    # Create IF instance
    if_func = IF()

    # Get input data
    HIGH = results['high']
    LOW = results['low']
    CLOSE = results['close']

    # Use loc for assignments
    results.loc[:, 'X_34'] = if_func(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
    results.loc[:, 'X_35'] = if_func(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)
    results.loc[:, 'X_36'] = HHV(results['X_34'], 3)
    results.loc[:, 'X_37'] = LLV(results['X_35'], 3)
    results.loc[:, 'X_38'] = BARSLAST(results['X_37'] < REF(results['X_37'], 1))
    results.loc[:, 'X_39'] = BARSLAST(results['X_36'] > REF(results['X_36'], 1))
    results.loc[:, 'X_40'] = if_func(HHV(results['X_34'], results['X_39'] + 1) == results['X_34'], 1, 0)
    results.loc[:, 'X_41'] = if_func(LLV(results['X_35'], results['X_38'] + 1) == results['X_35'], 1, 0)
    results.loc[:, 'X_42'] = BARSLAST(results['X_40'])
    results.loc[:, 'X_43'] = REF(LLV(results['X_35'], 3), results['X_42'])
    results.loc[:, 'X_44'] = BARSLAST(results['X_41'])
    results.loc[:, 'X_45'] = REF(HHV(results['X_34'], 3), results['X_44'])
    results.loc[:, 'X_46'] = VALUEWHEN(results['X_45'] > 0, results['X_45'])
    results.loc[:, 'X_47'] = VALUEWHEN(results['X_43'] > 0, results['X_43'])
    results.loc[:, 'X_48'] = if_func(CLOSE > results['X_46'], -1,
                            if_func(CLOSE < results['X_47'], 1, 0))
    results.loc[:, 'X_49'] = VALUEWHEN(results['X_48'] != 0, results['X_48'])
    results.loc[:, 'X_50'] = BARSLAST(CROSS(0, results['X_49']))
    results.loc[:, 'X_51'] = BARSLAST(CROSS(results['X_49'], 0))

    results.loc[:, 'X_52'] = if_func(results['X_49'] == 1,
                            if_func(LLV(results['X_46'], results['X_51'] + 1) == results['X_46'],
                                    results['X_46'],
                                    LLV(results['X_46'], results['X_51'] + 1)),
                            results['X_46'])

    results.loc[:, 'X_53'] = if_func(results['X_49'] == -1,
                            if_func(HHV(results['X_47'], results['X_50'] + 1) == results['X_47'],
                                    results['X_47'],
                                    HHV(results['X_47'], results['X_50'] + 1)),
                            results['X_47'])

    results.loc[:, 'X_54'] = if_func(CLOSE > results['X_52'], -1,
                            if_func(CLOSE < results['X_53'], 1, 0))
    results.loc[:, 'X_55'] = VALUEWHEN(results['X_54'] != 0, results['X_54'])
    results.loc[:, 'X_56'] = BARSLAST(CROSS(0, results['X_54']))
    results.loc[:, 'X_57'] = BARSLAST(CROSS(results['X_54'], 0))

    results.loc[:, 'X_58'] = if_func(results['X_55'] == 1,
                            if_func(LLV(results['X_52'], results['X_57'] + 1) == results['X_52'],
                                    results['X_52'],
                                    LLV(results['X_52'], results['X_57'] + 1)),
                            if_func(HHV(results['X_53'], results['X_56'] + 1) == results['X_53'],
                                    results['X_53'],
                                    HHV(results['X_53'], results['X_56'] + 1)))

    # Output signals
    results.loc[:, 'long_stop'] = if_func(results['X_55'] < 0, results['X_58'], np.nan)
    results.loc[:, 'short_stop'] = if_func(results['X_55'] > 0, results['X_58'], np.nan)
    results.loc[:, 'G'] = results['X_58']
    results.loc[:, 'long'] = results['X_55'] < 0
    results.loc[:, 'short'] = results['X_55'] > 0
    results.loc[:, 'signal'] = np.where(results['short'] == 1, '空', '多')

    return results

def CROSS(series1, series2):
    """
    Helper function to detect crossover between two series or between a series and a scalar
    """
    # Convert scalar to Series if needed
    if isinstance(series1, (int, float)):
        if isinstance(series2, pd.Series):
            series1 = pd.Series([series1] * len(series2), index=series2.index)
    elif isinstance(series2, (int, float)):
        if isinstance(series1, pd.Series):
            series2 = pd.Series([series2] * len(series1), index=series1.index)

    return (series1 > series2) & (series1.shift(1) <= series2.shift(1))


def count_same_from_right(arr):
    if not arr:  # 如果数组为空，返回0
        return 0

    last_element = arr[-1]  # 获取最后一个元素
    count = 0

    # 从右向左遍历数组
    for i in range(len(arr) - 1, -1, -1):
        if arr[i] == last_element:
            count += 1
        else:
            break  # 一旦遇到不同的元素，就停止计数

    return count

def count_consecutive_elements(signals):
    """
    计算数组中连续出现的"空"和"多"的数量，以及它们交替出现的次数

    参数:
    signals: 包含"空"和"多"的列表

    返回:
    一个字典，包含以下信息:
    - consecutive_empty: 每次连续出现的"空"的数量列表
    - consecutive_many: 每次连续出现的"多"的数量列表
    - alternation_count: "空"和"多"交替出现的次数
    """
    if not signals:
        return {
            "consecutive_empty": [],
            "consecutive_many": [],
            "alternation_count": 0
        }

    # 初始化结果
    consecutive_empty = []
    consecutive_many = []
    alternation_count = 0

    # 初始化计数器和当前元素
    current_element = signals[0]
    current_count = 1

    # 遍历信号数组（从第二个元素开始）
    for i in range(1, len(signals)):
        if signals[i] == current_element:
            # 如果当前元素与前一个相同，增加计数
            current_count += 1
        else:
            # 如果元素变化，记录前一个元素的连续计数
            if current_element == "空":
                consecutive_empty.append(current_count)
            else:  # current_element == "多"
                consecutive_many.append(current_count)

            # 增加交替计数（每次元素类型改变）
            alternation_count += 1

            # 重置计数器和当前元素
            current_element = signals[i]
            current_count = 1

    # 处理最后一组连续元素
    if current_element == "空":
        consecutive_empty.append(current_count)
    else:  # current_element == "多"
        consecutive_many.append(current_count)

    return {
        "consecutive_empty": consecutive_empty,
        "consecutive_many": consecutive_many,
        "alternation_count": alternation_count
    }



def wave_signal_trading(df, longlimit, shortlimit, volume, api=None, close_today_first=False):
    """
    波浪信号交易函数 - 实际下单版本
    
    参数:
    df: 包含信号数据的DataFrame
    longlimit: 多单持仓限制
    shortlimit: 空单持仓限制  
    volume: 每次交易手数
    api: TqApi实例，用于实际下单（由主程序传递）
    close_today_first: 是否优先平今仓（True=优先平今，False=优先平昨）
    """
    strategy_name = '突破火线'
    symbol = df.symbol.iloc[-1]
    symbol_name = df.symbol.iloc[-1]

    # 初始化策略自己的持仓管理器
    position_mgr = PositionManager(symbol)

    signals = df.signal.tolist()
    signal_last = count_same_from_right(signals)
    signal_price = df.close.iloc[-signal_last]
    current_price = int(df.close.iloc[-1])
    current_signal = signals[-1]
    stop_price = df.G.iloc[-1]
    duration = df.duration.iloc[-1]

    # 统计信号
    result = count_consecutive_elements(signals)
    print("连续出现的'空'的数量:", result["consecutive_empty"])
    print("连续出现的'多'的数量:", result["consecutive_many"])
    if result["consecutive_empty"]:
        print('空单平均持续周期:', sum(result["consecutive_empty"]) / len(result["consecutive_empty"]))
    if result["consecutive_many"]:
        print('多单平均持续周期:', sum(result["consecutive_many"]) / len(result["consecutive_many"]))
    print("'空'和'多'交替出现的次数:", result["alternation_count"])

    pnl = current_price - signal_price if current_signal == '多' else signal_price - current_price
    print(f"{symbol}: 周期:{duration} 秒, current_signal:{current_signal}, Current price: {current_price}")
    print(f"signal_price: {signal_price}, signal_last: {signal_last}, pnl: {pnl}, 止损:{stop_price}")
    print(f"持续周期:{count_same_from_right(signals)}")
    
    # 使用策略自己的持仓管理器获取持仓信息
    current_long = position_mgr.get_position('long')
    current_short = position_mgr.get_position('short')
    print(f"策略持仓 - 多单:{current_long}手, 空单:{current_short}手")
    print(signals[-35:])

    # 信号处理 - 实际下单
    if len(signals) >= 2:
        if signals[-2] == '多' and signals[-1] == '空':
            speak_text(f"{symbol_name}{strategy_name} 发出做空信号")

            # 检查是否可以开空单
            if position_mgr.can_open_position('short', volume, shortlimit):
                if api:
                    try:
                        # 获取当前行情，设置限价（开空单用卖一价或稍低价格）
                        quote = api.get_quote(symbol)
                        limit_price = quote.bid_price1 if quote.bid_price1 > 0 else current_price - 1
                        print(f"执行开空单: {symbol}, 数量: {volume}手, 限价: {limit_price}")
                        order = api.insert_order(symbol=symbol, direction="SELL", offset="OPEN", volume=volume, limit_price=limit_price)
                        print(f"开空单委托已提交: 订单ID {order.order_id}")
                        # 更新策略持仓记录
                        position_mgr.update_position('short', volume, 'open')
                    except Exception as e:
                        print(f"开空单失败: {e}")
                else:
                    print(f"模拟开空单: {symbol}, 数量: {volume}手")
                    position_mgr.update_position('short', volume, 'open')
            else:
                print(f"空单持仓已达限额 {shortlimit}手，不开新仓")

            # 检查是否平多单 (盈利条件)
            if current_long > 0 and pnl > 20:  # 盈利超过20点
                close_volume = min(volume, current_long)
                if api:
                    try:
                        # 获取实际持仓信息
                        position = api.get_position(symbol)
                        pos_long_his = position.pos_long_his  # 历史多头持仓
                        pos_long_today = position.pos_long_today  # 当日多头持仓
                        
                        print(f"实际持仓 - 多单历史:{pos_long_his}手, 多单当日:{pos_long_today}手")
                        print(f"平仓策略: {'优先平今' if close_today_first else '优先平昨'}")
                        
                        if close_today_first:
                            # 优先平当日仓位（CLOSETODAY），再平历史仓位（CLOSE）
                            if pos_long_today > 0:
                                close_today_volume = min(close_volume, pos_long_today)
                                # 获取当前行情，设置限价（平多单用卖一价或稍低价格）
                                quote = api.get_quote(symbol)
                                limit_price = quote.bid_price1 if quote.bid_price1 > 0 else current_price - 1
                                print(f"执行平多单(当日仓): {symbol}, 数量: {close_today_volume}手, 盈利: {pnl:.2f}点, 限价: {limit_price}")
                                order = api.insert_order(symbol=symbol, direction="SELL", offset="CLOSETODAY", volume=close_today_volume, limit_price=limit_price)
                                print(f"平多单(当日仓)委托已提交: 订单ID {order.order_id}")
                                close_volume -= close_today_volume
                            
                            # 如果还有需要平仓的量，平历史仓位
                            if close_volume > 0 and pos_long_his > 0:
                                close_his_volume = min(close_volume, pos_long_his)
                                # 获取当前行情，设置限价（平多单用卖一价或稍低价格）
                                quote = api.get_quote(symbol)
                                limit_price = quote.bid_price1 if quote.bid_price1 > 0 else current_price - 1
                                print(f"执行平多单(历史仓): {symbol}, 数量: {close_his_volume}手, 盈利: {pnl:.2f}点, 限价: {limit_price}")
                                order = api.insert_order(symbol=symbol, direction="SELL", offset="CLOSE", volume=close_his_volume, limit_price=limit_price)
                                print(f"平多单(历史仓)委托已提交: 订单ID {order.order_id}")
                        else:
                            # 优先平历史仓位（CLOSE），再平当日仓位（CLOSETODAY）
                            if pos_long_his > 0:
                                close_his_volume = min(close_volume, pos_long_his)
                                # 获取当前行情，设置限价（平多单用卖一价或稍低价格）
                                quote = api.get_quote(symbol)
                                limit_price = quote.bid_price1 if quote.bid_price1 > 0 else current_price - 1
                                print(f"执行平多单(历史仓): {symbol}, 数量: {close_his_volume}手, 盈利: {pnl:.2f}点, 限价: {limit_price}")
                                order = api.insert_order(symbol=symbol, direction="SELL", offset="CLOSE", volume=close_his_volume, limit_price=limit_price)
                                print(f"平多单(历史仓)委托已提交: 订单ID {order.order_id}")
                                close_volume -= close_his_volume
                            
                            # 如果还有需要平仓的量，平当日仓位
                            if close_volume > 0 and pos_long_today > 0:
                                close_today_volume = min(close_volume, pos_long_today)
                                # 获取当前行情，设置限价（平多单用卖一价或稍低价格）
                                quote = api.get_quote(symbol)
                                limit_price = quote.bid_price1 if quote.bid_price1 > 0 else current_price - 1
                                print(f"执行平多单(当日仓): {symbol}, 数量: {close_today_volume}手, 盈利: {pnl:.2f}点, 限价: {limit_price}")
                                order = api.insert_order(symbol=symbol, direction="SELL", offset="CLOSETODAY", volume=close_today_volume, limit_price=limit_price)
                                print(f"平多单(当日仓)委托已提交: 订单ID {order.order_id}")
                        
                        # 更新策略持仓记录
                        position_mgr.update_position('long', min(volume, current_long), 'close')
                    except Exception as e:
                        print(f"平多单失败: {e}")
                else:
                    print(f"模拟平多单: {symbol}, 数量: {close_volume}手, 盈利: {pnl:.2f}点")
                    position_mgr.update_position('long', close_volume, 'close')
            else:
                print(f"多单平仓条件不满足: 持仓{current_long}手, 盈利{pnl:.2f}点")

        elif signals[-2] == '空' and signals[-1] == '多':
            speak_text(f"{symbol_name}{strategy_name} 发出做多信号")

            # 检查是否可以开多单
            if position_mgr.can_open_position('long', volume, longlimit):
                if api:
                    try:
                        # 获取当前行情，设置限价（开多单用买一价或稍高价格）
                        quote = api.get_quote(symbol)
                        limit_price = quote.ask_price1 if quote.ask_price1 > 0 else current_price + 1
                        print(f"执行开多单: {symbol}, 数量: {volume}手, 限价: {limit_price}")
                        order = api.insert_order(symbol=symbol, direction="BUY", offset="OPEN", volume=volume, limit_price=limit_price)
                        print(f"开多单委托已提交: 订单ID {order.order_id}")
                        # 更新策略持仓记录
                        position_mgr.update_position('long', volume, 'open')
                    except Exception as e:
                        print(f"开多单失败: {e}")
                else:
                    print(f"模拟开多单: {symbol}, 数量: {volume}手")
                    position_mgr.update_position('long', volume, 'open')
            else:
                print(f"多单持仓已达限额 {longlimit}手，不开新仓")

            # 检查是否平空单 (盈利条件)
            if current_short > 0 and pnl > 20:  # 盈利超过20点
                close_volume = min(volume, current_short)
                if api:
                    try:
                        # 获取实际持仓信息
                        position = api.get_position(symbol)
                        pos_short_his = position.pos_short_his  # 历史空头持仓
                        pos_short_today = position.pos_short_today  # 当日空头持仓
                        
                        print(f"实际持仓 - 空单历史:{pos_short_his}手, 空单当日:{pos_short_today}手")
                        print(f"平仓策略: {'优先平今' if close_today_first else '优先平昨'}")
                        
                        if close_today_first:
                            # 优先平当日仓位（CLOSETODAY），再平历史仓位（CLOSE）
                            if pos_short_today > 0:
                                close_today_volume = min(close_volume, pos_short_today)
                                # 获取当前行情，设置限价（平空单用买一价或稍高价格）
                                quote = api.get_quote(symbol)
                                limit_price = quote.ask_price1 if quote.ask_price1 > 0 else current_price + 1
                                print(f"执行平空单(当日仓): {symbol}, 数量: {close_today_volume}手, 盈利: {pnl:.2f}点, 限价: {limit_price}")
                                order = api.insert_order(symbol=symbol, direction="BUY", offset="CLOSETODAY", volume=close_today_volume, limit_price=limit_price)
                                print(f"平空单(当日仓)委托已提交: 订单ID {order.order_id}")
                                close_volume -= close_today_volume
                            
                            # 如果还有需要平仓的量，平历史仓位
                            if close_volume > 0 and pos_short_his > 0:
                                close_his_volume = min(close_volume, pos_short_his)
                                # 获取当前行情，设置限价（平空单用买一价或稍高价格）
                                quote = api.get_quote(symbol)
                                limit_price = quote.ask_price1 if quote.ask_price1 > 0 else current_price + 1
                                print(f"执行平空单(历史仓): {symbol}, 数量: {close_his_volume}手, 盈利: {pnl:.2f}点, 限价: {limit_price}")
                                order = api.insert_order(symbol=symbol, direction="BUY", offset="CLOSE", volume=close_his_volume, limit_price=limit_price)
                                print(f"平空单(历史仓)委托已提交: 订单ID {order.order_id}")
                        else:
                            # 优先平历史仓位（CLOSE），再平当日仓位（CLOSETODAY）
                            if pos_short_his > 0:
                                close_his_volume = min(close_volume, pos_short_his)
                                # 获取当前行情，设置限价（平空单用买一价或稍高价格）
                                quote = api.get_quote(symbol)
                                limit_price = quote.ask_price1 if quote.ask_price1 > 0 else current_price + 1
                                print(f"执行平空单(历史仓): {symbol}, 数量: {close_his_volume}手, 盈利: {pnl:.2f}点, 限价: {limit_price}")
                                order = api.insert_order(symbol=symbol, direction="BUY", offset="CLOSE", volume=close_his_volume, limit_price=limit_price)
                                print(f"平空单(历史仓)委托已提交: 订单ID {order.order_id}")
                                close_volume -= close_his_volume
                            
                            # 如果还有需要平仓的量，平当日仓位
                            if close_volume > 0 and pos_short_today > 0:
                                close_today_volume = min(close_volume, pos_short_today)
                                # 获取当前行情，设置限价（平空单用买一价或稍高价格）
                                quote = api.get_quote(symbol)
                                limit_price = quote.ask_price1 if quote.ask_price1 > 0 else current_price + 1
                                print(f"执行平空单(当日仓): {symbol}, 数量: {close_today_volume}手, 盈利: {pnl:.2f}点, 限价: {limit_price}")
                                order = api.insert_order(symbol=symbol, direction="BUY", offset="CLOSETODAY", volume=close_today_volume, limit_price=limit_price)
                                print(f"平空单(当日仓)委托已提交: 订单ID {order.order_id}")
                        
                        # 更新策略持仓记录
                        position_mgr.update_position('short', min(volume, current_short), 'close')
                    except Exception as e:
                        print(f"平空单失败: {e}")
                else:
                    print(f"模拟平空单: {symbol}, 数量: {close_volume}手, 盈利: {pnl:.2f}点")
                    position_mgr.update_position('short', close_volume, 'close')
            else:
                print(f"空单平仓条件不满足: 持仓{current_short}手, 盈利{pnl:.2f}点")

    return {
        'symbol': symbol,
        'current_price': current_price,
        'current_signal': current_signal,
        'long_position': position_mgr.get_position('long'),
        'short_position': position_mgr.get_position('short'),
        'position_manager': position_mgr
    }

if __name__ == '__main__':
    # 测试数据
    datafile = 'data_OI_15.csv'
    df = pd.read_csv(datafile)
    df = calculate_signals(df)
    print(df)
    print(f"止损价格: {df['G'].iloc[-1]}")
    
    # 交易参数
    longlimit = 10   # 多单持仓限制
    shortlimit = 10  # 空单持仓限制
    volume = 1       # 每次交易手数
    
    # 模拟交易模式（不连接实际API）
    print("\n=== 模拟交易模式 ===")
    result = wave_signal_trading(df, longlimit, shortlimit, volume, api=None)
    print(f"交易结果: {result}")
    
    # 实际交易模式示例（需要取消注释并配置正确的认证信息）
    """
    print("\n=== 实际交易模式 ===")
    try:
        # 初始化TqApi（需要配置正确的认证信息）
        api = TqApi(TqKq(), auth=TqAuth("your_username", "your_password"))
        
        # 执行实际交易
        result = wave_signal_trading(df, longlimit, shortlimit, volume, api=api)
        print(f"实际交易结果: {result}")
        
        # 关闭API连接
        api.close()
        
    except Exception as e:
        print(f"实际交易模式失败: {e}")
        print("请检查TqApi配置和网络连接")
    """
