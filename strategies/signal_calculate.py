def count_consecutive_elements(signals):
    """
    计算数组中连续出现的"空"和"多"的数量，以及它们交替出现的次数

    参数:
    signals: 包含"空"和"多"的列表

    返回:
    一个字典，包含以下信息:
    - consecutive_empty: 每次连续出现的"空"的数量列表
    - consecutive_many: 每次连续出现的"多"的数量列表
    - alternation_count: "空"和"多"交替出现的次数
    """
    if not signals:
        return {
            "consecutive_empty": [],
            "consecutive_many": [],
            "alternation_count": 0
        }

    # 初始化结果
    consecutive_empty = []
    consecutive_many = []
    alternation_count = 0

    # 初始化计数器和当前元素
    current_element = signals[0]
    current_count = 1

    # 遍历信号数组（从第二个元素开始）
    for i in range(1, len(signals)):
        if signals[i] == current_element:
            # 如果当前元素与前一个相同，增加计数
            current_count += 1
        else:
            # 如果元素变化，记录前一个元素的连续计数
            if current_element == "空":
                consecutive_empty.append(current_count)
            else:  # current_element == "多"
                consecutive_many.append(current_count)

            # 增加交替计数（每次元素类型改变）
            alternation_count += 1

            # 重置计数器和当前元素
            current_element = signals[i]
            current_count = 1

    # 处理最后一组连续元素
    if current_element == "空":
        consecutive_empty.append(current_count)
    else:  # current_element == "多"
        consecutive_many.append(current_count)

    return {
        "consecutive_empty": consecutive_empty,
        "consecutive_many": consecutive_many,
        "alternation_count": alternation_count
    }


# 测试函数
signals = ['空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '空', '多', '多', '多', '多', '多', '多', '多', '多', '多', '多', '多', '多', '多', '多', '多', '多']
result = count_consecutive_elements(signals)
print("连续出现的'空'的数量:", result["consecutive_empty"])
print("连续出现的'多'的数量:", result["consecutive_many"])
print("'空'和'多'交替出现的次数:", result["alternation_count"])