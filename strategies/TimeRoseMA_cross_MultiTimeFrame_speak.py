import copy
import sys
import os
import pandas as pd
import time
from typing import List, Dict

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tqsdk import TqApi, TqAccount, TqKq
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from signalSend_noblock import socket, sendsignal

from paths import Paths
from MyTT import CROSS, MA
from utils.utils import tradingTime


class MultiCycleTradingStrategy:
    def __init__(self, api, symbol, intervals: List[int], single_volume=1, bklimit=200, sklimit=200):
        """
        初始化多周期交易策略

        :param api: TqApi实例
        :param symbol: 交易合约
        :param intervals: 需要处理的时间周期列表（秒）
        :param single_volume: 单次交易量
        :param bklimit: 多头最大持仓量
        :param sklimit: 空头最大持仓量
        """
        self.api = api
        self.symbol = symbol
        self.intervals = sorted(intervals)
        self.single_volume = single_volume
        self.bklimit = bklimit
        self.sklimit = sklimit

        # 初始化各周期数据存储
        self.klines_dict = {}
        self.signals_dict = {}
        self.last_bar_times = {}

        self.init_data_storage()
        self.setup_logging()
        self.prepare_sound_messages()

    def init_data_storage(self):
        """初始化数据存储结构"""
        for interval in self.intervals:
            klines = self.api.get_kline_serial(self.symbol, duration_seconds=interval, data_length=8964)
            self.klines_dict[interval] = klines
            self.last_bar_times[interval] = None
            self.signals_dict[interval] = []

    def setup_logging(self):
        """设置日志"""
        try:
            userid = self.api.get_account().user_id.split('-')[0]
        except:
            userid = 'moni'

        logfilename = f'{userid}_{self.symbol}_multicycle'
        logfilename = Paths.log(logfilename)
        mylog.add(logfilename + '.log', encoding='utf-8')
        mylog.info(f'策略初始化: {self.symbol}, 周期: {self.intervals}')

    def prepare_sound_messages(self):
        """准备语音提示消息"""
        productid = self.symbol.split('.')[1]
        self.sound_messages = {
            interval: {
                'openlong': f'{productid} {interval // 60}分钟发出做多信号',
                'openshort': f'{productid} {interval // 60}分钟发出做空信号',
                'closelong': f'{productid} {interval // 60}分钟发出平多单信号',
                'closeshort': f'{productid} {interval // 60}分钟发出平空单信号'
            } for interval in self.intervals
        }

    def calculate_ma_cross_signals(self, bars, period=13):
        """计算MA交叉信号"""
        CLOSE = bars.close.values
        ups = list(CROSS(CLOSE, MA(CLOSE, period)))
        dns = list(CROSS(MA(CLOSE, period), CLOSE))

        signals = []
        for i in range(len(ups)):
            if ups[i] == 0 and dns[i] == 0:
                signals.append(0)
            else:
                if ups[i] == 1:
                    signals.append(1)
                if dns[i] == 1:
                    signals.append(-1)

        return signals

    def generate_signal_info(self, bars, period, quote, interval):
        """生成信号信息"""
        C = bars.close
        SYMBOL = bars.symbol.iloc[0]

        trmac = ma(C, period)
        ups = crossup(C, trmac)
        dns = crossdown(C, trmac)
        upslist = ups.tolist()
        dnslist = dns.tolist()
        bkdist = self.be_apart_from(upslist)
        skdist = self.be_apart_from(dnslist)

        signalnow = '空' if bkdist > skdist else '多'
        distnow = skdist if bkdist > skdist else bkdist

        signalprice = C.iloc[-distnow]
        sigfloatprofit = quote.last_price - signalprice if signalnow == '多' else signalprice - quote.last_price
        updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]

        signal = {
            '合约': SYMBOL,
            '周期': interval,
            '时间': updatetime,
            '当前信号': signalnow,
            '持续周期': distnow,
            '信号价格': signalprice,
            '现价': quote.last_price,
            '信号盈亏': sigfloatprofit
        }

        return signal, upslist, dnslist, signalnow, distnow

    def be_apart_from(self, signal_list):
        """计算信号连续距离"""
        for i in range(len(signal_list) - 1, -1, -1):
            if signal_list[i] == 1:
                return len(signal_list) - 1 - i
        return len(signal_list)

    def trade_decision(self, quote, position, interval, signal_info, upslist, dnslist):
        """根据信号做交易决策"""
        signalnow = signal_info['当前信号']
        distnow = signal_info['持续周期']

        bkvol = position.pos_long
        skvol = position.pos_short

        # 做多信号
        if upslist[-1]:
            mylog.info(f'{self.symbol} {interval}分钟发出做多信号....')
            speak_text(self.sound_messages[interval]['openlong'])

            bkprice = quote.last_price + 1
            if bkvol < self.bklimit:
                orderVol = self.single_volume
                if orderVol > 0:
                    BK(self.api, symbol=self.symbol, order_price=bkprice, volume=orderVol)
                    mylog.info(f'{self.symbol} 下多单: 数量{orderVol}, 价格{bkprice}')

            # 平空单逻辑
            if skvol > 0:
                if position.float_profit_short > 5 * skvol:
                    BP(self.api, symbol=self.symbol, order_price=bkprice, volume=self.single_volume)
                    mylog.info(f'{self.symbol} 平空单, 数量{self.single_volume}, 价格{bkprice}')

        # 做空信号
        if dnslist[-1]:
            mylog.info(f'{self.symbol} {interval}分钟发出做空信号....')
            speak_text(self.sound_messages[interval]['openshort'])

            skprice = quote.last_price - 1
            if skvol < self.sklimit:
                orderVol = self.single_volume
                if orderVol > 0:
                    SK(self.api, symbol=self.symbol, order_price=skprice, volume=orderVol)
                    mylog.info(f'{self.symbol} 下空单: 数量{orderVol}, 价格{skprice}')

            # 平多单逻辑
            if bkvol > 0:
                if position.float_profit_long > 5 * bkvol:
                    SP(self.api, symbol=self.symbol, order_price=skprice, volume=self.single_volume, today_prefer=True)
                    mylog.info(f'{self.symbol} 平多单, 数量{self.single_volume}, 价格{skprice}')

    def run(self):
        """策略主运行函数"""
        speak_text('程序开始运行')
        quote = self.api.get_quote(self.symbol)
        position = self.api.get_position(self.symbol)

        while True:
            self.api.wait_update()

            # 处理每个时间周期的信号
            for interval in self.intervals:
                klines = self.klines_dict[interval]

                # 检查是否有新K线
                if self.api.is_changing(klines.iloc[-1], "datetime"):
                    current_bar_time = klines.datetime.tolist()[-1]

                    if current_bar_time != self.last_bar_times[interval]:
                        self.last_bar_times[interval] = current_bar_time

                        # 更新K线
                        newk = klines.iloc[-2].to_frame().T
                        klines_updated = pd.concat([klines, newk], ignore_index=True)
                        self.klines_dict[interval] = klines_updated

                        # 生成信号
                        signal_info, upslist, dnslist, signalnow, distnow = self.generate_signal_info(
                            klines_updated, period=13, quote=quote, interval=interval
                        )

                        # 发送信号
                        sendsignal(socket, 'multicycle', signal_info)
                        print(f"{interval}分钟信号:", signal_info)

                        # 交易决策
                        self.trade_decision(quote, position, interval, signal_info, upslist, dnslist)


def runstrategy():
    # 可配置的多周期列表
    intervals = [60, 180, 300, 900]  # 1, 3, 5, 15分钟

    product = 'OI'
    symbol_query = product
    single_volume = 1
    bklimit = 200
    sklimit = 200

    # 初始化API
    api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
    symbol = api.query_cont_quotes(product_id=product)[0]

    # 创建并运行策略
    strategy = MultiCycleTradingStrategy(
        api,
        symbol,
        intervals=intervals,
        single_volume=single_volume,
        bklimit=bklimit,
        sklimit=sklimit
    )
    strategy.run()


if __name__ == "__main__":
    runstrategy()