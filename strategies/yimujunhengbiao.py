'''
n1: 1, 36, 9
n2:, 1,36, 26
n3, 1, 60, 52



AA:=(HHV(HIGH,120)-LLV(LOW,120))/30;

VAR1:=(2*CLOSE+HIGH+LOW)/4;

转折线:(HHV(HIGH,N1)+LLV(LOW,N1))/2,COLORRED;

基准线:(HHV(HIGH,N2)+LLV(LOW,N2))/2,COLORYELLOW,LINETHICK3;

延迟线:REFX(CLOSE,N2-1),COLORGREEN,LINETHICK2;

SPA:=REF((转折线+基准线)/2,N2-1),COLORYELLOW;

SPB:=REF((HHV(HIGH,N3)+LLV(LOW,N3))/2,N2-1),COLORYELLOW;

STICKLINE(SPA<SPB,SPA,SPB,0.0,-1),COLOR339933;

STICKLINE(SPA>=SPB,SPA,SPB,0.0,-1),COLOR0033CC;

POLYLINE(1,SPA),COLORYELLOW,LINEthICK3;

POLYLINE(1,SPB),COLORGRAY;

POLYLINE(1,基准线),COLORBLUE,LINETHICK3;

POLYLINE(1,转折线),COLORRED,LINETHICK2;
'''

# from MyTT import *
import pandas as pd
from tqsdk.tafunc import crossup,crossdown, hhv, llv, time_to_str
from myfunction import be_apart_from
from tradefuncs import *
from paths import Paths
from loguru import logger as mylog
from speaktext import speak_text
import copy


# from constants import *
def yimuturn(bars):
    N1 = 9
    HIGH = bars.high
    LOW = bars.low
    turnline = (hhv(HIGH, N1) + llv(LOW, N1)) / 2
    ups=crossup(bars.close, turnline)
    dns=crossdown(bars.close, turnline)
    return ups, dns, turnline

def signal_info(ups, dns, bars):
    C = bars.close
    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)

    if bkdist > skdist:
        signalnow = '空'
        distnow = skdist
    else:
        signalnow = '多'
        distnow = bkdist

    C = bars.close
    interval = bars.duration.iloc[-1]
    SYMBOL = bars.symbol.iloc[-1]

    signalprice = C.iloc[-distnow]
    sigfloatprofit = C.iloc[-1] - signalprice if signalnow == '多' else signalprice - C.iloc[-1]
    updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    print('时间:', updatetime, SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice,
          '现价:', C.iloc[-1],
          '信号盈亏:', sigfloatprofit)
    uplist = ups.tolist()
    average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
    print('平均信号距离：', average_signal_distance)
    print('upsignal:  ', ups.iloc[-40:].tolist())
    print('downsignal:', dns.iloc[-40:].tolist())


def  yimu_strategy(api, symbol, interval, single_volume, bklimit, sklimit):
    strategyname = 'yimu'
    acct = api.get_account()
    # disp_account(acct)
    try:
        userid = acct.user_id.split('-')[0]
    except:
        userid = 'moni'

    logfilename = Paths.log('_'.join([userid, symbol, strategyname]))

    mylog.add(logfilename + '.log', encoding='utf-8')
    savebars = False
    quote = api.get_quote(symbol)

    if '@' in symbol:
        SYMBOL = quote.underlying_symbol
    else:
        SYMBOL = symbol

    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines

    productid = SYMBOL.split('.')[1]
    minutes = str(klines_tmp.duration.iloc[0])
    soundmsg = ''.join([productid, minutes])

    # 设置语音提醒信息
    openlong_sound = soundmsg + '分钟' + '发出做多信号'
    openshort_sound = soundmsg + '分钟' + '发出做空信号'
    closelong_sound = soundmsg + '分钟' + '发出平多单信号'
    closeshort_sound = soundmsg + '分钟' + '发出平空单信号'

    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()

    position = api.get_position(SYMBOL)
    acc = api.get_account()
    bkvol = position.pos_long
    bkvol_yd = position.pos_long_his
    bkvol_td = position.pos_long_today

    skvol = position.pos_short
    skvol_yd = position.pos_short_his
    skvol_td = position.pos_short_today

    skflag = True
    bkflag = True
    spflag = True
    bpflag = True
    speakyn = True
    dayopensp = True
    dayopenbp = True

    fundavailable = acc.available
    # disp_day_info(daymean, last3mean, hlmax, homean, olmean)
    # disp_0Day_info(quote)
    # disp_account(acct)

    ups, dns, turnline = yimuturn(klines1)

    signal_info(ups, dns, klines1)
    soundmsg = ''.join([productid, minutes])

    # 设置语音提醒信息
    openlong_sound = soundmsg + '分钟' + '发出做多信号'
    openshort_sound = soundmsg + '分钟' + '发出做空信号'
    closelong_sound = soundmsg + '分钟' + '发出平多单信号'
    closeshort_sound = soundmsg + '分钟' + '发出平空单信号'


    while True:

        api.wait_update()

        if api.is_changing(daybars.iloc[-1], "datetime"):
            savebars = True
            dayopensp = True
            dayopenbp = True

            hl = daybar.high - daybar.low
            ho = daybar.high - daybar.open
            ol = daybar.open - daybar.low
            daymean = int(hl.mean())
            homean = ho.mean()
            olmean = ol.mean()
            hlmax = max(hl)
            last3mean = int(hl.iloc[-4:-1].mean())
            dayopen = quote.open
            dayhigh = quote.highest
            daylow = quote.lowest
            preclose = quote.pre_close
            dayvib = dayhigh - daylow
            gap = dayopen - preclose
            gapcover = False
            print('日线数据更新完成。。。')
            mylog.info('日线数据更新完成。。。')

        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            # disp_day_info(daymean, last3mean, hlmax, homean, olmean)
            # disp_0Day_info(quote)
            print('balance:', acct.balance, 'poslong:', position.pos_long, 'posshort:', position.pos_short,
                  'poslong float profit:', position.float_profit_long, 'posshort float profit:',
                  position.float_profit_short)

            speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                newk = newk.to_frame()
                newk = newk.T
                klines1 = pd.concat([klines1, newk], ignore_index=True)


            O = klines1.open
            H = klines1.high
            L = klines1.low
            C = klines1.close

            # hl = ema(H - L, 23).iloc[-1]
            # cover_gap = max(int(hl), 3)

            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today
            bkfreeze = position.volume_long_frozen

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today
            skfreeze = position.volume_short_frozen

            ups,dns, zhuanzhe = yimuturn(klines1)
            signal_info(ups, dns, klines1)

            # 交易部分
            # basevalue = average_signal_distance * 2
            # order_volume = (basevalue - distnow) * single_volume if distnow < average_signal_distance else single_volume

            order_volume = single_volume
            upslist=ups.tolist()
            dnslist=dns.tolist()
            if upslist[-1]:
                mylog.info('发出做多信号....')
                signalnow = '多'
                bkprice = quote.last_price + 1
                mylog.info(['bkprice', bkprice, 'skprofit', position.float_profit_short, 'skvol', skvol])

                # 下多单
                if bkvol < bklimit:
                    # orderVol = min(bklimit - bkvol, skvol - bkvol + 1)
                    # print('signalprofit:', sigfloatprofit)
                    orderVol = single_volume
                    BK(api, symbol=SYMBOL, order_price=bkprice - 5, volume=orderVol)
                    mylog.info([SYMBOL, '下多单:', '数量', orderVol, '价格', bkprice])
                else:
                    mylog.info('持仓数量超过bklimit,不再下单。')

                # 平空单
                if skvol >= single_volume and skvol >= bkvol:
                    if position.float_profit_short > 50 * skvol:  # 空单盈利
                        # BP(api, symbol=SYMBOL, order_price=bkprice, volume=skvol)
                        BP(api, symbol=SYMBOL, order_price=bkprice - 5, volume=single_volume)
                        mylog.info([SYMBOL, '平空单', '数量', single_volume, '价格', bkprice])

                    else:
                        mylog.info('空单浮动盈利不达到要求，不平仓.')
                speak_text(openlong_sound)

            if dnslist[-1]:
                mylog.info('发出做空信号....')
                signalnow = '空'
                skprice = quote.last_price - 1
                bkvol = position.pos_long
                bkvol_cost = position.open_price_long

                mylog.info(['skprice', skprice, 'bkprofit', position.float_profit_long, 'bkvol', bkvol])

                # 下空单
                print('skvol:', skvol, 'sklimit:', sklimit)
                if skvol < sklimit:
                    # orderVol = min(sklimit - skvol, bkvol + 1 - skvol)
                    orderVol = single_volume

                    SK(api, symbol=SYMBOL, order_price=skprice + 5, volume=orderVol)
                    mylog.info([SYMBOL, '下空单', '数量', orderVol, '价格', skprice])
                else:
                    mylog.info('skvol >= sklimit, no trading.....')

                # 平多单
                if bkvol >= skvol and bkvol > 0:
                    if position.float_profit_long > 2 * bkvol:  # 多单盈利
                        # SP(api, symbol=SYMBOL, order_price=skprice, volume=bkvol)
                        SP(api, symbol=SYMBOL, order_price=skprice + 5, volume=single_volume)
                        mylog.info(['ping duo dan.', 'volume', single_volume, 'price', skprice])

                    else:
                        mylog.info('float profit of long pos does not match the condition. dont cover.')
                speak_text(openshort_sound)

    # pass


def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date
    # from accounts import tgjyzjy as acct

    product = 'OI'
    interval = 60
    bklimit = 60
    sklimit = 60
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="ningyukun1,258369", disable_print=True, debug=None)
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    symbol = api.query_cont_quotes(product_id=product)[0]

    yimu_strategy(api, symbol, interval, single_volume, bklimit, sklimit)


if __name__ == "__main__":
    runstrategy()