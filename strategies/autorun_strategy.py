import time
from TimeRoseMA_cross__oi_walkquant_15s_speak import runstrategy
from tqsdk import TqApi, TqKq
from utils import tradingTime

api = TqApi(TqKq(), auth=("bigwolf,ftp123"), disable_print=True)
print(api._auth._user_name)


while True:
    # if True:
    if tradingTime():
        api = TqApi(TqKq(), auth=("bigwolf,ftp123"), disable_print=True)
        runstrategy()

    else:
        print('not in the trading time...')
        api.close()
        acct=api.get_account()
        print(acct)
        time.sleep(60)
