"""
TimeRoseMA_cross 多合约多时间周期策略
重构版本，支持同时处理多个合约，每个合约支持多个时间周期
"""

import copy
import sys
import os
import pandas as pd
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import threading
from queue import Queue

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tqsdk import TqApi, TqAccount, TqKq
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from signalSend_noblock import socket, sendsignal

try:
    from .paths import Paths
except:
    from paths import Paths

from MyTT import MA, CROSS
from utils.utils import tradingTime


@dataclass
class SymbolTimeFrameConfig:
    """合约时间周期配置"""
    symbol: str                    # 合约代码
    interval: int                  # 时间间隔（秒）
    bklimit: int = 1              # 多单限制
    sklimit: int = 1              # 空单限制
    single_volume: int = 1        # 单次交易量
    period: int = 13              # MA周期
    enabled: bool = True          # 是否启用
    
    @property
    def timeframe_name(self) -> str:
        """时间周期名称"""
        return f"{self.interval//60}m"
    
    @property
    def config_id(self) -> str:
        """配置唯一标识"""
        return f"{self.symbol}_{self.timeframe_name}"


@dataclass
class SignalInfo:
    """信号信息"""
    symbol: str
    interval: int
    signal_type: str              # '多' or '空'
    signal_price: float
    current_price: float
    signal_profit: float
    duration: int                 # 持续周期
    timestamp: str
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            '合约': self.symbol,
            '周期': self.interval,
            '时间': self.timestamp,
            '当前信号': self.signal_type,
            '持续周期': self.duration,
            '信号价格': self.signal_price,
            '现价': self.current_price,
            '信号盈亏': self.signal_profit
        }


class SymbolTimeFrameStrategy:
    """单个合约单个时间周期的策略实例"""
    
    def __init__(self, config: SymbolTimeFrameConfig, api: TqApi):
        self.config = config
        self.api = api
        self.symbol = config.symbol
        self.interval = config.interval
        
        # 初始化数据
        self.quote = None
        self.position = None
        self.klines = None
        self.klines_tmp = None
        
        # 信号状态
        self.current_signal = '多'
        self.signal_price = 0.0
        self.signal_duration = 0
        self.last_update_time = None
        
        # 语音提示
        self.sound_messages = {}
        
        # 日志
        self.logger = None
        
        self._initialize()
    
    def _initialize(self):
        """初始化策略"""
        try:
            # 获取合约信息
            self.quote = self.api.get_quote(self.symbol)
            self.position = self.api.get_position(self.symbol)
            
            # 获取K线数据
            self.klines = self.api.get_kline_serial(
                self.symbol, 
                duration_seconds=self.interval, 
                data_length=8964
            )
            self.klines_tmp = self.api.get_kline_serial(
                self.symbol, 
                self.interval, 
                10
            )
            
            # 设置语音提示
            self._setup_sound_messages()
            
            # 设置日志
            self._setup_logger()
            
            # 初始化信号
            self._calculate_initial_signal()
            
        except Exception as e:
            print(f"初始化策略失败 {self.config.config_id}: {e}")
            raise
    
    def _setup_sound_messages(self):
        """设置语音提示消息"""
        chinese_name = self.quote.instrument_name
        minutes = str(int(self.interval / 60))
        soundmsg = f"{chinese_name} {minutes}"
        
        self.sound_messages = {
            'openlong': f"{soundmsg}分钟发出做多信号",
            'openshort': f"{soundmsg}分钟发出做空信号",
            'closelong': f"{soundmsg}分钟发出平多单信号",
            'closeshort': f"{soundmsg}分钟发出平空单信号"
        }
    
    def _setup_logger(self):
        """设置日志"""
        try:
            acct = self.api.get_account()
            userid = acct.user_id.split('-')[0] if acct.user_id else 'moni'
        except:
            userid = 'moni'
        
        logfilename = f"{userid}_{self.symbol}_{self.config.timeframe_name}_ma_cross"
        logfilename = Paths.log(logfilename)
        
        # 创建独立的logger实例
        self.logger = mylog.bind(strategy=self.config.config_id)
        self.logger.add(f"{logfilename}.log", encoding='utf-8')
    
    def _calculate_initial_signal(self):
        """计算初始信号"""
        if len(self.klines) > self.config.period:
            signal_info = self._calculate_ma_cross_signal()
            if signal_info:
                self.current_signal = signal_info.signal_type
                self.signal_price = signal_info.signal_price
                self.signal_duration = signal_info.duration
    
    def _calculate_ma_cross_signal(self) -> Optional[SignalInfo]:
        """计算MA交叉信号"""
        try:
            C = self.klines.close
            trmac = ma(C, self.config.period)
            ups = crossup(C, trmac)
            dns = crossdown(C, trmac)
            
            upslist = ups.tolist()
            dnslist = dns.tolist()
            bkdist = self._be_apart_from(upslist)
            skdist = self._be_apart_from(dnslist)
            
            if bkdist > skdist:
                signal_type = '空'
                duration = skdist
            else:
                signal_type = '多'
                duration = bkdist
            
            signal_price = C.iloc[-duration]
            current_price = self.quote.last_price
            signal_profit = (current_price - signal_price) if signal_type == '多' else (signal_price - current_price)
            
            timestamp = time_to_str(self.klines.datetime.iloc[-1]).split(' ')[1].split('.')[0]
            
            return SignalInfo(
                symbol=self.symbol,
                interval=self.interval,
                signal_type=signal_type,
                signal_price=signal_price,
                current_price=current_price,
                signal_profit=signal_profit,
                duration=duration,
                timestamp=timestamp
            )
            
        except Exception as e:
            self.logger.error(f"计算信号失败: {e}")
            return None
    
    def _be_apart_from(self, signal_list: List) -> int:
        """计算距离最近信号的距离"""
        try:
            last_signal_index = None
            for i in range(len(signal_list) - 1, -1, -1):
                if signal_list[i] == 1:
                    last_signal_index = i
                    break
            
            if last_signal_index is not None:
                return len(signal_list) - 1 - last_signal_index
            else:
                return len(signal_list)
        except:
            return 0
    
    def update_klines(self) -> bool:
        """更新K线数据"""
        try:
            if self.api.is_changing(self.klines_tmp.iloc[-1], "datetime"):
                newk = self.klines_tmp.iloc[-2]
                bdt = self.klines.datetime.tolist()
                tt = newk.datetime
                
                if tt not in bdt:
                    newk_frame = newk.to_frame().T
                    self.klines = pd.concat([self.klines, newk_frame], ignore_index=True)
                    return True
            return False
        except Exception as e:
            self.logger.error(f"更新K线失败: {e}")
            return False
    
    def process_signal_update(self) -> Optional[SignalInfo]:
        """处理信号更新"""
        if self.update_klines():
            signal_info = self._calculate_ma_cross_signal()
            if signal_info:
                # 检查是否有新信号
                if (signal_info.signal_type != self.current_signal or 
                    abs(signal_info.signal_price - self.signal_price) > 0.01):
                    
                    self.current_signal = signal_info.signal_type
                    self.signal_price = signal_info.signal_price
                    self.signal_duration = signal_info.duration
                    
                    # 发送信号
                    sendsignal(socket, 'ma_cross_multi', signal_info.to_dict())
                    
                    # 语音提示
                    if signal_info.signal_type == '多':
                        speak_text(self.sound_messages['openlong'])
                    else:
                        speak_text(self.sound_messages['openshort'])
                    
                    self.logger.info(f"新信号: {signal_info.to_dict()}")
                    
                    return signal_info
        
        return None
    
    def execute_trade(self, signal_info: SignalInfo):
        """执行交易"""
        try:
            if signal_info.signal_type == '多':
                self._execute_long_trade(signal_info)
            else:
                self._execute_short_trade(signal_info)
        except Exception as e:
            self.logger.error(f"执行交易失败: {e}")
    
    def _execute_long_trade(self, signal_info: SignalInfo):
        """执行做多交易"""
        bkprice = self.quote.last_price + 1
        
        # 检查持仓限制
        if self.position.pos_long < self.config.bklimit:
            BK(self.api, symbol=self.symbol, order_price=bkprice, volume=self.config.single_volume)
            self.logger.info(f"下多单: 数量{self.config.single_volume}, 价格{bkprice}")
        
        # 平空单
        if self.position.pos_short > 0:
            if self.position.float_profit_short > 5 * self.position.pos_short:
                BP(self.api, symbol=self.symbol, order_price=bkprice, volume=self.config.single_volume)
                self.logger.info(f"平空单: 数量{self.config.single_volume}, 价格{bkprice}")
    
    def _execute_short_trade(self, signal_info: SignalInfo):
        """执行做空交易"""
        skprice = self.quote.last_price - 1
        
        # 检查持仓限制
        if self.position.pos_short < self.config.sklimit:
            SK(self.api, symbol=self.symbol, order_price=skprice, volume=self.config.single_volume)
            self.logger.info(f"下空单: 数量{self.config.single_volume}, 价格{skprice}")
        
        # 平多单
        if self.position.pos_long > 0:
            if self.position.float_profit_long > 5 * self.position.pos_long:
                SP(self.api, symbol=self.symbol, order_price=skprice, volume=self.config.single_volume, today_prefer=True)
                self.logger.info(f"平多单: 数量{self.config.single_volume}, 价格{skprice}")
    
    def get_current_signal_info(self) -> SignalInfo:
        """获取当前信号信息"""
        signal_info = self._calculate_ma_cross_signal()
        return signal_info if signal_info else SignalInfo(
            symbol=self.symbol,
            interval=self.interval,
            signal_type=self.current_signal,
            signal_price=self.signal_price,
            current_price=self.quote.last_price,
            signal_profit=0.0,
            duration=self.signal_duration,
            timestamp=time.strftime("%H:%M:%S")
        )


class MultiSymbolMultiTimeFrameManager:
    """多合约多时间周期管理器"""

    def __init__(self, api: TqApi, configs: List[SymbolTimeFrameConfig]):
        self.api = api
        self.configs = configs
        self.strategies: Dict[str, SymbolTimeFrameStrategy] = {}
        self.running = False
        self.update_queue = Queue()

        # 按合约分组配置
        self.symbol_configs: Dict[str, List[SymbolTimeFrameConfig]] = {}
        for config in configs:
            if config.symbol not in self.symbol_configs:
                self.symbol_configs[config.symbol] = []
            self.symbol_configs[config.symbol].append(config)

        self._initialize_strategies()

    def _initialize_strategies(self):
        """初始化所有策略实例"""
        print("=== 初始化多合约多时间周期策略 ===")

        for config in self.configs:
            if config.enabled:
                try:
                    # 解析合约代码
                    symbol = self._resolve_symbol(config.symbol)
                    config.symbol = symbol  # 更新为实际合约代码

                    strategy = SymbolTimeFrameStrategy(config, self.api)
                    self.strategies[config.config_id] = strategy

                    print(f"✓ 初始化策略: {config.config_id} ({symbol})")

                except Exception as e:
                    print(f"✗ 初始化策略失败: {config.config_id} - {e}")

        print(f"成功初始化 {len(self.strategies)} 个策略实例")

    def _resolve_symbol(self, symbol: str) -> str:
        """解析合约代码"""
        try:
            # 如果是产品代码，获取主力合约
            if len(symbol) <= 3 and symbol.isalpha():
                main_contract = self.api.query_cont_quotes(product_id=symbol)[0]
                print(f"产品 {symbol} -> 主力合约 {main_contract}")
                return main_contract
            else:
                return symbol
        except Exception as e:
            print(f"解析合约代码失败 {symbol}: {e}")
            return symbol

    def start(self):
        """启动策略"""
        self.running = True
        print("=== 启动多合约多时间周期策略 ===")

        try:
            while self.running:
                self.api.wait_update()
                self._process_updates()

        except KeyboardInterrupt:
            print("策略被用户中断")
        except Exception as e:
            print(f"策略运行出错: {e}")
        finally:
            self.stop()

    def _process_updates(self):
        """处理更新"""
        for strategy_id, strategy in self.strategies.items():
            try:
                # 处理信号更新
                signal_info = strategy.process_signal_update()
                if signal_info:
                    print(f"[{strategy_id}] 新信号: {signal_info.to_dict()}")

                    # 执行交易
                    strategy.execute_trade(signal_info)

                # 处理行情更新
                if self.api.is_changing(strategy.quote):
                    current_signal = strategy.get_current_signal_info()
                    # 发送实时信号更新
                    sendsignal(socket, 'ma_cross_realtime', current_signal.to_dict())

            except Exception as e:
                print(f"处理策略更新失败 {strategy_id}: {e}")

    def stop(self):
        """停止策略"""
        self.running = False
        print("策略已停止")

    def get_all_signals(self) -> Dict[str, SignalInfo]:
        """获取所有策略的当前信号"""
        signals = {}
        for strategy_id, strategy in self.strategies.items():
            signals[strategy_id] = strategy.get_current_signal_info()
        return signals

    def get_symbol_signals(self, symbol: str) -> Dict[str, SignalInfo]:
        """获取指定合约的所有时间周期信号"""
        signals = {}
        for strategy_id, strategy in self.strategies.items():
            if strategy.symbol == symbol:
                signals[strategy_id] = strategy.get_current_signal_info()
        return signals

    def print_status(self):
        """打印策略状态"""
        print("\n=== 策略状态总览 ===")

        for symbol, configs in self.symbol_configs.items():
            print(f"\n合约: {symbol}")
            for config in configs:
                if config.config_id in self.strategies:
                    strategy = self.strategies[config.config_id]
                    signal = strategy.get_current_signal_info()
                    print(f"  {config.timeframe_name}: {signal.signal_type} "
                          f"(价格: {signal.signal_price:.2f}, "
                          f"盈亏: {signal.signal_profit:.2f})")
                else:
                    print(f"  {config.timeframe_name}: 未启用")


def create_default_configs() -> List[SymbolTimeFrameConfig]:
    """创建默认配置"""
    configs = []

    # 默认合约和时间周期
    symbols = ['SH', 'OI', 'ag']
    timeframes = [60, 180, 300, 900]  # 1m, 3m, 5m, 15m

    for symbol in symbols:
        for interval in timeframes:
            config = SymbolTimeFrameConfig(
                symbol=symbol,
                interval=interval,
                bklimit=1,
                sklimit=1,
                single_volume=1,
                period=13,
                enabled=True
            )
            configs.append(config)

    return configs


def create_custom_configs(symbol_timeframe_map: Dict[str, List[int]],
                         default_params: Dict = None) -> List[SymbolTimeFrameConfig]:
    """
    创建自定义配置

    Args:
        symbol_timeframe_map: 合约到时间周期的映射，如 {'SH': [60, 300], 'OI': [180, 900]}
        default_params: 默认参数
    """
    if default_params is None:
        default_params = {
            'bklimit': 1,
            'sklimit': 1,
            'single_volume': 1,
            'period': 13,
            'enabled': True
        }

    configs = []
    for symbol, intervals in symbol_timeframe_map.items():
        for interval in intervals:
            config = SymbolTimeFrameConfig(
                symbol=symbol,
                interval=interval,
                **default_params
            )
            configs.append(config)

    return configs


def run_multi_symbol_multi_timeframe_strategy(
    configs: List[SymbolTimeFrameConfig] = None,
    auth: str = "quant_ggh,Qiai1301"
):
    """
    运行多合约多时间周期策略

    Args:
        configs: 策略配置列表，如果为None则使用默认配置
        auth: TqSDK认证信息
    """

    if configs is None:
        configs = create_default_configs()

    print("=== 多合约多时间周期策略启动 ===")
    print(f"配置数量: {len(configs)}")

    # 按合约分组显示配置
    symbol_groups = {}
    for config in configs:
        if config.symbol not in symbol_groups:
            symbol_groups[config.symbol] = []
        symbol_groups[config.symbol].append(config.timeframe_name)

    for symbol, timeframes in symbol_groups.items():
        print(f"合约 {symbol}: {', '.join(timeframes)}")

    # 初始化API
    api = TqApi(TqKq(), auth=auth, disable_print=True)

    try:
        # 创建管理器
        manager = MultiSymbolMultiTimeFrameManager(api, configs)

        # 显示初始状态
        manager.print_status()

        # 启动策略
        manager.start()

    except Exception as e:
        print(f"策略运行失败: {e}")
    finally:
        api.close()


def run_single_symbol_multi_timeframe(
    symbol: str,
    timeframes: List[int] = None,
    auth: str = "quant_ggh,Qiai1301",
    **kwargs
):
    """
    运行单个合约的多时间周期策略

    Args:
        symbol: 合约代码
        timeframes: 时间周期列表（秒），默认为[60, 180, 300, 900]
        auth: TqSDK认证信息
        **kwargs: 其他配置参数
    """

    if timeframes is None:
        timeframes = [60, 180, 300, 900]  # 1m, 3m, 5m, 15m

    # 创建配置
    configs = []
    for interval in timeframes:
        config = SymbolTimeFrameConfig(
            symbol=symbol,
            interval=interval,
            bklimit=kwargs.get('bklimit', 1),
            sklimit=kwargs.get('sklimit', 1),
            single_volume=kwargs.get('single_volume', 1),
            period=kwargs.get('period', 13),
            enabled=True
        )
        configs.append(config)

    run_multi_symbol_multi_timeframe_strategy(configs, auth)


def run_multi_symbol_single_timeframe(
    symbols: List[str],
    interval: int = 300,
    auth: str = "quant_ggh,Qiai1301",
    **kwargs
):
    """
    运行多个合约的单时间周期策略

    Args:
        symbols: 合约代码列表
        interval: 时间间隔（秒），默认5分钟
        auth: TqSDK认证信息
        **kwargs: 其他配置参数
    """

    # 创建配置
    configs = []
    for symbol in symbols:
        config = SymbolTimeFrameConfig(
            symbol=symbol,
            interval=interval,
            bklimit=kwargs.get('bklimit', 1),
            sklimit=kwargs.get('sklimit', 1),
            single_volume=kwargs.get('single_volume', 1),
            period=kwargs.get('period', 13),
            enabled=True
        )
        configs.append(config)

    run_multi_symbol_multi_timeframe_strategy(configs, auth)


# 兼容原有ma_cross函数的包装器
def ma_cross_multi(api, symbol_configs: List[Tuple[str, int]],
                   single_volume=1, bklimit=1, sklimit=1, period=13):
    """
    兼容原有ma_cross函数的多合约版本

    Args:
        api: TqApi实例
        symbol_configs: [(symbol, interval), ...] 合约和时间周期配置
        single_volume: 单次交易量
        bklimit: 多单限制
        sklimit: 空单限制
        period: MA周期
    """

    configs = []
    for symbol, interval in symbol_configs:
        config = SymbolTimeFrameConfig(
            symbol=symbol,
            interval=interval,
            bklimit=bklimit,
            sklimit=sklimit,
            single_volume=single_volume,
            period=period,
            enabled=True
        )
        configs.append(config)

    manager = MultiSymbolMultiTimeFrameManager(api, configs)
    manager.start()


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1]

        if mode == "default":
            # 运行默认配置
            run_multi_symbol_multi_timeframe_strategy()

        elif mode == "single_symbol":
            # 运行单个合约多时间周期
            symbol = sys.argv[2] if len(sys.argv) > 2 else "SH"
            run_single_symbol_multi_timeframe(symbol)

        elif mode == "multi_symbol":
            # 运行多个合约单时间周期
            symbols = sys.argv[2:] if len(sys.argv) > 2 else ["SH", "OI", "ag"]
            run_multi_symbol_single_timeframe(symbols)

        elif mode == "custom":
            # 自定义配置示例
            custom_map = {
                'SH': [60, 300],      # SH: 1分钟和5分钟
                'OI': [180, 900],     # OI: 3分钟和15分钟
                'ag': [300]           # ag: 5分钟
            }
            configs = create_custom_configs(custom_map)
            run_multi_symbol_multi_timeframe_strategy(configs)

        else:
            print("用法:")
            print("  python TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py default")
            print("  python TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py single_symbol [SYMBOL]")
            print("  python TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py multi_symbol [SYMBOL1] [SYMBOL2] ...")
            print("  python TimeRoseMA_cross_MultiSymbol_MultiTimeFrame.py custom")
    else:
        # 默认运行
        run_multi_symbol_multi_timeframe_strategy()
