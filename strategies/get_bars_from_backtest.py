from tqsdk import TqApi, TqBacktest, TqSim
from datetime import date, datetime, timedelta

def get_future_data(contract_symbol, start_date):
    """
    获取从指定日期到当前日期的期货合约数据
    :param contract_symbol: 合约类型，如 "OI"（菜籽油）、"rb"（螺纹钢）
    :param start_date: 开始日期，格式为 "YYYY-MM-DD"
    :param auth: 快期账户认证信息，格式为 (用户名, 密码)
    """
    # 将开始日期转换为 datetime 对象
    start_dt = datetime.strptime(start_date, "%Y-%m-%d").date()
    end_dt = date.today()  # 结束日期为当前日期

    # 创建回测 API 实例，并传入快期账户认证信息
    api = TqApi(TqSim(), backtest=TqBacktest(start_dt=start_dt, end_dt=end_dt), auth=TqAuth("smartmanp", "ftp123"))

    # 获取主力合约的代码
    symbol = f"KQ.m@{contract_symbol}.MAIN"  # 主力合约
    duration_seconds = 86400  # 日K线

    # 获取K线数据
    klines = api.get_kline_serial(symbol, duration_seconds)

    # 打印从指定日期到当前日期的K线数据
    print(f"从 {start_date} 到 {end_dt} 的 {contract_symbol} 主力合约日K线数据：")
    for kline in klines:
        kline_date = datetime.fromtimestamp(kline["datetime"] / 1e9).strftime("%Y-%m-%d")
        print(f"日期: {kline_date}, 开盘价: {kline['open']}, 最高价: {kline['high']}, 最低价: {kline['low']}, 收盘价: {kline['close']}")

    # 关闭 API
    api.close()

# 示例：获取菜籽油（OI）从 2023-10-01 到当前日期的数据
# 替换为你的快期账户用户名和密码
from tqsdk import TqAuth
get_future_data("OI", "2023-10-01")