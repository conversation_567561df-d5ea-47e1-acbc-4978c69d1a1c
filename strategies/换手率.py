from tqsdk import *

api = TqApi(TqKq(), auth="quant_zcn,Qiai1301")

symbol_list = api.query_cont_quotes()

# quotes=api.get_quote(symbols)
exchange_ratios = list()
async def demo(SYMBOL):
    quote = await api.get_quote(SYMBOL)  # 支持 await 异步，这里会订阅合约，等到收到合约行情才返回
    try:
        换手率 = (quote.volume/quote.open_interest)*100
        exchange_ratios.append([SYMBOL,换手率])
        print(f"quote: {SYMBOL}", quote.datetime, quote.last_price, '换手率：', 换手率)
    except:
        换手率 = 0
        print(SYMBOL)



    # 这一行就会打印出合约的最新行情
    async with api.register_update_notify() as update_chan:
        async for _ in update_chan:
            if api.is_changing(quote):
                换手率 = (quote.volume / quote.open_interest) * 100
                exchange_ratios.append([SYMBOL, 换手率])
                # exchange_ratios.sort()
                print(f"quote: {SYMB<PERSON>}", quote.datetime, quote.last_price, '换手率：', 换手率)

                # print(SYMBOL, quote.datetime, quote.last_price, '换手率：', (quote.volume/quote.open_interest)*100)
            # ... 策略代码 ...



for symbol in symbol_list:
    api.create_task(demo(symbol))  # 为每个合约创建异步任务

while True:
    api.wait_update()
    print('updated...')
    print(exchange_ratios)


