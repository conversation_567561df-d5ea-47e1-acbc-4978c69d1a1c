from MyTT import *
from MyTT_plus import HHV, LLV, REF_plus

from tqsdk import TqA<PERSON>, TqAccount, TqKq
from pytdx.exhq import TdxExHq_API
from pytdx.params import TDXParams
import time


def weiswave_calculate(bars):
    HIGH = bars.high
    LOW = bars.low
    CLOSE = bars.close

    X_34 = IF(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
    X_35 = IF(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)
    X_36 = HHV(X_34, 3)
    X_37 = LLV(X_35, 3)
    X_38 = BARSLAST(X_37 < REF(X_37, 1))
    X_39 = BARSLAST(X_36 > REF(X_36, 1))

    X_38_1 = X_38 + 1
    X_39_1 = X_39 + 1

    X_40 = IF(HHV(X_34, X_39 + 1) == X_34, 1, 0)
    X_41 = IF(LLV(X_35, X_38 + 1) == X_35, 1, 0)
    X_42 = BARSLAST(X_40)

    X_43 = REF_plus(LLV(X_35, 3), X_42)
    X_44 = BARSLAST(X_41)

    X_45 = REF_plus(HHV(X_34, 3), X_44)

    X_46 = VALUEWHEN(X_45 > 0, X_45)
    X_47 = VALUEWHEN(X_43 > 0, X_43)
    X_48 = IF(CLOSE > X_46, (-1), IF(CLOSE < X_47, 1, 0))
    X_49 = VALUEWHEN(X_48 != 0, X_48)
    X_50 = BARSLAST(CROSS(0, X_49))
    X_51 = BARSLAST(CROSS(X_49, 0))

    X_52 = IF(X_49 == 1, IF(LLV(X_46, X_51 + 1) == X_46, X_46, LLV(X_46, X_51 + 1)), X_46)
    X_53 = IF(X_49 == (-1), IF(HHV(X_47, X_50 + 1) == X_47, X_47, HHV(X_47, X_50 + 1)), X_47)

    X_54 = IF(CLOSE > X_52, (-1), IF(CLOSE < X_53, 1, 0))
    X_55 = VALUEWHEN(X_54 != 0, X_54)

    X_56 = BARSLAST(CROSS(0, X_54))
    X_57 = BARSLAST(CROSS(X_54, 0))
    X_58 = IF(X_55 == 1, IF(LLV(X_52, X_57 + 1) == X_52, X_52, LLV(X_52, X_57 + 1)), IF(HHV(X_53, X_56 + 1) == X_53, X_53, HHV(X_53, X_56 + 1)))

    多头止损 = IF(X_55 < 0, X_58, [0]).tolist()
    空头止损 = IF(X_55 > 0, X_58, [0]).tolist()

    print(多头止损[-30:])
    print(空头止损[-30:])
    if 多头止损[-1] > 0:
        print('当前信号多：', '止损:', 多头止损[-1])
    elif 空头止损[-1] > 0:
        print('当前信号空：', '止损:', 空头止损[-1])

    return 多头止损, 空头止损


def gen_WeisWave_signals(ups, dns, bars):
    signals = []
    for i in range(len(ups)):
        if i == 0:
            tmpsignal = 0

        else:
            # if i < len(ups):
            if ups[i-1] == 0 and ups[i] > 0:
                tmpsignal = ['buy', bars.close.iloc[-1], ups[i]]

            else:
                tmpsignal = 0
        signals.append(tmpsignal)

    return signals


if __name__ == "__main__":

    product = 'OI'
    symbol = product
    interval = 60
    bklimit = 100
    sklimit = 100
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    # api = TqApi(TqKq(), auth="wolfquant,ftp123")
    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")
    symbol = api.query_cont_quotes(product_id=product)[0]

    klines = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964)
    api.close()

    klines.to_pickle(symbol + str(interval) + '.pkl')
    # klines = pd.read_pickle('CZCE.OI40160.pkl')
    ups, dns = weiswave_calculate(klines)
    signals = gen_WeisWave_signals(ups, dns, klines)

    api = TdxExHq_API(heartbeat=True)

    with api.connect('47.106.209.131', 7727):
        # print(api.to_df(api.get_instrument_bars(TDXParams.KLINE_TYPE_EXHQ_1MIN, 74, 'BABA')).tail())
        # print(api.to_df(api.get_instrument_bars(TDXParams.KLINE_TYPE_DAILY, 31, '00001')).tail())
        bars1 = api.to_df(api.get_instrument_bars(TDXParams.KLINE_TYPE_1MIN, 28, 'OI2401'))
        bars5 = api.to_df(api.get_instrument_bars(TDXParams.KLINE_TYPE_5MIN, 28, 'OI2401'))
        bars15 = api.to_df(api.get_instrument_bars(TDXParams.KLINE_TYPE_15MIN, 28, 'OI2401'))

        while True:
            bars = api.to_df(api.get_instrument_bars(TDXParams.KLINE_TYPE_1MIN, 28, 'OI2401'))
            print(bars.datetime.iloc[-1])
            ups, dns = weiswave_calculate(bars)
            signals = gen_WeisWave_signals(ups, dns, bars)

            time.sleep(interval)
