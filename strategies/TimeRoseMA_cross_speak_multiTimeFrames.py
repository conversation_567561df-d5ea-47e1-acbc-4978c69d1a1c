import copy
import sys
import os
import pandas as pd
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from signalSend import socket, sendsignal
from dataclasses import dataclass
from time import sleep
import time
try:
    from .paths import Paths
except:
    from paths import Paths

from typing import List
speak_text('程序开始运行')


@dataclass
class TimeframeConfig:
    interval: int
    period: int = 13
    single_volume: int = 1
    bklimit: int = 200
    sklimit: int = 200


class TimeframeStrategy:
    def __init__(self, api, symbol: str, config: TimeframeConfig):
        self.api = api
        self.symbol = symbol
        self.config = config
        self.klines = api.get_kline_serial(symbol, duration_seconds=config.interval, data_length=8964)
        self.klines_tmp = api.get_kline_serial(symbol, config.interval, 10)
        self.klines1 = copy.deepcopy(self.klines)
        self.signalnow = ''
        self.distnow = 0
        self.signalprice = 0
        self.profitlosslist = []
        self.savebars = True

    def calculate_signals(self, quote):
        C = self.klines1.close
        trmac = ma(C, self.config.period)
        ups = crossup(C, trmac)
        dns = crossdown(C, trmac)
        upslist = ups.tolist()
        dnslist = dns.tolist()
        bkdist = be_apart_from(upslist)
        skdist = be_apart_from(dnslist)

        if bkdist > skdist:
            self.signalnow = '空'
            self.distnow = skdist
        else:
            self.signalnow = '多'
            self.distnow = bkdist

        self.signalprice = C.iloc[-self.distnow]
        sigfloatprofit = quote.last_price - self.signalprice if self.signalnow == '多' else self.signalprice - quote.last_price

        return {'合约': self.symbol,
                '周期': self.config.interval,
                '时间': time_to_str(self.klines1.datetime.iloc[-1]).split(' ')[1].split('.')[0],
                '当前信号': self.signalnow,
                '持续周期': self.distnow,
                '信号价格': self.signalprice,
                '现价': C.iloc[-1],
                '信号盈亏': sigfloatprofit}

    def handle_trades(self, quote, position):
        if not (self.klines1.iloc[-1] and quote):
            return

        upslist = crossup(self.klines1.close, ma(self.klines1.close, self.config.period)).tolist()
        dnslist = crossdown(self.klines1.close, ma(self.klines1.close, self.config.period)).tolist()

        if upslist[-1]:
            self._handle_long_signal(quote, position)
        if dnslist[-1]:
            self._handle_short_signal(quote, position)

    def _handle_long_signal(self, quote, position):
        bkprice = quote.last_price + 1
        if position.pos_long < self.config.bklimit:
            BK(self.api, symbol=self.symbol, order_price=bkprice, volume=self.config.single_volume)

        if position.pos_short > 0:
            if position.float_profit_short > 5 * position.pos_short or \
                    (quote.last_price - self.signalprice if self.signalnow == '多' else self.signalprice - quote.last_price) > 5:
                BP(self.api, symbol=self.symbol, order_price=bkprice, volume=self.config.single_volume)

    def _handle_short_signal(self, quote, position):
        skprice = quote.last_price - 1
        if position.pos_short < self.config.sklimit:
            SK(self.api, symbol=self.symbol, order_price=skprice, volume=self.config.single_volume)

        if position.pos_long > 0:
            if position.float_profit_long > 5 * position.pos_long:
                SP(self.api, symbol=self.symbol, order_price=skprice, volume=self.config.single_volume, today_prefer=True)

    def update_klines(self, new_bar):
        if new_bar.datetime in self.klines1.datetime.tolist():
            return False
        newk = new_bar.to_frame().T
        self.klines1 = pd.concat([self.klines1, newk], ignore_index=True)
        return True


class MultiTimeframeTrader:
    def __init__(self, api, symbol: str, timeframes: List[TimeframeConfig]):
        self.api = api
        self.symbol = symbol
        self.strategies = {
            tf.interval: TimeframeStrategy(api, symbol, tf)
            for tf in timeframes
        }
        self.quote = api.get_quote(symbol)
        self.position = api.get_position(symbol)
        self._setup_logging()

    def _setup_logging(self):
        userid = self._get_user_id()
        logfilename = '_'.join([userid, self.symbol, 'macross'])
        logfilename = Paths.log(logfilename)
        mylog.add(logfilename + '.log', encoding='utf-8')

    def _get_user_id(self):
        try:
            return self.api.get_account().user_id.split('-')[0]
        except:
            return 'moni'

    def run(self):
        speak_text('程序开始运行')
        while True:
            self.api.wait_update()

            for strategy in self.strategies.values():
                if self.api.is_changing(strategy.klines_tmp.iloc[-1], "datetime"):
                    signal = strategy.calculate_signals(self.quote)
                    strategy.handle_trades(self.quote, self.position)
                    sendsignal(socket, 'macross', signal)

                if self.api.is_changing(self.quote):
                    signal = strategy.calculate_signals(self.quote)
                    sendsignal(socket, 'macross', signal)


def runstrategy():
    from tqsdk import TqApi, TqKq

    product = 'OI'
    timeframes = [
        TimeframeConfig(interval=60),
        TimeframeConfig(interval=180),
        TimeframeConfig(interval=300),
        TimeframeConfig(interval=900)
    ]

    api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
    symbol = api.query_cont_quotes(product_id=product)[0]

    trader = MultiTimeframeTrader(api, symbol, timeframes)
    trader.run()


if __name__ == "__main__":
    runstrategy()