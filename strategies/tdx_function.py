from typing import Union, Callable
import pandas as pd
import numpy as np


def HHV(X: pd.Series, N: pd.Series | int | float) -> pd.Series:
    """
    Calculate the maximum value of X in the past N periods (including current period)

    Parameters:
    -----------
    X : pd.Series
        Input series to calculate maximum values from
    N : pd.Series or int or float
        Number of periods to look back. Can be variable (pd.Series) or constant (int/float)

    Returns:
    --------
    pd.Series
        Series containing maximum values

    Notes:
    ------
    1. N includes current bar
    2. If N=0, calculate from first valid value
    3. If N is valid but current bars < N, use actual available bars
    4. If N is NaN, return NaN
    5. N can be variable (pd.Series)
    """

    # Initialize result series with same index as X
    result = pd.Series(index=X.index, dtype=float)

    # Handle case where N is a series (variable lookback)
    if isinstance(N, pd.Series):
        for i in range(len(X)):
            current_n = N.iloc[i]

            # Handle NaN in N
            if pd.isna(current_n):
                result.iloc[i] = np.nan
                continue

            # Convert to int and handle N=0
            current_n = int(current_n) if current_n > 0 else i + 1

            # Get the lookback window
            start_idx = max(0, i - current_n + 1)
            window = X.iloc[start_idx:i + 1]

            # Calculate maximum
            result.iloc[i] = window.max()

    # Handle case where N is constant
    else:
        # Handle NaN N
        if pd.isna(N):
            return pd.Series(np.nan, index=X.index)

        # Convert to int and handle N=0
        N = int(N) if N > 0 else len(X)

        # Calculate rolling maximum
        result = X.rolling(window=N, min_periods=1).max()

    return result


def LLV(X: pd.Series, N: pd.Series | int | float) -> pd.Series:
    """
    Calculate the minimum value of X in the past N periods (including current period)

    Parameters:
    -----------
    X : pd.Series
        Input series to calculate minimum values from
    N : pd.Series or int or float
        Number of periods to look back. Can be variable (pd.Series) or constant (int/float)

    Returns:
    --------
    pd.Series
        Series containing minimum values

    Notes:
    ------
    1. N includes current bar
    2. If N=0, calculate from first valid value
    3. If N is valid but current bars < N, use actual available bars
    4. If N is NaN, return NaN
    5. N can be variable (pd.Series)
    """

    # Initialize result series with same index as X
    result = pd.Series(index=X.index, dtype=float)

    # Handle case where N is a series (variable lookback)
    if isinstance(N, pd.Series):
        for i in range(len(X)):
            current_n = N.iloc[i]

            # Handle NaN in N
            if pd.isna(current_n):
                result.iloc[i] = np.nan
                continue

            # Convert to int and handle N=0
            current_n = int(current_n) if current_n > 0 else i + 1

            # Get the lookback window
            start_idx = max(0, i - current_n + 1)
            window = X.iloc[start_idx:i + 1]

            # Calculate minimum
            result.iloc[i] = window.min()

    # Handle case where N is constant
    else:
        # Handle NaN N
        if pd.isna(N):
            return pd.Series(np.nan, index=X.index)

        # Convert to int and handle N=0
        N = int(N) if N > 0 else len(X)

        # Calculate rolling minimum
        result = X.rolling(window=N, min_periods=1).min()

    return result




def REF(X: pd.Series, N: pd.Series | int | float) -> pd.Series:
    """
    Reference value from N periods ago

    Parameters:
    -----------
    X : pd.Series
        Input series
    N : pd.Series or int or float
        Number of periods to look back

    Returns:
    --------
    pd.Series
        Series containing values from N periods ago
    """
    result = pd.Series(index=X.index, dtype=float)

    # Handle variable N
    if isinstance(N, pd.Series):
        for i in range(len(X)):
            current_n = N.iloc[i]

            # Handle NaN N
            if pd.isna(current_n):
                result.iloc[i] = np.nan
                continue

            # Handle N = 0
            if current_n == 0:
                result.iloc[i] = X.iloc[i]
                continue

            # Convert N to int
            current_n = int(current_n)

            # Check if we have enough data
            if i < current_n:
                result.iloc[i] = np.nan
            else:
                result.iloc[i] = X.iloc[i - current_n]

    # Handle constant N
    else:
        # Handle NaN N
        if pd.isna(N):
            return pd.Series(np.nan, index=X.index)

        # Handle N = 0
        if N == 0:
            return X.copy()

        # Convert to int and shift
        N = int(N)
        result = X.shift(N)

    return result


def BARSLAST(COND: pd.Series) -> pd.Series:
    """
    Number of bars since last condition was true

    Parameters:
    -----------
    COND : pd.Series
        Boolean series representing condition

    Returns:
    --------
    pd.Series
        Number of bars since condition was last true
    """
    result = pd.Series(index=COND.index, dtype=float)
    last_true = -1

    for i in range(len(COND)):
        if COND.iloc[i]:
            result.iloc[i] = 0
            last_true = i
        else:
            if last_true == -1:
                result.iloc[i] = np.nan
            else:
                result.iloc[i] = i - last_true

    return result


def VALUEWHEN(COND: pd.Series, X: pd.Series) -> pd.Series:
    """
    Get value of X when condition was last true

    Parameters:
    -----------
    COND : pd.Series
        Boolean series representing condition
    X : pd.Series
        Values to track

    Returns:
    --------
    pd.Series
        Values of X when condition was last true
    """
    result = pd.Series(index=COND.index, dtype=float)
    last_value = np.nan

    for i in range(len(COND)):
        if COND.iloc[i]:
            last_value = X.iloc[i]
        result.iloc[i] = last_value

    return result






class TradingContext:
    """
    Context class to store variable references for self-referencing calculations
    """

    def __init__(self):
        self.variables = {}


class IF:
    """
    Class implementation of IF function that supports self-referencing
    """

    def __init__(self, context: TradingContext = None):
        self.context = context if context is not None else TradingContext()

    def __call__(self,
                 COND: Union[pd.Series, bool],
                 A: Union[pd.Series, float, int, bool, Callable],
                 B: Union[pd.Series, float, int, bool, Callable]) -> pd.Series:
        """
        IF function implementation: if COND then A else B

        Parameters:
        -----------
        COND : pd.Series or bool
            Condition to evaluate
        A : pd.Series, number, bool, or callable
            Value to return when condition is True
        B : pd.Series, number, bool, or callable
            Value to return when condition is False

        Returns:
        --------
        pd.Series
            Result of the IF operation
        """
        # Convert scalar COND to Series if needed
        if isinstance(COND, (bool, int, float)):
            COND = pd.Series([COND] * len(next(x for x in [A, B]
                                               if isinstance(x, pd.Series))))

        result = pd.Series(index=COND.index, dtype=float)

        # Handle self-referencing through callables
        if callable(A):
            A = A()
        if callable(B):
            B = B()

        # Convert scalar A and B to Series if needed
        if isinstance(A, (bool, int, float)):
            A = pd.Series([A] * len(COND))
        if isinstance(B, (bool, int, float)):
            B = pd.Series([B] * len(COND))

        # Calculate result iteratively to handle potential self-references
        for i in range(len(COND)):
            if pd.isna(COND.iloc[i]):
                result.iloc[i] = np.nan
            else:
                result.iloc[i] = A.iloc[i] if COND.iloc[i] else B.iloc[i]

        return result


def create_if_function():
    """
    Create an IF function instance with a new context
    """
    context = TradingContext()
    return IF(context)


def CROSS(series1, series2):
    """
    Helper function to detect crossover between two series or between a series and a scalar
    """
    # Convert scalar to Series if needed
    if isinstance(series1, (int, float)):
        if isinstance(series2, pd.Series):
            series1 = pd.Series([series1] * len(series2), index=series2.index)
    elif isinstance(series2, (int, float)):
        if isinstance(series1, pd.Series):
            series2 = pd.Series([series2] * len(series1), index=series1.index)

    return (series1 > series2) & (series1.shift(1) <= series2.shift(1))



