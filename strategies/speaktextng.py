
try:
    import pyttsx3
    ttsengine = pyttsx3.Speaker()
    ttsengine.setProperty('voice', 'zh')

    def speak_text(text):
        if text:
            ttsengine.say(text)

except:
    def speak_text(text):
        if text:
            print(text)


if __name__ == "__main__":
    speak_text('安装了语音模块吗?')
# {'pty': '5', 'language': 'cmn', 'age': '--', 'gender': 'M', 'voice_name': 'Chinese_(Mandarin)', 'file': 'sit/cmn'}
