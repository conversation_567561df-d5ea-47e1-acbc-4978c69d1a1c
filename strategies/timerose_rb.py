import copy

from baseStrategy import StrategyBase
from tqsdk.tafunc import crossup, crossdown, time_to_str, ema, trma, ma
from myfunction import be_apart_from
from loguru import logger as mylog




class TimeRose_RB(StrategyBase):

    def __init__(self, api, symbol, interval, bklimit, sklimit, single_volume):
        super(TimeRose_RB, self).__init__(api, symbol, interval, bklimit, sklimit, single_volume)

        self.klines0 = self.api.get_kline_serial(self.symbol, duration_seconds=self.interval, data_length=8964)
        self.klines_tmp = self.api.get_kline_serial(self.symbol, duration_seconds=self.interval, data_length=10)
        self.klines = copy.deepcopy(self.klines0)

        del self.klines0
        self.quote = api.get_quote(self.symbol)

    def indicator_calculate(self, bars):
        C=bars.close
        period = 13


        trmac = ma(C, period)
        ups = crossup(C, trmac)
        dns = crossdown(C, trmac)
        upslist = ups.tolist()
        dnslist = dns.tolist()
        bkdist = be_apart_from(upslist)
        skdist = be_apart_from(dnslist)
        if bkdist > skdist:
            signalnow = 'SK'
            distnow = skdist
        else:
            signalnow = 'BK'
            distnow = bkdist

        signalprice = C.iloc[-distnow]
        sigfloatprofit = self.quote.last_price - signalprice if signalnow == 'BK' else signalprice - self.quote.last_price
        # print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)
        uplist = ups.tolist()
        average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
        print('平均信号距离：', average_signal_distance)
        print('upsignal:  ', ups.iloc[-40:].tolist())
        print('downsignal:', dns.iloc[-40:].tolist())

        return uplist


    def run(self):
        while True:
            self.api.wait_update()

            if self.api.is_changing(self.klines_tmp.iloc[-1], "datetime"):

                newk = self.klines_tmp.iloc[-2]
                bdt = self.klines.datetime.tolist()
                tt = newk.datetime

                if tt in bdt:
                    mylog.info('发现重复数据, 跳过...', time_to_str(tt))
                else:
                    self.klines = self.klines.append(newk)

                self.indicator_calculate(self.klines)



if __name__ == '__main__':
    print('let us begin....')

    from tqsdk import TqApi, TqAccount
    from datetime import date
    from accounts_zjy import cyzjy as acct

    product = 'CZCE.OI209'
    product = 'SHFE.ag2212'
    symbol = product
    interval = 60
    bklimit = 3
    sklimit = 3
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]


    strategy=TimeRose_RB(api, symbol, interval, bklimit, sklimit, single_volume)
    strategy.run()
