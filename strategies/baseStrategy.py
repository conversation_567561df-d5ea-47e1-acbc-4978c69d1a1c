'''
策略参数：
合约：名称，代码，最小变动价位，合约乘数， 上市日期，下市日期
账号：名称，代码，资金，仓位
策略：名称，代码，参数

'''





class StrategyBase:
    def __init__(self, api, symbol, interval, bk_limit, sk_limit, single_volume):
        self.api = api
        self.symbol = symbol
        self.interval = interval
        self.bk_limit = bk_limit
        self.sk_limit = sk_limit
        self.single_volume = single_volume

        #get symbol info
        # self.symbol_info=self.api.query_


    def bk(self, volume, price):
        orderid = self.api.insert_order(self.symbol, direction='BUY', offset='OPEN', limit_price=price, volume=volume)
        return orderid

    def sk(self, volume, price):
        orderid = self.api.insert_order(self.symbol, direction='SELL', offset='OPEN', limit_price=price, volume=volume)
        return orderid

    def bp(self,volume, price, preferToday=False):
        exchangeid=self.symbol.split('.')[0]

        if exchangeid in ['DCE', 'CZCE']: # zhengzhou and dalian exchange
            orderid=self.api.insert_order(self.symbol, direction='BUY', offset='CLOSE', limit_price=price, volume=volume)

        elif exchangeid in ['SHFE', 'INE', 'CFFEX']:
            position = self.api.get_position(self.symbol)

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today
            skfreeze = position.volume_short_frozen
            skvol_available = skvol - skfreeze

            if preferToday:
                if skvol_td>=volume:
                    orderid=self.api.insert_order(self.symbol, direction='BUY', offset='CLOSETODAY', limit_price=price, volume=volume)
                else:
                    if skvol_td>0:
                        orderid=self.api.insert_order(self.symbol, direction='BUY', offset='CLOSETODAY', limit_price=price, volume=skvol_td)
                        orderid1=self.api.insert_order(self.symbol, direction='BUY', offset='CLOSETODAY', limit_price=price, volume=volume-skvol_td)
                    else:
                        orderid = self.api.insert_order(self.symbol, direction='BUY', offset='CLOSE', limit_price=price, volume=skvol_td)
            else:
                if skvol_yd >= volume:
                    orderid = self.api.insert_order(self.symbol, direction='BUY', offset='CLOSE', limit_price=price, volume=volume)
                else:
                    if skvol_yd>0:
                        orderid = self.api.insert_order(self.symbol, direction='BUY', offset='CLOSE', limit_price=price, volume=skvol_yd)
                        orderid1= self.api.insert_order(self.symbol, direction='BUY', offset='CLOSETODAY', limit_price=price, volume=volume-skvol_yd)
                    else:
                        orderid = self.api.insert_order(self.symbol, direction='BUY', offset='CLOSETODAY', limit_price=price, volume=volume)



        else:
            print('wrong exchangeid.')
            return None

        return orderid

    def sp(self, volume, price, preferToday=False):
        exchangeid = self.symbol.split('.')[0]

        if exchangeid in ['DCE', 'CZCE']:  # zhengzhou and dalian exchange
            orderid = self.api.insert_order(self.symbol, direction='BUY', offset='CLOSE', limit_price=price,
                                            volume=volume)

        elif exchangeid in ['SHFE', 'INE', 'FEEX']:
            position = self.api.get_position(self.symbol)

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today
            skfreeze = position.volume_short_frozen
            skvol_available = skvol - skfreeze
            if preferToday:
                if skvol_td >= volume:
                    orderid = self.api.insert_order(self.symbol, direction='SELL', offset='CLOSETODAY',
                                                    limit_price=price, volume=volume)
                else:
                    if skvol_td > 0:
                        orderid = self.api.insert_order(self.symbol, direction='SELL', offset='CLOSETODAY',
                                                        limit_price=price, volume=skvol_td)
                        orderid1 = self.api.insert_order(self.symbol, direction='SELL', offset='CLOSETODAY',
                                                         limit_price=price, volume=volume - skvol_td)
                    else:
                        orderid = self.api.insert_order(self.symbol, direction='SELL', offset='CLOSE', limit_price=price,
                                                        volume=skvol_td)
            else:
                if skvol_yd >= volume:
                    orderid = self.api.insert_order(self.symbol, direction='SELL', offset='CLOSE', limit_price=price,
                                                    volume=volume)
                else:
                    if skvol_yd > 0:
                        orderid = self.api.insert_order(self.symbol, direction='SELL', offset='CLOSE', limit_price=price,
                                                        volume=skvol_yd)
                        orderid1 = self.api.insert_order(self.symbol, direction='SELL', offset='CLOSETODAY',
                                                         limit_price=price, volume=volume - skvol_yd)
                    else:
                        orderid = self.api.insert_order(self.symbol, direction='SELL', offset='CLOSETODAY',
                                                        limit_price=price, volume=volume)



        else:
            print('wrong exchangeid.')
            return None

        return orderid

    def on_tick(self):
        pass

    def on_bar(self):
        pass

    def indicator_calculate(self):
        pass

    def gen_signal(self):
        pass

    def on_signal(self):
        pass

    def on_order(self):
        pass

    def on_trade(self):
        pass

    def run(self):
        pass



if __name__ == '__main__':
    from tqsdk import TqApi, TqAccount, TqKq

    product = 'OI'
    interval = 15
    bklimit = 100
    sklimit = 150
    single_volume = 5

    api = TqApi(TqKq(), auth="quant_zcn,Qiai1301")

    symbol = api.query_cont_quotes(product_id=product)
    symbol_info=api.query_symbol_info(symbol).to_dict()
    print(symbol_info)
    account_info=api.get_account()
    position_info=api.get_position()

    api.close()
