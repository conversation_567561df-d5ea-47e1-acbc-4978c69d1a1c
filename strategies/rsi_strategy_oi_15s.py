# from myfunction import HLV, HHV, LLV, MID, cross, CROSS, crossup, crossdown
import pandas as pd
from tqsdk import TqApi, TqAccount, TqKq
import copy
from loguru import logger as mylog
from speaktext import speak_text

from tqsdk.ta import RSI
from tqsdk.tafunc import time_to_str
import numpy as np

from tradefuncs import *


def gen_rsi_signals(rsi, klines):
    lastsig = []
    sigall = []
    sigcount = 0


    if not rsi:
        return None
    else:

        for i in range(len(rsi)):

            if i == 0 and rsi[i] > 20:
                # lastsig.append(['bpk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                lastsig.append(['bpk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                sigall.append('bpk')
            if i == 0 and rsi[i] < 80:
                lastsig.append(['spk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                sigall.append('spk')
            # else:
            #     pass

            if rsi[i] > 20 and rsi[i - 1] <= 20:
                lastsig.append(['bpk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                sigall.append('bpk')
                sigcount = 0
            elif rsi[i] < 80 and rsi[i - 1] >= 80:
                lastsig.append(['spk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                sigall.append('spk')
                sigcount = 0
            else:
                sigcount += 1
                sigall.append(sigcount)

    print(sigall[-50:])
    sks = sigall.count('spk')
    bks = sigall.count('bpk')

    print('bks:', bks)
    print('sks:', sks)
    # print('最大距离：', max(sigall))
    print('平均信号距离:', len(sigall) / (sks + bks))

    print()
    return lastsig, sigall


def rsi_info_display(bars):
    strategy = '佛系一号'
    interval = bars.iloc[0].duration
    rsi = RSI(bars, 6).rsi
    rsi = rsi.tolist()
    # rsi = np.round((rsi),2)
    print(np.round(rsi[-10:], 2))

    # rsiup = crossup(rsi, 20)
    # rsidown = crossdown(rsi, 80)

    (lastsig, sigall) = gen_rsi_signals(rsi, bars)

    def sig_profit_analysis(lastsig):
        bkprices:list=[]
        skprices:list=[]

        for sig in lastsig:
            if sig[0]=='bpk':
                bkprices.append(sig[2])
            if sig[0]=='spk':
                skprices.append(sig[2])

        for i in range(len(skprices)):
            print(len(skprices))
            print(len(bkprices))
            if len(bkprices)>0:
                for j in range(len(bkprices)):
                    if bkprices[j]<skprices[i]:
                        print(skprices[i]-bkprices[j])
                        skprices.pop(i)
                        bkprices.pop(j)
                        i=0
                        break
        print('')

    sig_profit_analysis(lastsig)

    dnsprices = []
    upprices = []
    for s in lastsig:
        if s[0] == 'bpk':
            upprices.append(s[2])
        if s[0] == 'spk':
            dnsprices.append(s[2])

    print('dns numbers：', len(dnsprices), 'ups numbers:', len(upprices))
    print(sum(dnsprices) - sum(upprices))

    if len(lastsig) > 0:

        # average_signal_periods = int(len(bars) / len(lastsig))
        # print('---------------------R S I------------------------')
        # print('当前时间:', bars.iloc[-1].datetime, 'K线数量:', len(bars), '信号数:', len(lastsig), '平均信号周期:', average_signal_periods)
        # print('最近的信号:', lastsig[-1], sigall[-20:])
        # print('K线周期:', interval, 'K线数量:', len(bars), '信号数:', len(lastsig), '平均信号周期:', int(len(bars) / len(lastsig)))
        # print('信号价格:', lastsig[-1][2], '当前价:', bars.iloc[-1].close)
        # print('最后k线:', bars.iloc[-1].datetime, '收盘价:', bars.iloc[-1].close)
        # print('--------------------------------------------------')
        signal = sigall[-1]
        if signal == 'bpk' or signal == 'spk':
            signaltmp = '买入' if signal == 'bpk' else '卖出'
            # mylog.info([strategy, interval, stock, stockname, '发出交易信号', signal])
            print('价格:', lastsig[-1][2])

            email_info = ' '.join([strategy,
                                   # stockname,
                                   str(interval), '分钟:',
                                   '发出',
                                   signaltmp,
                                   '信号...',
                                   '价格:', str(lastsig[-1][2]),
                                   '信号时间：',
                                   time_to_str(lastsig[-1][1])])
            return email_info, signal

        else:
            return 0, 0

    else:
        print('本标的无交易信号...')
        return 0, 0


def trade_order(signal):
    pass


def run():
    product = 'OI'
    interval = 15
    bklimit = 5
    sklimit = 4
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_zcn,Qiai1301")

    SYMBOL = api.query_cont_quotes(product_id=product)[0]
    quote = api.get_quote(SYMBOL)
    pos = api.get_position(SYMBOL)
    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines.to_csv('oi15.csv')
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines
    signalinfo, signal = rsi_info_display(klines1)
    print(signalinfo)

    while True:
        api.wait_update()
        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            # disp_day_info(daymean, last3mean, hlmax, homean, olmean)
            # disp_0Day_info(quote)
            # print('balance:', acct.balance, 'poslong:', position.pos_long, 'posshort:', position.pos_short,
            #       'poslong float profit:', position.float_profit_long, 'posshort float profit:',
            #       position.float_profit_short)

            speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                newk = newk.to_frame()
                newk = newk.T
                klines1 = pd.concat([klines1, newk], ignore_index=True)

            signalinfo, signal = rsi_info_display(klines1)

            bkvol = pos.pos_long
            skvol = pos.pos_short

            if signal:
                order_price = quote.last_price

                if signal == 'bpk' and bkvol < bklimit:
                    BK(api, SYMBOL, order_price, single_volume)

                if signal == 'spk' and skvol < sklimit:
                    SK(api, SYMBOL, order_price, single_volume)

                print(signal, 'price:', order_price)
                speak_text(signal)


if __name__ == '__main__':
    run()
