from tqsdk import TqApi, TqBacktest, TqAuth
from datetime import datetime
from tqsdk.tafunc import time_to_str
import pandas as pd

def update_kline_data(kline_data, kline_tmp):
    """
    当实时数据kline_tmp发生变化时，将其倒数第二个数据追加到历史K线数据中
    使用datetime字段检查重复数据

    Parameters:
    kline_data (pd.DataFrame): 历史K线数据，包含datetime字段
    kline_tmp (pd.DataFrame): 实时K线数据，包含datetime字段

    Returns:
    pd.DataFrame: 更新后的历史K线数据
    """
    if len(kline_tmp) < 2 or len(kline_data) == 0:
        return kline_data

    # 获取要添加的新数据（倒数第二行）
    new_row = kline_tmp.iloc[-2:-1]
    new_datetime = new_row['datetime'].iloc[0]

    # 检查是否存在重复数据
    # 使用datetime字段进行查找
    if not (kline_data['datetime'] == new_datetime).any():
        # 如果该datetime不存在，则添加新数据
        kline_data = pd.concat([kline_data, new_row])
        # 确保数据按datetime排序
        kline_data = kline_data.sort_values('datetime').reset_index(drop=True)

    return kline_data



# 获取用户输入的起始时间
# start_time_str = input("请输入起始时间（格式：YYYY-MM-DD）：")
start_time_str = '2018-06-01'
end_time_str = '2018-12-31'
start_dt = datetime.strptime(start_time_str, "%Y-%m-%d")
end_dt = datetime.strptime(end_time_str, "%Y-%m-%d")
# 获取当前时间
# end_dt = datetime.now()

# 获取用户输入的合约符号，例如 DCE.OI2305
# symbol = input("请输入合约符号（例如 DCE.OI2305）：")
symbol = '<EMAIL>'
# 创建回测实例
backtest = TqBacktest(start_dt=start_dt, end_dt=end_dt)

# 创建 API 实例，替换 "快期账户" 和 "密码" 为实际凭据
api = TqApi(backtest=backtest, auth=TqAuth("smartmanp", "ftp123"))

# 设置 K 线周期，例如每日（86400 秒）
duration_seconds = 60

# 获取 K 线数据
kline_data = api.get_kline_serial(symbol, duration_seconds, data_length=10000).dropna()
kline_tmp = api.get_kline_serial(symbol, duration_seconds, data_length=10)
print(len(kline_data))

# 现在 kline_data 是一个 pandas DataFrame，包含从起始时间到当前时间的历史 K 线数据
print(kline_data)
from weiswave_claude_dataframe import calculate_signals,wave_signal

while True:
    api.wait_update()
    if api.is_changing(kline_tmp.iloc[-1],'datetime'):
        kline_data=update_kline_data(kline_data,kline_tmp)
        # df=calculate_signals(kline_data)
        # kline_data.to_csv(f'kline_data_{duration_seconds}.csv')
        # wave_signal(df)
        try:
            print(time_to_str(kline_data.iloc[-1].datetime))
        except:
            pass
    else:
        kline_data.to_csv(f'kline_data_{duration_seconds}_{start_time_str}.csv')


# 关闭 API 释放资源
api.close()