from tqsdk import TqApi, TqAccount, TqKq
from datetime import date


product = 'OI'
symbol=product
interval = 60
bklimit = 300
sklimit = 300
single_volume = 1

# 交易账号设置
api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True)
symbol = api.query_cont_quotes(product_id=product)[0]
order = api.insert_order(symbol, direction='BUY', offset='OPEN', limit_price=8327, volume=1)
orders = list()
orders.append(order)
while True:
    api.wait_update()
    # 获取最新价
    quote = api.get_quote(symbol)
    print("单状态: %s, 已成交: %d 手" % (order.status, order.volume_orign - order.volume_left))
    print(orders[0].order_id, orders[0].status)



