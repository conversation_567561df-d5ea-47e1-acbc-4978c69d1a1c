# from myfunction import HLV, HHV, LLV, MID, cross, CROSS, crossup, crossdown
import pandas as pd

from tqsdk.ta import RSI
from tqsdk.tafunc import time_to_str
import numpy as np

from tradefuncs import *


def gen_rsi_signals(rsi, klines):
    lastsig = []
    sigall = []
    sigcount = 0


    if not rsi:
        return None
    else:

        for i in range(len(rsi)):

            if i == 0 and rsi[i] > 20:
                # lastsig.append(['bpk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                lastsig.append(['bpk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                sigall.append('bpk')
            if i == 0 and rsi[i] < 80:
                lastsig.append(['spk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                sigall.append('spk')
            # else:
            #     pass

            if rsi[i] > 20 and rsi[i - 1] <= 20:
                lastsig.append(['bpk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                sigall.append('bpk')
                sigcount = 0
            elif rsi[i] < 80 and rsi[i - 1] >= 80:
                lastsig.append(['spk', time_to_str(klines.iloc[i].datetime), klines.iloc[i].close])
                sigall.append('spk')
                sigcount = 0
            else:
                sigcount += 1
                sigall.append(sigcount)

    print(sigall[-50:])
    sks = sigall.count('spk')
    bks = sigall.count('bpk')

    print('bks:', bks)
    print('sks:', sks)
    # print('最大距离：', max(sigall))
    print('平均信号距离:', len(sigall) / (sks + bks))

    print()
    return lastsig, sigall
def sig_profit_analysis(lastsig):
    bkprices:list=[]
    skprices:list=[]

    for sig in lastsig:
        if sig[0]=='bpk':
            bkprices.append(sig[2])
        if sig[0]=='spk':
            skprices.append(sig[2])
    pd.to_pickle(bkprices, 'bkprices.pkl')
    pd.to_pickle(skprices, 'skprices.pkl')
    print(max(bkprices), min(bkprices))
    print(max(skprices), min(skprices))
    skp=np.array(skprices)
    bkp=np.array(bkprices)

    while len(skp)>0:
        smin=np.min(skp)
        bmin=np.min(bkp)
        if bmin<smin:
            skp.remove(min(skp))
            bkp.remove(min(bkp))


    while len(skprices)>0:
        for i in range(len(skprices)):
            print(len(skprices))
            print(len(bkprices))
            if len(bkprices)>0:
                for j in range(len(bkprices)):
                    skprice = skprices[i]
                    bkprice = bkprices[j]
                    if bkprice<skprice:
                        print(skprices[i], bkprices[j])
                        print(skprices[i]-bkprices[j])
                        skprices.pop(i)
                        bkprices.pop(j)

                        break
            break
        # break
            # continue

        print('found one pair...')



def rsi_info_display(bars):
    strategy = '佛系一号'
    interval = bars.iloc[0].duration
    rsi = RSI(bars, 6).rsi
    rsi = rsi.tolist()
    # rsi = np.round((rsi),2)
    print(np.round(rsi[-10:], 2))

    # rsiup = crossup(rsi, 20)
    # rsidown = crossdown(rsi, 80)

    (lastsig, sigall) = gen_rsi_signals(rsi, bars)


    return lastsig

    dnsprices = []
    upprices = []
    for s in lastsig:
        if s[0] == 'bpk':
            upprices.append(s[2])
        if s[0] == 'spk':
            dnsprices.append(s[2])

    print('dns numbers：', len(dnsprices), 'ups numbers:', len(upprices))
    print(sum(dnsprices) - sum(upprices))

    if len(lastsig) > 0:

        # average_signal_periods = int(len(bars) / len(lastsig))
        # print('---------------------R S I------------------------')
        # print('当前时间:', bars.iloc[-1].datetime, 'K线数量:', len(bars), '信号数:', len(lastsig), '平均信号周期:', average_signal_periods)
        # print('最近的信号:', lastsig[-1], sigall[-20:])
        # print('K线周期:', interval, 'K线数量:', len(bars), '信号数:', len(lastsig), '平均信号周期:', int(len(bars) / len(lastsig)))
        # print('信号价格:', lastsig[-1][2], '当前价:', bars.iloc[-1].close)
        # print('最后k线:', bars.iloc[-1].datetime, '收盘价:', bars.iloc[-1].close)
        # print('--------------------------------------------------')
        signal = sigall[-1]
        if signal == 'bpk' or signal == 'spk':
            signaltmp = '买入' if signal == 'bpk' else '卖出'
            # mylog.info([strategy, interval, stock, stockname, '发出交易信号', signal])
            print('价格:', lastsig[-1][2])

            email_info = ' '.join([strategy,
                                   # stockname,
                                   str(interval), '分钟:',
                                   '发出',
                                   signaltmp,
                                   '信号...',
                                   '价格:', str(lastsig[-1][2]),
                                   '信号时间：',
                                   time_to_str(lastsig[-1][1])])
            return email_info, signal

        else:
            return 0, 0

    else:
        print('本标的无交易信号...')
        return 0, 0


if __name__ == '__main__':
    bars = pd.read_csv('oi15.csv')

    lastsig=rsi_info_display(bars)

    sig_profit_analysis(lastsig)

    print('completed.')