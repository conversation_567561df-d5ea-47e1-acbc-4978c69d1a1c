import json
import os


class PositionManager:
    def __init__(self, symbol, positions_file=None):
        self.symbol = symbol
        self.positions_file = positions_file or f"positions_{symbol.replace('.', '_')}.json"
        self.positions = self.load_positions()

    def load_positions(self):
        """从文件加载持仓数据"""
        if os.path.exists(self.positions_file):
            try:
                with open(self.positions_file, 'r', encoding='utf-8') as f:
                    positions = json.load(f)
                    print(f"加载持仓数据: {positions}")
                    return positions
            except Exception as e:
                print(f"加载持仓文件失败: {e}")

        # 默认持仓
        default_positions = {
            'symbol': self.symbol,
            'long': 0,
            'short': 0,
            'last_update': ''
        }
        self.save_positions(default_positions)
        return default_positions

    def save_positions(self, positions=None):
        """保存持仓数据到文件"""
        if positions is None:
            positions = self.positions

        try:
            with open(self.positions_file, 'w', encoding='utf-8') as f:
                json.dump(positions, f, ensure_ascii=False, indent=2)
            print(f"保存持仓数据: {positions}")
        except Exception as e:
            print(f"保存持仓文件失败: {e}")

    def update_position(self, position_type, volume, action='open'):
        """更新持仓"""
        import datetime

        if action == 'open':
            self.positions[position_type] += volume
            print(f"开{position_type}单 {volume}手，当前持仓: {self.positions[position_type]}")
        elif action == 'close':
            self.positions[position_type] = max(0, self.positions[position_type] - volume)
            print(f"平{position_type}单 {volume}手，当前持仓: {self.positions[position_type]}")

        self.positions['last_update'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.save_positions()

    def can_open_position(self, position_type, volume, limit):
        """检查是否可以开仓"""
        current_pos = self.positions[position_type]
        if current_pos + volume > limit:
            print(f"{position_type}单持仓限制: 当前{current_pos} + 新增{volume} > 限额{limit}")
            return False
        return True

    def get_position(self, position_type):
        """获取当前持仓"""
        return self.positions[position_type]


def wave_signal_trading(df, longlimit, shortlimit, volume):
    strategy_name = '突破火线'
    symbol = df.symbol.iloc[-1]
    symbol_name = df.symbol.iloc[-1]

    # 初始化持仓管理器
    position_mgr = PositionManager(symbol)

    signals = df.signal.tolist()
    signal_last = count_same_from_right(signals)
    signal_price = df.close.iloc[-signal_last]
    current_price = int(df.close.iloc[-1])
    current_signal = signals[-1]
    stop_price = df.G.iloc[-1]
    duration = df.duration.iloc[-1]

    # 统计信号
    result = count_consecutive_elements(signals)
    print("连续出现的'空'的数量:", result["consecutive_empty"])
    print("连续出现的'多'的数量:", result["consecutive_many"])
    if result["consecutive_empty"]:
        print('空单平均持续周期:', sum(result["consecutive_empty"]) / len(result["consecutive_empty"]))
    if result["consecutive_many"]:
        print('多单平均持续周期:', sum(result["consecutive_many"]) / len(result["consecutive_many"]))
    print("'空'和'多'交替出现的次数:", result["alternation_count"])

    pnl = current_price - signal_price if current_signal == '多' else signal_price - current_price
    print(f"{symbol}: 周期:{duration} 秒, current_signal:{current_signal}, Current price: {current_price}")
    print(f"signal_price: {signal_price}, signal_last: {signal_last}, pnl: {pnl}, 止损:{stop_price}")
    print(f"持续周期:{count_same_from_right(signals)}")
    print(f"当前持仓 - 多单:{position_mgr.get_position('long')}, 空单:{position_mgr.get_position('short')}")
    print(signals[-35:])

    # 信号处理
    if len(signals) >= 2:
        if signals[-2] == '多' and signals[-1] == '空':
            speak_text(f"{symbol_name}{strategy_name} 发出做空信号")

            # 检查是否可以开空单
            if position_mgr.can_open_position('short', volume, shortlimit):
                # 模拟开空单
                print(f"开空单: {symbol}, 价格: {current_price}, 数量: {volume}")
                position_mgr.update_position('short', volume, 'open')
                # 这里可以添加实际的API调用
                # api.insert_order(symbol, 'SELL', 'OPEN', current_price, volume)
            else:
                print(f"空单持仓已达限额 {shortlimit}，不开新仓")

            # 检查是否平多单 (盈利条件)
            current_long = position_mgr.get_position('long')
            if current_long > 0 and pnl > 20:  # 盈利超过20点
                close_volume = min(volume, current_long)
                print(f"平多单: {symbol}, 价格: {current_price}, 数量: {close_volume}, 盈利: {pnl}")
                position_mgr.update_position('long', close_volume, 'close')
                # api.insert_order(symbol, 'SELL', 'CLOSE', current_price, close_volume)
            else:
                print(f"多单平仓条件不满足: 持仓{current_long}, 盈利{pnl}")

        elif signals[-2] == '空' and signals[-1] == '多':
            speak_text(f"{symbol_name}{strategy_name} 发出做多信号")

            # 检查是否可以开多单
            if position_mgr.can_open_position('long', volume, longlimit):
                # 模拟开多单
                print(f"开多单: {symbol}, 价格: {current_price}, 数量: {volume}")
                position_mgr.update_position('long', volume, 'open')
                # api.insert_order(symbol, 'BUY', 'OPEN', current_price, volume)
            else:
                print(f"多单持仓已达限额 {longlimit}，不开新仓")

            # 检查是否平空单 (盈利条件)
            current_short = position_mgr.get_position('short')
            if current_short > 0 and pnl > 20:  # 盈利超过20点
                close_volume = min(volume, current_short)
                print(f"平空单: {symbol}, 价格: {current_price}, 数量: {close_volume}, 盈利: {pnl}")
                position_mgr.update_position('short', close_volume, 'close')
                # api.insert_order(symbol, 'BUY', 'CLOSE', current_price, close_volume)
            else:
                print(f"空单平仓条件不满足: 持仓{current_short}, 盈利{pnl}")

    return position_mgr