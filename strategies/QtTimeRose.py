import copy
import sys
import time
import pandas as pd
from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from loguru import logger as mylog
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QTextEdit, QLabel, QMainWindow
from PyQt5.QtCore import QTimer


# 用来代替实际的 speak_text 实现。
def speak_text(message):
    print(message)


def disp_day_info(daymean, last3mean, hlmax, homean, olmean, day_info_display):
    info = (f"最大价差: {hlmax}\n"
            f"平均价差: {daymean}\n"
            f"三日平均: {last3mean}\n"
            f"上价差: {homean}\n"
            f"下价差: {olmean}\n")
    day_info_display.setText(info)


def disp_0Day_info(quote, day_update_display):
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    gapcover = False

    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    info = (f'{quote.datetime} {quote.instrument_id}\n'
            f'缺口: {gap} 回补: {gapcover}\n'
            f'现价: {quote.last_price} BB: {BB}\n'
            f'今日价差: {dayvib}\n'
            f'上价差: {upvib} 下价差: {dnvib}')
    day_update_display.setText(info)


def MaCrossCaculate(bars, period, quote, signal_display):
    C = bars.close
    SYMBOL = bars.symbol.iloc[0]
    interval = bars.duration.iloc[0]

    trmac = ma(C, period)
    ups = crossup(C, trmac)
    dns = crossdown(C, trmac)
    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)
    if bkdist > skdist:
        signalnow = 'SK'
        distnow = skdist
    else:
        signalnow = 'BK'
        distnow = bkdist

    updatetime = time_to_str(bars.datetime.iloc[-1]).split(' ')[1].split('.')[0]
    signalprice = C.iloc[-distnow]
    sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price

    info = (f"时间: {updatetime} SYMBOL: {SYMBOL} interval: {interval}\n"
            f"当前信号: {signalnow} 持续周期: {distnow} 信号价格: {signalprice}\n"
            f"现价: {C.iloc[-1]} 信号盈亏: {sigfloatprofit}\n"
            f"平均信号距离: {int(len(upslist) / (upslist.count(1) * 2))}")
    signal_display.setText(info)

    signal = {'合约': SYMBOL, '周期': interval, '时间': updatetime, '当前信号': signalnow,
              '持续周期': distnow, '信号价格': signalprice, '现价': C.iloc[-1],
              '信号盈亏': sigfloatprofit}

    return signal


def get_Signals_Ma(bars, period):
    from MyTT import CROSS, MA
    CLOSE = bars.close.values
    OPEN = bars.open.values
    HIGH = bars.high.values
    LOW = bars.low.values
    N = period

    ups = list(CROSS(CLOSE, MA(CLOSE, N)))
    dns = list(CROSS(MA(CLOSE, N), CLOSE))

    signals = []

    for i in range(len(ups)):
        if ups[i] == 0 and dns[i] == 0:
            signals.append(0)

        else:
            if ups[i] == 1:
                signals.append(1)
            if dns[i] == 1:
                signals.append(-1)

    return ups, dns, signals


def cal_signaldist(signal_display, signals):
    updistance = []
    dndistance = []
    upcount = 0
    dncount = 0
    upinitialized = False
    dninitialized = False

    for i in range(len(signals)):
        if signals[i] == 1:
            upinitialized = True
            upcount = 0

        if signals[i] == -1:
            upcount += 1
            updistance.append(upcount)
            upinitialized = False

        if signals[i] == 0 and upinitialized:
            upcount += 1

        else:
            continue

    for i in range(len(signals)):
        if signals[i] == -1:
            dninitialized = True
            dncount = 0

        if signals[i] == 1:
            dncount += 1
            dndistance.append(dncount)
            dninitialized = False

        if signals[i] == 0 and dninitialized:
            dncount += 1

        else:
            continue

    info = (f"上信号距离: {updistance}\n"
            f"下信号距离: {dndistance}")
    signal_display.setText(info)

    return updistance, dndistance


def disp_account(acct, account_display):
    acct_info = dict(acct)
    account_display.setText(str(acct_info))


class TradingApp(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        self.setWindowTitle('Trading Info Display')

        layout = QVBoxLayout()

        self.day_info_display = QLabel(self)
        layout.addWidget(self.day_info_display)

        self.day_update_display = QLabel(self)
        layout.addWidget(self.day_update_display)

        self.signal_display = QLabel(self)
        layout.addWidget(self.signal_display)

        self.account_display = QTextEdit(self)
        self.account_display.setReadOnly(True)
        layout.addWidget(self.account_display)

        self.setLayout(layout)
        self.setGeometry(100, 100, 800, 600)


def main():
    from tqsdk import TqApi, TqAccount
    from accounts_zjy import tgjyzjy as acct

    app = QApplication(sys.argv)
    trading_app = TradingApp()
    trading_app.show()

    product = 'SHFE.rb2301'
    symbol = product
    interval = 60
    bklimit = 2
    sklimit = 2
    single_volume = 1

    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    klines = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964)

    def update_data():
        quote = api.get_quote(symbol)
        disp_0Day_info(quote, trading_app.day_update_display)

        acct = api.get_account()
        disp_account(acct, trading_app.account_display)

        ups, dns, signals = get_Signals_Ma(klines, period=13)
        cal_signaldist(trading_app.signal_display, signals)

        daybars = api.get_kline_serial(symbol, duration_seconds=60 * 60 * 24, data_length=365)
        daybar = daybars.dropna()
        hl = daybar.high - daybar.low
        ho = daybar.high - daybar.open
        ol = daybar.open - daybar.low
        hlmax = max(hl)
        daymean = int(hl.mean())
        last3mean = int(hl.iloc[-4:-1].mean())
        homean = ho.mean()
        olmean = ol.mean()
        disp_day_info(daymean, last3mean, hlmax, homean, olmean, trading_app.day_info_display)

    timer = QTimer()
    timer.timeout.connect(update_data)
    timer.start(1000)  # 每秒更新一次

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
