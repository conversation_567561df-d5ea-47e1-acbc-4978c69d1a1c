
def disp_0Day_info(quote):
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    gapcover = False

    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    print(quote.datetime, quote.instrument_id, '缺口：', gap, '回补:', gapcover, '现价:', quote.last_price, 'BB:', BB, '今日价差：',
          dayvib,
          '上价差:', upvib, '下价差:', dnvib)

    return BB, dayvib


if __name__ == '__main__':
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date
    from speaktext import speak_text
    product = 'OI'
    symbol = product
    interval = 60
    bklimit = 300
    sklimit = 300
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_zcn,Qiai1301", disable_print=True)
    symbol = api.query_cont_quotes(product_id=product)[0]
    quote = api.get_quote(symbol)
    while True:
        api.wait_update()
        if api.is_changing(quote):

            BB, dayvib = disp_0Day_info(quote)
            if BB>0.9 and dayvib>160:
                speak_text('请注意....')

            elif BB<0.1 and dayvib>160:
                speak_text('请注意....')


