import pandas as pd
import numpy as np

skp = pd.read_pickle('skprices.pkl')
bkp=pd.read_pickle('bkprices.pkl')

while len(skp)>0:
    print(len(skp))
    # if min(skp)>min(bkp):
    #     skp.pop(skp.index(min(skp)))
    #     bkp.pop(bkp.index(min(bkp)))
    #
    # if max(skp)>max(bkp):
    #     skp.pop(skp.index(max(skp)))
    #     bkp.pop(bkp.index(max(bkp)))
    for i in skp:
        for j in bkp:
            if i>j:
                print(i,j)
                skp.pop(skp.index(i))
                bkp.pop(bkp.index(j))

                break


