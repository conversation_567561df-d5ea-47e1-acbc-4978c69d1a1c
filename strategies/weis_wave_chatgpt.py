import time

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

from tqsdk import TqApi, TqKq

product = 'OI'
interval = 60 * 15
bklimit = 50
sklimit = 10
single_volume = 100

# 交易账号设置
api = TqApi(TqKq(), auth="bigwolf,ftp123", disable_print=True)

symbol = api.query_cont_quotes(product_id=product)[0]
df = api.get_kline_serial(symbol, duration_seconds=interval, data_length=1000)


# 定义积累、分配、跳空和跳空陷阱四种交易类型
def get_trading_type(price_change, volume_change):
    if price_change > 0 and volume_change > 0:
        return 'buy'
    elif price_change < 0 and volume_change < 0:
        return 'sell'
    elif price_change > 0.05 and volume_change < 0:
        return 'spring'
    elif price_change < -0.05 and volume_change > 0:
        return 'upthrust'
    else:
        return 'no_trading_type'

def cal_signaldist(signals, buysignal=1, sellsignal=-1):
    updistance = []
    dndistance = []
    upcount = 0
    dncount = 0
    upinitialized = False
    dninitialized = False

    for i in range(len(signals)):
        if signals[i] == buysignal:
            upinitialized = True
            upcount = 0

        if signals[i] == sellsignal:
            upcount += 1
            updistance.append(upcount)
            upinitialized = False

        if signals[i] == 0 and upinitialized:
            upcount += 1

        else:
            continue

    for i in range(len(signals)):
        if signals[i] == sellsignal:
            dninitialized = True
            dncount = 0

        if signals[i] == buysignal:
            dncount += 1
            dndistance.append(dncount)
            dninitialized = False

        if signals[i] == 0 and dninitialized:
            dncount += 1

        else:
            continue

    return updistance, dndistance


while True:

    api.wait_update()



    # 计算股票价格的变化率和交易量的变化率
    df['price_change'] = df['close'].pct_change()
    df['volume_change'] = df['volume'].pct_change()

    # 对每日的股票价格变化率和交易量变化率进行分类
    df['trading_type'] = df.apply(lambda x: get_trading_type(x['price_change'], x['volume_change']), axis=1)

    price_change = df.price_change.tolist()

    signals = df.trading_type.tolist()
    close_price = df.close.tolist()

    signalset = set(signals)
    print(signals[-40:])
    buy_price_sum = 0
    sell_price_sum = 0

    for s in range(len(signals)):
        if signals[s] == 'buy':
            buy_price_sum += close_price[s]
        if signals[s] == 'sell':
            sell_price_sum += close_price[s]

    print(sell_price_sum - buy_price_sum)

    ups, dns = cal_signaldist(signals, 'accumulaion', 'distribution')


    # 统计不同交易类型的起始点和终止点
    def get_trading_points(trading_type):
        start_points = []
        end_points = []
        trading_flag = False
        for i in range(len(trading_type)):
            if trading_type[i] != 'no_trading_type' and not trading_flag:
                start_points.append(i)
                trading_flag = True
            elif trading_type[i] == 'no_trading_type' and trading_flag:
                end_points.append(i - 1)
                trading_flag = False
        if trading_flag:
            end_points.append(len(trading_type) - 1)
        return start_points, end_points


    # 计算交易类型对应的平均价格和交易量

    def get_trading_features(df, start_points, end_points):
        trading_features = []
        for start, end in zip(start_points, end_points):
            trading_df = df[start:end + 1]
            trading_type = trading_df['trading_type'].iloc[0]
            # price_mean = trading_df['close'].mean()
            price_mean = trading_df['close'].iloc[-1]
            volume_mean = trading_df['volume'].mean()
            trading_features.append((trading_type, price_mean, volume_mean))
        return trading_features


    # 统计不同交易类型的起始点和终止点
    start_points, end_points = get_trading_points(df['trading_type'])

    # 计算交易类型对应的平均价格和交易量
    trading_features = get_trading_features(df, start_points, end_points)

    # 绘制股票价格图和交易类型标记
    fig, ax = plt.subplots(figsize=(15, 8))
    ax.plot(df['datetime'], df['close'])

    for start, end in zip(start_points, end_points):
        trading_type = trading_features[start_points.index(start)][0]
        ax.axvspan(df['datetime'].iloc[start], df['datetime'].iloc[end], alpha=0.5,
                   color='red' if trading_type == 'distribution' else 'green')
        plt.show()
        time.sleep(1)


    # 简单的交易策略

    def simple_strategy(trading_type, price_mean, volume_mean):
        if trading_type == 'accumulation':  # and price_mean > 100 and volume_mean > 1000000:
            return 'buy'
        elif trading_type == 'distribution':  # and price_mean < 90 and volume_mean > 1000000:
            return 'sell'
        else:
            return 'no_action'


    # 对每个交易区间执行交易策略
    buy_price_sum = 0
    sell_price_sum = 0

    for trading_type, price_mean, volume_mean in trading_features:
        action = simple_strategy(trading_type, price_mean, volume_mean)
        if action == 'buy':
            print('Buy at average price:', price_mean)
            buy_price_sum += price_mean
        elif action == 'sell':
            print('Sell at average price:', price_mean)
            sell_price_sum += price_mean
        else:
            print('No action for this trading type:', trading_type)

    print('buypricesum:', buy_price_sum, 'sellpricesum', sell_price_sum, 'delta:', sell_price_sum - buy_price_sum)
    api.close()
