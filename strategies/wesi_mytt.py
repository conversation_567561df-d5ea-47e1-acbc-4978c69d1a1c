from MyTT import *
from tqsdk import TqApi, TqAccount, TqKq

product = 'OI'
symbol = product
interval = 60
bklimit = 100
sklimit = 100
single_volume = 1

# 交易账号设置
# api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
# symbol = api.query_cont_quotes(product_id=product)[0]
# api = TqApi(TqKq(), auth="wolfquant,ftp123")
api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")
symbol = api.query_cont_quotes(product_id=product)[0]

klines = api.get_kline_serial(symbol, duration_seconds=interval, data_length=8964)
high = klines.high
low = klines.low

HIGH = high
LOW=low

RB = 3
HC = 2
bs = 0
DV = 1
IK = 6
X_1 = high
X_2 = low
X_3 = 1
X_4 = 1
X_5 = 1
X_6 = 1
X_7 = 1
X_8 = IF(X_7 == 1, REF(X_7, 1) - 1, IF(X_7 == (-1), REF(X_7, 1) + 1, X_7))
X_9 = IF(X_8 < 0, X_8 + 1, X_8 - 1)
# X_10=TROUGHBARS(3,RB,1)
# X_11=PEAKBARS(3,RB,1)
# X_12=IF(X_10==0,(-1),IF(X_11==0,1,0))
# X_13=IF(X_10==0,X_11,IF(X_11==0,-X_10,IF(X_10>X_11,X_11,-X_10)))
X_14 = 1
X_15 = BARSLAST(X_14 < 0)
X_16 = BARSLAST(X_14 > 0)
X_17 = IF(X_15 == 0, (-1), IF(X_16 == 0, 1, 0))
X_18 = IF(X_15 == 0, X_16, IF(X_16 == 0, -X_15, IF(X_15 > X_16, X_16, -X_15)))
X_19 = IF(HC == 2, X_12, IF(HC == 3, X_17, X_7))
X_20 = IF(HC == 2, X_13, IF(HC == 3, X_18, X_9))
X_21 = IF(X_20 < 0, -X_20, X_20)
周期 = X_21
X_22 = IF(X_20 > 0, 100 * (X_2 / REF(X_1, X_21) - 1), 100 * (X_1 / REF(X_2, X_21) - 1))
涨跌幅 = X_22, NODRAW, COLORWHITE
# X_23=INBLOCK(1)=1 AND NOT(NAMEINCLUDE(2))
X_23 = True
X_24 = IF(BS == 0, X_21, MIN(X_21, BS))
X_25 = IF(X_20 > 0, 100 * (X_2 / REF(X_1, X_24) - 1), 100 * (X_1 / REF(X_2, X_24) - 1))
X_26 = IF(X_23, AMOUNT / 100000000, vol / 100)
X_27 = SUM(X_26, X_24)
X_28 = X_27 / X_25 / DV
速度指数 = X_28, NODRAW, COLORWHITE
# DRAWLINE(X_3 AND X_19=(-1),X_2,X_3 AND X_19=1,X_1,0),DOTLINE,COLORYELLOW
# DRAWLINE(X_3 AND X_19=1,X_1,X_3 AND X_19=(-1),X_2,0),DOTLINE,COLORYELLOW
# DRAWNUMBER(X_4 AND X_19=1,HIGH,ROUND(X_28)),COLORWHITE,LINETHICK9
# DRAWNUMBER(X_4 AND X_19=(-1),LOW,ROUND(X_28)),COLORWHITE
X_29 = BARSLAST(X_19=1)
X_30 = BARSLAST(X_19=(-1))
# DRAWNUMBER(X_4 AND ISLASTBAR AND X_29>X_30,HIGH,ROUND(X_28)),COLORYELLOW,LINETHICK9
# DRAWNUMBER(X_4 AND ISLASTBAR AND X_29<X_30,LOW,ROUND(X_28)),COLORYELLOW

X_31 = BARSLAST(X_19=1)
X_32 = BARSLAST(X_19=(-1))
X_33 = IF(X_31 == 0 or X_32 == 0, DRAWNULL, (REF(X_1, X_31) + REF(X_2, X_32)) / 2)
R50 %= IF(X_5, X_33, DRAWNULL), POINTDOT, COLORWHITE

X_34 = IF(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
X_35 = IF(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)
X_36 = HHV(X_34, 3)
X_37 = LLV(X_35, 3)
X_38 = BARSLAST(X_37 < REF(X_37, 1))
X_39 = BARSLAST(X_36 > REF(X_36, 1))
X_40 = IF(HHV(X_34, X_39 + 1) == X_34, 1, 0)
X_41 = IF(LLV(X_35, X_38 + 1) == X_35, 1, 0)
X_42 = BARSLAST(X_40)
X_43 = REF(LLV(X_35, 3), X_42)
X_44 = BARSLAST(X_41)

X_45 = REF(HHV(X_34, 3), X_44)

X_46 = VALUEWHEN(X_45 > 0, X_45)
X_47 = VALUEWHEN(X_43 > 0, X_43)
X_48 = IF(CLOSE > X_46, (-1), IF(CLOSE < X_47, 1, 0))
X_49 = VALUEWHEN(X_48 != 0, X_48)
X_50 = BARSLAST(crOSS(0, X_49))
X_51 = BARSLAST(CROSS(X_49, 0))

X_52 = IF(X_49 == 1, IF(LLV(X_46, X_51 + 1) == X_46, X_46, LLV(X_46, X_51 + 1)), X_46)
X_53 = IF(X_49 == (-1), IF(HHV(X_47, X_50 + 1) == X_47, X_47, HHV(X_47, X_50 + 1)), X_47)

X_54 = IF(CLOSE > X_52, (-1), IF(CLOSE < X_53, 1, 0))
X_55 = VALUEWHEN(X_54 != 0, X_54)

X_56 = BARSLAST(CROSS(0, X_54))
X_57 = BARSLAST(CROSS(X_54, 0))
X_58 = IF(X_55 == 1, IF(LLV(X_52, X_57 + 1) == X_52, X_52, LLV(X_52, X_57 + 1)), IF(HHV(X_53, X_56 + 1) == X_53, X_53, HHV(X_53, X_56 + 1)))

多头止损 = IF(X_55 < 0, X_58, )
空头止损 = IF(X_55 > 0, X_58, )
