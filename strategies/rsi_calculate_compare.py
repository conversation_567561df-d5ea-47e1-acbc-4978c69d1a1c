# from myfunction import HLV, HHV, LLV, MID, cross, CROSS, crossup, crossdown
import pandas as pd
from tqsdk import TqApi, TqAccount, TqKq
import copy, pickle, os
from loguru import logger as mylog
from speaktext import speak_text

from tqsdk.ta import RSI as tqrsi
from MyTT import SMA, REF, ABS, RD, MAX
from tqsdk.tafunc import time_to_str
import numpy as np

def RSI(CLOSE, N=6):
    # RSI指标,和通达信小数点2位相同
    DIF = (CLOSE - REF(CLOSE, 1)).dropna()
    S1 = SMA(MAX(DIF, 0), N)
    S2 = SMA(ABS(DIF), N) * 100
    return RD(S1 / S2)

    # return RD(SMA(MAX(DIF,0), N) / SMA(ABS(DIF), N) * 100)

def rsi_info_display(bars):
    strategy = '佛系一号'
    interval = bars.iloc[0].duration
    rsi = RSI(bars, 6).rsi
    rsi = rsi.tolist()
    print(np.round(rsi[-10:], 2))
    # rsiup = crossup(rsi, 20)
    # rsidown = crossdown(rsi, 80)



def run():
    product = 'OI'
    interval = 60*60
    bklimit = 300
    sklimit = 300
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_zcn,Qiai1301")
    SYMBOL = api.query_cont_quotes(product_id=product)[0]
    quote = api.get_quote(SYMBOL)
    pos=api.get_position(SYMBOL)
    filename = 'barsdata60.pkl'
    if os.path.exists(filename):
        klines=pd.read_pickle(filename)

    else:
        klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
        klines.to_pickle(filename)

    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    klines2 = klines1.dropna()
    rsitq = tqrsi(klines1, 6).dropna().reset_index(drop=True)
    rsi = RSI(klines1.close, 6)

    del klines
    api.close()
    # klines1.dropna(inplace=True)


if __name__ == '__main__':
    run()
