from tqsdk import TqA<PERSON>, TqKq, Tq<PERSON><PERSON>unt, TqAuth, TqBacktest
from loguru import logger as mylog
from addict import Dict
import copy

# simulate account
simulate_account = "quant_zcn,Qiai1301"
api = TqApi(TqKq(), auth=simulate_account)

# real trading account
productid = 'OI'
interval = 60
data_length = 8964

symbol = api.query_cont_quotes(product_id=productid)[0]

bars0 = api.get_kline_serial(symbol, duration_seconds=interval, data_length=data_length)

bars = copy.deepcopy(bars0)

quote = api.get_quote(symbol)


