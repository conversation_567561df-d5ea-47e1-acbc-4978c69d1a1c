from pydispatch import dispatcher

SIG<PERSON>L = 'my-first-signal'
SIGNAL2 = 'my-second-signal'


#
def event_handler(sender, signal, data):
    print('hello', data)
    print(sender, signal)


dispatcher.connect(event_handler)

dispatcher.send(data='test')


def handle_event(sender, signal):
    """Simple event handler"""
    print('Signal was sent by', sender, signal)


dispatcher.connect(handle_event, signal=SIGNAL, sender=dispatcher.Any)

first_sender = 'tianhm'
second_sender = 'zjy'


def main():


    dispatcher.send(signal=SIGNAL, sender=first_sender)
    dispatcher.send(signal=SIGNAL2, sender=second_sender)


def handle_specific_event(sender, moo):
    """Handle a simple event, requiring a "moo" parameter"""
    print('Specialized event for %(sender)s moo=%(moo)r' % locals())


dispatcher.connect(handle_specific_event, signal=SIGNAL2, sender=dispatcher.Any)
dispatcher.send(signal=SIGNAL2, sender='tianhm', moo='test')

if __name__ == '__main__':
    main()
    # handle_event(first_sender, 'test for the test')
