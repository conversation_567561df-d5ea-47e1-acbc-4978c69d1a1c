import pandas as pd
import numpy as np

from wh_tdx_functions import HHV, LLV, REF, BARSLAST, VALUEWHEN






# 指标计算函数
def calculate_indicator(df):
    # X_34: IF(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
    df['X_34'] = np.where(df['HIGH'] < REF(df['LOW'], 1), REF(df['LOW'], 1), df['HIGH'])

    # X_35: IF(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)
    df['X_35'] = np.where(df['LOW'] > REF(df['HIGH'], 1), REF(df['HIGH'], 1), df['LOW'])

    # X_36: HHV(X_34, 3)
    df['X_36'] = HHV(df['X_34'], 3)

    # X_37: LLV(X_35, 3)
    df['X_37'] = LLV(df['X_35'], 3)

    # X_38: BARSLAST(X_37 < REF(X_37, 1))
    df['X_38'] = BARSLAST(df['X_37'] < REF(df['X_37'], 1))

    # X_39: BARSLAST(X_36 > REF(X_36, 1))
    df['X_39'] = BARSLAST(df['X_36'] > REF(df['X_36'], 1))

    # X_40: IF(HHV(X_34, X_39 + 1) = X_34, 1, 0)
    df['X_40'] = np.where(HHV(df['X_34'], df['X_39'] + 1) == df['X_34'], 1, 0)

    # X_41: IF(LLV(X_35, X_38 + 1) = X_35, 1, 0)
    df['X_41'] = np.where(LLV(df['X_35'], df['X_38'] + 1) == df['X_35'], 1, 0)

    # X_42: BARSLAST(X_40)
    df['X_42'] = BARSLAST(df['X_40'] == 1)

    # X_43: REF(LLV(X_35, 3), X_42)
    df['X_43'] = REF(LLV(df['X_35'], 3), df['X_42'])

    # X_44: BARSLAST(X_41)
    df['X_44'] = BARSLAST(df['X_41'] == 1)

    # X_45: REF(HHV(X_34, 3), X_44)
    df['X_45'] = REF(HHV(df['X_34'], 3), df['X_44'])

    # X_46: VALUEWHEN(X_45 > 0, X_45)
    df['X_46'] = VALUEWHEN(df['X_45'] > 0, df['X_45'])

    # X_47: VALUEWHEN(X_43 > 0, X_43)
    df['X_47'] = VALUEWHEN(df['X_43'] > 0, df['X_43'])

    # X_48: IF(CLOSE > X_46, (-1), IF(CLOSE < X_47, 1, 0))
    df['X_48'] = np.where(df['CLOSE'] > df['X_46'], -1, np.where(df['CLOSE'] < df['X_47'], 1, 0))

    # X_49: VALUEWHEN(X_48 <> 0, X_48)
    df['X_49'] = VALUEWHEN(df['X_48'] != 0, df['X_48'])

    # X_50: BARSLAST(CROSS(0, X_49))
    df['X_50'] = BARSLAST(df['X_49'] == 0)

    # X_51: BARSLAST(CROSS(X_49, 0))
    df['X_51'] = BARSLAST(df['X_49'] != 0)

    # X_52: IF(X_49 = 1, IF(LLV(X_46, X_51 + 1) = X_46, X_46, LLV(X_46, X_51 + 1)), X_46)
    df['X_52'] = np.where(df['X_49'] == 1,
                          np.where(LLV(df['X_46'], df['X_51'] + 1) == df['X_46'], df['X_46'], LLV(df['X_46'], df['X_51'] + 1)),
                          df['X_46'])

    # X_53: IF(X_49 = (-1), IF(HHV(X_47, X_50 + 1) = X_47, X_47, HHV(X_47, X_50 + 1)), X_47)
    df['X_53'] = np.where(df['X_49'] == -1,
                          np.where(HHV(df['X_47'], df['X_50'] + 1) == df['X_47'], df['X_47'], HHV(df['X_47'], df['X_50'] + 1)),
                          df['X_47'])

    # X_54: IF(CLOSE > X_52, (-1), IF(CLOSE < X_53, 1, 0))
    df['X_54'] = np.where(df['CLOSE'] > df['X_52'], -1, np.where(df['CLOSE'] < df['X_53'], 1, 0))

    # X_55: VALUEWHEN(X_54 <> 0, X_54)
    df['X_55'] = VALUEWHEN(df['X_54'] != 0, df['X_54'])

    # X_56: BARSLAST(CROSS(0, X_54))
    df['X_56'] = BARSLAST(df['X_54'] == 0)

    # X_57: BARSLAST(CROSS(X_54, 0))
    df['X_57'] = BARSLAST(df['X_54'] != 0)

    # X_58: IF(X_55 = 1, IF(LLV(X_52, X_57 + 1) = X_52, X_52, LLV(X_52, X_57 + 1)), IF(HHV(X_53, X_56 + 1) = X_53, X_53, HHV(X_53, X_56 + 1)))
    df['X_58'] = np.where(df['X_55'] == 1,
                          np.where(LLV(df['X_52'], df['X_57'] + 1) == df['X_52'], df['X_52'], LLV(df['X_52'], df['X_57'] + 1)),
                          np.where(HHV(df['X_53'], df['X_56'] + 1) == df['X_53'], df['X_53'], HHV(df['X_53'], df['X_56'] + 1)))

    # 多头止损: IF(X_55 < 0, X_58, NULL)
    df['多头止损'] = np.where(df['X_55'] < 0, df['X_58'], np.nan)

    # 空头止损: IF(X_55 > 0, X_58, NULL)
    df['空头止损'] = np.where(df['X_55'] > 0, df['X_58'], np.nan)

    # G: X_58
    df['G'] = df['X_58']

    # W1: X_55 < 0
    df['W1'] = df['X_55'] < 0

    # W2: X_55 > 0
    df['W2'] = df['X_55'] > 0

    return df

# 示例使用
# 假设 df 是一个包含 HIGH, LOW, CLOSE 列的 DataFrame
# df = pd.read_csv('your_data.csv')  # 从文件加载数据
# df = calculate_indicator(df)
# print(df.head())

df =pd.read_csv('180.csv')
df.columns = df.columns.str.upper()
df = calculate_indicator(df)
print(df)
print(df['G'].iloc[-1])
