from tqsdk import *
api = TqApi(TqKq(), auth="quant_zcn,Qiai1301")


def disp_bars_info(bars):
    symbol = bars.symbol.iloc[-1]
    interval = bars.duration.iloc[-1]
    hl = bars.high - bars.low
    ho = bars.high - bars.open
    ol = bars.open - bars.low
    hlmax = max(hl)
    barmean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    print(symbol,interval, '最大价差:', hlmax, '平均价差:', barmean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:', int(olmean))
    return barmean

productid='OI'
symbol = api.query_cont_quotes(product_id=productid)[0]
bars = api.get_kline_serial(symbol=symbol, duration_seconds=60*60*24,data_length=8964).dropna()
quote=api.get_quote(symbol)
api.close()
OPEN= quote.open

barmean=disp_bars_info(bars)

gridnum=10

updistance=int(barmean/2)
dndistance=int(barmean/2)

upstep=updistance/gridnum
dnstep=dndistance/gridnum

upsteps = []
dnsteps = []
print(OPEN)

for s in range(gridnum):
    upsteps.append(OPEN+int((s+1)*upstep))
    dnsteps.append(OPEN-int((s+1)*dnstep))

print(upsteps)
print(dnsteps)






