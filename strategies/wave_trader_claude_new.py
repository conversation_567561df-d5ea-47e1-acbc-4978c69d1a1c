import numpy as np
import pandas as pd


def VALUEWHEN(COND, X):
    """
    When the condition COND is met, returns the current value of X.
    If the condition is not met, returns the value of X the last time CO<PERSON> was met.

    Parameters:
    -----------
    COND : array-like
        Boolean array representing the condition
    X : array-like
        Values to be returned when condition is met

    Returns:
    --------
    array-like
        Values of X when condition was last met
    """
    # Convert inputs to numpy arrays for consistent handling
    cond_array = np.array(COND, dtype=bool)
    x_array = np.array(X)

    # Initialize result array with NaN
    result = np.full_like(x_array, np.nan, dtype=float)

    # Initialize last valid value as NaN
    last_valid_value = np.nan

    # Iterate through the arrays
    for i in range(len(cond_array)):
        if cond_array[i]:
            # If condition is met, use current X value
            last_valid_value = x_array[i]

        # Store the value (either current if condition met, or last valid)
        result[i] = last_valid_value

    return result


def IFELSE(COND, A, B):
    """
    Returns A if COND is true, otherwise returns B.

    Parameters:
    -----------
    COND : array-like or scalar
        Boolean condition
    A : array-like or scalar
        Value to return if condition is true
    B : array-like or scalar
        Value to return if condition is false

    Returns:
    --------
    array-like or scalar
        Values of either A or B based on condition
    """
    # Convert inputs to numpy arrays for consistent handling
    if hasattr(COND, '__len__') and not isinstance(COND, (str, bytes)):
        cond_array = np.array(COND, dtype=bool)

        # Handle scalar A and B with array COND
        if not hasattr(A, '__len__') or isinstance(A, (str, bytes)):
            a_array = np.full_like(cond_array, A, dtype=float)
        else:
            a_array = np.array(A)

        if not hasattr(B, '__len__') or isinstance(B, (str, bytes)):
            b_array = np.full_like(cond_array, B, dtype=float)
        else:
            b_array = np.array(B)

        # Create result array
        result = np.where(cond_array, a_array, b_array)
    else:
        # Handle scalar COND
        result = A if COND else B

    return result


def ISLASTBAR(df):
    """
    Returns True for the last bar in the dataframe
    """
    result = np.zeros(len(df), dtype=bool)
    if len(result) > 0:
        result[-1] = True
    return result


def wavetrader(df):
    """
    Implements the WaveTrader strategy based on the given formula.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with OHLC price data

    Returns:
    --------
    pandas.DataFrame
        DataFrame with original data and additional WaveTrader indicators
    """
    # Make a copy of the input DataFrame to avoid modifying the original
    result_df = df.copy()

    # Get required price data
    high = df['high'].values
    low = df['low'].values
    close = df['close'].values
    open_price = df['open'].values

    # Create shifted versions of high and low
    high_1 = np.roll(high, 1)
    high_2 = np.roll(high, 2)
    low_1 = np.roll(low, 1)
    low_2 = np.roll(low, 2)

    # Set first two elements to NaN since they don't have 2 previous values
    # Now you can assign NaN
    # Convert to float type
    high_1 = high_1.astype(float)
    high_2 = high_2.astype(float)
    low_1 = low_1.astype(float)
    low_2 = low_2.astype(float)
    # Now you can assign NaN
    high_1[:2] = np.nan
    high_2[:2] = np.nan
    low_1[:2] = np.nan
    low_2[:2] = np.nan

    # HH1: IFELSE(H<REF(H,1)&&REF(H,1)<REF(H,2),REF(H,2),0)
    # Detect two consecutive lower highs
    hh1_cond = (high < high_1) & (high_1 < high_2)
    hh1 = IFELSE(hh1_cond, high_2, 0)

    # LL1: IFELSE(L>REF(L,1)&&REF(L,1)>REF(L,2),REF(L,2),0)
    # Detect two consecutive higher lows
    ll1_cond = (low > low_1) & (low_1 > low_2)
    ll1 = IFELSE(ll1_cond, low_2, 0)

    # HH2: VALUEWHEN(HH1>0,HH1)
    # Remember the last significant high
    hh2 = VALUEWHEN(hh1 > 0, hh1)

    # LL2: VALUEWHEN(LL1>0,LL1)
    # Remember the last significant low
    ll2 = VALUEWHEN(ll1 > 0, ll1)

    # K1: IFELSE(CLOSE>HH2,-3,IFELSE(CLOSE<LL2,1,0))
    # Generate initial signal: -3 for buy (close above resistance), 1 for sell (close below support)
    k1 = IFELSE(close > hh2, -3, IFELSE(close < ll2, 1, 0))

    # K2: VALUEWHEN(K1<>0,K1),NODRAW
    # Remember the last non-zero signal
    k2 = VALUEWHEN(k1 != 0, k1)

    # G:=IFELSE(K2=1,HH2,LL2)
    # Store the reference price level (HH2 for sell signals, LL2 for buy signals)
    g = IFELSE(k2 == 1, hh2, ll2)

    # G1:=VALUEWHEN(ISLASTBAR,G)
    # Store the last value of G
    g1 = VALUEWHEN(ISLASTBAR(df), g)

    # W1:=K2
    # Store the signal
    w1 = k2

    # W2:=OPEN-CLOSE
    # Calculate the bar range
    w2 = open_price - close

    # HT:=IFELSE(OPEN>CLOSE,OPEN,CLOSE)
    # Higher of open or close
    ht = IFELSE(open_price > close, open_price, close)

    # LT:=IFELSE(OPEN<CLOSE,OPEN,CLOSE)
    # Lower of open or close
    lt = IFELSE(open_price < close, open_price, close)

    # Generate buy and sell signals
    buy_signals = (k2 == -3)
    sell_signals = (k2 == 1)

    # Add all calculated values to the result DataFrame
    result_df['HH1'] = hh1
    result_df['LL1'] = ll1
    result_df['HH2'] = hh2
    result_df['LL2'] = ll2
    result_df['K1'] = k1
    result_df['K2'] = k2
    result_df['G'] = g
    result_df['G1'] = g1
    result_df['W1'] = w1
    result_df['W2'] = w2
    result_df['HT'] = ht
    result_df['LT'] = lt
    result_df['BUY'] = buy_signals
    result_df['SELL'] = sell_signals

    return result_df

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date
    # from accounts import tgjyzjy as acct

    product = 'rb'
    symbol = product
    interval = 60
    bklimit = 200
    sklimit = 200
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    api = TqApi(TqKq(), auth="walkquant,ftp123", disable_print=True, debug=None)
    symbol = api.query_cont_quotes(product_id=product)[0]
    klines = api.get_kline_serial(symbol, interval, data_length=10000).dropna()
    wavetrader(klines)
    api.close()

if __name__ == "__main__":
    runstrategy()


#
# if __name__ == "__main__":
#     # Create a sample DataFrame for testing
#     data = {
#         'DATE': pd.date_range(start='2023-01-01', periods=30),
#         'OPEN': [100, 102, 104, 103, 105, 107, 108, 106, 104, 105,
#                  103, 101, 102, 104, 106, 105, 103, 104, 106, 108,
#                  110, 112, 110, 108, 106, 104, 103, 105, 107, 109],
#         'HIGH': [105, 106, 108, 106, 108, 110, 112, 109, 106, 108,
#                  106, 104, 105, 108, 110, 108, 105, 107, 109, 112,
#                  115, 118, 114, 112, 109, 106, 105, 108, 110, 112],
#         'LOW': [98, 100, 102, 100, 103, 105, 106, 103, 102, 103,
#                 100, 98, 100, 102, 104, 102, 100, 102, 104, 106,
#                 108, 110, 108, 105, 103, 101, 100, 102, 105, 107],
#         'CLOSE': [102, 104, 106, 105, 107, 109, 110, 108, 105, 107,
#                   104, 102, 103, 106, 108, 106, 104, 105, 107, 110,
#                   112, 114, 112, 108, 105, 103, 102, 105, 108, 110]
#     }
#
#     # Create DataFrame
#     df = pd.DataFrame(data)
#
#     # Apply the wavetrader strategy
#     result = wavetrader(df)
#
#     # Display results
#     print("WaveTrader Strategy Results:")
#     print(result[['DATE', 'OPEN', 'HIGH', 'LOW', 'CLOSE', 'HH1', 'LL1', 'HH2', 'LL2', 'K1', 'K2', 'BUY', 'SELL']])
#
#     # Count buy and sell signals
#     buy_count = result['BUY'].sum()
#     sell_count = result['SELL'].sum()
#
#     print(f"\nTotal Buy Signals: {buy_count}")
#     print(f"Total Sell Signals: {sell_count}")
#
#     # Show the latest signals
#     last_5_days = result.tail(5)
#     print("\nLast 5 Days Signals:")
#     print(last_5_days[['DATE', 'CLOSE', 'HH2', 'LL2', 'K2', 'BUY', 'SELL']])