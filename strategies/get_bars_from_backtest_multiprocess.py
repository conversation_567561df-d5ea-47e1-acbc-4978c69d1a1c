from tqsdk import TqApi, TqBacktest, TqAuth
from datetime import datetime, timedelta
from tqsdk.tafunc import time_to_str
import pandas as pd
from dateutil.relativedelta import relativedelta
import multiprocessing


def update_kline_data(kline_data, kline_tmp):
    """更新K线数据（保持原函数不变）"""
    if len(kline_tmp) < 2 or len(kline_data) == 0:
        return kline_data

    new_row = kline_tmp.iloc[-2:-1]
    new_datetime = new_row['datetime'].iloc[0]

    if not (kline_data['datetime'] == new_datetime).any():
        kline_data = pd.concat([kline_data, new_row])
        kline_data = kline_data.sort_values('datetime').reset_index(drop=True)

    return kline_data


def download_month(params):
    """下载单个月份数据的进程函数"""
    symbol, duration_seconds, start_dt, end_dt, username, password = params
    try:
        auth = TqAuth(username, password)
        backtest = TqBacktest(start_dt=start_dt, end_dt=end_dt)
        api = TqApi(backtest=backtest, auth=auth)

        # 获取K线数据
        kline_data = api.get_kline_serial(symbol, duration_seconds, data_length=10000).dropna()
        kline_tmp = api.get_kline_serial(symbol, duration_seconds, data_length=10)

        print(f"开始下载 {start_dt.strftime('%Y-%m')} 数据...")

        while True:
            api.wait_update()
            if api.is_changing(kline_tmp.iloc[-1], 'datetime'):
                kline_data = update_kline_data(kline_data, kline_tmp)
            else:
                # 生成包含月份信息的文件名
                filename = f"kline_data_{duration_seconds}_{start_dt.strftime('%Y-%m')}.csv"
                kline_data.to_csv(filename)
                print(f"已保存 {filename}")
                break

        api.close()
        return True
    except Exception as e:
        print(f"下载 {start_dt.strftime('%Y-%m')} 数据时出错: {str(e)}")
        return False


def generate_month_params(start_str, end_str, symbol, username, password):
    """生成多进程参数列表"""
    params_list = []
    duration_seconds = 60

    current_dt = datetime.strptime(start_str, "%Y-%m-%d")
    end_dt = datetime.strptime(end_str, "%Y-%m-%d")

    while current_dt <= end_dt:
        # 计算当月结束时间
        month_end = current_dt + relativedelta(months=1) - timedelta(days=1)
        if month_end > end_dt:
            month_end = end_dt

        # 添加任务参数
        params = (
            symbol,
            duration_seconds,
            current_dt,
            month_end,
            username,
            password
        )
        params_list.append(params)

        # 移到下个月
        current_dt = current_dt + relativedelta(months=1)

    return params_list


if __name__ == '__main__':
    # 配置参数
    symbol = '<EMAIL>'
    username = "smartmanp"
    password = "ftp123"
    start_date = '2019-01-01'
    end_date = '2024-12-31'

    # 生成任务参数列表
    task_params = generate_month_params(start_date, end_date, symbol, username, password)

    # 创建进程池（最大并发进程数10）
    pool = multiprocessing.Pool(processes=10)

    # 提交所有任务
    results = []
    for params in task_params:
        results.append(pool.apply_async(download_month, (params,)))

    # 等待所有任务完成
    pool.close()
    pool.join()

    # 检查任务结果
    success_count = sum([r.get() for r in results if r.get() is not None])
    print(f"所有任务完成，成功{success_count}个，失败{len(results) - success_count}个")