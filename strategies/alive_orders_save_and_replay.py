from tqsdk import TqApi, TqAccount, TqKq
import pickle
import time
def save_alive_orders(api):
    orders = api.get_order()
    orderlist = []

    for order in orders.items():
        orderlist.append(order)

    orderlist1 = []
    for o in orderlist:
        orderlist1.append(o[1])

    aliveorderlist = []
    for o in orderlist1:
        if o.status == 'ALIVE':
            tmporder = [o.exchange_id, o.instrument_id, o.direction, o.offset, o.volume_orign, o.limit_price]
            if tmporder not in orderlist:
                aliveorderlist.append(tmporder)

    print('number of active orders:', len(aliveorderlist))

    finishedorderlist = []

    for o in orderlist1:
        if o.status == 'FINISHED':
            tmporder = [o.exchange_id, o.instrument_id, o.direction, o.offset, o.volume_orign, o.limit_price]
            if tmporder not in finishedorderlist:
                finishedorderlist.append(tmporder)

    if aliveorderlist:
        filename=api.get_account().user_id+'_alive_orders.pkl'
        f = open(filename, 'wb')
        pickle.dump(aliveorderlist, f)
        f.close()
        print('save alive orders done...')

    if finishedorderlist:
        filename = api.get_account().user_id + '_finished_orders.pkl'
        f = open(filename, 'wb')
        pickle.dump(finishedorderlist, f)
        f.close()
        print('save finished orders done...')


def replay_alive_orders(api):
    userid = api.get_account().user_id
    filename = userid + '_alive_orders.pkl'
    file = open(filename, 'rb')
    orders = pickle.load(file)

    print('total orders:',len(orders))

    for o in orders:
        symbol = o[0]+'.'+o[1]
        direction = o[2]
        offset = o[3]
        volume = o[4]
        price = o[5]

        order=api.insert_order(symbol, direction=direction, offset=offset, limit_price=price, volume=volume)





def runstrategy():

    from datetime import date
    # from accounts import cyzjy as acct


    product = 'OI'
    symbol=product
    interval = 60
    bklimit = 500
    sklimit = 500
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    api = TqApi(TqKq(), auth="bigwolf,ftp123")
    # api = TqApi(TqKq(), auth="walkquant,ftp123")
    symbol = api.query_cont_quotes(product_id=product)[0]


    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(symbol, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()
    action = input("Save the Orders,please press S; Replay the Orders, please press R:(S/R)")
    if action.upper() == 'R':
        replay_alive_orders(api)
        api.close()
        return
    elif action.upper() == 'S':

        save_alive_orders(api)
        api.close()
        return
    else:
        print('wrong input, quit.')

        api.close()

        return

    # orders = api.get_order()
    # save_orders = True

    while True:

        if (time.localtime().tm_hour == 14 and time.localtime().tm_min >= 59) and save_orders:
            save_alive_orders(api)
            save_orders=False

        api.wait_update()
        # print('api watching...')

        if api.is_changing(daybars.iloc[-1], "datetime"):
            save_orders = True
            dayopensp = True
            dayopenbp = True

            replay_alive_orders(api)


            print('日线数据更新完成。。。')

        if api.is_changing(orders):
            print('orders changed.')



if __name__ =="__main__":
    runstrategy()
