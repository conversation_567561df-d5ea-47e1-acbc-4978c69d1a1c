#!/bin/bash

set -e

# === 可配置参数 ===
SCRIPT_NAME="weiswave_claude_df_bigwolf_eb60.py"
BASE_NAME="weiswave_claude_df_bigwolf_eb60"
VERSION=$(date +"%Y%m%d_%H%M%S")
BUILD_MODE=${1:-dev}
BUILD_ROOT="dist"
BUILD_DIR="$BUILD_ROOT/${BASE_NAME}_${BUILD_MODE}_${VERSION}"
ZIP_NAME="${BASE_NAME}_${VERSION}.zip"

echo "🛠 构建模式: $BUILD_MODE"
echo "📂 输出目录: $BUILD_DIR"

# === 获取 tqsdk lzma 文件路径 ===
LZMA_FILE=$(python3 -c 'import tqsdk, os; print(os.path.join(os.path.dirname(tqsdk.__file__), "expired_quotes.json.lzma"))')

# === 通用参数 ===
COMMON_ARGS="
  --include-package=pandas
  --include-module=pandas._config.localization
  --include-package=tqsdk
  --include-package=websockets
  --include-module=websockets.asyncio
  --include-module=websockets.legacy
  --include-data-files=$LZMA_FILE=tqsdk/expired_quotes.json.lzma
  --nofollow-import-to=tests,pytest,unittest,doctest
  --enable-plugin=anti-bloat
  --jobs=30
  --output-dir=$BUILD_DIR
"

# === 判断源码是否修改（略高级） ===
LAST_HASH_FILE=".last_build_hash"
CURRENT_HASH=$(find . -type f \( -name '*.py' -o -name '*.json' \) -exec sha1sum {} \; | sha1sum)

if [ -f "$LAST_HASH_FILE" ]; then
  LAST_HASH=$(cat "$LAST_HASH_FILE")
  if [ "$CURRENT_HASH" == "$LAST_HASH" ]; then
    echo "🔁 未检测到源码改动，跳过构建。"
    exit 0
  fi
fi

# === 正式构建 ===
if [ "$BUILD_MODE" = "prod" ]; then
  echo "🚀 生产编译中..."
  nuitka \
    --standalone \
    $COMMON_ARGS \
    "$SCRIPT_NAME"
  
  echo "📦 打包为 zip..."
  cd "$BUILD_DIR"
  zip -r "../$ZIP_NAME" ./*
  cd -
else
  echo "🔧 开发模式编译中..."
  nuitka \
    --module \
    $COMMON_ARGS \
    "$SCRIPT_NAME"

  echo "🧪 开发编译成功，尝试运行..."
  python3 -c "import ${BASE_NAME%%.py}" || echo "⚠️ 运行失败，可能缺 main 函数或其他问题"
fi

# === 保存当前哈希
echo "$CURRENT_HASH" > "$LAST_HASH_FILE"

echo "✅ 构建完成！输出目录：$BUILD_DIR"
if [ "$BUILD_MODE" = "prod" ]; then
  echo "📦 ZIP 包位置：$BUILD_ROOT/$ZIP_NAME"
fi
