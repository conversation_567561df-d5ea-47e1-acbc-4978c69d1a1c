import time

# 主程序
from tqsdk import Tq<PERSON><PERSON>, TqAuth, TqKq
from multiTimeFrames_base_with_margin_check import MultiTimeframeStrategy
from utils.utils import parse_time, get_time_period, is_trading_time

if __name__ == "__main__":
    from accountSimulate import thj as account

    account = account.tqacc
    api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)

    try:
        product_id = 'OI'
        # product_id = 'rb'
        # product_id = 'SA'

        symbol = api.query_cont_quotes(product_id=product_id)[0]
        symbol = 'CZCE.OI509'
        print(f"交易的合约是: {symbol}")
        time_periods = get_time_period(api, symbol)
        posfile_name = f"{symbol}_{account.split(',')[0]}.json"
        # 定义交易周期（以秒为单位）和每个周期的持仓限制
        # timeframes = [60, 180, 800, 900]  # 1分钟, 3分钟, 13分20秒, 15分钟
        max_positions = {
            15: {'long': 80, 'short': 80},
            60: {'long': 50, 'short': 50},
            180: {'long': 30, 'short': 30},
            300: {'long': 20, 'short': 20},
            800: {'long': 15, 'short': 15},
            900: {'long': 10, 'short': 10}
        }

        while True:

            if is_trading_time(time_periods):
                try:
                    api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)
                    strategy = MultiTimeframeStrategy(api, symbol, max_positions, positions_file=posfile_name)

                    while True:
                        api.wait_update()
                        strategy.update()

                except Exception as e:
                    print(e)
                    time.sleep(10)


            else:
                print('非交易时间:', time.asctime())
                time.sleep(1)
                # continue

    except KeyboardInterrupt:
        print("策略已停止")

    finally:
        api.close()
