from TimeRoseMA_cross_multi_interval_class_base_1 import timeRoseMultiInterval, pos_interval_info

if __name__ == '__main__':
    import pandas as pd

    symbol = 'CZCE.OI601'
    account = 'bigwolf,ftp123'
    pos_interval_info_file = account.split(',')[0] + '_' + symbol.split('.')[1] + '_pos_interval_info.pkl'

    try:
        instance_map = pd.read_pickle(pos_interval_info_file)
        print('持仓数据读取完成。')
    except Exception as e:
        print(e)

        s60 = pos_interval_info(1, 1, 1, 1, 1)
        s180 = pos_interval_info(3, 3, 0, 0, 1)
        s300 = pos_interval_info(5, 5, 0, 0, 1)
        s900 = pos_interval_info(15, 15, 0, 0, 1)

        instance_map = {
            60: s60,
            180: s180,
            300: s300,
            900: s900
        }
        print('持仓数据不存在，初始化持仓数据完成。')

    bot = timeRoseMultiInterval(account, symbol, bklimit=20, sklimit=20, singlevolume=1,
                                interval_parameters=instance_map, pos_instance_file=pos_interval_info_file)

    bot.run()

