#!/bin/bash

FILE_LIST=(
    "strategies/CZCE.OI309_15.pkl"
    "strategies/CZCE.OI311_15.pkl"
    "strategies/CZCE.OI40115.pkl"
    "strategies/CZCE.OI401300.pkl"
    "strategies/CZCE.OI40160.pkl"
    "strategies/CZCE.OI401900.pkl"
    "strategies/CZCE.OI401_15.pkl"
    "strategies/CZCE.OI405_15.pkl"
) # Add your file paths here, separated by spaces or newlines if they are on different lines in the script editor.

for FILE in "${FILE_LIST[@]}"; do
     git rm --cached "$FILE" || { echo "Failed to remove $FILE." ; continue; }
done
git add . && git commit -m "Remove specific files that were not part of any working tree or index state before the last checkout and are no longer
necessary in version control history after deletion from workspace/working directory."
