from strategyMA_cross_dual_direction_hedge import ma_cross

product = 'pp'
interval = 60
bklimit = 125
sklimit = 125
single_volume = 1




def runstrategy(product, interval, sklimit, bklimit, single_volume):
    from tqsdk import TqApi, TqKq, TqBacktest, TqAuth
    from datetime import date


    # 交易账号设置
    api = TqApi(TqKq(), auth=("wolfquant,ftp123"))
    symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


runstrategy(product, interval, sklimit, bklimit, single_volume)