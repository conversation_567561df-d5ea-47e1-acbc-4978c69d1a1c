import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any

class TradingStrategy:
    def __init__(self, initial_capital: float = 100000, ma_window: int = 13, max_positions: int = 10):
        self.initial_capital = initial_capital
        self.portfolio_value = initial_capital
        self.trades: List[Dict[str, Any]] = []
        self.signal_profits: List[float] = []
        self.contract_multiplier = 10
        self.ma_window = ma_window
        self.price_history = []
        
        self.long_positions: List[float] = []
        self.short_positions: List[float] = []
        self.long_profits: List[float] = []
        self.short_profits: List[float] = []
        
        self.max_positions = max_positions

    def calculate_improved_ma(self, prices: List[float]) -> float:
        return sum(prices[-self.ma_window:]) / len(prices[-self.ma_window:])

    def on_bar(self, bar: pd.Series) -> None:
        self.price_history.append(bar['close'])
        
        if len(self.price_history) >= self.ma_window:
            current_ma = self.calculate_improved_ma(self.price_history)
            prev_ma = self.calculate_improved_ma(self.price_history[:-1])
            
            if bar['close'] > current_ma and self.price_history[-2] <= prev_ma:
                self.handle_long_signal(bar)
            elif bar['close'] < current_ma and self.price_history[-2] >= prev_ma:
                self.handle_short_signal(bar)
        
        # 更新组合价值，包括未实现盈亏
        self.update_portfolio_value(bar['close'])

    def handle_long_signal(self, bar: pd.Series) -> None:
        self.close_profitable_shorts(bar)
        
        if len(self.long_positions) < self.max_positions:
            self.long_positions.append(bar['close'])
            self.execute_trade(bar, 1, '开多单')
        else:
            print(f"多单持仓已达到最大限制 {self.max_positions}，不再开新多单。")

    def handle_short_signal(self, bar: pd.Series) -> None:
        self.close_profitable_longs(bar)
        
        if len(self.short_positions) < self.max_positions:
            self.short_positions.append(bar['close'])
            self.execute_trade(bar, -1, '开空单')
        else:
            print(f"空单持仓已达到最大限制 {self.max_positions}，不再开新空单。")

    def close_profitable_longs(self, bar: pd.Series) -> None:
        for long_price in self.long_positions[:]:
            if bar['close'] > long_price:
                profit = (bar['close'] - long_price) * self.contract_multiplier
                self.long_profits.append(profit)
                self.long_positions.remove(long_price)
                self.execute_trade(bar, -1, '平多单', profit)

    def close_profitable_shorts(self, bar: pd.Series) -> None:
        for short_price in self.short_positions[:]:
            if bar['close'] < short_price:
                profit = (short_price - bar['close']) * self.contract_multiplier
                self.short_profits.append(profit)
                self.short_positions.remove(short_price)
                self.execute_trade(bar, 1, '平空单', profit)

    def execute_trade(self, bar: pd.Series, direction: int, action: str, profit: float = 0) -> None:
        trade = {
            'datetime': bar.name,
            'price': bar['close'],
            'action': action,
            'quantity': 1,
            'symbol': bar['symbol'],
            'portfolio_value': self.portfolio_value
        }
        if profit != 0:
            trade['profit'] = profit
        self.trades.append(trade)

    def update_portfolio_value(self, current_price: float) -> None:
        unrealized_pnl = sum((current_price - price) * self.contract_multiplier for price in self.long_positions) + \
                         sum((price - current_price) * self.contract_multiplier for price in self.short_positions)
        realized_pnl = sum(self.long_profits) + sum(self.short_profits)
        self.portfolio_value = self.initial_capital + realized_pnl + unrealized_pnl

    def get_results(self) -> Dict[str, Any]:
        total_realized_profit = sum(self.long_profits) + sum(self.short_profits)
        winning_trades = sum(1 for profit in self.long_profits + self.short_profits if profit > 0)
        total_trades = len(self.long_profits) + len(self.short_profits)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        return {
            'trades': self.trades,
            'long_profits': self.long_profits,
            'short_profits': self.short_profits,
            'total_realized_profit': total_realized_profit,
            'win_rate': win_rate,
            'final_portfolio_value': self.portfolio_value,
            'final_long_positions': len(self.long_positions),
            'final_short_positions': len(self.short_positions),
            'unrealized_long_pnl': sum((self.price_history[-1] - price) * self.contract_multiplier for price in self.long_positions),
            'unrealized_short_pnl': sum((price - self.price_history[-1]) * self.contract_multiplier for price in self.short_positions)
        }

class BacktestEngine:
    def __init__(self, strategy: TradingStrategy):
        self.strategy = strategy

    def run(self, data: pd.DataFrame) -> None:
        for _, bar in data.iterrows():
            self.strategy.on_bar(bar)

    def get_results(self) -> Dict[str, Any]:
        return self.strategy.get_results()

def print_results(results: Dict[str, Any]) -> None:
    for trade in results['trades']:
        print(f"时间: {trade['datetime']}, 动作: {trade['action']}, 价格: {trade['price']}, 数量: {trade['quantity']}, 交易品种: {trade['symbol']}")
        if 'profit' in trade:
            print(f"利润: {trade['profit']:.2f}")
        print(f"组合价值: {trade['portfolio_value']:.2f}")
        print("---")

    print(f"总实现盈亏: {results['total_realized_profit']:.2f}")
    print(f"未实现多头盈亏: {results['unrealized_long_pnl']:.2f}")
    print(f"未实现空头盈亏: {results['unrealized_short_pnl']:.2f}")
    print(f"总盈亏（包括未实现）: {results['final_portfolio_value'] - results['trades'][0]['portfolio_value']:.2f}")
    print(f"胜率: {results['win_rate']:.2%}")
    print(f"最终组合价值: {results['final_portfolio_value']:.2f}")
    print(f"最终多头持仓数量: {results['final_long_positions']}")
    print(f"最终空头持仓数量: {results['final_short_positions']}")

    print("多头交易盈亏:")
    for i, profit in enumerate(results['long_profits'], 1):
        print(f"交易 {i}: {profit:.2f}")

    print("空头交易盈亏:")
    for i, profit in enumerate(results['short_profits'], 1):
        print(f"交易 {i}: {profit:.2f}")

if __name__ == "__main__":
    # 读取CSV文件
    df = pd.read_csv('data5.csv')

    # 将datetime列转换为datetime类型并设置为索引
    df['datetime'] = pd.to_datetime(df['datetime'], unit='ns')
    df.set_index('datetime', inplace=True)
    df.sort_index(inplace=True)

    # 创建策略实例和回测引擎
    strategy = TradingStrategy(max_positions=10)  # 设置最大持仓数为10
    engine = BacktestEngine(strategy)

    # 运行回测
    engine.run(df)

    # 获取并打印结果
    results = engine.get_results()
    print_results(results)
