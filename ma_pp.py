import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMA import trmastrategy
from time import sleep
import time

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq, TqAccount

    symbol = 'DCE.pp2201'
    symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 16
    sklimit = 0
    single_volume = 1
    from accounts_zjy import cyzjy as acct

    # 设置日志文件
    mylog.add('./logs/' + acct.name + symbol + "{time}.log", encoding='utf-8')
    mylog.add(acct.name + symbol + "{time}.log", encoding='utf-8')

    # 创建api
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    # 交易账号设置
    # api = TqApi(TqKq(), auth="bigwolf,ftp123")

    trmastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
