
import pandas as pd
import numpy as np

def moving_average(a, b):
    result = []
    for idx, window in enumerate(b):
        if idx < window:
            result.append(None)  # or continue depending on how you want to handle the start
        else:
            result.append(sum(a[idx-window:idx]) / window)
    return result

def moving_average_pd(a, b):
    a = pd.Series(a)
    b = pd.Series(b)
    return a.rolling(window=b, min_periods=1).mean()
