"""
多时间周期策略回测演示
使用模拟数据展示策略效果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class MockDataGenerator:
    """模拟数据生成器"""
    
    def __init__(self, start_price: float = 9600, days: int = 30):
        self.start_price = start_price
        self.days = days
        
    def generate_1min_data(self) -> pd.DataFrame:
        """生成1分钟模拟数据"""
        # 生成时间序列
        start_time = datetime.now() - timedelta(days=self.days)
        minutes = self.days * 24 * 60
        
        timestamps = [start_time + timedelta(minutes=i) for i in range(minutes)]
        
        # 生成价格数据（带趋势和噪声）
        np.random.seed(42)
        
        # 基础趋势
        trend = np.sin(np.linspace(0, 4*np.pi, minutes)) * 200
        
        # 随机游走
        random_walk = np.cumsum(np.random.randn(minutes) * 5)
        
        # 组合价格
        prices = self.start_price + trend + random_walk
        
        # 生成OHLC数据
        data = []
        for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
            if i == 0:
                open_price = close
            else:
                open_price = data[i-1]['close']
            
            # 添加随机波动
            volatility = np.random.uniform(0.5, 2.0)
            high = max(open_price, close) + np.random.uniform(0, volatility)
            low = min(open_price, close) - np.random.uniform(0, volatility)
            volume = np.random.randint(100, 1000)
            
            data.append({
                'datetime': timestamp,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        return pd.DataFrame(data)
    
    def resample_to_timeframe(self, df_1m: pd.DataFrame, timeframe_minutes: int) -> pd.DataFrame:
        """将1分钟数据重采样到指定时间周期"""
        df = df_1m.copy()
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        # 重采样
        freq = f'{timeframe_minutes}T'
        resampled = df.resample(freq).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        resampled.reset_index(inplace=True)
        return resampled


class SimpleBacktestEngine:
    """简化回测引擎"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}  # {timeframe: {'direction': 'long/short', 'entry_price': float, 'quantity': int}}
        self.trade_log = []
        self.equity_curve = [initial_capital]
        
        # 策略参数
        self.short_ma = 5
        self.long_ma = 20
        self.max_position_ratio = 0.1
        self.commission_rate = 0.0003
        
    def calculate_ma_signals(self, df: pd.DataFrame) -> Dict:
        """计算均线信号"""
        if len(df) < self.long_ma:
            return {'signal': 'none', 'ma_short': 0, 'ma_long': 0}
        
        df = df.copy()
        df['ma_short'] = df['close'].rolling(self.short_ma).mean()
        df['ma_long'] = df['close'].rolling(self.long_ma).mean()
        df['ma_diff'] = df['ma_short'] - df['ma_long']
        
        current = df.iloc[-1]
        previous = df.iloc[-2] if len(df) > 1 else current
        
        # 金叉死叉
        if previous['ma_short'] <= previous['ma_long'] and current['ma_short'] > current['ma_long']:
            signal = 'golden_cross'
        elif previous['ma_short'] >= previous['ma_long'] and current['ma_short'] < current['ma_long']:
            signal = 'death_cross'
        else:
            # 回归信号
            if len(df) >= 5:
                recent_diffs = df['ma_diff'].tail(5).values
                if (current['ma_diff'] < 0 and len(recent_diffs) >= 3 and
                    recent_diffs[-1] > recent_diffs[-2] > recent_diffs[-3]):
                    signal = 'upward_regression'
                elif (current['ma_diff'] > 0 and len(recent_diffs) >= 3 and
                      recent_diffs[-1] < recent_diffs[-2] < recent_diffs[-3]):
                    signal = 'downward_regression'
                else:
                    signal = 'none'
            else:
                signal = 'none'
        
        return {
            'signal': signal,
            'ma_short': current['ma_short'],
            'ma_long': current['ma_long'],
            'price': current['close'],
            'is_up': current['close'] > current['open']
        }
    
    def should_open_position(self, current_signal: Dict, higher_signal: Dict) -> str:
        """判断开仓方向"""
        # 多仓条件：当前金叉 + 当前K线涨 + 大周期向上回归
        if (current_signal['signal'] == 'golden_cross' and 
            current_signal['is_up'] and 
            higher_signal['signal'] == 'upward_regression'):
            return 'long'
        
        # 空仓条件：当前死叉 + 当前K线跌 + 大周期向下回归
        elif (current_signal['signal'] == 'death_cross' and 
              not current_signal['is_up'] and 
              higher_signal['signal'] == 'downward_regression'):
            return 'short'
        
        return 'none'
    
    def open_position(self, timeframe: str, direction: str, price: float, timestamp: datetime):
        """开仓"""
        if timeframe in self.positions:
            return False
        
        # 计算仓位
        available_capital = self.current_capital * self.max_position_ratio / 5  # 5个时间周期
        quantity = int(available_capital / price)
        
        if quantity <= 0:
            return False
        
        self.positions[timeframe] = {
            'direction': direction,
            'entry_price': price,
            'quantity': quantity,
            'entry_time': timestamp
        }
        
        self.trade_log.append({
            'timestamp': timestamp,
            'timeframe': timeframe,
            'action': 'OPEN',
            'direction': direction,
            'price': price,
            'quantity': quantity
        })
        
        return True
    
    def close_position(self, timeframe: str, price: float, timestamp: datetime, reason: str = ""):
        """平仓"""
        if timeframe not in self.positions:
            return False
        
        position = self.positions[timeframe]
        
        # 计算盈亏
        if position['direction'] == 'long':
            pnl = (price - position['entry_price']) * position['quantity']
        else:
            pnl = (position['entry_price'] - price) * position['quantity']
        
        # 扣除手续费
        commission = position['entry_price'] * position['quantity'] * self.commission_rate * 2
        net_pnl = pnl - commission
        
        # 更新资金
        self.current_capital += net_pnl
        
        self.trade_log.append({
            'timestamp': timestamp,
            'timeframe': timeframe,
            'action': 'CLOSE',
            'direction': position['direction'],
            'price': price,
            'quantity': position['quantity'],
            'pnl': net_pnl,
            'reason': reason
        })
        
        del self.positions[timeframe]
        return True
    
    def run_backtest(self, data_dict: Dict[str, pd.DataFrame]):
        """运行回测"""
        print("开始回测...")
        
        # 获取1分钟数据作为主时间轴
        main_data = data_dict['1m']
        timeframes = ['1m', '15m', '4h', '1d']
        
        print(f"回测数据: {len(main_data)} 根1分钟K线")
        print(f"时间范围: {main_data.iloc[0]['datetime']} 到 {main_data.iloc[-1]['datetime']}")
        
        for i in range(len(main_data)):
            current_time = main_data.iloc[i]['datetime']
            current_price = main_data.iloc[i]['close']
            
            # 更新权益曲线
            self.equity_curve.append(self.current_capital)
            
            # 处理各时间周期（简化版本）
            for tf in timeframes:
                if self._should_process_timeframe(i, tf):
                    self._process_timeframe(tf, data_dict[tf], current_time, i)
        
        # 平掉所有持仓
        final_price = main_data.iloc[-1]['close']
        final_time = main_data.iloc[-1]['datetime']
        for tf in list(self.positions.keys()):
            self.close_position(tf, final_price, final_time, "回测结束")
        
        return self._calculate_results()
    
    def _should_process_timeframe(self, index: int, timeframe: str) -> bool:
        """判断是否处理该时间周期"""
        if timeframe == '1m':
            return True
        elif timeframe == '15m':
            return index % 15 == 0
        elif timeframe == '4h':
            return index % 240 == 0
        elif timeframe == '1d':
            return index % 1440 == 0
        return False
    
    def _process_timeframe(self, timeframe: str, df: pd.DataFrame, current_time: datetime, main_index: int):
        """处理时间周期信号"""
        # 找到对应的数据点
        df_filtered = df[pd.to_datetime(df['datetime']) <= current_time]
        if len(df_filtered) < self.long_ma:
            return
        
        # 计算当前信号
        current_signal = self.calculate_ma_signals(df_filtered)
        
        # 获取大周期信号（简化）
        timeframe_hierarchy = ['1m', '15m', '4h', '1d']
        current_idx = timeframe_hierarchy.index(timeframe)
        
        if current_idx < len(timeframe_hierarchy) - 1:
            higher_tf = timeframe_hierarchy[current_idx + 1]
            higher_data = df[pd.to_datetime(df['datetime']) <= current_time]
            higher_signal = self.calculate_ma_signals(higher_data) if len(higher_data) >= self.long_ma else {'signal': 'none'}
        else:
            higher_signal = {'signal': 'upward_regression'}  # 简化处理
        
        # 检查开仓
        direction = self.should_open_position(current_signal, higher_signal)
        if direction != 'none' and timeframe not in self.positions:
            self.open_position(timeframe, direction, current_signal['price'], current_time)
    
    def _calculate_results(self) -> Dict:
        """计算回测结果"""
        total_return = self.current_capital - self.initial_capital
        return_rate = total_return / self.initial_capital * 100
        
        # 最大回撤
        equity_series = pd.Series(self.equity_curve)
        rolling_max = equity_series.expanding().max()
        drawdown = (equity_series - rolling_max) / rolling_max * 100
        max_drawdown = drawdown.min()
        
        # 交易统计
        closed_trades = [t for t in self.trade_log if t['action'] == 'CLOSE']
        total_trades = len(closed_trades)
        
        if total_trades > 0:
            winning_trades = len([t for t in closed_trades if t['pnl'] > 0])
            win_rate = winning_trades / total_trades * 100
            avg_pnl = np.mean([t['pnl'] for t in closed_trades])
        else:
            winning_trades = 0
            win_rate = 0
            avg_pnl = 0
        
        return {
            'total_return': total_return,
            'return_rate': return_rate,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'win_rate': win_rate,
            'avg_pnl': avg_pnl,
            'equity_curve': self.equity_curve,
            'trade_log': self.trade_log
        }


def run_demo_backtest():
    """运行演示回测"""
    print("=" * 60)
    print("多时间周期策略回测演示")
    print("=" * 60)
    
    # 生成模拟数据
    print("生成模拟数据...")
    generator = MockDataGenerator(start_price=9600, days=30)
    data_1m = generator.generate_1min_data()
    
    # 生成多时间周期数据
    data_dict = {
        '1m': data_1m,
        '15m': generator.resample_to_timeframe(data_1m, 15),
        '4h': generator.resample_to_timeframe(data_1m, 240),
        '1d': generator.resample_to_timeframe(data_1m, 1440)
    }
    
    print("数据生成完成:")
    for tf, df in data_dict.items():
        print(f"  {tf}: {len(df)} 条数据")
    
    # 运行回测
    engine = SimpleBacktestEngine(initial_capital=100000)
    results = engine.run_backtest(data_dict)
    
    # 显示结果
    print("\n" + "=" * 60)
    print("回测结果")
    print("=" * 60)
    print(f"总收益: {results['total_return']:,.2f}")
    print(f"收益率: {results['return_rate']:.2f}%")
    print(f"最大回撤: {results['max_drawdown']:.2f}%")
    print(f"总交易次数: {results['total_trades']}")
    print(f"盈利交易: {results['winning_trades']}")
    print(f"胜率: {results['win_rate']:.2f}%")
    print(f"平均盈亏: {results['avg_pnl']:.2f}")
    
    # 绘制图表
    plot_results(results, data_1m)
    
    # 保存交易记录
    if results['trade_log']:
        trade_df = pd.DataFrame(results['trade_log'])
        trade_df.to_csv('demo_trade_log.csv', index=False, encoding='utf-8')
        print(f"\n交易记录已保存为 demo_trade_log.csv")
    
    return results


def plot_results(results: Dict, price_data: pd.DataFrame):
    """绘制回测结果"""
    try:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 价格走势
        ax1.plot(price_data['close'].values)
        ax1.set_title('价格走势')
        ax1.set_ylabel('价格')
        ax1.grid(True)
        
        # 权益曲线
        ax2.plot(results['equity_curve'])
        ax2.set_title('权益曲线')
        ax2.set_ylabel('资金')
        ax2.grid(True)
        
        # 回撤曲线
        equity_series = pd.Series(results['equity_curve'])
        rolling_max = equity_series.expanding().max()
        drawdown = (equity_series - rolling_max) / rolling_max * 100
        
        ax3.fill_between(range(len(drawdown)), drawdown, 0, alpha=0.3, color='red')
        ax3.set_title('回撤曲线')
        ax3.set_ylabel('回撤 (%)')
        ax3.grid(True)
        
        # 交易分布
        if results['trade_log']:
            closed_trades = [t for t in results['trade_log'] if t['action'] == 'CLOSE']
            pnls = [t['pnl'] for t in closed_trades]
            
            ax4.hist(pnls, bins=20, alpha=0.7, color='blue')
            ax4.axvline(x=0, color='red', linestyle='--')
            ax4.set_title('盈亏分布')
            ax4.set_xlabel('盈亏')
            ax4.set_ylabel('频次')
            ax4.grid(True)
        
        plt.tight_layout()
        plt.savefig('demo_backtest_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("图表已保存为 demo_backtest_results.png")
        
    except Exception as e:
        print(f"绘图失败: {e}")


if __name__ == "__main__":
    run_demo_backtest()
