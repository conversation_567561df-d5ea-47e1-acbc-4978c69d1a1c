import schedule
import time

from speaktextng import speak_text
def job():
    print([time.asctime(), "I'm working..."])
    speak_text("I'm working...")

schedule.every(15).minutes.do(job)
# schedule.every().hour.do(job)
# schedule.every().day.at("10:30").do(job)
# schedule.every().monday.do(job)
# schedule.every().wednesday.at("13:15").do(job)
# schedule.every().minute.at(":17").do(job)




while True:
    schedule.run_pending()
    time.sleep(1)