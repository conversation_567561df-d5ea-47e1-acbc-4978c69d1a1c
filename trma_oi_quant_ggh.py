# import copy
# from myfunction import *

# from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
# from tradefuncs import *
# from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMAsdk2 import trmastrategy
from strategies.alive_orders_save_and_replay import save_alive_orders, replay_alive_orders
# from time import sleep
# import time

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    config = {
        'symbol': 'CZCE.OI209',
        'interval': 15,
        'bklimit': 330,
        'sklimit': 330,
        'single_volume': 1
    }

    # 交易账号设置
    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301", disable_print=True)
    symbol = api.query_cont_quotes(product_id='OI')[0]
    save_alive_orders(api)

    trmastrategy(api, symbol=symbol, interval=config['interval'], single_volume=config['single_volume'], bklimit=config['bklimit'], sklimit=config['sklimit'])
