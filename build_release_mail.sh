#!/bin/bash

set -e

# === 配置项 ===
SCRIPT_NAME="weiswave_claude_df_bigwolf_eb60.py"
BASE_NAME="weiswave_claude_df_bigwolf_eb60"
VERSION=$(date +"%Y%m%d_%H%M%S")
BUILD_MODE=${1:-dev}
BUILD_ROOT="dist"
BUILD_DIR="$BUILD_ROOT/${BASE_NAME}_${BUILD_MODE}_${VERSION}"
ZIP_NAME="${BASE_NAME}_${VERSION}.zip"
EMAIL="<EMAIL>"

echo "🛠 构建模式: $BUILD_MODE"
echo "📂 输出目录: $BUILD_DIR"

# === 获取 lzma 文件路径 ===
LZMA_FILE=$(python3 -c 'import tqsdk, os; print(os.path.join(os.path.dirname(tqsdk.__file__), "expired_quotes.json.lzma"))')

COMMON_ARGS="
  --include-package=pandas
  --include-module=pandas._config.localization
  --include-package=tqsdk
  --include-package=websockets
  --include-module=websockets.asyncio
  --include-module=websockets.legacy
  --include-data-files=$LZMA_FILE=tqsdk/expired_quotes.json.lzma
  --nofollow-import-to=tests,pytest,unittest,doctest
  --enable-plugin=anti-bloat
  --jobs=30
  --output-dir=$BUILD_DIR
"

LAST_HASH_FILE=".last_build_hash"
CURRENT_HASH=$(find . -type f \( -name '*.py' -o -name '*.json' \) -exec sha1sum {} \; | sha1sum)

if [ -f "$LAST_HASH_FILE" ]; then
  LAST_HASH=$(cat "$LAST_HASH_FILE")
  if [ "$CURRENT_HASH" == "$LAST_HASH" ]; then
    echo "🔁 未检测到源码改动，跳过构建。"
    exit 0
  fi
fi

BUILD_STATUS="成功 ✅"

trap 'BUILD_STATUS="失败 ❌"; send_email' ERR

send_email() {
  SUBJECT="Nuitka 编译[$BUILD_MODE] $BUILD_STATUS"
  BODY="构建模式: $BUILD_MODE\n版本: $VERSION\n状态: $BUILD_STATUS\n\n输出目录: $BUILD_DIR"

  if [ "$BUILD_MODE" = "prod" ]; then
    BODY+="\nZIP 包: $BUILD_ROOT/$ZIP_NAME"
  fi

  echo -e "$BODY" | mail -s "$SUBJECT" "$EMAIL"
  echo "📧 已发送邮件通知到: $EMAIL"
}

# === 正式构建 ===
if [ "$BUILD_MODE" = "prod" ]; then
  echo "🚀 正在进行生产编译..."
  nuitka --standalone $COMMON_ARGS "$SCRIPT_NAME"
  cd "$BUILD_DIR" && zip -r "../$ZIP_NAME" ./* && cd -
else
  echo "🔧 正在进行开发编译..."
  nuitka --module $COMMON_ARGS "$SCRIPT_NAME"
  python3 -c "import ${BASE_NAME%%.py}" || echo "⚠️ 模块导入测试失败"
fi

echo "$CURRENT_HASH" > "$LAST_HASH_FILE"

send_email
