import os
import subprocess

def check_if_process_running(process_name):
    try:
        # 获取所有进程的命令行参数
        cmd = "ps -eo args"
        # 执行命令，获取输出
        output = subprocess.check_output(cmd, shell=True)
        # 将输出划分为行
        procs = output.decode().split("\n")
        # 对每一行进行检查
        for proc in procs:
            # 如果完全匹配，则返回True
            if proc.lower() == process_name.lower():
                print(process_name, "进程正在运行")
                return True
        # 如果到这里都没有找到匹配的进程，则返回False
        print(process_name, "进程未运行")
        return False
    except Exception as e:
        print(e)
        return False

check_if_process_running('python /home/<USER>/wolfnasgit/new/dingdangNo6/trma_oi_ggh.py')
