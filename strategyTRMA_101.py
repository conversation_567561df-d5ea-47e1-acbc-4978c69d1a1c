import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog

from time import sleep
import time


def trmastrategy(api, symbol, interval, single_volume, bklimit, sklimit, period=13):
    strategyname = 'trma'
    acct = api.get_account()
    userid = acct.user_id.split('-')[0]
    logfilename = '_'.join([userid, symbol, strategyname])
    mylog.add(logfilename + '.log', encoding='utf-8')
    savebars = False
    quote = api.get_quote(symbol)

    if '@' in symbol:
        SYMBOL = quote.underlying_symbol
    else:
        SYMBOL = symbol

    trading_symbol = SYMBOL.split('.')[1]

    ticks = api.get_tick_serial(SYMBOL)
    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines

    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()
    hl = daybar.high - daybar.low
    ho = daybar.high - daybar.open
    ol = daybar.open - daybar.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    N = 23
    O = klines1.open
    H = klines1.high
    L = klines1.low
    C = klines1.close

    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    KNUM = len(klines1)
    gapcover = False

    position = api.get_position(SYMBOL)
    acc = api.get_account()
    bkvol = position.pos_long
    bkvol_yd = position.pos_long_his
    bkvol_td = position.pos_long_today

    skvol = position.pos_short
    skvol_yd = position.pos_short_his
    skvol_td = position.pos_short_today

    skflag = True
    bkflag = True
    spflag = True
    bpflag = True
    speakyn = True
    dayopensp = True
    dayopenbp = True

    fundavailable = acc.available
    disp_day_info(quote, daymean, last3mean, hlmax)

    while True:

        api.wait_update()

        if api.is_changing(daybars.iloc[-1], "datetime"):
            savebars = True
            dayopensp = True
            dayopenbp = True

            hl = daybar.high - daybar.low
            ho = daybar.high - daybar.open
            ol = daybar.open - daybar.low
            daymean = int(hl.mean())
            homean = ho.mean()
            olmean = ol.mean()
            hlmax = max(hl)
            last3mean = int(hl.iloc[-4:-1].mean())
            dayopen = quote.open
            dayhigh = quote.highest
            daylow = quote.lowest
            preclose = quote.pre_close
            dayvib = dayhigh - daylow
            gap = dayopen - preclose
            gapcover = False
            print('日线数据更新完成。。。')
            mylog.info('日线数据更新完成。。。')

        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            # 恢复下单标志, 确保k线周期内如果有多个信号的话,执行一次.
            disp_day_info(quote, daymean, last3mean, hlmax)
            speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                klines1 = klines1.append(newk)

            O = klines1.open
            H = klines1.high
            L = klines1.low
            C = klines1.close

            hl = ema(H - L, 23).iloc[-1]
            cover_gap = max(int(hl), 3)

            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today
            bkfreeze = position.volume_long_frozen

            # volume_long_frozen_today: int
            # volume_long_frozen_his: int




            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today
            skfreeze = position.volume_short_frozen


            trmac = trma(C, period)

            ups = crossup(C, trmac)
            dns = crossdown(C, trmac)
            bkdist = be_apart_from(ups.tolist())
            skdist = be_apart_from(dns.tolist())
            if bkdist > skdist:
                signalnow = 'SK'
                distnow = skdist
            else:
                signalnow = 'BK'
                distnow = bkdist

            signalprice = C.iloc[-distnow]
            sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
            print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)
            uplist = ups.tolist()
            average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
            print('平均信号距离：', average_signal_distance)
            print('upsignal:  ', ups.iloc[-40:].tolist())
            print('downsignal:', dns.iloc[-40:].tolist())

            # 交易部分
            basevalue = average_signal_distance * 2
            order_volume = (basevalue - distnow)*single_volume if distnow < average_signal_distance else single_volume
            order_volume = single_volume
            if C.iloc[-1] > trmac.iloc[-1]:
                print('下多单信号区间。。。')
                print('skvol', skvol, 'sklimit', sklimit, 'bkvol:', bkvol, 'bklimit', bklimit)
                order_volume = min(order_volume, bklimit-bkvol)
                if bkvol < bklimit:  # and hlmax < daymean
                    print('持仓小于持仓限额，下多单。。。')
                    bkprice = quote.last_price - 1

                    BK(api, symbol=SYMBOL, order_price=bkprice, volume=order_volume)

                    spprice = max(H.iloc[-2], bkprice + cover_gap+1)
                    SP(api, symbol=SYMBOL, order_price=spprice, volume=order_volume, today=True)
                    mylog.info(['bk', SYMBOL, bkprice, order_volume, cover_gap])
                    mylog.info(['sp', SYMBOL, spprice, order_volume, spprice - bkprice])
                    bkflag = False

                else:
                    print('持仓限额:', bklimit, '多单持仓:', bkvol, '持仓超过持仓限额，不再下单。。。')

            if C.iloc[-1] < trmac.iloc[-1]:
                print('做空区间。。。。', 'skvol', skvol, 'sklimit', sklimit, 'bkvol:', bkvol,'bklimit', bklimit)

                order_volume = min(order_volume, sklimit - skvol)
                if skvol < sklimit:  # and hlmax < daymean:
                    print('持仓小于持仓限额， 下空单。。。')
                    skprice = quote.last_price + 1
                    SK(api, symbol=SYMBOL, order_price=quote.last_price - 1, volume=order_volume)

                    bpprice = min(L.iloc[-2], skprice - cover_gap-1)
                    BP(api, symbol=SYMBOL, order_price=bpprice, volume=order_volume, today=True)
                    mylog.info(['sk', SYMBOL, skprice, order_volume, cover_gap])
                    mylog.info(['bp', SYMBOL, bpprice, order_volume, skprice - bpprice])
                else:
                    print('持仓限额：', sklimit, '持仓：', skvol, '持仓超过持仓限额， 不再下单。。。。')

        if api.is_changing(quote):
            if gap > 0:
                if quote.lowest < quote.pre_close:
                    gapcover = True
            else:
                if quote.highest > quote.pre_close:
                    gapcover = True

        if api.is_changing(position):
            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today

        if time.localtime().tm_hour > 15 and time.localtime().tm_min > 15 and savebars:
            klines1.to_csv(SYMBOL + '.csv')
            savebars = False

    pass


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    symbol = 'SHFE.ag2105'
    # symbol = 'CZCE.SR101'
    # symbol = 'DCE.pp2105'
    # symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 3000000
    sklimit = 3000
    single_volume = 500

    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123")

    emastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
