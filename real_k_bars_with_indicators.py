from tqsdk import TqApi, TqAuth, TqKq
from tqsdk.ta import MA, ATR, BOLL
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import pandas as pd
import numpy as np

# 初始化 TqApi
# api = TqApi(auth=TqAuth("YOUR_USERNAME", "YOUR_PASSWORD"))
api = TqApi(TqKq(), auth="wolfquant2023,Qiai1301")
# 获取 CZCE.OI501 的 1 分钟 K 线数据
klines = api.get_kline_serial("CZCE.OI501", 60)

# 计算指标
ma5 = MA(klines, 5)
boll = BOLL(klines, 20, 2)
atr = ATR(klines, 14)


def plot_candlestick(ax, df):
    ax.clear()
    width = 0.8
    width2 = 0.1

    up = df[df.close > df.open]
    down = df[df.close < df.open]
    equal = df[df.close == df.open]

    # 上涨K线 - 红色
    ax.bar(up.index, up.close - up.open, width, bottom=up.open, color='red')
    ax.vlines(up.index, up.low, up.high, color='red', linewidth=1)

    # 下跌K线 - 绿色
    ax.bar(down.index, down.close - down.open, width, bottom=down.open, color='green')
    ax.vlines(down.index, down.low, down.high, color='green', linewidth=1)

    # 开盘价等于收盘价的K线
    ax.bar(equal.index, width2, width, bottom=equal.open - width2 / 2, color='cyan')
    ax.vlines(equal.index, equal.low, equal.high, color='cyan', linewidth=1)

    # 绘制 MA5 线
    ax.plot(df.index, ma5.ma, color='yellow', linewidth=1, label='MA5')

    # 绘制布林带
    ax.plot(df.index, boll.mid, color='white', linestyle='--', linewidth=1, label='BOLL Mid')
    ax.plot(df.index, boll.upper, color='magenta', linestyle='--', linewidth=1, label='BOLL Upper')
    ax.plot(df.index, boll.lower, color='cyan', linestyle='--', linewidth=1, label='BOLL Lower')

    # 绘制 CCCC, LLLL, HHHH 线
    ax.plot(df.index, df['close'].shift(1), color='white', linestyle='--', linewidth=2, label='CCCC')
    ax.plot(df.index, df[['open', 'close']].shift(1).min(axis=1), color='lime', linestyle='--', linewidth=1,
            label='LLLL')
    ax.plot(df.index, df[['open', 'close']].shift(1).max(axis=1), color='magenta', linestyle='--', linewidth=1,
            label='HHHH')

    # 绘制其他指标线
    for i in range(len(df) - 1):
        if df['close'].iloc[i] > boll.upper.iloc[i] and df['close'].iloc[i] > ma5.ma.iloc[i]:
            ax.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['high'].iloc[i]], color='cyan')
        elif df['close'].iloc[i] < boll.lower.iloc[i] and df['close'].iloc[i] < ma5.ma.iloc[i]:
            ax.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['high'].iloc[i]], color='yellow')

    ax.set_title('CZCE.OI501 1分钟K线图')
    ax.grid(True, linestyle=':', alpha=0.3)
    ax.legend(loc='upper left')
    plt.xticks(rotation=45)


fig, ax = plt.subplots(figsize=(15, 8))
plt.style.use('dark_background')


def update(frame):
    api.wait_update()
    if api.is_changing(klines.iloc[-1], "datetime"):
        df = pd.DataFrame({
            "open": klines.open,
            "high": klines.high,
            "low": klines.low,
            "close": klines.close
        })
        plot_candlestick(ax, df)


ani = FuncAnimation(fig, update, interval=100, cache_frame_data=False)
plt.show()

# 主循环
try:
    while True:
        api.wait_update()
except KeyboardInterrupt:
    print("程序已终止")
finally:
    api.close()