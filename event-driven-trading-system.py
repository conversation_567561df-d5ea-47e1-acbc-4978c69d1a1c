import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any

class TradingStrategy:
    def __init__(self, initial_capital: float = 100000):
        self.portfolio_value = initial_capital
        self.current_position = 0
        self.trades: List[Dict[str, Any]] = []
        self.signal_profits: List[float] = []
        self.entry_price = 0
        self.current_quantity = 1
        self.consecutive_losses = 0
        self.contract_multiplier = 10

    def calculate_improved_ma(self, series: pd.Series, window: int = 13) -> pd.Series:
        return series.rolling(window=window, min_periods=1).mean()

    def on_bar(self, bar: pd.Series, prev_bar: pd.Series) -> None:
        # 计算移动平均线
        if 'MA' not in bar:
            bar['MA'] = self.calculate_improved_ma(pd.Series([prev_bar['close'], bar['close']]))[-1]

        # 生成交易信号
        if bar['close'] > bar['MA'] and prev_bar['close'] <= prev_bar['MA']:
            signal = 1  # 买入信号
        elif bar['close'] < bar['MA'] and prev_bar['close'] >= prev_bar['MA']:
            signal = -1  # 卖出信号
        else:
            signal = 0  # 无信号

        if signal != 0:
            self.execute_trade(bar, signal)

    def execute_trade(self, bar: pd.Series, signal: int) -> None:
        if self.current_position == 0:
            # 开仓
            self.current_position = signal
            self.entry_price = bar['close']
            
            self.trades.append({
                'datetime': bar.name,
                'price': self.entry_price,
                'action': '开多单' if signal == 1 else '开空单',
                'quantity': self.current_quantity,
                'symbol': bar['symbol'],
                'portfolio_value': self.portfolio_value
            })
        else:
            # 平仓
            exit_price = bar['close']
            price_diff = exit_price - self.entry_price if self.current_position > 0 else self.entry_price - exit_price
            profit = price_diff * self.contract_multiplier * self.current_quantity
            self.portfolio_value += profit
            
            self.trades.append({
                'datetime': bar.name,
                'price': exit_price,
                'action': '平多单' if self.current_position > 0 else '平空单',
                'quantity': self.current_quantity,
                'symbol': bar['symbol'],
                'profit': profit,
                'portfolio_value': self.portfolio_value
            })
            
            # 记录本次信号的盈亏
            self.signal_profits.append(profit)
            
            # 更新连续亏损次数和下单数量
            if profit < 0:
                self.consecutive_losses += 1
                self.current_quantity = min(self.current_quantity + 1, 10)  # 增加下单数量，但不超过10
            else:
                self.consecutive_losses = 0
                self.current_quantity = 1  # 盈利后重置为1
            
            # 反向开仓
            self.current_position = signal
            self.entry_price = exit_price
            
            self.trades.append({
                'datetime': bar.name,
                'price': self.entry_price,
                'action': '开多单' if signal == 1 else '开空单',
                'quantity': self.current_quantity,
                'symbol': bar['symbol'],
                'portfolio_value': self.portfolio_value
            })

    def get_results(self) -> Dict[str, Any]:
        total_profit = sum(trade['profit'] for trade in self.trades if 'profit' in trade)
        winning_trades = sum(1 for trade in self.trades if 'profit' in trade and trade['profit'] > 0)
        total_trades = sum(1 for trade in self.trades if 'profit' in trade)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        final_portfolio_value = self.trades[-1]['portfolio_value'] if self.trades else self.portfolio_value

        return {
            'trades': self.trades,
            'signal_profits': self.signal_profits,
            'total_profit': total_profit,
            'win_rate': win_rate,
            'final_portfolio_value': final_portfolio_value
        }

class BacktestEngine:
    def __init__(self, strategy: TradingStrategy):
        self.strategy = strategy

    def run(self, data: pd.DataFrame) -> None:
        for i in range(1, len(data)):
            print(data.iloc[i], data.iloc[i-1])
            self.strategy.on_bar(data.iloc[i], data.iloc[i-1])

    def get_results(self) -> Dict[str, Any]:
        return self.strategy.get_results()

def print_results(results: Dict[str, Any]) -> None:
    for trade in results['trades']:
        print(f"时间: {trade['datetime']}, 动作: {trade['action']}, 价格: {trade['price']}, 数量: {trade['quantity']}, 交易品种: {trade['symbol']}")
        if 'profit' in trade:
            print(f"利润: {trade['profit']:.2f}")
        print(f"组合价值: {trade['portfolio_value']:.2f}")
        print("---")

    print(f"总盈亏: {results['total_profit']:.2f}")
    print(f"胜率: {results['win_rate']:.2%}")
    print(f"最终组合价值: {results['final_portfolio_value']:.2f}")

    print("每个信号结束后的盈亏:")
    for i, profit in enumerate(results['signal_profits'], 1):
        print(f"信号 {i}: {profit:.2f}")

if __name__ == "__main__":
    # 读取CSV文件
    df = pd.read_csv('data.csv')

    # 将datetime列转换为datetime类型并设置为索引
    df['datetime'] = pd.to_datetime(df['datetime'], unit='ns')
    df.set_index('datetime', inplace=True)
    df.sort_index(inplace=True)

    # 创建策略实例和回测引擎
    strategy = TradingStrategy()
    engine = BacktestEngine(strategy)

    # 运行回测
    engine.run(df)

    # 获取并打印结果
    results = engine.get_results()
    print_results(results)
