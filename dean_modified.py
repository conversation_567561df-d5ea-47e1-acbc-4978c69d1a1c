#!/usr/bin/env python
#  -*- coding: utf-8 -*-
"""
多均线交易策略

该策略基于15秒周期的K线，使用3日、13日和55日三重均线交叉系统进行交易决策。
主要特点:
- 根据55日均线确定大趋势，55均线上方只做多，下方只做空
- 使用3日和13日均线交叉确认具体交易信号
- 根据不同市场情况调整持仓量
- 含有盈利平仓和亏损锁仓机制
- 支持震荡区间内的仓位管理

原作者: dean
重构日期: 2025-04-01
"""

import math
from datetime import datetime
from tqsdk import TqApi, TqAuth, TqKq, TargetPosTask
from tqsdk.tafunc import ma

# 常量定义
SHORT_PERIOD = 3  # 短周期均线
LONG_PERIOD = 13  # 长周期均线
BIG_PERIOD = 55  # 大周期均线(趋势判断)
TREND_VOLUME = 2  # 趋势开仓手数
VIBRATION_VOLUME = 1  # 波动开仓手数
DURATION_SECONDS = 15  # K线周期(秒)
PRODUCT_ID = 'OI'  # 交易品种


def replace_none_nan_with_zero(value):
    """将None和NaN值替换为0"""
    if value is None or (isinstance(value, float) and math.isnan(value)):
        return 0
    return value


class MovingAverageStrategy:
    """
    三重均线交易策略类

    基于3日、13日和55日均线的交叉关系进行交易决策
    """

    def __init__(self, api, symbol):
        """初始化策略参数和数据"""
        self.api = api
        self.symbol = symbol
        self.target_pos = TargetPosTask(api, symbol)  # 目标持仓管理工具
        self.active_order = None

        # 获取K线数据并计算均线
        self.klines = api.get_kline_serial(symbol, duration_seconds=DURATION_SECONDS)
        self.update_indicators()

    def update_indicators(self):
        """更新技术指标"""
        self.big_avg = ma(self.klines["close"], BIG_PERIOD)  # 55日均线
        self.long_avg = ma(self.klines["close"], LONG_PERIOD)  # 13日均线
        self.short_avg = ma(self.klines["close"], SHORT_PERIOD)  # 3日均线

    def get_latest_ma_values(self):
        """获取最新的均线值"""
        return {
            'ma03': self.short_avg.iloc[-1],
            'ma13': self.long_avg.iloc[-1],
            'ma55': self.big_avg.iloc[-1]
        }

    def get_position_info(self):
        """获取当前持仓信息"""
        position = self.api.get_position(self.symbol)

        # 处理持仓信息
        long_sum = replace_none_nan_with_zero(position.volume_long)
        short_sum = replace_none_nan_with_zero(position.volume_short)
        long_profit = replace_none_nan_with_zero(position.float_profit_long)
        short_profit = replace_none_nan_with_zero(position.float_profit_short)

        return {
            'position': position,
            'long_sum': long_sum,
            'short_sum': short_sum,
            'long_profit': long_profit,
            'short_profit': short_profit,
            'total_profit': long_profit + short_profit
        }

    def is_short_crossing_big(self):
        """判断短期均线是否穿越大周期均线"""
        # 3日均线上穿55日均线
        if self.short_avg.iloc[-2] < self.big_avg.iloc[-2] and self.short_avg.iloc[-1] > self.big_avg.iloc[-1]:
            return 1  # 上穿
        # 3日均线下穿55日均线
        elif self.short_avg.iloc[-2] > self.big_avg.iloc[-2] and self.short_avg.iloc[-1] < self.big_avg.iloc[-1]:
            return -1  # 下穿
        else:
            return 0  # 无穿越

    def is_short_crossing_long(self):
        """判断短期均线是否穿越长期均线"""
        # 3日均线上穿13日均线
        if self.short_avg.iloc[-2] < self.long_avg.iloc[-2] and self.short_avg.iloc[-1] > self.long_avg.iloc[-1]:
            return 1  # 上穿
        # 3日均线下穿13日均线
        elif self.short_avg.iloc[-2] > self.long_avg.iloc[-2] and self.short_avg.iloc[-1] < self.long_avg.iloc[-1]:
            return -1  # 下穿
        else:
            return 0  # 无穿越

    def place_order(self, direction, offset, volume):
        """下单函数，返回委托单引用"""
        limit_price = int(self.short_avg.iloc[-1])  # 以短期均线价格为限价
        return self.api.insert_order(
            symbol=self.symbol,
            direction=direction,
            offset=offset,
            volume=volume,
            limit_price=limit_price
        )

    def handle_short_crossing_big(self):
        """处理短期均线穿越大周期均线的情况"""
        pos_info = self.get_position_info()
        position = pos_info['position']
        cross_signal = self.is_short_crossing_big()

        # 3日均线上穿55日均线：平空单或补多单锁仓
        if cross_signal == 1:
            if pos_info['short_profit'] > 0 and pos_info['short_sum'] > 0:
                # 如果空单盈利，则平空单
                return self.place_order("BUY", "CLOSE", pos_info['short_sum'])
            elif abs(position.pos) > 0:
                # 如果有持仓，补多单来锁仓
                return self.place_order("BUY", "OPEN", abs(position.pos))

        # 3日均线下穿55日均线：平多单或补空单锁仓
        elif cross_signal == -1:
            if pos_info['long_profit'] > 0 and pos_info['long_sum'] > 0:
                # 如果多单盈利，则平多单
                return self.place_order("SELL", "CLOSE", pos_info['long_sum'])
            elif abs(position.pos) > 0:
                # 如果有持仓，补空单来锁仓
                return self.place_order("SELL", "OPEN", abs(position.pos))

        return None

    def handle_trend_trading(self):
        """处理趋势交易策略，根据均线排列确定交易方向"""
        pos_info = self.get_position_info()
        ma_values = self.get_latest_ma_values()
        cross_signal = self.is_short_crossing_long()

        # 多头趋势：13日均线在55日均线上方
        if ma_values['ma13'] > ma_values['ma55']:
            # 3日均线上穿13日均线，做多
            if cross_signal == 1:
                print("趋势向上:3日均线上穿,空单如果盈利平空,空单不盈利就开多锁单")
                if pos_info['short_profit'] > 0 and pos_info['short_sum'] > 0:
                    # 平空单
                    order = self.place_order("BUY", "CLOSE", pos_info['short_sum'])
                    # 由于Python函数只能返回一个值，这里选择先下单后返回开多单
                    self.place_order("BUY", "OPEN", TREND_VOLUME)
                    return order
                elif abs(pos_info['position'].pos) > 0:
                    # 补多锁仓
                    order = self.place_order("BUY", "OPEN", abs(pos_info['position'].pos))
                    # 开多单
                    self.place_order("BUY", "OPEN", TREND_VOLUME)
                    return order
                else:
                    # 直接开多单
                    return self.place_order("BUY", "OPEN", TREND_VOLUME)

            # 3日均线下穿13日均线，平多不做空
            elif cross_signal == -1:
                print("趋势向上:3日均线下穿,平多不做空")
                if pos_info['long_profit'] > 0 and pos_info['long_sum'] > 0:
                    return self.place_order("SELL", "CLOSE", pos_info['long_sum'])

        # 空头趋势：13日均线在55日均线下方
        elif ma_values['ma13'] < ma_values['ma55']:
            # 3日均线上穿13日均线，平空不做多
            if cross_signal == 1:
                print("趋势向下:3日均线上穿,平空不做多")
                if pos_info['short_profit'] > 0 and pos_info['short_sum'] > 0:
                    return self.place_order("BUY", "CLOSE", pos_info['short_sum'])

            # 3日均线下穿13日均线，平多做空
            elif cross_signal == -1:
                print("趋势向下:3日均线下穿,多单如果盈利平多,多单不盈利就开空锁单")
                if pos_info['long_profit'] > 0 and pos_info['long_sum'] > 0:
                    # 平多单
                    order = self.place_order("SELL", "CLOSE", pos_info['long_sum'])
                    # 开空单
                    self.place_order("SELL", "OPEN", TREND_VOLUME)
                    return order
                elif abs(pos_info['position'].pos) > 0:
                    # 补空锁仓
                    order = self.place_order("SELL", "OPEN", abs(pos_info['position'].pos))
                    # 开空单
                    self.place_order("SELL", "OPEN", TREND_VOLUME)
                    return order
                else:
                    # 直接开空单
                    return self.place_order("SELL", "OPEN", TREND_VOLUME)

        else:
            print("均线未形成明显趋势排列,保持持仓现状")

        return None

    def run_strategy(self):
        """运行策略的主函数"""
        try:
            print("----开始交易：------")
            while True:
                self.api.wait_update()

                # 如果K线更新，重新计算指标和交易信号
                if self.api.is_changing(self.klines.iloc[-1], "datetime"):
                    # 更新技术指标
                    self.update_indicators()

                    # 获取并打印持仓信息
                    pos_info = self.get_position_info()
                    position = pos_info['position']
                    print(f"{datetime.now()} 当前持仓: {position.pos}手 "
                          f"多单:{pos_info['long_sum']}  空单:{pos_info['short_sum']}  "
                          f"总盈利:{pos_info['total_profit']}")

                    # 先处理55日线的穿越，优先级高
                    order = self.handle_short_crossing_big()
                    if order:
                        self.active_order = order
                    else:
                        # 再处理13日线的穿越
                        order = self.handle_trend_trading()
                        if order:
                            self.active_order = order

                # 检查订单状态变化
                if self.active_order and self.api.is_changing(self.active_order):
                    print(f"委托单状态: {self.active_order.status}, "
                          f"已成交: {self.active_order.volume_orign - self.active_order.volume_left} 手")

        except KeyboardInterrupt:
            print("策略已停止")

        finally:
            # 关闭连接，释放资源
            self.api.close()


def main():
    """主函数"""
    # 创建API连接
    api = TqApi(TqKq(), auth=TqAuth("dean2024", "456123"))

    try:
        # 获取主力合约
        symbol = api.query_cont_quotes(product_id=PRODUCT_ID)[0]

        # 创建并运行策略
        strategy = MovingAverageStrategy(api, symbol)
        strategy.run_strategy()

    except Exception as e:
        print(f"发生错误: {e}")
        api.close()


if __name__ == "__main__":
    main()