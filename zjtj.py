# from tqsdk.tafunc import ema as EMA
# from tqsdk.tafunc import ref as REF
# from tqsdk.tafunc import crossup as CROSS
# from tqsdk.tafunc import crossdown as CROSSDOWN
from tqsdk.tafunc import time_to_str
from MyTT import CROSS, REF, EMA
from myfunction import be_apart_from

def zjtj(bars, N=8):
    CLOSE = bars.close
    SYMBOL = bars.iloc[0].symbol
    interval = bars.iloc[0].duration

    VAR1 = EMA(EMA(CLOSE, N), N)
    trendline = (VAR1 - REF(VAR1, 1)) / REF(VAR1, 1) * 1000
    # A10=CROSS(trendline,0)

    upslist = list(CROSS(trendline, 0))

    dnslist = list(CROSS(0, trendline))
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)

    if bkdist > skdist:
        signalnow = 'SK'
        distnow = skdist
    else:
        signalnow = 'BK'
        distnow = bkdist

    signalprice = CLOSE.iloc[-distnow]
    sigfloatprofit = CLOSE.iloc[-1] - signalprice if signalnow == 'BK' else signalprice - CLOSE.iloc[-1]
    print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', CLOSE.iloc[-1], '信号盈亏:', sigfloatprofit)
    average_signal_distance = int(len(upslist) / (upslist.count(1) * 2))
    print('平均信号距离：', average_signal_distance)
    print('upsignal:  ', upslist[-40:])
    print('downsignal:', dnslist[-40:])

    signal = {'合约': SYMBOL, '周期': interval,'时间': time_to_str(bars.datetime.iloc[-1]), '当前信号': signalnow,
              '持续周期': distnow, '信号价格': signalprice, '现价': CLOSE.iloc[-1],
              '信号盈亏': sigfloatprofit}




    return upslist, dnslist


