from strategies.TimeRoseMA_cross_MultiInterval_speak import ma_multiIntervals_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date
    # from accounts import cyzjy as acct


    product = 'SH'
    interval = 60
    bklimit = 1
    sklimit = 1
    single_volume = 1

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)

    api = TqApi(TqKq(), auth="quant_thj,Qiai1301")
    symbol = api.query_cont_quotes(product_id=product)[0]
    # symbol = 'CZCE.OI501'
    ma_multiIntervals_cross(api, symbol, interval, single_volume, bklimit, sklimit)


runstrategy()
