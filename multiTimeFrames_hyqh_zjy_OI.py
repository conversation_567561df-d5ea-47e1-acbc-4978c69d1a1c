# 主程序
from tqsdk import TqA<PERSON>, TqAuth, TqKq
from multiTimeFrames_base_with_margin_check import MultiTimeframeStrategy
from utils.utils import parse_time, get_time_period, is_trading_time
import time

if __name__ == "__main__":
    from tqsdk import TqAccount
    from accounts_zjy import hyqhzjy as account

    acct = account
    # api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)
    # api = TqApi(TqAccount("H徽商期货", "588333", "qiai1301"), auth=acct.tqacc)
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    try:
        product_id = 'OI'

        symbol = api.query_cont_quotes(product_id=product_id)[0]
        print(f"交易的合约是: {symbol}")
        time_periods = get_time_period(api, symbol)
        # 定义交易周期（以秒为单位）和每个周期的持仓限制
        # timeframes = [60, 180, 800, 900]  # 1分钟, 3分钟, 13分20秒, 15分钟
        max_positions = {
            60: {'long': 7, 'short': 7},
            180: {'long': 5, 'short': 5},
            300: {'long': 3, 'short': 3},
            900: {'long': 2, 'short': 2}
        }

        while True:

            if is_trading_time(time_periods):
                try:
                    # api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)
                    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
                    symbol = api.query_cont_quotes(product_id=product_id)[0]
                    strategy = MultiTimeframeStrategy(api, symbol, max_positions, positions_file=f"{account.name}{account.investorid}.json")
                    while True:
                        api.wait_update()
                        strategy.update()
                except Exception as e:
                    print(e)
                    time.sleep(10)
            else:
                print('非交易时间:', time.asctime())
                time.sleep(1)
                # continue

    except KeyboardInterrupt:
        print("策略已停止")

    finally:
        api.close()
