"""
测试多合约同时运行时的文件冲突问题
"""

import subprocess
import sys
import time
import os
import glob
from datetime import datetime


def test_temp_file_naming():
    """测试临时文件命名机制"""
    print("=" * 60)
    print("测试临时文件命名机制")
    print("=" * 60)
    
    # 模拟多个合约的临时文件生成
    contracts = ['ag', 'rb', 'cu']
    timeframes = ['1m', '3m', '5m', '15m']
    
    print("模拟临时文件命名:")
    for contract in contracts:
        for timeframe in timeframes:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            pid = os.getpid()
            filename = f"temp_strategy_{contract}_{timeframe}_{pid}_{timestamp}.py"
            print(f"  {contract} {timeframe}: {filename}")
        print()


def test_concurrent_strategies():
    """测试并发策略启动"""
    print("=" * 60)
    print("测试并发策略启动")
    print("=" * 60)
    
    contracts = ['ag', 'rb']
    processes = []
    
    try:
        # 同时启动多个合约的策略
        for contract in contracts:
            print(f"启动 {contract} 策略...")
            
            cmd = [
                sys.executable,
                "TimeRoseMA_cross_ag_MultiTimeFrames.py",
                contract,
                "independent"
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            processes.append((contract, process))
            print(f"  {contract} 策略进程 PID: {process.pid}")
            
            # 短暂延迟避免同时启动
            time.sleep(2)
        
        print(f"\n已启动 {len(processes)} 个策略进程")
        
        # 等待一段时间让策略启动
        print("等待策略启动...")
        time.sleep(10)
        
        # 检查临时文件
        print("\n检查生成的临时文件:")
        temp_files = glob.glob("temp_strategy_*.py")
        
        if temp_files:
            print(f"找到 {len(temp_files)} 个临时文件:")
            for file in sorted(temp_files):
                size = os.path.getsize(file)
                print(f"  {file} ({size} bytes)")
            
            # 检查是否有重复文件名
            if len(temp_files) == len(set(temp_files)):
                print("✓ 所有临时文件名都是唯一的")
            else:
                print("✗ 发现重复的临时文件名")
        else:
            print("未找到临时文件")
        
        # 检查进程状态
        print("\n检查进程状态:")
        for contract, process in processes:
            if process.poll() is None:
                print(f"  {contract}: 运行中 (PID: {process.pid})")
            else:
                print(f"  {contract}: 已退出 (返回码: {process.returncode})")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
    
    finally:
        # 清理进程
        print("\n清理进程...")
        for contract, process in processes:
            if process.poll() is None:
                print(f"终止 {contract} 进程...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
        
        # 清理临时文件
        print("清理临时文件...")
        temp_files = glob.glob("temp_strategy_*.py")
        for file in temp_files:
            try:
                os.remove(file)
                print(f"  删除: {file}")
            except Exception as e:
                print(f"  删除失败 {file}: {e}")


def test_file_conflict_simulation():
    """模拟文件冲突情况"""
    print("=" * 60)
    print("模拟文件冲突情况")
    print("=" * 60)
    
    # 创建测试文件
    test_files = []
    
    try:
        # 模拟旧的命名方式（会冲突）
        print("1. 测试旧的命名方式（会冲突）:")
        old_style_files = [
            "temp_strategy_1m.py",
            "temp_strategy_3m.py",
            "temp_strategy_5m.py",
            "temp_strategy_15m.py"
        ]
        
        for i, filename in enumerate(old_style_files):
            content = f"# 合约 {i+1} 的策略文件\nprint('Contract {i+1}')\n"
            with open(filename, 'w') as f:
                f.write(content)
            test_files.append(filename)
            print(f"  创建: {filename}")
        
        # 模拟第二个合约覆盖文件
        print("\n  模拟第二个合约覆盖文件:")
        for filename in old_style_files:
            content = "# 第二个合约覆盖了第一个合约的文件\nprint('Contract 2 overwrote Contract 1')\n"
            with open(filename, 'w') as f:
                f.write(content)
            print(f"  覆盖: {filename}")
        
        # 检查文件内容
        print("\n  检查文件内容:")
        for filename in old_style_files:
            with open(filename, 'r') as f:
                first_line = f.readline().strip()
                print(f"  {filename}: {first_line}")
        
        print("  结果: 第一个合约的文件被覆盖了！")
        
        # 测试新的命名方式（不会冲突）
        print("\n2. 测试新的命名方式（不会冲突）:")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pid = os.getpid()
        
        new_style_files = []
        contracts = ['ag', 'rb']
        timeframes = ['1m', '3m']
        
        for contract in contracts:
            for tf in timeframes:
                filename = f"temp_strategy_{contract}_{tf}_{pid}_{timestamp}.py"
                content = f"# {contract} 合约 {tf} 时间周期策略\nprint('{contract} {tf} strategy')\n"
                with open(filename, 'w') as f:
                    f.write(content)
                new_style_files.append(filename)
                test_files.append(filename)
                print(f"  创建: {filename}")
        
        # 检查新方式的文件内容
        print("\n  检查文件内容:")
        for filename in new_style_files:
            with open(filename, 'r') as f:
                first_line = f.readline().strip()
                print(f"  {filename}: {first_line}")
        
        print("  结果: 每个合约都有独立的文件，没有冲突！")
        
    except Exception as e:
        print(f"模拟测试出错: {e}")
    
    finally:
        # 清理测试文件
        print("\n清理测试文件...")
        for filename in test_files:
            try:
                os.remove(filename)
                print(f"  删除: {filename}")
            except Exception as e:
                print(f"  删除失败 {filename}: {e}")


def test_batch_startup():
    """测试批量启动"""
    print("=" * 60)
    print("测试批量启动")
    print("=" * 60)
    
    # 使用批量管理脚本测试
    if os.path.exists("start_scheduled_strategies.py"):
        print("使用批量管理脚本测试...")
        
        try:
            # 启动多个策略
            cmd = [sys.executable, "start_scheduled_strategies.py", "start", "ag", "rb"]
            
            print("执行命令:", " ".join(cmd))
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 等待启动
            time.sleep(5)
            
            # 检查临时文件
            temp_files = glob.glob("temp_strategy_*.py")
            print(f"\n生成的临时文件数量: {len(temp_files)}")
            
            # 按合约分组
            ag_files = [f for f in temp_files if "_ag_" in f]
            rb_files = [f for f in temp_files if "_rb_" in f]
            
            print(f"ag 合约文件: {len(ag_files)}")
            print(f"rb 合约文件: {len(rb_files)}")
            
            if len(ag_files) > 0 and len(rb_files) > 0:
                print("✓ 多合约文件生成正常，没有冲突")
            else:
                print("✗ 文件生成异常")
            
            # 终止进程
            process.terminate()
            process.wait()
            
        except Exception as e:
            print(f"批量测试出错: {e}")
    else:
        print("批量管理脚本不存在，跳过此测试")


def main():
    """主测试函数"""
    print("多合约文件冲突测试")
    print("=" * 80)
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        
        if test_type == "naming":
            test_temp_file_naming()
        elif test_type == "concurrent":
            test_concurrent_strategies()
        elif test_type == "conflict":
            test_file_conflict_simulation()
        elif test_type == "batch":
            test_batch_startup()
        else:
            print("可用测试:")
            print("  python test_multi_contract_conflict.py naming      # 文件命名测试")
            print("  python test_multi_contract_conflict.py concurrent  # 并发策略测试")
            print("  python test_multi_contract_conflict.py conflict    # 冲突模拟测试")
            print("  python test_multi_contract_conflict.py batch       # 批量启动测试")
    else:
        # 运行所有测试
        test_temp_file_naming()
        test_file_conflict_simulation()
        
        choice = input("\n是否进行实际并发测试? (需要TqSDK认证) [y/N]: ").strip().lower()
        if choice in ['y', 'yes']:
            test_concurrent_strategies()


if __name__ == "__main__":
    main()
