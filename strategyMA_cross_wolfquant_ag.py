import copy
import sys

from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema, trma, ma
from tradefuncs import *
from speaktextng import speak_text
from loguru import logger as mylog

from time import sleep
import time


def disp_day_info(daymean, last3mean, hlmax, homean, olmean):
    print('最大价差:', hlmax, '平均价差:', daymean, '三日平均:', last3mean, '上价差:', int(homean), '下价差:', int(olmean))


def disp_0Day_info(quote):
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    gapcover = False

    if gap > 0:
        if quote.lowest < quote.pre_close:
            gapcover = True
    else:
        if quote.highest > quote.pre_close:
            gapcover = True

    BB = (quote.last_price - quote.lowest) / (quote.highest - quote.lowest) if quote.highest - quote.lowest > 0 else 0.5
    BB = float('%.2f' % BB)

    upvib = quote.highest - quote.open
    dnvib = quote.open - quote.lowest

    print(quote.datetime, quote.instrument_id, '缺口：', gap, '回补:', gapcover, '现价:', quote.last_price, 'BB:', BB, '今日价差：',
          dayvib,
          '上价差:', upvib, '下价差:', dnvib)


def MaCrossCaculate(C, period, quote):
    trmac = ma(C, period)
    ups = crossup(C, trmac)
    dns = crossdown(C, trmac)
    upslist = ups.tolist()
    dnslist = dns.tolist()
    bkdist = be_apart_from(upslist)
    skdist = be_apart_from(dnslist)
    if bkdist > skdist:
        signalnow = 'SK'
        distnow = skdist
    else:
        signalnow = 'BK'
        distnow = bkdist

    signalprice = C.iloc[-distnow]
    sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
    # print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)
    uplist = ups.tolist()
    average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
    print('平均信号距离：', average_signal_distance)
    print('upsignal:  ', ups.iloc[-40:].tolist())
    print('downsignal:', dns.iloc[-40:].tolist())


def ma_cross(api, symbol, interval, single_volume, bklimit, sklimit, period=13):
    strategyname = 'macross'
    acct = api.get_account()
    try:
        userid = acct.user_id.split('-')[0]
    except:
        userid = 'moni'

    logfilename = '_'.join([userid, symbol, strategyname])
    mylog.add(logfilename + '.log', encoding='utf-8')
    savebars = False
    quote = api.get_quote(symbol)

    if '@' in symbol:
        SYMBOL = quote.underlying_symbol
    else:
        SYMBOL = symbol

    trading_symbol = SYMBOL.split('.')[1]

    ticks = api.get_tick_serial(SYMBOL)
    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines

    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()
    hl = daybar.high - daybar.low
    ho = daybar.high - daybar.open
    ol = daybar.open - daybar.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    N = 23
    O = klines1.open
    H = klines1.high
    L = klines1.low
    C = klines1.close

    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    KNUM = len(klines1)
    gapcover = False

    position = api.get_position(SYMBOL)
    acc = api.get_account()
    bkvol = position.pos_long
    bkvol_yd = position.pos_long_his
    bkvol_td = position.pos_long_today

    skvol = position.pos_short
    skvol_yd = position.pos_short_his
    skvol_td = position.pos_short_today

    skflag = True
    bkflag = True
    spflag = True
    bpflag = True
    speakyn = True
    dayopensp = True
    dayopenbp = True

    fundavailable = acc.available
    disp_day_info(daymean, last3mean, hlmax, homean, olmean)
    disp_0Day_info(quote)

    MaCrossCaculate(C, period, quote)
    # print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)

    while True:

        api.wait_update()

        if api.is_changing(daybars.iloc[-1], "datetime"):
            savebars = True
            dayopensp = True
            dayopenbp = True

            hl = daybar.high - daybar.low
            ho = daybar.high - daybar.open
            ol = daybar.open - daybar.low
            daymean = int(hl.mean())
            homean = ho.mean()
            olmean = ol.mean()
            hlmax = max(hl)
            last3mean = int(hl.iloc[-4:-1].mean())
            dayopen = quote.open
            dayhigh = quote.highest
            daylow = quote.lowest
            preclose = quote.pre_close
            dayvib = dayhigh - daylow
            gap = dayopen - preclose
            gapcover = False
            print('日线数据更新完成。。。')
            mylog.info('日线数据更新完成。。。')

        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            disp_day_info(daymean, last3mean, hlmax, homean, olmean)
            disp_0Day_info(quote)
            print('balance:', acct.balance, 'poslong:', position.pos_long, 'posshort:', position.pos_short,
                  'poslong float profit:', position.float_profit_long, 'posshort float profit:',
                  position.float_profit_short)

            speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                klines1 = klines1.append(newk)

            O = klines1.open
            H = klines1.high
            L = klines1.low
            C = klines1.close

            hl = ema(H - L, 23).iloc[-1]
            cover_gap = max(int(hl), 3)

            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today
            bkfreeze = position.volume_long_frozen

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today
            skfreeze = position.volume_short_frozen

            trmac = ma(C, period)
            ups = crossup(C, trmac)
            dns = crossdown(C, trmac)
            upslist = ups.tolist()
            dnslist = dns.tolist()
            bkdist = be_apart_from(upslist)
            skdist = be_apart_from(dnslist)
            if bkdist > skdist:
                signalnow = 'SK'
                distnow = skdist
            else:
                signalnow = 'BK'
                distnow = bkdist

            signalprice = C.iloc[-distnow]
            sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
            print(SYMBOL, interval, '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price,
                  '信号盈亏:', sigfloatprofit)
            uplist = ups.tolist()
            average_signal_distance = int(len(uplist) / (uplist.count(1) * 2))
            print('平均信号距离：', average_signal_distance)
            print('upsignal:  ', ups.iloc[-40:].tolist())
            print('downsignal:', dns.iloc[-40:].tolist())

            # 交易部分
            basevalue = average_signal_distance * 2
            order_volume = (basevalue - distnow) * single_volume if distnow < average_signal_distance else single_volume
            order_volume = single_volume

            if upslist[-1]:
                mylog.info([SYMBOL, '发出做多信号....'])
                speak_text('发出做多信号')

                bkprice = quote.last_price
                mylog.info(['bkprice', bkprice, 'skprofit', position.float_profit_short, 'skvol', skvol])

                if skvol > 0:
                    if position.float_profit_short > 5 * skvol:  # 空单盈利
                        BP(api, symbol=SYMBOL, order_price=bkprice, volume=skvol)
                        mylog.info(['ping kong dan...', 'volume', skvol, 'price', bkprice])

                    else:
                        mylog.info('short float proft doesnt match, dont cover.')

                        orderVol = min(bklimit - bkvol, skvol - bkvol + 1)

                        if orderVol <= 0:
                            mylog.info('持仓数量已经达到,不下单...')
                        else:
                            BK(api, symbol=SYMBOL, order_price=bkprice, volume=orderVol)
                            mylog.info(['open new long pos', 'volume', orderVol, 'price', bkprice])

                else:
                    if bkvol == 0:
                        BK(api, symbol=SYMBOL, order_price=bkprice, volume=single_volume)
                        mylog.info(['open new short pos', 'volume', single_volume, 'price', bkprice])

            if dnslist[-1]:
                mylog.info([SYMBOL, '发出做空信号....'])
                speak_text('发出做空信号')
                skprice = quote.last_price
                bkvol = position.pos_long
                bkvol_cost = position.open_price_long

                mylog.info(['skprice', skprice, 'bkprofit', position.float_profit_long, 'bkvol', bkvol])

                if bkvol > 0:
                    if position.float_profit_long > 5 * bkvol:  # 多单盈利
                        SP(api, symbol=SYMBOL, order_price=skprice, volume=bkvol)
                        mylog.info(['ping duo dan.', 'volume', bkvol, 'price', skprice])

                    else:
                        mylog.info('float profit of long pos does not match the condition. dont cover.')
                        orderVol = min(sklimit - skvol, bkvol + 1 - skvol)
                        if orderVol <= 0:
                            mylog.info('持仓数量已达要求,不下单.')

                        else:
                            SK(api, symbol=SYMBOL, order_price=skprice, volume=orderVol)
                            mylog.info(['open short pos', 'volume', orderVol, 'price', skprice])


                if skvol == 0:
                    SK(api, symbol=SYMBOL, order_price=skprice, volume=single_volume)
                    mylog.info(['open new short pos', 'volume', single_volume, 'price', skprice])

        if api.is_changing(quote):
            if gap > 0:
                if quote.lowest < quote.pre_close:
                    gapcover = True
            else:
                if quote.highest > quote.pre_close:
                    gapcover = True

        if api.is_changing(position):
            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today

        hr = time.localtime().tm_hour
        mi = time.localtime().tm_min
        if ((time.localtime().tm_hour == 15 and time.localtime().tm_min > 15) or (
                time.localtime().tm_hour == 23 and time.localtime().tm_min > 30)):
            klines1.to_csv(SYMBOL + '.csv')
            api.close()
            mylog.info('no trading time, quit.')
            sys.exit(0)


def runstrategy():
    from tqsdk import TqApi, TqKq, TqBacktest, TqAuth
    from datetime import date
    product = 'ag'
    interval = 60
    bklimit = 6
    sklimit = 60
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth=("wolfquant,ftp123"))
    symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


runstrategy()
