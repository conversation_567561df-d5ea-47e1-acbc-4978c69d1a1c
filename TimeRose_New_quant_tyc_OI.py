from TimeRoseMA_cross_multi_interval_class_base_1 import timeRoseMultiInterval, pos_interval_info

if __name__ == '__main__':
    symbol = 'CZCE.OI501'
    account = 'quant_tyc,Qiai1301'

    s60 = pos_interval_info(0, 1, 0, 0, 1)
    s180 = pos_interval_info(0, 3, 0, 0, 1)
    s300 = pos_interval_info(0, 5, 0, 0, 1)
    s900 = pos_interval_info(0, 15, 0, 0, 1)

    instance_map = {
        60: s60,
        180: s180,
        300: s300,
        900: s900
    }
    pos_interval_info_file = account.split(',')[0] + '_' + symbol.split('.')[1] + '_pos_interval_info.pkl'
    bot = timeRoseMultiInterval(account, symbol, bklimit=20, sklimit=20, singlevolume=1, interval_parameters=instance_map,pos_instance_file=pos_interval_info_file)

    bot.run()
