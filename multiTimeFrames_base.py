import json
import os
import time
from datetime import datetime
import copy
from utils.utils import parse_time, get_time_period, is_trading_time

import pandas as pd
from loguru import logger as log


class MultiTimeframeStrategy:
    def __init__(self, api, symbol, max_positions, positions_file='positions.json', history_length=1000):
        self.api = api
        self.symbol = symbol
        self.max_positions = max_positions
        self.timeframes = list(self.max_positions.keys())
        self.userid = self.api.get_account().user_id
        self.positions_file = self.userid + self.symbol + '_' + positions_file
        self.history_length = history_length
        self.klines = {}
        self.last_update_time = {}
        self.time_periods = get_time_period(self.api, self.symbol)
        self.log = log.info
        self.load_positions()

        for tf in self.timeframes:
            # 获取初始历史数据
            klines = copy.deepcopy(self.api.get_kline_serial(self.symbol, tf, data_length=self.history_length))
            self.klines[tf] = pd.DataFrame(klines).set_index('datetime')

    def load_positions(self):
        if os.path.exists(self.positions_file):
            with open(self.positions_file, 'r') as f:
                self.positions = json.load(f)
                print(f"当前持仓：{self.positions}")
            self.log("Loaded positions from file.")
        else:
            self.positions = {str(tf): {'long': 0, 'short': 0} for tf in self.timeframes}
            self.log(f"Initialized new positions, {self.positions_file}")

    def save_positions(self):
        with open(self.positions_file, 'w') as f:
            json.dump(self.positions, f)
            print(self.positions)
        self.log("Saved positions to file.")

    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")

    def check_crossover(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] > ma.iloc[-1] and close.iloc[-2] <= ma.iloc[-2]

    def check_crossunder(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] < ma.iloc[-1] and close.iloc[-2] >= ma.iloc[-2]

    def get_position_profit(self, position_type):
        position = self.api.get_position(self.symbol)
        if position_type == 'long':
            return position.float_profit_long
        else:
            return position.float_profit_short

    def execute_trade(self, timeframe, action, position_type):
        timeframe_str = str(timeframe)
        print(f"持仓限额：{self.max_positions}")
        print(f"当前持仓：{self.positions}")
        if action == 'open':
            if self.positions[timeframe_str][position_type] < self.max_positions[timeframe][position_type]:
                self.positions[timeframe_str][position_type] += 1
                volume = 1 if position_type == 'long' else -1
                self.api.insert_order(symbol=self.symbol, direction="BUY" if position_type == 'long' else "SELL",
                                      offset="OPEN", volume=abs(volume),
                                      limit_price=self.klines[timeframe].iloc[-1].close)
                self.log(f"{timeframe}秒周期: 开{position_type}单")
                self.save_positions()
                print(f"当前持仓：{self.positions}")
            else:
                self.log(f"{timeframe}秒周期: {position_type}单数量已达到上限，无法开仓。")
        elif action == 'close':
            if self.positions[timeframe_str][position_type] > 0:
                profit = self.get_position_profit(position_type)
                if profit >= 0:
                    volume_to_close = self.positions[timeframe_str][position_type]
                    # volume_to_close = 1
                    direction = "SELL" if position_type == 'long' else "BUY"

                    if self.symbol.startswith("SHFE"):
                        try:
                            # 获取持仓信息
                            position_info = self.api.get_position(self.symbol)

                            # 根据持仓方向获取对应的持仓数量
                            if position_type == 'long':
                                hist_pos = position_info.volume_long_his
                                today_pos = position_info.volume_long_today
                            else:
                                hist_pos = position_info.volume_short_his
                                today_pos = position_info.volume_short_today

                            # 先平历史仓位
                            if hist_pos > 0:
                                hist_volume_to_close = min(hist_pos, volume_to_close)
                                self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE",
                                                      volume=hist_volume_to_close,
                                                      limit_price=self.klines[timeframe].iloc[-1].close)
                                volume_to_close -= hist_volume_to_close
                                self.log(f"{timeframe}秒周期: 平{position_type}历史仓位 {hist_volume_to_close} 手")

                            # 如果还有剩余需要平仓的量，平今日仓位
                            if volume_to_close > 0:
                                today_volume_to_close = min(today_pos, volume_to_close)
                                self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSETODAY",
                                                      volume=today_volume_to_close,
                                                      limit_price=self.klines[timeframe].iloc[-1].close)
                                self.log(f"{timeframe}秒周期: 平{position_type}今日仓位 {today_volume_to_close} 手")
                        except AttributeError as e:
                            self.log(f"获取SHFE合约位置时出错: {str(e)}")
                            return  # 出错时退出函数，不执行后续操作
                        except Exception as e:
                            self.log(f"平仓SHFE合约时出错: {str(e)}")
                            return  # 出错时退出函数，不执行后续操作
                    else:
                        # 非SHFE合约，直接平仓
                        self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE",
                                              volume=volume_to_close, limit_price=self.klines[timeframe].iloc[-1].close)
                        self.log(f"{timeframe}秒周期: 平{position_type}单 {volume_to_close} 手")

                    self.positions[timeframe_str][position_type] = 0
                    self.save_positions()
                    print(self.positions)
                else:
                    self.log(f"{timeframe}秒周期: {position_type}单亏损,不平仓")

    def update_klines(self, timeframe):
        temp_klines = self.api.get_kline_serial(self.symbol, timeframe, data_length=2)
        temp_df = pd.DataFrame(temp_klines).set_index('datetime')
        # print(self.klines[timeframe].index[-1])
        # print(temp_df.index[-1])
        if temp_df.index[-2] > self.klines[timeframe].index[-1]:
            # If there's a new complete K-line, update our stored K-lines
            new_kline = temp_df.iloc[-2]
            if new_kline.name not in self.klines[timeframe].index:
                self.klines[timeframe].loc[new_kline.name] = new_kline
                # print(f"更新{timeframe}秒K线", self.klines[timeframe].tail())
                # print(f"更新{timeframe}秒K线", len(self.klines[timeframe]))
                return True
        return False

    def run(self, timeframe):
        # 检查并更新k线数据
        if self.update_klines(timeframe):
            kline = self.klines[timeframe]

            ma13 = kline.close.rolling(13).mean()
            self.position = self.api.get_position(self.symbol)
            self.total_short_sum = sum(position['short'] for position in self.positions.values())
            self.total_long_sum = sum(position['long'] for position in self.positions.values())
            print(f" total_long_sum:{self.total_long_sum}, total_short_sum:{self.total_short_sum}")
            self.log(f"{self.symbol}:{timeframe}秒周期: 当前持仓: 多单:{self.position.pos_long}, 空单:{self.position.pos_short}")

            if self.check_crossover(kline.close, ma13):
                self.log(f"{self.symbol}:{timeframe}秒周期: 收盘价大于13日均线, 发出开多信号")
                print(self.positions)
                self.execute_trade(timeframe, 'open', 'long')
                self.execute_trade(timeframe, 'close', 'short')
            elif self.check_crossunder(kline.close, ma13):
                print(self.positions)
                self.log(f"{self.symbol}:{timeframe}秒周期: 收盘价小于13日均线, 发出开空信号")
                self.execute_trade(timeframe, 'open', 'short')
                self.execute_trade(timeframe, 'close', 'long')

    def update(self):
        if is_trading_time(self.time_periods):
            for timeframe in self.timeframes:
                self.run(timeframe)
        else:
            self.log(f"非交易时间, 当前时间: {time.asctime()}")
            self.api.close()
            return


# 主程序
if __name__ == '__main__':
    from gooey import Gooey
    @Gooey()
    def main():
        from tqsdk import TqApi, TqAuth, TqKq
        from accountSimulate import ggh as account

        account = account.tqacc
        api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)
        try:
            product_id = 'OI'
            # product_id = 'rb'
            # product_id = 'SA'

            symbol = api.query_cont_quotes(product_id=product_id)[0]
            # symbol = 'CZCE.OI505'
            print(f"交易的合约是: {symbol}")
            time_periods = get_time_period(api, symbol)

            # 定义交易周期（以秒为单位）和每个周期的持仓限制
            # timeframes = [60, 180, 800, 900]  # 1分钟, 3分钟, 13分20秒, 15分钟
            max_positions = {
                15: {'long': 10, 'short': 10},
                60: {'long': 10, 'short': 10},
                180: {'long': 10, 'short': 10},
                300: {'long': 10, 'short': 10},
                800: {'long': 10, 'short': 10},
                900: {'long': 10, 'short': 10}
            }

            while True:

                if is_trading_time(time_periods):
                    try:
                        api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)
                        strategy = MultiTimeframeStrategy(api, symbol, max_positions)
                        while True:
                            api.wait_update()
                            strategy.update()
                    except Exception as e:
                        print(e)
                        time.sleep(10)
                else:
                    print('非交易时间:', time.asctime())
                    time.sleep(1)
                    # continue

        except KeyboardInterrupt:
            print("策略已停止")

        finally:
            api.close()

    main()