import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any


class TradingStrategy:
    def __init__(self, initial_capital: float = 100000, ma_window: int = 13):
        self.portfolio_value = initial_capital
        self.current_position = 0
        self.trades: List[Dict[str, Any]] = []
        self.signal_profits: List[float] = []
        self.entry_price = 0
        self.current_quantity = 1
        self.consecutive_losses = 0
        self.contract_multiplier = 10
        self.ma_window = ma_window
        self.price_history = []

    def calculate_improved_ma(self, prices: List[float]) -> float:
        return sum(prices[-self.ma_window:]) / len(prices[-self.ma_window:])

    def on_bar(self, bar: pd.Series) -> None:
        # Update price history
        self.price_history.append(bar['close'])

        # Calculate moving average if we have enough data points
        if len(self.price_history) >= self.ma_window:
            current_ma = self.calculate_improved_ma(self.price_history)
            prev_ma = self.calculate_improved_ma(self.price_history[:-1])

            # Generate trading signal
            if bar['close'] > current_ma and self.price_history[-2] <= prev_ma:
                signal = 1  # Buy signal
            elif bar['close'] < current_ma and self.price_history[-2] >= prev_ma:
                signal = -1  # Sell signal
            else:
                signal = 0  # No signal

            if signal != 0:
                self.execute_trade(bar, signal)

    def execute_trade(self, bar: pd.Series, signal: int) -> None:
        if self.current_position == 0:
            # Open position
            self.current_position = signal
            self.entry_price = bar['close']

            self.trades.append({
                'datetime': bar.name,
                'price': self.entry_price,
                'action': '开多单' if signal == 1 else '开空单',
                'quantity': self.current_quantity,
                'symbol': bar['symbol'],
                'portfolio_value': self.portfolio_value
            })
        else:
            # Close position
            exit_price = bar['close']
            price_diff = exit_price - self.entry_price if self.current_position > 0 else self.entry_price - exit_price
            profit = price_diff * self.contract_multiplier * self.current_quantity
            self.portfolio_value += profit

            self.trades.append({
                'datetime': bar.name,
                'price': exit_price,
                'action': '平多单' if self.current_position > 0 else '平空单',
                'quantity': self.current_quantity,
                'symbol': bar['symbol'],
                'profit': profit,
                'portfolio_value': self.portfolio_value
            })

            # Record profit for this signal
            self.signal_profits.append(profit)

            # Update consecutive losses and order quantity
            if profit < 0:
                self.consecutive_losses += 1
                self.current_quantity = min(self.current_quantity + 1, 10)  # Increase order quantity, but not exceeding 10
            else:
                self.consecutive_losses = 0
                self.current_quantity = 1  # Reset to 1 after a profit

            # Open reverse position
            self.current_position = signal
            self.entry_price = exit_price

            self.trades.append({
                'datetime': bar.name,
                'price': self.entry_price,
                'action': '开多单' if signal == 1 else '开空单',
                'quantity': self.current_quantity,
                'symbol': bar['symbol'],
                'portfolio_value': self.portfolio_value
            })

    def get_results(self) -> Dict[str, Any]:
        total_profit = sum(trade['profit'] for trade in self.trades if 'profit' in trade)
        winning_trades = sum(1 for trade in self.trades if 'profit' in trade and trade['profit'] > 0)
        total_trades = sum(1 for trade in self.trades if 'profit' in trade)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        final_portfolio_value = self.trades[-1]['portfolio_value'] if self.trades else self.portfolio_value

        return {
            'trades': self.trades,
            'signal_profits': self.signal_profits,
            'total_profit': total_profit,
            'win_rate': win_rate,
            'final_portfolio_value': final_portfolio_value
        }


class BacktestEngine:
    def __init__(self, strategy: TradingStrategy):
        self.strategy = strategy

    def run(self, data: pd.DataFrame) -> None:
        for _, bar in data.iterrows():
            self.strategy.on_bar(bar)

    def get_results(self) -> Dict[str, Any]:
        return self.strategy.get_results()


def print_results(results: Dict[str, Any]) -> None:
    for trade in results['trades']:
        print(f"时间: {trade['datetime']}, 动作: {trade['action']}, 价格: {trade['price']}, 数量: {trade['quantity']}, 交易品种: {trade['symbol']}")
        if 'profit' in trade:
            print(f"利润: {trade['profit']:.2f}")
        print(f"组合价值: {trade['portfolio_value']:.2f}")
        print("---")

    print(f"总盈亏: {results['total_profit']:.2f}")
    print(f"胜率: {results['win_rate']:.2%}")
    print(f"最终组合价值: {results['final_portfolio_value']:.2f}")

    print("每个信号结束后的盈亏:")
    # for i, profit in enumerate(results['signal_profits'], 1):
    #     print(f"信号 {i}: {profit:.2f}")


if __name__ == "__main__":
    # Read CSV file
    df = pd.read_csv('data5.csv')

    # Convert datetime column to datetime type and set as index
    df['datetime'] = pd.to_datetime(df['datetime'], unit='ns')
    df.set_index('datetime', inplace=True)
    df.sort_index(inplace=True)

    # Create strategy instance and backtest engine
    strategy = TradingStrategy()
    engine = BacktestEngine(strategy)

    # Run backtest
    engine.run(df)

    # Get and print results
    results = engine.get_results()
    print_results(results)