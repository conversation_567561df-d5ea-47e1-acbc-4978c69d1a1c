import tkinter as tk
from tkinter import ttk
import json
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 或 ['Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False
# 读取数据
with open('simulatedaysummary_new.json', 'r', encoding='utf-8') as file:
    data = json.load(file)

dates = list(data.keys())
account_balances = {}
for date in dates:
    for user in data[date]:
        user_name = user["user_name"]
        if user_name not in account_balances:
            account_balances[user_name] = []
        account_balances[user_name].append(user["balance"])
accounts = list(account_balances.keys())

root = tk.Tk()
root.title("账号资金曲线查看器")
selected_account = tk.StringVar(value=accounts[0])

def plot_balance(account):
    fig, ax = plt.subplots(figsize=(8, 4))
    ax.plot(dates, account_balances[account], marker='o')
    ax.set_title(f"{account} 资金曲线")
    ax.set_xlabel("交易日")
    ax.set_ylabel("资金")
    ax.tick_params(axis='x', rotation=45)
    fig.tight_layout()
    return fig

def update_plot(*args):
    fig = plot_balance(selected_account.get())
    canvas.figure = fig
    canvas.draw()

account_menu = ttk.OptionMenu(root, selected_account, accounts[0], *accounts)
account_menu.pack(pady=10)

refresh_btn = ttk.Button(root, text="刷新", command=update_plot)
refresh_btn.pack(pady=5)

fig = plot_balance(accounts[0])
canvas = FigureCanvasTkAgg(fig, master=root)
canvas.get_tk_widget().pack()

root.mainloop()