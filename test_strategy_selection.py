"""
测试策略选择功能
验证命令行参数和策略配置
"""

import subprocess
import sys
from loguru import logger

def test_command_line_help():
    """测试命令行帮助信息"""
    print("=" * 60)
    print("测试命令行帮助信息")
    print("=" * 60)
    
    try:
        # 运行帮助命令
        result = subprocess.run([
            sys.executable, "llt_multi_contract_analyzer.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            help_text = result.stdout
            
            # 检查是否包含策略选择参数
            checks = [
                ("--strategy", "策略选择参数"),
                ("LLT", "LLT策略选项"),
                ("MA", "MA策略选项"),
                ("DualMA", "双均线策略选项"),
                ("--ma-period", "MA周期参数"),
                ("--ma-range", "MA范围参数"),
                ("--fast-range", "快线范围参数"),
                ("--slow-range", "慢线范围参数"),
            ]
            
            print("✅ 命令行帮助信息获取成功")
            print("\n📋 参数检查:")
            
            all_found = True
            for param, desc in checks:
                if param in help_text:
                    print(f"  ✅ {desc}: {param}")
                else:
                    print(f"  ❌ {desc}: {param} 未找到")
                    all_found = False
            
            if all_found:
                print("\n✅ 所有策略参数都已正确添加")
                return True
            else:
                print("\n❌ 部分策略参数缺失")
                return False
        else:
            print(f"❌ 获取帮助信息失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_strategy_config_creation():
    """测试策略配置创建"""
    print("=" * 60)
    print("测试策略配置创建")
    print("=" * 60)
    
    try:
        from llt_multi_contract_analyzer import create_strategy_config_from_args, StrategyConfig
        
        # 模拟命令行参数
        class MockArgs:
            def __init__(self, strategy, d_range=[10, 101], ma_range=[5, 51], 
                        fast_range=[3, 11], slow_range=[10, 31]):
                self.strategy = strategy
                self.d_range = d_range
                self.ma_range = ma_range
                self.fast_range = fast_range
                self.slow_range = slow_range
        
        # 测试LLT策略配置
        print("\n📊 测试LLT策略配置:")
        llt_args = MockArgs("LLT")
        llt_config = create_strategy_config_from_args(llt_args)
        
        print(f"  策略类型: {llt_config.strategy_type}")
        print(f"  参数: {llt_config.parameters}")
        
        if (llt_config.strategy_type == "LLT" and 
            "d_value_range" in llt_config.parameters):
            print("  ✅ LLT策略配置正确")
        else:
            print("  ❌ LLT策略配置错误")
            return False
        
        # 测试MA策略配置
        print("\n📊 测试MA策略配置:")
        ma_args = MockArgs("MA", ma_range=[10, 21])
        ma_config = create_strategy_config_from_args(ma_args)
        
        print(f"  策略类型: {ma_config.strategy_type}")
        print(f"  参数: {ma_config.parameters}")
        
        if (ma_config.strategy_type == "MA" and 
            "period_range" in ma_config.parameters and
            ma_config.parameters["period_range"] == (10, 21)):
            print("  ✅ MA策略配置正确")
        else:
            print("  ❌ MA策略配置错误")
            return False
        
        # 测试双均线策略配置
        print("\n📊 测试双均线策略配置:")
        dual_ma_args = MockArgs("DualMA", fast_range=[5, 11], slow_range=[13, 21])
        dual_ma_config = create_strategy_config_from_args(dual_ma_args)
        
        print(f"  策略类型: {dual_ma_config.strategy_type}")
        print(f"  参数: {dual_ma_config.parameters}")
        
        if (dual_ma_config.strategy_type == "DualMA" and 
            "fast_range" in dual_ma_config.parameters and
            "slow_range" in dual_ma_config.parameters):
            print("  ✅ 双均线策略配置正确")
        else:
            print("  ❌ 双均线策略配置错误")
            return False
        
        print("\n✅ 所有策略配置创建正确")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_strategy_analysis_function():
    """测试策略分析函数"""
    print("=" * 60)
    print("测试策略分析函数")
    print("=" * 60)
    
    try:
        from llt_multi_contract_analyzer import run_strategy_analysis, StrategyConfig
        
        # 测试函数是否存在
        print("✅ run_strategy_analysis 函数导入成功")
        
        # 创建测试策略配置
        test_configs = [
            StrategyConfig("LLT", {"d_value_range": (15, 21)}),
            StrategyConfig("MA", {"period_range": (10, 16)}),
            StrategyConfig("DualMA", {"fast_range": (5, 8), "slow_range": (13, 16)})
        ]
        
        print("\n📊 策略配置测试:")
        for config in test_configs:
            print(f"  ✅ {config.strategy_type}策略配置: {config.parameters}")
        
        print("\n💡 注意: 实际运行需要真实的API连接和数据")
        print("💡 函数接口验证成功，可以正常调用")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_usage_examples():
    """显示使用示例"""
    print("=" * 60)
    print("策略选择使用示例")
    print("=" * 60)
    
    examples = [
        {
            "title": "LLT策略分析（默认）",
            "command": "python llt_multi_contract_analyzer.py --mode quick",
            "description": "使用默认LLT策略，D_VALUE范围20-81"
        },
        {
            "title": "LLT策略自定义参数",
            "command": "python llt_multi_contract_analyzer.py --strategy LLT --d-range 10 31 --mode quick",
            "description": "LLT策略，自定义D_VALUE范围10-31"
        },
        {
            "title": "MA策略分析",
            "command": "python llt_multi_contract_analyzer.py --strategy MA --mode quick",
            "description": "移动平均线策略，默认周期范围5-51"
        },
        {
            "title": "MA策略自定义参数",
            "command": "python llt_multi_contract_analyzer.py --strategy MA --ma-range 10 21 --mode quick",
            "description": "MA策略，自定义周期范围10-21"
        },
        {
            "title": "双均线策略分析",
            "command": "python llt_multi_contract_analyzer.py --strategy DualMA --mode quick",
            "description": "双均线策略，默认快线3-11，慢线10-31"
        },
        {
            "title": "双均线策略自定义参数",
            "command": "python llt_multi_contract_analyzer.py --strategy DualMA --fast-range 5 11 --slow-range 13 21 --mode quick",
            "description": "双均线策略，自定义快线5-11，慢线13-21"
        },
        {
            "title": "MA策略完整分析",
            "command": "python llt_multi_contract_analyzer.py --strategy MA --mode full --plot-curves",
            "description": "MA策略完整分析并绘制资金曲线"
        },
        {
            "title": "指定合约组分析",
            "command": "python llt_multi_contract_analyzer.py --strategy MA --groups agricultural --mode quick",
            "description": "只分析农产品合约的MA策略"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")
    
    print(f"\n📋 参数说明:")
    print(f"  --strategy: 选择策略类型 (LLT, MA, DualMA)")
    print(f"  --d-range: LLT策略的D_VALUE范围")
    print(f"  --ma-range: MA策略的周期范围")
    print(f"  --fast-range: 双均线策略的快线周期范围")
    print(f"  --slow-range: 双均线策略的慢线周期范围")
    print(f"  --mode: 分析模式 (quick, full, all-main)")
    print(f"  --groups: 合约组 (agricultural, metals, chemicals, etc.)")
    print(f"  --plot-curves: 绘制资金曲线图")


def main():
    """主测试函数"""
    print("策略选择功能测试")
    print("=" * 80)
    
    tests = [
        ("命令行帮助信息", test_command_line_help),
        ("策略配置创建", test_strategy_config_creation),
        ("策略分析函数", test_strategy_analysis_function),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 策略选择功能测试成功！")
        show_usage_examples()
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
