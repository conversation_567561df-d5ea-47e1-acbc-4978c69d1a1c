"""
测试不同策略模式的工作情况
"""

import subprocess
import sys
import time
import os


def test_strategy_mode(product: str, mode: str, duration: int = 30):
    """测试特定模式的策略"""
    print("=" * 60)
    print(f"测试 {product} {mode} 模式")
    print("=" * 60)
    
    cmd = [
        sys.executable,
        "TimeRoseMA_cross_ag_MultiTimeFrames.py",
        product,
        mode,
        "quant_ggh,Qiai1301"
    ]
    
    print(f"测试命令: {' '.join(cmd)}")
    
    # 创建日志文件
    log_file = f"test_{product}_{mode}.log"
    
    try:
        # 启动进程
        with open(log_file, 'w', encoding='utf-8') as f:
            process = subprocess.Popen(
                cmd,
                stdout=f,
                stderr=subprocess.STDOUT,
                text=True
            )
        
        print(f"进程启动，PID: {process.pid}")
        print(f"日志文件: {log_file}")
        print(f"运行 {duration} 秒...")
        
        # 等待指定时间
        time.sleep(duration)
        
        # 检查进程状态
        if process.poll() is None:
            print("进程仍在运行")
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=10)
                print("进程已正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("进程已强制终止")
        else:
            print(f"进程已退出，返回码: {process.returncode}")
        
        # 读取并分析日志
        analyze_log(log_file, mode)
        
    except Exception as e:
        print(f"测试失败: {e}")


def analyze_log(log_file: str, mode: str):
    """分析日志文件"""
    print(f"\n分析 {mode} 模式日志:")
    print("-" * 40)
    
    try:
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if not content.strip():
                print("❌ 日志文件为空")
                return
            
            lines = content.strip().split('\n')
            print(f"📄 日志行数: {len(lines)}")
            
            # 检查关键信息
            key_indicators = {
                "策略启动": ["多时间周期策略启动", "策略启动"],
                "API连接": ["交易合约", "TqApi", "连接成功"],
                "进程启动": ["已启动", "策略进程", "PID"],
                "策略运行": ["运行", "执行", "处理"],
                "错误信息": ["错误", "失败", "异常", "Error", "Exception"]
            }
            
            found_indicators = {}
            for category, keywords in key_indicators.items():
                found_indicators[category] = []
                for line in lines:
                    for keyword in keywords:
                        if keyword in line:
                            found_indicators[category].append(line.strip())
                            break
            
            # 显示分析结果
            for category, found_lines in found_indicators.items():
                if found_lines:
                    print(f"✅ {category}: {len(found_lines)} 条")
                    for line in found_lines[:2]:  # 只显示前2条
                        print(f"   {line[:80]}...")
                else:
                    print(f"❌ {category}: 未找到")
            
            # 显示最后几行
            print(f"\n📝 最后5行日志:")
            for line in lines[-5:]:
                print(f"   {line}")
                
        else:
            print("❌ 日志文件不存在")
            
    except Exception as e:
        print(f"❌ 日志分析失败: {e}")


def compare_modes():
    """对比不同模式"""
    print("=" * 80)
    print("策略模式对比测试")
    print("=" * 80)
    
    modes = ["optimized", "independent", "threaded"]
    product = "cu"
    
    results = {}
    
    for mode in modes:
        print(f"\n🔄 测试 {mode} 模式...")
        
        try:
            test_strategy_mode(product, mode, duration=20)
            results[mode] = "完成"
        except Exception as e:
            print(f"❌ {mode} 模式测试失败: {e}")
            results[mode] = f"失败: {e}"
        
        print("\n" + "="*40)
        time.sleep(2)  # 间隔测试
    
    # 显示总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    for mode, result in results.items():
        print(f"{mode:12}: {result}")
    
    # 推荐使用的模式
    print("\n📋 使用建议:")
    print("- independent: 推荐用于生产环境（稳定性最好）")
    print("- threaded: 可用于开发测试")
    print("- optimized: 需要进一步调试（当前有问题）")


def test_batch_manager():
    """测试批量管理器"""
    print("=" * 60)
    print("测试批量管理器")
    print("=" * 60)
    
    # 清理状态
    subprocess.run([sys.executable, "start_scheduled_strategies_v2.py", "cleanup"])
    
    # 启动策略
    print("启动策略...")
    result = subprocess.run([
        sys.executable, 
        "start_scheduled_strategies_v2.py", 
        "start", 
        "cu"
    ], capture_output=True, text=True)
    
    print("启动结果:")
    print(result.stdout)
    if result.stderr:
        print("错误:")
        print(result.stderr)
    
    # 等待一段时间
    time.sleep(10)
    
    # 检查状态
    print("\n检查状态...")
    result = subprocess.run([
        sys.executable, 
        "start_scheduled_strategies_v2.py", 
        "list"
    ], capture_output=True, text=True)
    
    print("状态结果:")
    print(result.stdout)
    
    # 停止策略
    print("\n停止策略...")
    result = subprocess.run([
        sys.executable, 
        "start_scheduled_strategies_v2.py", 
        "stop", 
        "cu"
    ], capture_output=True, text=True)
    
    print("停止结果:")
    print(result.stdout)


def main():
    """主函数"""
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        
        if test_type == "modes":
            compare_modes()
        elif test_type == "batch":
            test_batch_manager()
        elif test_type in ["optimized", "independent", "threaded"]:
            product = sys.argv[2] if len(sys.argv) > 2 else "cu"
            duration = int(sys.argv[3]) if len(sys.argv) > 3 else 30
            test_strategy_mode(product, test_type, duration)
        else:
            print("用法:")
            print("  python test_strategy_modes.py modes                    # 对比所有模式")
            print("  python test_strategy_modes.py batch                    # 测试批量管理器")
            print("  python test_strategy_modes.py optimized [product] [duration]  # 测试特定模式")
            print("  python test_strategy_modes.py independent [product] [duration]")
            print("  python test_strategy_modes.py threaded [product] [duration]")
    else:
        print("快速测试批量管理器...")
        test_batch_manager()


if __name__ == "__main__":
    main()
