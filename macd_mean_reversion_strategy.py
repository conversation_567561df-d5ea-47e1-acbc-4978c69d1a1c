import pandas as pd
import numpy as np
from typing import Tuple, List


class TradingStrategy:
    def __init__(self, lookback_period: int = 20):
        self.lookback_period = lookback_period
        self.positions = []
        self.trades = []

    def calculate_macd(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        # 计算快线和慢线的EMA
        exp12 = data['close'].ewm(span=12, adjust=False).mean()
        exp26 = data['close'].ewm(span=26, adjust=False).mean()

        # 计算DIFF和DEA
        data['diff'] = exp12 - exp26
        data['dea'] = data['diff'].ewm(span=9, adjust=False).mean()
        data['macd'] = 2 * (data['diff'] - data['dea'])

        return data

    def calculate_mean_reversion(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算均值回归信号"""
        # 计算移动平均和标准差
        data['ma'] = data['close'].rolling(window=self.lookback_period).mean()
        data['std'] = data['close'].rolling(window=self.lookback_period).std()

        # 计算上下轨道
        data['upper_band'] = data['ma'] + 2 * data['std']
        data['lower_band'] = data['ma'] - 2 * data['std']

        # 生成均值回归信号
        data['mean_reversion_long'] = (data['close'] < data['lower_band']).astype(int)
        data['mean_reversion_short'] = (data['close'] > data['upper_band']).astype(int)

        return data

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        # 计算指标
        data = self.calculate_macd(data)
        data = self.calculate_mean_reversion(data)

        # 初始化信号列
        data['signal'] = 0

        # MACD穿越0轴信号
        data['macd_cross_up'] = (data['macd'] > 0) & (data['macd'].shift(1) <= 0)
        data['macd_cross_down'] = (data['macd'] < 0) & (data['macd'].shift(1) >= 0)

        # DIFF穿越DEA信号
        data['diff_cross_up'] = (data['diff'] > data['dea']) & (data['diff'].shift(1) <= data['dea'].shift(1))
        data['diff_cross_down'] = (data['diff'] < data['dea']) & (data['diff'].shift(1) >= data['dea'].shift(1))

        # 生成最终信号
        for i in range(1, len(data)):
            # 开多仓条件
            if data.iloc[i]['macd_cross_up']:
                data.iloc[i, data.columns.get_loc('signal')] = 1

            # 开空仓条件
            elif data.iloc[i]['macd_cross_down']:
                data.iloc[i, data.columns.get_loc('signal')] = -1

            # 平多仓条件
            elif (data.iloc[i]['mean_reversion_short'] or data.iloc[i]['diff_cross_down']) and \
                    data.iloc[i - 1]['signal'] == 1:
                data.iloc[i, data.columns.get_loc('signal')] = 0

            # 平空仓条件
            elif (data.iloc[i]['mean_reversion_long'] or data.iloc[i]['diff_cross_up']) and \
                    data.iloc[i - 1]['signal'] == -1:
                data.iloc[i, data.columns.get_loc('signal')] = 0

            # 保持前一个信号
            else:
                data.iloc[i, data.columns.get_loc('signal')] = data.iloc[i - 1]['signal']

        return data

    def backtest(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, List]:
        """执行回测"""
        # 生成交易信号
        data = self.generate_signals(data)

        # 计算收益
        data['returns'] = data['close'].pct_change()
        data['strategy_returns'] = data['signal'].shift(1) * data['returns']

        # 记录交易
        trades = []
        position = 0
        entry_price = 0

        for i in range(1, len(data)):
            current_signal = data.iloc[i]['signal']
            prev_signal = data.iloc[i - 1]['signal']

            # 开仓或换仓
            if current_signal != prev_signal:
                # 平仓
                if position != 0:
                    exit_price = data.iloc[i]['close']
                    profit = (exit_price - entry_price) * position
                    trades.append({
                        'exit_time': data.iloc[i]['datetime'],
                        'exit_price': exit_price,
                        'profit': profit
                    })

                # 开新仓
                if current_signal != 0:
                    position = current_signal
                    entry_price = data.iloc[i]['close']
                    trades.append({
                        'entry_time': data.iloc[i]['datetime'],
                        'entry_price': entry_price,
                        'position': position
                    })
                else:
                    position = 0
                    entry_price = 0

        return data, trades


def run_strategy():
    # 读取数据
    data = pd.read_csv('data.csv')
    data['datetime'] = pd.to_datetime(data['datetime'].astype(float), unit='ns')

    # 初始化策略并执行回测
    strategy = TradingStrategy(lookback_period=20)
    results, trades = strategy.backtest(data)

    # 计算策略评估指标
    total_returns = results['strategy_returns'].sum()
    sharpe_ratio = np.sqrt(252) * results['strategy_returns'].mean() / results['strategy_returns'].std()
    max_drawdown = (results['strategy_returns'].cumsum() - results['strategy_returns'].cumsum().cummax()).min()

    print(f"策略总收益率: {total_returns:.2%}")
    print(f"夏普比率: {sharpe_ratio:.2f}")
    print(f"最大回撤: {max_drawdown:.2%}")
    print(f"总交易次数: {len(trades)}")

    return results, trades


if __name__ == "__main__":
    results, trades = run_strategy()
