"""
诊断策略进程快速退出的问题
"""

import subprocess
import sys
import time
import os


def test_strategy_command():
    """测试策略命令是否能正常运行"""
    print("=" * 60)
    print("测试策略命令")
    print("=" * 60)
    
    cmd = [
        sys.executable,
        "TimeRoseMA_cross_ag_MultiTimeFrames.py",
        "cu",
        "quant_ggh,Qiai1301"
    ]
    
    print(f"测试命令: {' '.join(cmd)}")
    
    try:
        # 直接运行命令，捕获输出
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30  # 30秒超时
        )
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
            
        if result.returncode != 0:
            print(f"命令执行失败，返回码: {result.returncode}")
        else:
            print("命令执行成功")
            
    except subprocess.TimeoutExpired:
        print("命令执行超时（这是正常的，说明策略在运行）")
    except Exception as e:
        print(f"命令执行出错: {e}")


def test_import_issues():
    """测试导入问题"""
    print("=" * 60)
    print("测试导入问题")
    print("=" * 60)
    
    # 测试主要导入
    test_imports = [
        "from tqsdk import TqApi, TqKq",
        "from strategies.TimeRoseMA_cross_speak import ma_cross",
        "import schedule",
        "from datetime import datetime, time as dt_time",
    ]
    
    for import_stmt in test_imports:
        try:
            exec(import_stmt)
            print(f"✓ {import_stmt}")
        except Exception as e:
            print(f"✗ {import_stmt} - 错误: {e}")


def test_file_existence():
    """测试文件是否存在"""
    print("=" * 60)
    print("测试文件存在性")
    print("=" * 60)
    
    files_to_check = [
        "TimeRoseMA_cross_ag_MultiTimeFrames.py",
        "strategies/TimeRoseMA_cross_speak.py",
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✓ {file_path} 存在")
        else:
            print(f"✗ {file_path} 不存在")


def test_simple_strategy():
    """测试简单策略启动"""
    print("=" * 60)
    print("测试简单策略启动")
    print("=" * 60)
    
    cmd = [
        sys.executable,
        "TimeRoseMA_cross_ag_MultiTimeFrames.py",
        "cu"
    ]
    
    print(f"简化命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        print(f"进程启动，PID: {process.pid}")
        
        # 等待5秒
        time.sleep(5)
        
        if process.poll() is None:
            print("进程仍在运行")
            # 读取部分输出
            try:
                stdout, _ = process.communicate(timeout=2)
                print("输出:")
                print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
            except subprocess.TimeoutExpired:
                process.kill()
                stdout, _ = process.communicate()
                print("进程被强制终止")
                print("输出:")
                print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
        else:
            print(f"进程已退出，返回码: {process.returncode}")
            stdout, _ = process.communicate()
            print("输出:")
            print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
            
    except Exception as e:
        print(f"测试失败: {e}")


def test_auth_parameter():
    """测试认证参数"""
    print("=" * 60)
    print("测试认证参数")
    print("=" * 60)
    
    # 测试不同的认证参数格式
    auth_formats = [
        "quant_ggh,Qiai1301",
        '"quant_ggh,Qiai1301"',
        "smartmanp,ftp123",
    ]
    
    for auth in auth_formats:
        print(f"测试认证: {auth}")
        
        cmd = [
            sys.executable,
            "TimeRoseMA_cross_ag_MultiTimeFrames.py",
            "cu",
            auth
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print(f"  ✓ 认证 {auth} 可用")
            else:
                print(f"  ✗ 认证 {auth} 失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"    错误: {result.stderr[:200]}")
                    
        except subprocess.TimeoutExpired:
            print(f"  ✓ 认证 {auth} 可用（进程运行中）")
        except Exception as e:
            print(f"  ✗ 认证 {auth} 测试失败: {e}")


def check_python_environment():
    """检查Python环境"""
    print("=" * 60)
    print("检查Python环境")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查关键库
    required_packages = [
        "tqsdk",
        "pandas",
        "numpy",
        "schedule",
        "psutil",
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")


def create_minimal_test():
    """创建最小测试脚本"""
    print("=" * 60)
    print("创建最小测试脚本")
    print("=" * 60)
    
    test_script = """
import sys
print("Python版本:", sys.version)
print("参数:", sys.argv)

try:
    from tqsdk import TqApi, TqKq
    print("✓ TqSDK导入成功")
    
    # 尝试创建API连接
    if len(sys.argv) > 1:
        auth = sys.argv[1]
        print(f"使用认证: {auth}")
        api = TqApi(TqKq(), auth=auth, disable_print=True)
        print("✓ API连接成功")
        api.close()
        print("✓ API关闭成功")
    else:
        print("未提供认证参数")
        
except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback
    traceback.print_exc()

print("测试脚本执行完成")
"""
    
    with open("minimal_test.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("已创建 minimal_test.py")
    
    # 运行最小测试
    cmd = [sys.executable, "minimal_test.py", "quant_ggh,Qiai1301"]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        print("最小测试结果:")
        print(f"返回码: {result.returncode}")
        print("输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
            
    except Exception as e:
        print(f"最小测试失败: {e}")


def main():
    """主函数"""
    print("策略进程退出问题诊断")
    print("=" * 80)
    
    # 运行所有诊断测试
    check_python_environment()
    test_file_existence()
    test_import_issues()
    create_minimal_test()
    test_auth_parameter()
    test_simple_strategy()
    
    print("\n" + "=" * 80)
    print("诊断完成")
    print("=" * 80)


if __name__ == "__main__":
    main()
