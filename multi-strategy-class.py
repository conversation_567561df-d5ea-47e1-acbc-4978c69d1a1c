import pandas as pd
import numpy as np
from abc import ABC, abstractmethod

class BaseStrategy(ABC):
    @abstractmethod
    def calculate_indicators(self, df):
        pass

    @abstractmethod
    def generate_signals(self, df):
        pass

class WaveStrategy(BaseStrategy):
    @staticmethod
    def MA(series, n):
        return series.rolling(window=n).mean()

    @staticmethod
    def VALUEWHEN(condition, value):
        return value[condition].reindex(value.index).ffill()

    @staticmethod
    def REFX1(series, n):
        return series.shift(n)

    def calculate_indicators(self, df):
        df['MA1'] = self.MA(df['close'], 5)
        df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
                             df['high'].shift(2), 0)
        df['HH2'] = self.VALUEWHEN(df['HH1'] > 0, df['HH1'])
        df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)), df['low'].shift(2), 0)
        df['LL2'] = self.VALUEWHEN(df['LL1'] > 0, df['LL1'])
        df['K1'] = np.where(df['close'] > df['HH2'], -3, np.where(df['close'] < df['LL2'], 1, 0))
        df['K2'] = self.VALUEWHEN(df['K1'] != 0, df['K1'])
        df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
        df['G1'] = df['G'].iloc[-1]
        df['W1'] = df['K2']
        df['W2'] = df['open'] - df['close']
        df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
        df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])
        df['LLL'] = np.minimum(df['open'].shift(1), df['close'].shift(1))
        df['HHH'] = np.maximum(df['open'].shift(1), df['close'].shift(1))
        df['CCCC'] = self.REFX1(df['close'], 1000)
        df['LLLL'] = self.REFX1(df['LLL'], 1000)
        df['HHHH'] = self.REFX1(df['HHH'], 1000)
        return df

    def generate_signals(self, df):
        df['wave_buy_signal'] = (df['close'] > df['MA1']) & (df['K2'] == 1)
        df['wave_sell_signal'] = (df['close'] < df['MA1']) & (df['K2'] == -3)
        return df

class RSIStrategy(BaseStrategy):
    def __init__(self, period=14, overbought=70, oversold=30):
        self.period = period
        self.overbought = overbought
        self.oversold = oversold

    def calculate_indicators(self, df):
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.period).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        return df

    def generate_signals(self, df):
        df['rsi_buy_signal'] = df['RSI'] < self.oversold
        df['rsi_sell_signal'] = df['RSI'] > self.overbought
        return df

class MACDStrategy(BaseStrategy):
    def __init__(self, fast_period=12, slow_period=26, signal_period=9):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period

    def calculate_indicators(self, df):
        df['EMA_fast'] = df['close'].ewm(span=self.fast_period, adjust=False).mean()
        df['EMA_slow'] = df['close'].ewm(span=self.slow_period, adjust=False).mean()
        df['MACD'] = df['EMA_fast'] - df['EMA_slow']
        df['Signal_Line'] = df['MACD'].ewm(span=self.signal_period, adjust=False).mean()
        df['MACD_Histogram'] = df['MACD'] - df['Signal_Line']
        return df

    def generate_signals(self, df):
        df['macd_buy_signal'] = (df['MACD'] > df['Signal_Line']) & (df['MACD'].shift(1) <= df['Signal_Line'].shift(1))
        df['macd_sell_signal'] = (df['MACD'] < df['Signal_Line']) & (df['MACD'].shift(1) >= df['Signal_Line'].shift(1))
        return df

class MAStrategy(BaseStrategy):
    def __init__(self, short_period=10, long_period=50):
        self.short_period = short_period
        self.long_period = long_period

    def calculate_indicators(self, df):
        df['MA_short'] = df['close'].rolling(window=self.short_period).mean()
        df['MA_long'] = df['close'].rolling(window=self.long_period).mean()
        return df

    def generate_signals(self, df):
        df['ma_buy_signal'] = df['MA_short'] > df['MA_long']
        df['ma_sell_signal'] = df['MA_short'] < df['MA_long']
        return df

class MultiStrategy:
    def __init__(self):
        self.strategies = {}

    def add_strategy(self, name, strategy):
        self.strategies[name] = strategy

    def run_strategies(self, data):
        if 'datetime' in data.columns:
            data = data.set_index('datetime')
        
        for name, strategy in self.strategies.items():
            data = strategy.calculate_indicators(data)
            data = strategy.generate_signals(data)
        
        return data

    def generate_combined_signals(self, data):
        # 这里可以实现综合多个策略信号的逻辑
        # 例如，可以使用投票机制或其他方法来结合不同策略的信号
        pass

# 使用示例
if __name__ == "__main__":
    # 假设我们有一个名为 'data.csv' 的文件，包含了所需的数据
    data = pd.read_csv('data.csv')
    
    multi_strategy = MultiStrategy()
    multi_strategy.add_strategy('wave', WaveStrategy())
    multi_strategy.add_strategy('rsi', RSIStrategy())
    multi_strategy.add_strategy('macd', MACDStrategy())
    multi_strategy.add_strategy('ma', MAStrategy())
    
    result = multi_strategy.run_strategies(data)
    
    print(result.tail())  # 打印最后几行结果

