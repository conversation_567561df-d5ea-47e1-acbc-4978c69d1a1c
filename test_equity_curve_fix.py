"""
测试权益曲线修复
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from loguru import logger
from llt_multi_contract_analyzer import ContractConfig, ContractAnalyzer

def create_simple_test_data():
    """创建简单测试数据"""
    np.random.seed(42)
    n_points = 100
    
    # 创建简单的价格数据
    base_price = 100
    trend = np.linspace(0, 10, n_points)
    noise = np.random.randn(n_points) * 0.5
    prices = base_price + trend + noise
    
    dates = pd.date_range('2024-01-01', periods=n_points, freq='5min')
    
    klines_data = pd.DataFrame({
        'datetime': dates,
        'open': prices,
        'high': prices + 0.5,
        'low': prices - 0.5,
        'close': prices,
        'volume': np.random.randint(100, 1000, n_points)
    })
    
    return klines_data


def test_equity_curve_generation():
    """测试权益曲线生成"""
    print("=" * 60)
    print("测试权益曲线生成修复")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='DEBUG', colorize=True)
    
    try:
        # 创建测试数据
        klines_data = create_simple_test_data()
        print(f"测试数据: {len(klines_data)}条K线")
        
        # 创建合约配置
        contract = ContractConfig("TEST.symbol", "测试合约", "TEST")
        
        # 创建分析器
        analyzer = ContractAnalyzer(contract, klines_data, "profit_only")
        
        # 测试单个回测
        d_value = 20
        alpha = 2 / (d_value + 1)
        
        print(f"\n🔍 测试单个回测:")
        print(f"D_VALUE: {d_value}, ALPHA: {alpha:.6f}")
        
        backtest_result = analyzer._run_backtest(alpha)
        
        print(f"回测结果:")
        print(f"  收益率: {backtest_result['return_rate']:.2f}%")
        print(f"  交易次数: {backtest_result['total_trades']}")
        print(f"  权益曲线点数: {len(backtest_result.get('equity_curve', []))}")
        
        # 测试参数优化
        print(f"\n🔍 测试参数优化:")
        optimization_results = analyzer.optimize_parameters(range(18, 23))  # 5个参数
        
        if optimization_results:
            print(f"优化结果数量: {len(optimization_results)}")
            best = optimization_results[0]
            print(f"最优参数: D_VALUE={best['D_VALUE']}, 收益率={best['return_rate']:.2f}%")
            
            # 测试生成分析结果
            print(f"\n🔍 测试生成分析结果:")
            analysis_result = analyzer.generate_analysis_result(optimization_results)
            
            if analysis_result:
                print(f"分析结果:")
                print(f"  最优D_VALUE: {analysis_result.best_d_value}")
                print(f"  收益率: {analysis_result.return_rate:.2f}%")
                print(f"  权益曲线点数: {len(analysis_result.equity_curve)}")
                print(f"  时间点数: {len(analysis_result.equity_dates)}")
                
                if len(analysis_result.equity_curve) > 0:
                    print(f"  权益曲线范围: {min(analysis_result.equity_curve):.2f} ~ {max(analysis_result.equity_curve):.2f}")
                    print("✅ 权益曲线数据正常")
                    return True
                else:
                    print("❌ 权益曲线数据为空")
                    return False
            else:
                print("❌ 生成分析结果失败")
                return False
        else:
            print("❌ 参数优化失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_equity_curve_plotting():
    """测试权益曲线绘制"""
    print("=" * 60)
    print("测试权益曲线绘制")
    print("=" * 60)
    
    try:
        from llt_multi_contract_analyzer import ContractAnalysisResult, plot_equity_curves_from_results
        from datetime import datetime, timedelta
        
        # 创建模拟分析结果
        contract = ContractConfig("TEST.symbol", "测试合约", "TEST")
        
        # 创建真实的权益曲线数据
        equity_curve = [0.0]
        for i in range(1, 51):
            # 模拟权益变化
            change = np.random.randn() * 5 + 0.2  # 轻微上升趋势
            equity_curve.append(equity_curve[-1] + change)
        
        equity_dates = [datetime.now() + timedelta(minutes=5*i) for i in range(51)]
        
        result = ContractAnalysisResult(
            contract=contract,
            best_d_value=20,
            best_alpha=0.095,
            total_trades=15,
            total_pnl=equity_curve[-1],
            win_rate=80.0,
            return_rate=equity_curve[-1] / 100,  # 假设初始资金100
            winning_trades=12,
            losing_trades=3,
            avg_win=15.0,
            avg_loss=-8.0,
            profit_factor=1.8,
            max_drawdown=20.0,
            sharpe_ratio=1.2,
            analysis_period_days=30,
            data_quality_score=95.0,
            equity_curve=equity_curve,
            equity_dates=equity_dates
        )
        
        print(f"模拟分析结果:")
        print(f"  合约: {result.contract.symbol}")
        print(f"  收益率: {result.return_rate:.2f}%")
        print(f"  权益曲线点数: {len(result.equity_curve)}")
        print(f"  时间点数: {len(result.equity_dates)}")
        
        # 测试绘制
        print(f"\n📈 测试绘制权益曲线...")
        plot_equity_curves_from_results([result], "test_equity_fix", top_n=1)
        
        print(f"✅ 权益曲线绘制完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("权益曲线修复测试")
    print("=" * 80)
    
    tests = [
        ("权益曲线生成", test_equity_curve_generation),
        ("权益曲线绘制", test_equity_curve_plotting),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 权益曲线修复成功！")
        print("\n📋 现在可以正确获取和绘制权益曲线:")
        print("# 分析时自动绘制真实权益曲线")
        print("python llt_multi_contract_analyzer.py --mode quick --plot-curves")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
