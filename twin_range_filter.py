import tqsdk

# 初始化环境
tq = tqsdk.TqApi(backtest=True)

# 回测配置
tq.set_backtesting_mode(True)
tq.set_account(initial_cash=1000000, margin_ratio=10.0)  # 1w初始资金

# 回测数据（使用历史数据进行回测）
stock_code = 'SHFE.cu'  # 品种代码
data_from = tqsdk.KLineDataType_1Min  # 获取分钟级别的K线数据
data_to = tqsdk.KLineDataType_10Years  #获取10年级别的K线数据

# 回测参数
fast_period = 27
slow_period = 55
mult1 = 1.6
mult2 = 2.0

# 创建策略类
class TwinRangeFilter:
    def __init__(self):
        self.data = tq.get_kline_data(stock_code, data_from, data_to)
        self.smrng1 = self.data.get_sma(fast_period) * mult1
        self.smrng2 = self.data.get_sma(slow_period) * mult2
        self.filt = (self.smrng1 + self.smrng2) / 2
        self.upward = [0] * len(self.data)

    def on_bar(self):
        if not self.data.is_long_position():
            if self.data.close > self.filt and \
               ((self.data.close > self.data.pre_close) or (self.upward[-1] == 1)):
                return 'long'
            elif self.data.close > self.filt and \
                 ((self.data.close < self.data.pre_close) and (self.upward[-1] == 1)):
                return 'long'
        else:
            if self.data.is_short_position():
                if self.data.close < self.filt and \
                   ((self.data.close < self.data.pre_close) or (self.upward[-1] == -1)):
                    return 'short'

    def on_order(self, order):
        # 如果是空头单，设置卖出价格为当前价
        if order.is_short_position():
            order.sell_price = order.close

# 实例化策略类
strategy = TwinRangeFilter()

# 回测
while True:
    strategy.on_bar()

