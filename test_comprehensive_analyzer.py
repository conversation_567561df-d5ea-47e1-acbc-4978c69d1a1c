"""
测试模拟盘资金曲线综合分析器
验证合并后的程序功能
"""

import json
import os
import sys
from datetime import datetime, timedelta
import random

def create_comprehensive_test_data():
    """创建综合测试数据"""
    print("创建综合测试数据...")
    
    # 生成30个交易日
    start_date = datetime(2024, 1, 1)
    dates = []
    for i in range(30):
        date = start_date + timedelta(days=i)
        if date.weekday() < 5:  # 只包含工作日
            dates.append(date.strftime('%Y-%m-%d'))
    
    # 创建多样化的账户
    accounts = [
        '张三的价值投资',
        '李四量化策略', 
        '王五趋势跟踪',
        '赵六套利基金',
        'Alpha对冲策略',
        'Beta增强组合',
        '稳健收益账户',
        '激进成长基金'
    ]
    
    data = {}
    
    for date_idx, date in enumerate(dates):
        data[date] = []
        
        for acc_idx, account in enumerate(accounts):
            # 不同账户的基础资金
            base_balance = 50000 + acc_idx * 30000
            
            # 设置随机种子确保可重现
            random.seed(hash(account + date) % 10000)
            
            # 模拟不同的投资策略表现
            if '价值投资' in account:
                # 稳定增长，低波动
                daily_return = 0.002 + random.gauss(0, 0.005)
            elif '量化策略' in account:
                # 中等收益，中等波动
                daily_return = 0.003 + random.gauss(0, 0.008)
            elif '趋势跟踪' in account:
                # 趋势性收益，较高波动
                trend_factor = 1 if date_idx > 15 else -0.5
                daily_return = 0.001 * trend_factor + random.gauss(0, 0.012)
            elif '套利基金' in account:
                # 低风险低收益
                daily_return = 0.001 + random.gauss(0, 0.003)
            elif 'Alpha' in account:
                # 高收益高风险
                daily_return = 0.005 + random.gauss(0, 0.015)
            elif 'Beta' in account:
                # 市场相关性强
                market_factor = 0.002 if date_idx % 3 == 0 else -0.001
                daily_return = market_factor + random.gauss(0, 0.010)
            elif '稳健' in account:
                # 非常稳定
                daily_return = 0.0015 + random.gauss(0, 0.002)
            else:  # 激进成长
                # 高波动
                daily_return = 0.004 + random.gauss(0, 0.020)
            
            # 计算累积收益
            if date_idx == 0:
                balance = base_balance
            else:
                prev_balance = base_balance * (1 + sum([
                    get_daily_return(account, dates[i], i) for i in range(date_idx)
                ]))
                balance = prev_balance * (1 + daily_return)
            
            # 确保不会亏损太多
            balance = max(balance, base_balance * 0.7)
            
            data[date].append({
                "user_name": account,
                "balance": round(balance, 2)
            })
    
    # 保存数据
    with open('simulatedaysummary_new.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"✓ 测试数据已创建: {len(accounts)}个账户, {len(dates)}个交易日")
    return True

def get_daily_return(account, date, day_idx):
    """获取特定账户特定日期的收益率（用于累积计算）"""
    random.seed(hash(account + date) % 10000)
    
    if '价值投资' in account:
        return 0.002 + random.gauss(0, 0.005)
    elif '量化策略' in account:
        return 0.003 + random.gauss(0, 0.008)
    elif '趋势跟踪' in account:
        trend_factor = 1 if day_idx > 15 else -0.5
        return 0.001 * trend_factor + random.gauss(0, 0.012)
    elif '套利基金' in account:
        return 0.001 + random.gauss(0, 0.003)
    elif 'Alpha' in account:
        return 0.005 + random.gauss(0, 0.015)
    elif 'Beta' in account:
        market_factor = 0.002 if day_idx % 3 == 0 else -0.001
        return market_factor + random.gauss(0, 0.010)
    elif '稳健' in account:
        return 0.0015 + random.gauss(0, 0.002)
    else:
        return 0.004 + random.gauss(0, 0.020)

def test_data_loading():
    """测试数据加载功能"""
    print("=" * 60)
    print("测试数据加载功能")
    print("=" * 60)
    
    try:
        # 检查数据文件
        if not os.path.exists('simulatedaysummary_new.json'):
            print("数据文件不存在，创建测试数据...")
            create_comprehensive_test_data()
        
        # 加载数据
        with open('simulatedaysummary_new.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        dates = list(data.keys())
        accounts = set()
        
        for date in dates:
            for user in data[date]:
                accounts.add(user["user_name"])
        
        print(f"✓ 数据加载成功")
        print(f"✓ 交易日数量: {len(dates)}")
        print(f"✓ 账户数量: {len(accounts)}")
        
        # 显示账户列表
        print(f"✓ 账户列表:")
        for i, account in enumerate(sorted(accounts), 1):
            print(f"  {i}. {account}")
        
        # 验证数据完整性
        total_data_points = 0
        for date in dates:
            total_data_points += len(data[date])
        
        expected_points = len(dates) * len(accounts)
        if total_data_points == expected_points:
            print(f"✓ 数据完整性验证通过: {total_data_points}个数据点")
        else:
            print(f"⚠️  数据完整性问题: 期望{expected_points}, 实际{total_data_points}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        return False

def test_program_import():
    """测试程序导入"""
    print("=" * 60)
    print("测试程序导入")
    print("=" * 60)
    
    try:
        # 测试导入新程序
        import importlib.util
        
        spec = importlib.util.spec_from_file_location(
            "comprehensive_analyzer", 
            "模拟盘资金曲线分析器.py"
        )
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print("✓ 综合分析器程序导入成功")
        
        # 检查主要类
        if hasattr(module, 'ComprehensiveFundAnalyzer'):
            print("✓ ComprehensiveFundAnalyzer 类存在")
        else:
            print("✗ ComprehensiveFundAnalyzer 类不存在")
            return False
        
        # 检查主要方法
        analyzer_class = module.ComprehensiveFundAnalyzer
        required_methods = [
            'setup_chinese_font',
            'setup_ui', 
            'load_data',
            'analyze_total_data',
            'analyze_account_data',
            'export_chart'
        ]
        
        for method in required_methods:
            if hasattr(analyzer_class, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 程序导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_comparison():
    """测试功能对比"""
    print("=" * 60)
    print("功能对比分析")
    print("=" * 60)
    
    # 原程序功能
    original_features = {
        "资金曲线gui.py": [
            "总体资金分析",
            "百分比变化图",
            "文件选择",
            "图表导出",
            "工具栏支持"
        ],
        "模拟盘资金曲线_分帐户.py": [
            "分账户分析",
            "账户选择",
            "详细统计信息",
            "中文字体支持",
            "GUI界面"
        ]
    }
    
    # 新程序功能
    new_features = [
        "总体资金分析",
        "分账户分析", 
        "双模式切换",
        "百分比变化图",
        "详细统计信息",
        "中文字体支持",
        "文件选择和重载",
        "图表导出",
        "工具栏支持",
        "状态栏显示",
        "错误处理",
        "自动数据处理"
    ]
    
    print("原程序功能:")
    for program, features in original_features.items():
        print(f"\n{program}:")
        for feature in features:
            print(f"  • {feature}")
    
    print(f"\n新程序功能 (模拟盘资金曲线分析器.py):")
    for feature in new_features:
        print(f"  • {feature}")
    
    # 功能覆盖分析
    all_original_features = []
    for features in original_features.values():
        all_original_features.extend(features)
    
    covered_features = 0
    for feature in all_original_features:
        if any(new_feat in feature or feature in new_feat for new_feat in new_features):
            covered_features += 1
    
    coverage_rate = covered_features / len(all_original_features) * 100
    print(f"\n功能覆盖率: {coverage_rate:.1f}% ({covered_features}/{len(all_original_features)})")
    
    # 新增功能
    truly_new_features = [
        "双模式切换",
        "自动数据处理", 
        "状态栏显示",
        "文件重载"
    ]
    
    print(f"\n新增功能:")
    for feature in truly_new_features:
        print(f"  + {feature}")
    
    return True

def show_usage_guide():
    """显示使用指南"""
    print("=" * 60)
    print("模拟盘资金曲线综合分析器使用指南")
    print("=" * 60)
    
    guide = [
        {
            "步骤": "1. 启动程序",
            "操作": "python 模拟盘资金曲线分析器.py",
            "说明": "启动综合分析器界面"
        },
        {
            "步骤": "2. 选择数据文件",
            "操作": "点击'选择文件'按钮",
            "说明": "选择simulatedaysummary_new.json文件"
        },
        {
            "步骤": "3. 选择分析模式",
            "操作": "在'分析模式'下拉框中选择",
            "说明": "'总体分析'或'分账户分析'"
        },
        {
            "步骤": "4. 执行分析",
            "操作": "点击'分析数据'按钮",
            "说明": "生成相应的资金曲线图表"
        },
        {
            "步骤": "5. 查看结果",
            "操作": "观察图表和统计信息",
            "说明": "上图显示资金曲线，下图显示收益率"
        },
        {
            "步骤": "6. 导出图表",
            "操作": "点击'导出图表'按钮",
            "说明": "保存为PNG、JPG、PDF或SVG格式"
        }
    ]
    
    for item in guide:
        print(f"\n{item['步骤']}")
        print(f"  操作: {item['操作']}")
        print(f"  说明: {item['说明']}")
    
    print(f"\n特色功能:")
    print(f"  • 双模式分析: 既可以看总体趋势，也可以分析单个账户")
    print(f"  • 智能字体: 自动检测和设置中文字体")
    print(f"  • 详细统计: 显示起始资金、最终资金、收益率等")
    print(f"  • 专业图表: 支持缩放、平移等交互操作")
    print(f"  • 多格式导出: 支持多种图片和矢量格式")

def main():
    """主测试函数"""
    print("模拟盘资金曲线综合分析器测试")
    print("=" * 80)
    
    tests = [
        ("数据加载功能", test_data_loading),
        ("程序导入", test_program_import),
        ("功能对比分析", test_feature_comparison),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示使用指南
    show_usage_guide()
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:15}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 模拟盘资金曲线综合分析器创建成功！")
        print("\n合并功能:")
        print("✓ 总体分析 (来自资金曲线gui.py)")
        print("✓ 分账户分析 (来自模拟盘资金曲线_分帐户.py)")
        print("✓ 中文字体支持")
        print("✓ 双模式切换")
        print("✓ 专业图表界面")
        print("✓ 多格式导出")
        print("\n现在可以运行:")
        print("python 模拟盘资金曲线分析器.py")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
