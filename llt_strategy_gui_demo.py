import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTk
import tkinter as tk
from tkinter import ttk, messagebox
import numpy as np
from datetime import datetime, timedelta
import matplotlib.dates as mdates

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 从原文件导入LLT计算函数
def cal_LLT(price: pd.Series, alpha: float):
    """
    Calculates the Low-Latency Trendline (LLT).
    """
    LLT = []
    price_value = price.values
    if len(price_value) < 2:
        return [float('nan')] * len(price_value)
    LLT.append(price_value[0])
    LLT.append(price_value[1])
    for i, e in enumerate(price_value):
        if i > 1:
            v = (alpha - alpha ** 2 / 4) * e + (alpha ** 2 / 2) * price_value[i - 1] - \
                (alpha - 3 * (alpha ** 2) / 4) * price_value[i - 2] + 2 * \
                (1 - alpha) * LLT[i - 1] - (1 - alpha) ** 2 * LLT[i - 2]
            LLT.append(v)
    return LLT

def generate_sample_data(length=1000):
    """生成模拟的K线数据"""
    np.random.seed(42)  # 固定随机种子，确保结果可重现
    
    # 生成时间序列
    start_time = datetime.now() - timedelta(days=length//48)  # 假设每30分钟一根K线
    times = [start_time + timedelta(minutes=30*i) for i in range(length)]
    
    # 生成价格数据（随机游走 + 趋势）
    base_price = 3000
    prices = [base_price]
    
    for i in range(1, length):
        # 添加一些趋势和随机波动
        trend = 0.001 * np.sin(i * 0.01)  # 长期趋势
        noise = np.random.normal(0, 2)    # 随机噪声
        price_change = trend + noise
        new_price = prices[-1] + price_change
        prices.append(max(new_price, 100))  # 确保价格不会太低
    
    # 创建DataFrame
    data = pd.DataFrame({
        'datetime': times,
        'close': prices
    })
    
    return data

def calculate_historical_signals_and_pnl(klines_data, alpha):
    """
    计算历史信号和累计盈亏
    """
    close_prices = pd.Series(klines_data.close)
    llt_series = cal_LLT(close_prices, alpha)
    
    # 创建结果DataFrame
    results = pd.DataFrame({
        'datetime': klines_data.datetime,
        'close': klines_data.close,
        'llt': llt_series
    })
    
    # 计算信号
    signals = []
    for i in range(len(llt_series)):
        if i == 0:
            signals.append(0)
        else:
            if llt_series[i] > llt_series[i-1]:
                signals.append(1)  # 买入信号
            elif llt_series[i] < llt_series[i-1]:
                signals.append(-1)  # 卖出信号
            else:
                signals.append(0)  # 无信号
    
    results['signal'] = signals
    
    # 计算持仓和盈亏
    position = 0  # 0: 空仓, 1: 多头, -1: 空头
    entry_price = 0
    trades = []
    cumulative_pnl = 0
    pnl_series = []
    
    for i in range(len(results)):
        current_price = results.iloc[i]['close']
        current_signal = results.iloc[i]['signal']
        current_time = results.iloc[i]['datetime']
        
        # 处理交易信号
        if current_signal == 1 and position != 1:  # 买入信号
            # 如果有空头持仓，先平仓
            if position == -1:
                pnl = entry_price - current_price
                cumulative_pnl += pnl
                trades.append({
                    'exit_time': current_time,
                    'exit_price': current_price,
                    'position_type': 'SHORT',
                    'pnl': pnl,
                    'cumulative_pnl': cumulative_pnl
                })
            
            # 开多头
            position = 1
            entry_price = current_price
            
        elif current_signal == -1 and position != -1:  # 卖出信号
            # 如果有多头持仓，先平仓
            if position == 1:
                pnl = current_price - entry_price
                cumulative_pnl += pnl
                trades.append({
                    'exit_time': current_time,
                    'exit_price': current_price,
                    'position_type': 'LONG',
                    'pnl': pnl,
                    'cumulative_pnl': cumulative_pnl
                })
            
            # 开空头
            position = -1
            entry_price = current_price
        
        # 计算当前浮动盈亏
        if position == 1:  # 多头
            floating_pnl = current_price - entry_price
        elif position == -1:  # 空头
            floating_pnl = entry_price - current_price
        else:  # 空仓
            floating_pnl = 0
        
        pnl_series.append(cumulative_pnl + floating_pnl)
    
    results['cumulative_pnl'] = pnl_series
    
    return results, trades

class LLTStrategyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("LLT策略历史资金曲线分析 (演示版)")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.klines_data = None
        self.historical_results = None
        self.historical_trades = None
        
        # 创建界面
        self.create_widgets()
        
        # 自动加载演示数据
        self.load_demo_data()
        
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 参数设置框架
        param_frame = ttk.LabelFrame(main_frame, text="参数设置", padding=10)
        param_frame.pack(fill=tk.X, pady=(0, 10))
        
        # D值
        ttk.Label(param_frame, text="D值:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.d_value_var = tk.StringVar(value="60")
        d_value_entry = ttk.Entry(param_frame, textvariable=self.d_value_var, width=10)
        d_value_entry.grid(row=0, column=1, padx=(0, 20))
        
        # 数据长度
        ttk.Label(param_frame, text="数据长度:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.data_length_var = tk.StringVar(value="1000")
        data_length_entry = ttk.Entry(param_frame, textvariable=self.data_length_var, width=10)
        data_length_entry.grid(row=0, column=3, padx=(0, 20))
        
        # 按钮
        self.load_button = ttk.Button(param_frame, text="重新生成数据", command=self.load_demo_data)
        self.load_button.grid(row=0, column=4, padx=(0, 10))
        
        self.analyze_button = ttk.Button(param_frame, text="分析策略", command=self.analyze_strategy)
        self.analyze_button.grid(row=0, column=5, padx=(0, 10))
        
        # 状态标签
        self.status_var = tk.StringVar(value="演示模式 - 使用模拟数据")
        status_label = ttk.Label(param_frame, textvariable=self.status_var, foreground="blue")
        status_label.grid(row=1, column=0, columnspan=6, sticky=tk.W, pady=(10, 0))
        
        # 创建Notebook用于多个图表
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 资金曲线标签页
        self.pnl_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.pnl_frame, text="资金曲线")
        
        # 价格和信号标签页
        self.price_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.price_frame, text="价格与信号")
        
        # 统计信息标签页
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="统计信息")
        
        # 创建matplotlib图表
        self.create_charts()
        
    def create_charts(self):
        # 资金曲线图
        self.pnl_fig, self.pnl_ax = plt.subplots(figsize=(12, 6))
        self.pnl_canvas = FigureCanvasTk(self.pnl_fig, self.pnl_frame)
        self.pnl_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 价格和信号图
        self.price_fig, (self.price_ax, self.signal_ax) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
        self.price_canvas = FigureCanvasTk(self.price_fig, self.price_frame)
        self.price_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 统计信息文本框
        self.stats_text = tk.Text(self.stats_frame, wrap=tk.WORD, font=('Courier', 10))
        stats_scrollbar = ttk.Scrollbar(self.stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def load_demo_data(self):
        """加载演示数据"""
        try:
            self.status_var.set("正在生成模拟数据...")
            self.root.update()
            
            data_length = int(self.data_length_var.get())
            self.klines_data = generate_sample_data(data_length)
            
            self.status_var.set(f"模拟数据生成完成，共{len(self.klines_data)}根K线")
            
            # 自动分析
            self.analyze_strategy()
            
        except Exception as e:
            messagebox.showerror("错误", f"数据生成失败: {str(e)}")
            self.status_var.set("数据生成失败")
        
    def analyze_strategy(self):
        """分析策略并绘制图表"""
        if self.klines_data is None:
            messagebox.showwarning("警告", "请先生成数据")
            return
            
        try:
            self.status_var.set("正在分析策略...")
            self.root.update()
            
            # 计算ALPHA
            d_value = int(self.d_value_var.get())
            alpha = 2 / (d_value + 1)
            
            # 计算历史信号和盈亏
            self.historical_results, self.historical_trades = calculate_historical_signals_and_pnl(
                self.klines_data, alpha
            )
            
            # 绘制图表
            self.plot_pnl_curve()
            self.plot_price_and_signals()
            self.update_statistics()
            
            self.status_var.set("分析完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"策略分析失败: {str(e)}")
            self.status_var.set("分析失败")
    
    def plot_pnl_curve(self):
        """绘制资金曲线"""
        self.pnl_ax.clear()
        
        if self.historical_results is None:
            return
            
        # 转换时间
        times = pd.to_datetime(self.historical_results['datetime'])
        pnl_values = self.historical_results['cumulative_pnl']
        
        # 绘制资金曲线
        self.pnl_ax.plot(times, pnl_values, 'b-', linewidth=2, label='累计盈亏')
        self.pnl_ax.axhline(y=0, color='r', linestyle='--', alpha=0.7)
        
        # 标记交易点
        if self.historical_trades:
            trade_times = [pd.to_datetime(trade['exit_time']) for trade in self.historical_trades]
            trade_pnls = [trade['cumulative_pnl'] for trade in self.historical_trades]
            
            # 盈利交易用绿色，亏损交易用红色
            for i, trade in enumerate(self.historical_trades):
                color = 'green' if trade['pnl'] > 0 else 'red'
                self.pnl_ax.scatter(trade_times[i], trade_pnls[i], 
                                  color=color, s=30, alpha=0.7, zorder=5)
        
        self.pnl_ax.set_title('历史资金曲线', fontsize=14, fontweight='bold')
        self.pnl_ax.set_xlabel('时间')
        self.pnl_ax.set_ylabel('累计盈亏 (点)')
        self.pnl_ax.grid(True, alpha=0.3)
        self.pnl_ax.legend()
        
        # 格式化x轴时间显示
        self.pnl_ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.setp(self.pnl_ax.xaxis.get_majorticklabels(), rotation=45)
        
        self.pnl_fig.tight_layout()
        self.pnl_canvas.draw()
    
    def plot_price_and_signals(self):
        """绘制价格和信号图"""
        self.price_ax.clear()
        self.signal_ax.clear()
        
        if self.historical_results is None:
            return
            
        times = pd.to_datetime(self.historical_results['datetime'])
        prices = self.historical_results['close']
        llt_values = self.historical_results['llt']
        signals = self.historical_results['signal']
        
        # 绘制价格和LLT
        self.price_ax.plot(times, prices, 'k-', linewidth=1, label='收盘价', alpha=0.7)
        self.price_ax.plot(times, llt_values, 'b-', linewidth=2, label='LLT')
        
        # 标记买卖信号
        buy_signals = times[signals == 1]
        buy_prices = prices[signals == 1]
        sell_signals = times[signals == -1]
        sell_prices = prices[signals == -1]
        
        if len(buy_signals) > 0:
            self.price_ax.scatter(buy_signals, buy_prices, color='red', marker='^', 
                                s=50, label='买入信号', zorder=5)
        if len(sell_signals) > 0:
            self.price_ax.scatter(sell_signals, sell_prices, color='green', marker='v', 
                                s=50, label='卖出信号', zorder=5)
        
        self.price_ax.set_title('价格与LLT指标', fontsize=12, fontweight='bold')
        self.price_ax.set_ylabel('价格')
        self.price_ax.legend()
        self.price_ax.grid(True, alpha=0.3)
        
        # 绘制信号图
        self.signal_ax.plot(times, signals, 'r-', linewidth=1, alpha=0.7)
        self.signal_ax.fill_between(times, 0, signals, alpha=0.3)
        self.signal_ax.set_title('交易信号', fontsize=12, fontweight='bold')
        self.signal_ax.set_xlabel('时间')
        self.signal_ax.set_ylabel('信号')
        self.signal_ax.set_ylim(-1.5, 1.5)
        self.signal_ax.grid(True, alpha=0.3)
        
        # 格式化x轴
        self.signal_ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.setp(self.signal_ax.xaxis.get_majorticklabels(), rotation=45)
        
        self.price_fig.tight_layout()
        self.price_canvas.draw()
    
    def update_statistics(self):
        """更新统计信息"""
        self.stats_text.delete(1.0, tk.END)
        
        if not self.historical_trades:
            self.stats_text.insert(tk.END, "暂无交易数据")
            return
            
        # 计算统计信息
        total_trades = len(self.historical_trades)
        winning_trades = [t for t in self.historical_trades if t['pnl'] > 0]
        losing_trades = [t for t in self.historical_trades if t['pnl'] < 0]
        
        total_pnl = self.historical_trades[-1]['cumulative_pnl']
        win_rate = len(winning_trades) / total_trades * 100 if total_trades > 0 else 0
        
        avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(t['pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0
        
        max_win = max([t['pnl'] for t in winning_trades]) if winning_trades else 0
        max_loss = min([t['pnl'] for t in losing_trades]) if losing_trades else 0
        
        # 计算最大回撤
        pnl_series = self.historical_results['cumulative_pnl']
        running_max = pnl_series.expanding().max()
        drawdown = pnl_series - running_max
        max_drawdown = drawdown.min()
        
        # 数据期间
        start_time = pd.to_datetime(self.historical_results['datetime'].iloc[0])
        end_time = pd.to_datetime(self.historical_results['datetime'].iloc[-1])
        
        # 格式化统计信息
        profit_loss_ratio = abs(avg_win/avg_loss) if avg_loss != 0 else 'N/A'
        return_drawdown_ratio = abs(total_pnl/max_drawdown) if max_drawdown != 0 else 'N/A'
        
        stats_text = f"""
=== LLT策略回测统计报告 (演示数据) ===

基本信息:
  数据类型: 模拟数据
  D值: {self.d_value_var.get()}
  Alpha: {2/(int(self.d_value_var.get())+1):.6f}
  数据期间: {start_time.strftime('%Y-%m-%d %H:%M')} 至 {end_time.strftime('%Y-%m-%d %H:%M')}
  总K线数: {len(self.historical_results)}

交易统计:
  总交易次数: {total_trades}
  盈利交易: {len(winning_trades)} 次
  亏损交易: {len(losing_trades)} 次
  胜率: {win_rate:.1f}%

盈亏统计:
  累计盈亏: {total_pnl:.2f} 点
  平均盈利: {avg_win:.2f} 点
  平均亏损: {avg_loss:.2f} 点
  最大单笔盈利: {max_win:.2f} 点
  最大单笔亏损: {max_loss:.2f} 点
  最大回撤: {max_drawdown:.2f} 点

风险指标:
  盈亏比: {profit_loss_ratio}
  收益回撤比: {return_drawdown_ratio}

=== 最近10笔交易明细 ===
"""
        
        # 添加最近交易明细
        recent_trades = self.historical_trades[-10:] if len(self.historical_trades) >= 10 else self.historical_trades
        for i, trade in enumerate(recent_trades, 1):
            trade_type = "多头" if trade['position_type'] == 'LONG' else "空头"
            trade_time = pd.to_datetime(trade['exit_time']).strftime('%m-%d %H:%M')
            stats_text += f"  {i:2d}. {trade_time} | {trade_type} | "
            stats_text += f"盈亏: {trade['pnl']:+6.2f} | 累计: {trade['cumulative_pnl']:7.2f}\n"
        
        self.stats_text.insert(tk.END, stats_text)

def main():
    root = tk.Tk()
    app = LLTStrategyGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()