from tqsdk import TqApi, TqAuth,TqAccount

# 创建API实例
# api = TqApi(auth=TqAuth("您的账户", "您的密码"))
from accounts_zjy import cyzjy as acct

api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)


# 获取合约的报价信息
quote = api.get_quote("SHFE.cu2112")

# 获取合约的每手手续费
commission = api.get_commission("SHFE.cu2112")

print(f"合约 SHFE.cu2112 的每手手续费为: {commission}")

# 关闭API
api.close()


#
# try:
#     # 获取所有期货合约
#     # future_symbols = api.query_quotes(product="FUTURE")
#     # future_symbols = api.query_quotes()
#     future_symbols = api.query_quotes(ins_class="FUTURE", product_id="OI")
#
#     # 遍历合约列表，获取每个合约的手续费信息
#     for symbol in future_symbols:
#         quote = api.get_quote(symbol)
#         commission_rate = quote.commission_rate
#         print(f"合约 {symbol} 的手续费率：{commission_rate}")
#
# finally:
#     # 关闭api
#     api.close()
