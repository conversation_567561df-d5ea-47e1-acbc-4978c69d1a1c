#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
混沌交易策略
使用混沌理论、李雅普诺夫指数和相空间重构分析市场
基于天勤量化TQSDK开发
"""

from tqsdk import TqApi, TqAuth
import numpy as np
import pandas as pd
from scipy.spatial.distance import pdist, squareform
import matplotlib.pyplot as plt
import time
import datetime
import json
import warnings

warnings.filterwarnings('ignore')


class ChaosTradeStrategy:
    def __init__(self, symbol, auth_info=None,
                 embedding_dimension=3, time_delay=2,
                 lyapunov_threshold=0.2, recurrence_threshold=0.1,
                 lookback_period=200, trade_period=100,
                 max_pos=3, risk_per_trade=0.01, stop_loss_pct=0.03,
                 max_daily_loss=0.05, time_stop_minutes=240):
        """
        初始化混沌交易策略

        参数:
        symbol (str): 交易合约代码，例如 "SHFE.au2106"
        auth_info (tuple): 天勤账户认证信息 (username, password)
        embedding_dimension (int): 相空间嵌入维度
        time_delay (int): 相空间时间延迟
        lyapunov_threshold (float): 李雅普诺夫指数阈值
        recurrence_threshold (float): 复现图阈值
        lookback_period (int): 回溯周期
        trade_period (int): 交易信号计算周期
        max_pos (int): 最大持仓数量
        risk_per_trade (float): 每笔交易风险占账户比例
        stop_loss_pct (float): 单笔交易止损百分比
        max_daily_loss (float): 每日最大亏损百分比
        time_stop_minutes (int): 持仓时间限制（分钟）
        """
        # 策略参数
        self.symbol = symbol
        self.embedding_dimension = embedding_dimension
        self.time_delay = time_delay
        self.lyapunov_threshold = lyapunov_threshold
        self.recurrence_threshold = recurrence_threshold
        self.lookback_period = lookback_period
        self.trade_period = trade_period

        # 风险与仓位管理参数
        self.max_pos = max_pos
        self.risk_per_trade = risk_per_trade
        self.stop_loss_pct = stop_loss_pct
        self.max_daily_loss = max_daily_loss
        self.time_stop_minutes = time_stop_minutes

        # 初始化API
        if auth_info:
            self.api = TqApi(auth=TqAuth(*auth_info))
        else:
            self.api = TqApi()

        # 获取合约信息
        self.quote = self.api.get_quote(self.symbol)

        # 设置K线数据，使用5分钟K线
        self.klines = self.api.get_kline_serial(self.symbol, 5 * 60, self.lookback_period * 2)

        # 交易状态
        self.position = 0  # 当前持仓状态：正数表示多头手数，负数表示空头手数，0无持仓
        self.entry_price = 0  # 入场价格
        self.entry_time = None  # 入场时间
        self.daily_pnl = 0  # 每日盈亏
        self.daily_trades = []  # 每日交易记录
        self.start_equity = self.get_account_equity()  # 初始账户权益
        self.orders = {}  # 记录订单信息

        # 记录日志
        self.log_info(f"混沌交易策略初始化完成，交易品种：{self.symbol}")
        self.log_info(f"嵌入维度：{self.embedding_dimension}, 时间延迟：{self.time_delay}")

    def get_account_equity(self):
        """获取账户权益"""
        account = self.api.get_account()
        return account.balance

    def get_current_position(self):
        """获取当前合约的持仓"""
        position = self.api.get_position(self.symbol)
        long_pos = position.pos_long
        short_pos = position.pos_short
        net_pos = long_pos - short_pos
        return net_pos

    def calculate_position_size(self, price, volatility):
        """
        计算仓位大小
        基于账户风险比例和市场波动率计算合适的仓位大小

        参数:
        price (float): 当前价格
        volatility (float): 价格波动率

        返回:
        int: 计算出的仓位大小（手数）
        """
        account = self.api.get_account()
        equity = account.balance

        # 计算每手价值和风险价值
        contract_value = self.quote.volume_multiple * price
        risk_amount = equity * self.risk_per_trade

        # 基于波动率调整风险系数
        volatility_factor = min(max(volatility, 0.01), 0.05)  # 限制波动率范围
        adjusted_risk = self.stop_loss_pct * (1 + volatility_factor * 5)

        # 计算仓位大小
        position_size = risk_amount / (adjusted_risk * contract_value)
        position_size = min(int(position_size), self.max_pos)
        position_size = max(position_size, 1)  # 至少开1手

        return position_size

    def place_order(self, direction, volume, price_type="BEST", price=None):
        """
        下单函数

        参数:
        direction (str): 买卖方向，"BUY"表示买入，"SELL"表示卖出
        volume (int): 下单手数
        price_type (str): 价格类型，"LIMIT"表示限价单，"BEST"表示市价单
        price (float): 限价单价格，市价单可忽略

        返回:
        str: 订单ID
        """
        if price_type == "BEST":
            order = self.api.insert_order(symbol=self.symbol, direction=direction, offset="OPEN", volume=volume, limit_price=price)
        else:
            order = self.api.insert_order(symbol=self.symbol, direction=direction, offset="OPEN", volume=volume, limit_price=price)

        self.orders[order.order_id] = {
            "direction": direction,
            "volume": volume,
            "status": "SUBMITTED"
        }

        self.log_info(f"下单：{direction} {volume}手 {self.symbol}")
        return order.order_id

    def close_position(self, direction, volume, price_type="BEST", price=None):
        """
        平仓函数

        参数:
        direction (str): 平仓方向，"BUY"表示买入平空，"SELL"表示卖出平多
        volume (int): 平仓手数
        price_type (str): 价格类型，"LIMIT"表示限价单，"BEST"表示市价单
        price (float): 限价单价格，市价单可忽略

        返回:
        str: 订单ID
        """
        if price_type == "BEST":
            order = self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE", volume=volume, limit_price=price)
        else:
            order = self.api.insert_order(symbol=self.symbol, direction=direction, offset="CLOSE", volume=volume, limit_price=price)

        self.orders[order.order_id] = {
            "direction": direction,
            "volume": volume,
            "status": "SUBMITTED",
            "is_close": True
        }

        self.log_info(f"平仓：{direction} {volume}手 {self.symbol}")
        return order.order_id

    def cancel_all_orders(self):
        """取消所有未完成订单"""
        for order_id, order_info in list(self.orders.items()):
            if order_info["status"] in ["SUBMITTED", "ACCEPTED"]:
                self.api.cancel_order(order_id)
                self.log_info(f"取消订单: {order_id}")

    def phase_space_reconstruction(self, time_series, dim, tau):
        """
        相空间重构

        参数:
        time_series (array): 时间序列数据
        dim (int): 嵌入维度
        tau (int): 时间延迟

        返回:
        array: 重构的相空间
        """
        N = len(time_series)
        if tau * (dim - 1) >= N:
            self.log_info("警告：参数设置导致相空间重构数据点不足")
            return np.array([])

        Y = np.zeros((N - (dim - 1) * tau, dim))

        for i in range(dim):
            Y[:, i] = time_series[i * tau:N - (dim - 1) * tau + i * tau]

        return Y

    def calculate_lyapunov_exponent(self, phase_space, k=5, max_iter=20):
        """
        计算最大李雅普诺夫指数

        参数:
        phase_space (array): 重构的相空间
        k (int): k近邻数
        max_iter (int): 最大迭代次数

        返回:
        float: 估计的最大李雅普诺夫指数
        """
        if len(phase_space) < k + 2:
            return 0

        # 计算距离矩阵
        D = squareform(pdist(phase_space, 'euclidean'))

        # 设置对角线为无穷大，避免自身作为近邻
        np.fill_diagonal(D, np.inf)

        n = len(phase_space)
        gamma = np.zeros(max_iter)

        for i in range(n - max_iter):
            # 找到k个最近邻
            idx = np.argsort(D[i])[:k + 1]
            neighbors = idx[1:k + 1]  # 排除自身

            # 计算初始距离
            d0 = np.mean(D[i, neighbors])
            if d0 == 0:
                continue

            # 计算未来点的距离增长
            for j in range(1, max_iter + 1):
                if i + j < n and all(n + j < n for n in neighbors):
                    # 计算当前距离
                    dj = np.mean([D[i + j, n + j] for n in neighbors if n + j < n])
                    if dj > 0 and j - 1 < len(gamma):
                        gamma[j - 1] += np.log(dj / d0)

        # 计算平均增长率
        valid_gammas = [g for g in gamma if g != 0]
        if not valid_gammas:
            return 0

        lyapunov = np.mean(valid_gammas) / max_iter
        return lyapunov

    def calculate_recurrence_plot(self, phase_space, threshold):
        """
        计算复现图

        参数:
        phase_space (array): 重构的相空间
        threshold (float): 阈值

        返回:
        array: 复现矩阵
        """
        if len(phase_space) == 0:
            return np.array([])

        D = squareform(pdist(phase_space, 'euclidean'))

        # 正规化距离矩阵
        if np.max(D) > 0:
            D = D / np.max(D)

        # 计算复现矩阵
        R = D < threshold
        return R

    def calculate_determinism(self, R, min_length=2):
        """
        计算确定性指标

        参数:
        R (array): 复现矩阵
        min_length (int): 最小对角线长度

        返回:
        float: 确定性指标
        """
        if len(R) == 0:
            return 0

        N = len(R)

        # 计算对角线长度分布
        diag_lengths = []
        for i in range(-(N - min_length), N - min_length + 1):
            diag = np.diag(R, i)
            if len(diag) >= min_length:
                # 计算连续对角线片段
                segments = []
                current_segment = 0

                for point in diag:
                    if point:
                        current_segment += 1
                    else:
                        if current_segment >= min_length:
                            segments.append(current_segment)
                        current_segment = 0

                # 处理最后一个片段
                if current_segment >= min_length:
                    segments.append(current_segment)

                diag_lengths.extend(segments)

        # 计算确定性指标
        if len(diag_lengths) == 0 or np.sum(R) == 0:
            return 0

        DET = sum(diag_lengths) / np.sum(R)
        return DET

    def calculate_laminarity(self, R, min_length=2):
        """
        计算层理性指标

        参数:
        R (array): 复现矩阵
        min_length (int): 最小垂直线长度

        返回:
        float: 层理性指标
        """
        if len(R) == 0:
            return 0

        N = len(R)

        # 计算垂直线长度分布
        vert_lengths = []
        for j in range(N):
            col = R[:, j]
            segments = []
            current_segment = 0

            for point in col:
                if point:
                    current_segment += 1
                else:
                    if current_segment >= min_length:
                        segments.append(current_segment)
                    current_segment = 0

            # 处理最后一个片段
            if current_segment >= min_length:
                segments.append(current_segment)

            vert_lengths.extend(segments)

        # 计算层理性指标
        if len(vert_lengths) == 0 or np.sum(R) == 0:
            return 0

        LAM = sum(vert_lengths) / np.sum(R)
        return LAM

    def calculate_volatility(self, prices, window=20):
        """
        计算价格波动率

        参数:
        prices (array): 价格序列
        window (int): 窗口大小

        返回:
        float: 波动率
        """
        if len(prices) < window:
            return 0.01

        returns = np.diff(prices) / prices[:-1]
        volatility = np.std(returns[-window:])
        return volatility

    def calculate_chaos_indicators(self, close_prices):
        """
        计算混沌指标

        参数:
        close_prices (array): 收盘价序列

        返回:
        dict: 混沌指标字典
        """
        # 计算对数收益率
        log_returns = np.diff(np.log(close_prices))

        # 相空间重构
        phase_space = self.phase_space_reconstruction(
            log_returns,
            self.embedding_dimension,
            self.time_delay
        )

        if len(phase_space) == 0:
            return {
                "lyapunov": 0,
                "determinism": 0,
                "laminarity": 0,
                "signal": 0
            }

        # 计算李雅普诺夫指数
        lyapunov = self.calculate_lyapunov_exponent(phase_space)

        # 计算复现图
        recurrence_matrix = self.calculate_recurrence_plot(phase_space, self.recurrence_threshold)

        # 计算确定性和层理性指标
        determinism = self.calculate_determinism(recurrence_matrix)
        laminarity = self.calculate_laminarity(recurrence_matrix)

        # 计算混沌交易信号
        # 信号 = 李雅普诺夫指数 * (确定性 - 层理性)
        signal = lyapunov * (determinism - laminarity)

        return {
            "lyapunov": lyapunov,
            "determinism": determinism,
            "laminarity": laminarity,
            "signal": signal
        }

    def check_time_stop(self):
        """检查是否触发时间止损"""
        if self.position != 0 and self.entry_time is not None:
            current_time = datetime.datetime.now()
            elapsed_minutes = (current_time - self.entry_time).total_seconds() / 60

            if elapsed_minutes >= self.time_stop_minutes:
                self.log_info(f"触发时间止损，已持仓{elapsed_minutes:.1f}分钟")
                return True
        return False

    def check_daily_risk(self):
        """检查是否超过每日最大亏损限制"""
        daily_loss_pct = self.daily_pnl / self.start_equity
        if daily_loss_pct <= -self.max_daily_loss:
            self.log_info(f"触发每日风险限制，当日亏损已达{-daily_loss_pct:.2%}")
            return True
        return False

    def check_stop_loss(self, current_price):
        """检查是否触发止损"""
        if self.position == 0 or self.entry_price == 0:
            return False

        if self.position > 0:  # 多头
            loss_pct = (current_price - self.entry_price) / self.entry_price
            if loss_pct <= -self.stop_loss_pct:
                self.log_info(f"多头触发止损，入场价：{self.entry_price}，当前价：{current_price}，亏损：{loss_pct:.2%}")
                return True
        elif self.position < 0:  # 空头
            loss_pct = (self.entry_price - current_price) / self.entry_price
            if loss_pct <= -self.stop_loss_pct:
                self.log_info(f"空头触发止损，入场价：{self.entry_price}，当前价：{current_price}，亏损：{loss_pct:.2%}")
                return True

        return False

    def update_pnl(self, exit_price):
        """更新盈亏"""
        if self.position == 0 or self.entry_price == 0:
            return

        position_size = abs(self.position)
        contract_value = self.quote.volume_multiple

        if self.position > 0:  # 多头
            trade_pnl = (exit_price - self.entry_price) * position_size * contract_value
        else:  # 空头
            trade_pnl = (self.entry_price - exit_price) * position_size * contract_value

        self.daily_pnl += trade_pnl

        # 记录交易
        trade_record = {
            "entry_time": self.entry_time.strftime("%Y-%m-%d %H:%M:%S") if self.entry_time else "",
            "exit_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "direction": "多" if self.position > 0 else "空",
            "size": position_size,
            "entry_price": self.entry_price,
            "exit_price": exit_price,
            "pnl": trade_pnl,
            "pnl_pct": (trade_pnl / (self.entry_price * position_size * contract_value)) if self.entry_price else 0
        }

        self.daily_trades.append(trade_record)
        self.log_info(f"平仓完成，{trade_record['direction']}单，盈亏：{trade_pnl:.2f}，收益率：{trade_record['pnl_pct']:.2%}")

    def log_info(self, msg):
        """记录日志信息"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] {msg}")

    def reset_daily_stats(self):
        """重置每日统计数据"""
        current_day = datetime.datetime.now().day
        if hasattr(self, 'last_reset_day') and self.last_reset_day == current_day:
            return

        self.log_info("重置每日统计数据")
        self.daily_pnl = 0
        self.daily_trades = []
        self.start_equity = self.get_account_equity()
        self.last_reset_day = current_day

    def process_order_status(self):
        """处理订单状态变化"""
        # 检查所有已提交的订单状态
        for order_id in list(self.orders.keys()):
            order = self.api.get_order(order_id)
            if order.status == "FINISHED":
                if order_id in self.orders:
                    old_status = self.orders[order_id]["status"]
                    if old_status != "FINISHED":
                        self.log_info(f"订单 {order_id} 已完成: {order.direction} {order.volume_left}/{order.volume_orign}手")
                        self.orders[order_id]["status"] = "FINISHED"

                        # 如果是开仓订单，更新持仓信息
                        if not self.orders[order_id].get("is_close", False):
                            volume = order.volume_left
                            if order.direction == "BUY":
                                # 买入开仓
                                self.position += volume
                                self.entry_price = order.trade_price
                                self.entry_time = datetime.datetime.now()
                            else:
                                # 卖出开仓
                                self.position -= volume
                                self.entry_price = order.trade_price
                                self.entry_time = datetime.datetime.now()

            elif order.status == "REJECTED":
                if order_id in self.orders:
                    old_status = self.orders[order_id]["status"]
                    if old_status != "REJECTED":
                        self.log_info(f"订单 {order_id} 被拒绝")
                        self.orders[order_id]["status"] = "REJECTED"

    def run(self):
        """运行策略"""
        self.log_info("混沌交易策略开始运行...")

        try:
            while True:
                # 重置每日统计
                self.reset_daily_stats()

                # 更新行情数据
                self.api.wait_update()

                # 处理订单状态变化
                self.process_order_status()

                # 确保有足够的K线数据
                if len(self.klines.close) <= self.lookback_period:
                    continue

                # 获取当前价格
                current_price = self.quote.last_price

                # 提取价格数据用于分析
                close_prices = self.klines.close.iloc[-self.lookback_period:]

                # 计算混沌指标
                chaos_indicators = self.calculate_chaos_indicators(close_prices)

                # 计算波动率
                volatility = self.calculate_volatility(close_prices)

                # 获取当前持仓
                current_pos = self.get_current_position()

                # 检查风险条件
                # 如果有持仓，检查是否需要止损或时间止损
                if current_pos != 0:
                    if self.check_stop_loss(current_price) or self.check_time_stop() or self.check_daily_risk():
                        # 平掉所有仓位
                        if current_pos > 0:  # 多头持仓
                            self.cancel_all_orders()  # 取消所有挂单
                            self.close_position("SELL", current_pos)  # 卖出平多
                            self.update_pnl(current_price)
                            self.position = 0
                            self.entry_price = 0
                            self.entry_time = None
                            self.log_info("触发风险控制，平多仓")
                        elif current_pos < 0:  # 空头持仓
                            self.cancel_all_orders()  # 取消所有挂单
                            self.close_position("BUY", abs(current_pos))  # 买入平空
                            self.update_pnl(current_price)
                            self.position = 0
                            self.entry_price = 0
                            self.entry_time = None
                            self.log_info("触发风险控制，平空仓")
                        continue

                # 交易信号判断
                lyapunov = chaos_indicators["lyapunov"]
                signal = chaos_indicators["signal"]

                # 计算动态阈值
                dynamic_threshold = self.lyapunov_threshold * (1 + volatility * 5)

                # 混沌交易信号判断
                if self.position == 0:  # 无持仓状态
                    # 计算动态仓位
                    pos_size = self.calculate_position_size(current_price, volatility)

                    # 当李雅普诺夫指数大于阈值且信号为正时做多
                    if lyapunov > dynamic_threshold and signal > 0:
                        self.place_order("BUY", pos_size)  # 买入开仓
                        self.log_info(f"混沌做多信号，李雅普诺夫指数: {lyapunov:.4f}, 信号值: {signal:.4f}, 开仓 {pos_size} 手")

                    # 当李雅普诺夫指数大于阈值且信号为负时做空
                    elif lyapunov > dynamic_threshold and signal < 0:
                        self.place_order("SELL", pos_size)  # 卖出开仓
                        self.log_info(f"混沌做空信号，李雅普诺夫指数: {lyapunov:.4f}, 信号值: {signal:.4f}, 开仓 {pos_size} 手")

                else:  # 已有持仓
                    # 当李雅普诺夫指数低于阈值或信号反转时平仓
                    if (lyapunov < self.lyapunov_threshold * 0.7) or \
                            (self.position > 0 and signal < 0) or \
                            (self.position < 0 and signal > 0):
                        # 平仓
                        if self.position > 0:  # 多头持仓
                            self.close_position("SELL", self.position)  # 卖出平多
                            self.log_info(f"混沌平多信号，李雅普诺夫指数: {lyapunov:.4f}, 信号值: {signal:.4f}")
                        else:  # 空头持仓
                            self.close_position("BUY", abs(self.position))  # 买入平空
                            self.log_info(f"混沌平空信号，李雅普诺夫指数: {lyapunov:.4f}, 信号值: {signal:.4f}")

                        self.update_pnl(current_price)
                        self.position = 0
                        self.entry_price = 0
                        self.entry_time = None

                # 每15分钟输出一次状态
                current_timestamp = self.klines.iloc[-1].datetime
                if current_timestamp != getattr(self, 'last_status_time', None):
                    if not hasattr(self, 'last_status_time') or \
                            (current_timestamp - self.last_status_time) >= 15 * 60 * 1000000000:  # 15分钟（纳秒）
                        status = {
                            "时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "合约": self.symbol,
                            "价格": current_price,
                            "波动率": f"{volatility:.4f}",
                            "李雅普诺夫指数": f"{lyapunov:.4f}",
                            "确定性": f"{chaos_indicators['determinism']:.4f}",
                            "层理性": f"{chaos_indicators['laminarity']:.4f}",
                            "混沌信号": f"{signal:.4f}",
                            "持仓": current_pos,
                            "持仓方向": "多" if self.position > 0 else "空" if self.position < 0