from strategies.TimeRoseMA_cross_speak import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount, TqKq
    from datetime import date
    from accounts_zjy import cyzjy as acct


    product = 'CZCE.OI305'
    symbol=product
    interval = 60*5
    bklimit = 500
    sklimit = 500
    single_volume = 5

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    api = TqApi(TqKq(), auth="wolfquant,ftp123")

    ma_cross(api, product, interval, single_volume, bklimit, sklimit,period=60)


runstrategy()
