from tqsdk import TqApi, TqAuth, TqKq
from tqsdk.ta import MA
import time

class MultiTimeframeStrategy:
    def __init__(self, api, symbol):
        self.api = api
        self.symbol = symbol
        self.timeframes = [3, 5, 15]  # 3分钟, 5分钟, 15分钟
        self.positions = {tf: {'long': 0, 'short': 0} for tf in self.timeframes}
        self.max_positions = {
            3: {'long': 1, 'short': 1},
            5: {'long': 2, 'short': 1},
            15: {'long': 1, 'short': 2}
        }  # 每个周期的多空单独限制
        self.klines = {}

        for tf in self.timeframes:
            self.klines[tf] = self.api.get_kline_serial(self.symbol, tf * 60)

    def check_crossover(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] > ma.iloc[-1] and close.iloc[-2] <= ma.iloc[-2]

    def check_crossunder(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] < ma.iloc[-1] and close.iloc[-2] >= ma.iloc[-2]

    def get_position_profit(self, position_type):
        position = self.api.get_position(self.symbol)
        if position_type == 'long':
            return position.float_profit_long
        else:
            return position.float_profit_short

    def execute_trade(self, timeframe, action, position_type):
        if action == 'open':
            if self.positions[timeframe][position_type] < self.max_positions[timeframe][position_type]:
                self.positions[timeframe][position_type] += 1
                volume = 1 if position_type == 'long' else -1
                self.api.insert_order(symbol=self.symbol, direction="BUY" if position_type == 'long' else "SELL", offset="OPEN", volume=volume)
                print(f"{timeframe}分钟周期: 开{position_type}单")
        elif action == 'close':
            if self.positions[timeframe][position_type] > 0:
                profit = self.get_position_profit(position_type)
                if profit >= 0:
                    self.positions[timeframe][position_type] -= 1
                    volume = 1 if position_type == 'long' else -1
                    self.api.insert_order(symbol=self.symbol, direction="SELL" if position_type == 'long' else "BUY", offset="CLOSE", volume=volume)
                    print(f"{timeframe}分钟周期: 平{position_type}单")
                else:
                    print(f"{timeframe}分钟周期: {position_type}单亏损,不平仓")

    def run(self, timeframe):
        kline = self.klines[timeframe]
        ma13 = kline.close.rolling(13).mean()

        if self.check_crossover(kline.close, ma13):
            self.execute_trade(timeframe, 'open', 'long')
            self.execute_trade(timeframe, 'close', 'short')
        elif self.check_crossunder(kline.close, ma13):
            self.execute_trade(timeframe, 'open', 'short')
            self.execute_trade(timeframe, 'close', 'long')

    def update(self):
        for timeframe in self.timeframes:
            self.run(timeframe)

# 主程序
# api = TqApi(auth=TqAuth("YOUR_USERNAME", "YOUR_PASSWORD"))
api = TqApi(TqKq(), auth="wolfquant,ftp123", disable_print=True, debug=None)
try:
    product_id = 'OI'
    symbol = api.query_cont_quotes(product_id=product_id)[0]
    print(f"交易的合约是: {symbol}")
    strategy = MultiTimeframeStrategy(api, symbol)

    while True:
        api.wait_update()
        strategy.update()

except KeyboardInterrupt:
    print("策略已停止")
finally:
    api.close()