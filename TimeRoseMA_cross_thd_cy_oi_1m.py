
from strategies.TimeRoseMA_cross_negtive_gap_singlevolume_NoSendSignal import ma_cross

def runstrategy():
    from tqsdk import TqApi, TqAccount
    from datetime import date
    from accounts_thd import cythd as acct


    product = 'OI'
    interval = 60
    bklimit = 2
    sklimit = 2
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    symbol = api.query_cont_quotes(product_id=product)[0]

    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


runstrategy()
