import pandas as pd
from tqsdk import Tq<PERSON><PERSON>, TqAuth, TqKq


# 1. Core LLT calculation function (unchanged)
def cal_LLT(price: pd.Series, alpha: float):
    """
    Calculates the Low-Latency Trendline (LLT).
    """
    LLT = []
    price_value = price.values
    if len(price_value) < 2:
        return [float('nan')] * len(price_value)
    LLT.append(price_value[0])
    LLT.append(price_value[1])
    for i, e in enumerate(price_value):
        if i > 1:
            v = (alpha - alpha ** 2 / 4) * e + (alpha ** 2 / 2) * price_value[i - 1] - \
                (alpha - 3 * (alpha ** 2) / 4) * price_value[i - 2] + 2 * \
                (1 - alpha) * LLT[i - 1] - (1 - alpha) ** 2 * LLT[i - 2]
            LLT.append(v)
    return LLT


# --- Live Trading Parameters ---
SYMBOL = "CZCE.OI509"
KLINE_PERIOD_SECONDS = 60  # Correct parameter name
D_VALUE = 60  # 默认值，将在优化中测试
ALPHA = 2 / (D_VALUE + 1)
VOLUME = 1
KLINE_DATA_LENGTH = 8964

# --- Initialize TqApi ---
api = TqApi(TqKq(), auth=TqAuth("walkquant", "ftp123"))

print(f"Live strategy started: Symbol={SYMBOL}, Period={KLINE_PERIOD_SECONDS}s")
print(f"Requesting {KLINE_DATA_LENGTH} K-lines of historical data...")

# Using the correct parameter name 'duration_seconds'
klines = api.get_kline_serial(SYMBOL, duration_seconds=KLINE_PERIOD_SECONDS, data_length=KLINE_DATA_LENGTH)

# 等待数据加载完成
#api.wait_update()

print(f"实际获取到的K线数量: {len(klines)}")
if len(klines) > 0:
    first_time = pd.to_datetime(klines.datetime.iloc[0])
    last_time = pd.to_datetime(klines.datetime.iloc[-1])
    print(f"数据时间范围: {first_time.strftime('%Y-%m-%d %H:%M')} 到 {last_time.strftime('%Y-%m-%d %H:%M')}")

# --- Historical Signal and P&L Calculation ---
def calculate_historical_signals_and_pnl(klines_data, alpha):
    """
    计算历史信号和累计盈亏
    """
    close_prices = pd.Series(klines_data.close)
    llt_series = cal_LLT(close_prices, alpha)
    
    # 创建结果DataFrame
    results = pd.DataFrame({
        'datetime': klines_data.datetime,
        'close': klines_data.close,
        'llt': llt_series
    })
    
    # 计算信号
    signals = []
    for i in range(len(llt_series)):
        if i == 0:
            signals.append(0)
        else:
            if llt_series[i] > llt_series[i-1]:
                signals.append(1)  # 买入信号
            elif llt_series[i] < llt_series[i-1]:
                signals.append(-1)  # 卖出信号
            else:
                signals.append(0)  # 无信号
    
    results['signal'] = signals
    
    # 计算持仓和盈亏
    position = 0  # 0: 空仓, 1: 多头, -1: 空头
    entry_price = 0
    trades = []
    cumulative_pnl = 0
    pnl_series = []
    
    for i in range(len(results)):
        current_price = results.iloc[i]['close']
        current_signal = results.iloc[i]['signal']
        current_time = pd.to_datetime(results.iloc[i]['datetime'])
        
        # 处理交易信号
        if current_signal == 1 and position != 1:  # 买入信号
            # 如果有空头持仓，先平仓
            if position == -1:
                pnl = entry_price - current_price
                cumulative_pnl += pnl
                trades.append({
                    'exit_time': current_time,
                    'exit_price': current_price,
                    'position_type': 'SHORT',
                    'pnl': pnl,
                    'cumulative_pnl': cumulative_pnl
                })
            
            # 开多头
            position = 1
            entry_price = current_price
            
        elif current_signal == -1 and position != -1:  # 卖出信号
            # 如果有多头持仓，先平仓
            if position == 1:
                pnl = current_price - entry_price
                cumulative_pnl += pnl
                trades.append({
                    'exit_time': current_time,
                    'exit_price': current_price,
                    'position_type': 'LONG',
                    'pnl': pnl,
                    'cumulative_pnl': cumulative_pnl
                })
            
            # 开空头
            position = -1
            entry_price = current_price
        
        # 计算当前浮动盈亏
        if position == 1:  # 多头
            floating_pnl = current_price - entry_price
        elif position == -1:  # 空头
            floating_pnl = entry_price - current_price
        else:  # 空仓
            floating_pnl = 0
        
        pnl_series.append(cumulative_pnl + floating_pnl)
    
    results['cumulative_pnl'] = pnl_series
    
    return results, trades

print("开始参数优化测试...\n")

# --- 参数优化功能 ---
def optimize_d_value(klines_data, d_value_range):
    """
    优化D_VALUE参数，返回收益率最高的前5个参数
    """
    optimization_results = []
    
    print("正在测试不同的D_VALUE参数...")
    print("D_VALUE | 交易次数 | 累计盈亏 | 胜率 | 收益率")
    print("-" * 50)
    
    for d_value in d_value_range:
        alpha = 2 / (d_value + 1)
        
        try:
            # 计算该参数下的回测结果
            results, trades = calculate_historical_signals_and_pnl(klines_data, alpha)
            
            if trades and len(trades) > 0:
                total_pnl = trades[-1]['cumulative_pnl']
                winning_trades = [t for t in trades if t['pnl'] > 0]
                win_rate = len(winning_trades) / len(trades) * 100
                
                # 计算收益率 (假设初始资金为10000)
                initial_capital = 10000
                return_rate = (total_pnl / initial_capital) * 100
                
                optimization_results.append({
                    'D_VALUE': d_value,
                    'ALPHA': alpha,
                    'total_trades': len(trades),
                    'total_pnl': total_pnl,
                    'win_rate': win_rate,
                    'return_rate': return_rate,
                    'winning_trades': len(winning_trades),
                    'losing_trades': len(trades) - len(winning_trades)
                })
                
                print(f"{d_value:7d} | {len(trades):8d} | {total_pnl:8.2f} | {win_rate:5.1f}% | {return_rate:6.2f}%")
            else:
                print(f"{d_value:7d} | {0:8d} | {0:8.2f} | {0:5.1f}% | {0:6.2f}%")
                
        except Exception as e:
            print(f"{d_value:7d} | 计算错误: {str(e)}")
            continue
    
    # 按收益率排序，返回前5名
    optimization_results.sort(key=lambda x: x['return_rate'], reverse=True)
    return optimization_results[:5]

# 执行参数优化
d_value_range = range(10, 101)  # 从10到100，步长为1
top_5_results = optimize_d_value(klines, d_value_range)

print("\n" + "="*60)
print("🏆 收益率前5名的D_VALUE参数:")
print("="*60)

for i, result in enumerate(top_5_results, 1):
    print(f"第{i}名:")
    print(f"  D_VALUE: {result['D_VALUE']}")
    print(f"  ALPHA: {result['ALPHA']:.6f}")
    print(f"  收益率: {result['return_rate']:.2f}%")
    print(f"  累计盈亏: {result['total_pnl']:.2f} 点")
    print(f"  交易次数: {result['total_trades']}")
    print(f"  胜率: {result['win_rate']:.1f}%")
    print(f"  盈利交易: {result['winning_trades']} 次")
    print(f"  亏损交易: {result['losing_trades']} 次")
    print("-" * 40)

# 使用最优参数进行详细回测
if top_5_results:
    best_d_value = top_5_results[0]['D_VALUE']
    best_alpha = top_5_results[0]['ALPHA']
    print(f"\n使用最优参数 D_VALUE={best_d_value} 进行详细回测...")
    
    # 更新全局参数
    D_VALUE = best_d_value
    ALPHA = best_alpha
else:
    print("\n未找到有效的优化结果，使用默认参数...")

# 保存优化结果到文件
if top_5_results:
    optimization_summary = []
    optimization_summary.append("LLT策略参数优化结果")
    optimization_summary.append("="*50)
    optimization_summary.append(f"测试合约: {SYMBOL}")
    optimization_summary.append(f"数据期间: {pd.to_datetime(klines.datetime.iloc[0]).strftime('%Y-%m-%d %H:%M')} 到 {pd.to_datetime(klines.datetime.iloc[-1]).strftime('%Y-%m-%d %H:%M')}")
    optimization_summary.append(f"K线数量: {len(klines)}")
    optimization_summary.append("")
    optimization_summary.append("收益率前5名参数:")
    optimization_summary.append("-" * 50)
    
    for i, result in enumerate(top_5_results, 1):
        optimization_summary.append(f"第{i}名: D_VALUE={result['D_VALUE']}, 收益率={result['return_rate']:.2f}%, 交易次数={result['total_trades']}, 胜率={result['win_rate']:.1f}%")
    
    # 写入文件
    with open('llt_optimization_results.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(optimization_summary))
    
    print(f"\n优化结果已保存到: llt_optimization_results.txt")

print("\n" + "="*60)

# 使用最优参数计算历史信号和盈亏
historical_results, historical_trades = calculate_historical_signals_and_pnl(klines, ALPHA)

# 打印统计信息
print("=== 最优参数历史回测统计 ===")
print(f"使用参数: D_VALUE={D_VALUE}, ALPHA={ALPHA:.6f}")
print(f"数据期间: {pd.to_datetime(klines.datetime.iloc[0]).strftime('%Y-%m-%d %H:%M')} 到 {pd.to_datetime(klines.datetime.iloc[-1]).strftime('%Y-%m-%d %H:%M')}")
print(f"总K线数量: {len(klines)}")
print(f"总交易次数: {len(historical_trades)}")

if historical_trades:
    total_pnl = historical_trades[-1]['cumulative_pnl']
    winning_trades = [t for t in historical_trades if t['pnl'] > 0]
    losing_trades = [t for t in historical_trades if t['pnl'] < 0]
    
    print(f"累计盈亏: {total_pnl:.2f} 点")
    print(f"盈利交易: {len(winning_trades)} 次")
    print(f"亏损交易: {len(losing_trades)} 次")
    
    if len(winning_trades) > 0:
        avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades)
        print(f"平均盈利: {avg_win:.2f} 点")
    
    if len(losing_trades) > 0:
        avg_loss = sum(t['pnl'] for t in losing_trades) / len(losing_trades)
        print(f"平均亏损: {avg_loss:.2f} 点")
    
    win_rate = len(winning_trades) / len(historical_trades) * 100 if historical_trades else 0
    print(f"胜率: {win_rate:.1f}%")
    
    # 打印最近5笔交易
    print("\n=== 最近5笔交易 ===")
    for trade in historical_trades[-5:]:
        print(f"{trade['exit_time'].strftime('%m-%d %H:%M')} | {trade['position_type']} | "
              f"盈亏: {trade['pnl']:+.2f} | 累计: {trade['cumulative_pnl']:.2f}")

print("\n" + "="*50)
print("开始实时交易监控...\n")

# Variable to store the entry price of the current position
entry_price = 0.0
# 实时交易累计盈亏跟踪
live_cumulative_pnl = historical_trades[-1]['cumulative_pnl'] if historical_trades else 0.0
live_trades = []
last_summary_time = pd.Timestamp.now()

# --- Live Trading Main Loop ---
while True:
    api.wait_update()

    if api.is_changing(klines.iloc[-1], "datetime"):
        # 1. Calculate signal
        close_prices = pd.Series(klines.close)
        current_price = klines.close.iloc[-1]
        llt_series = cal_LLT(close_prices, ALPHA)

        if len(llt_series) < 2:
            continue

        signal = 0
        if llt_series[-1] > llt_series[-2]:
            signal = 1
        elif llt_series[-1] < llt_series[-2]:
            signal = -1

        position = api.get_position(SYMBOL)
        dt_now = pd.to_datetime(klines.iloc[-1]["datetime"])

        # 2. Check and print floating PnL if in a position
        if entry_price != 0.0:
            pnl_points = 0
            if position.pos_long > 0:
                pnl_points = current_price - entry_price
                print(
                    f"[{dt_now.strftime('%H:%M:%S')}] LONG | Entry: {entry_price:.2f}, Current: {current_price:.2f}, PnL: {pnl_points:+.2f} pts")
            elif position.pos_short > 0:
                pnl_points = entry_price - current_price
                print(
                    f"[{dt_now.strftime('%H:%M:%S')}] SHORT | Entry: {entry_price:.2f}, Current: {current_price:.2f}, PnL: {pnl_points:+.2f} pts")

        # 3. Execute trading decisions
        if signal == 1:  # Buy signal
            if position.pos_short > 0:
                # 计算空头平仓盈亏
                if entry_price != 0.0:
                    pnl = entry_price - current_price
                    live_cumulative_pnl += pnl
                    live_trades.append({
                        'time': dt_now,
                        'type': 'SHORT_CLOSE',
                        'price': current_price,
                        'pnl': pnl,
                        'cumulative_pnl': live_cumulative_pnl
                    })
                    print(f"[{dt_now.strftime('%H:%M:%S')}] SHORT平仓 | 盈亏: {pnl:+.2f} | 累计: {live_cumulative_pnl:.2f}")
                
                print(f"[{dt_now.strftime('%H:%M:%S')}] Signal is BUY, closing short position of {position.pos_short} lots.")
                # **CORRECT WAY TO CLOSE SHORT**
                api.insert_order(symbol=SYMBOL, direction="BUY", offset="CLOSE", volume=position.pos_short)
                entry_price = 0.0
                
            if position.pos_long == 0:
                entry_price = current_price
                print(f"[{dt_now.strftime('%H:%M:%S')}] Opening LONG position at: {entry_price:.2f}")
                api.insert_order(symbol=SYMBOL, direction="BUY", offset="OPEN", volume=VOLUME)

        elif signal == -1:  # Sell signal
            if position.pos_long > 0:
                # 计算多头平仓盈亏
                if entry_price != 0.0:
                    pnl = current_price - entry_price
                    live_cumulative_pnl += pnl
                    live_trades.append({
                        'time': dt_now,
                        'type': 'LONG_CLOSE',
                        'price': current_price,
                        'pnl': pnl,
                        'cumulative_pnl': live_cumulative_pnl
                    })
                    print(f"[{dt_now.strftime('%H:%M:%S')}] LONG平仓 | 盈亏: {pnl:+.2f} | 累计: {live_cumulative_pnl:.2f}")
                
                print(f"[{dt_now.strftime('%H:%M:%S')}] Signal is SELL, closing long position of {position.pos_long} lots.")
                # **CORRECT WAY TO CLOSE LONG**
                api.insert_order(symbol=SYMBOL, direction="SELL", offset="CLOSE", volume=position.pos_long)
                entry_price = 0.0
                
            if position.pos_short == 0:
                entry_price = current_price
                print(f"[{dt_now.strftime('%H:%M:%S')}] Opening SHORT position at: {entry_price:.2f}")
                api.insert_order(symbol=SYMBOL, direction="SELL", offset="OPEN", volume=VOLUME)
        
        # 4. 定期显示累计盈亏统计 (每10分钟)
        if (pd.Timestamp.now() - last_summary_time).total_seconds() > 600:  # 10分钟
            current_floating_pnl = 0
            if entry_price != 0.0:
                if position.pos_long > 0:
                    current_floating_pnl = current_price - entry_price
                elif position.pos_short > 0:
                    current_floating_pnl = entry_price - current_price
            
            total_pnl = live_cumulative_pnl + current_floating_pnl
            print(f"\n=== 盈亏统计 [{dt_now.strftime('%H:%M:%S')}] ===")
            print(f"历史累计盈亏: {live_cumulative_pnl:.2f} 点")
            print(f"当前浮动盈亏: {current_floating_pnl:+.2f} 点")
            print(f"总盈亏: {total_pnl:.2f} 点")
            print(f"实时交易次数: {len(live_trades)}")
            print("=" * 40 + "\n")
            
            last_summary_time = pd.Timestamp.now()