"""
基于TqSDK的多时间周期双边市场策略
使用KQ.i@OI指数合约，从API获取多个周期的真实数据
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import time
from datetime import datetime, timedelta

try:
    from tqsdk import TqApi, TqKq
    from tqsdk.tafunc import ma, time_to_str
    from tradefuncs import BK, SK, BP, SP
    from loguru import logger
    TQSDK_AVAILABLE = True
except ImportError:
    print("警告: TqSDK未安装，请先安装: pip install tqsdk")
    TQSDK_AVAILABLE = False


class SignalType(Enum):
    """信号类型"""
    GOLDEN_CROSS = "金叉"
    DEATH_CROSS = "死叉"
    UPWARD_REGRESSION = "向上回归"
    DOWNWARD_REGRESSION = "向下回归"
    NONE = "无信号"


class PositionDirection(Enum):
    """持仓方向"""
    LONG = "多仓"
    SHORT = "空仓"
    NONE = "无持仓"


@dataclass
class StrategyConfig:
    """策略配置参数"""
    # 时间周期配置（秒）
    timeframes: List[int] = None
    
    # 数据获取配置
    base_data_length: int = 10000     # 1分钟数据获取数量
    
    # 均线参数
    short_ma_period: int = 5          # 短均线周期
    long_ma_period: int = 20          # 长均线周期
    
    # 止损参数
    stop_loss_periods: int = 10       # 止损计算的K线周期数
    
    # 风险控制参数
    max_position_ratio: float = 0.1   # 最大持仓比例（10%）
    max_leverage: float = 3.0         # 最大杠杆倍数
    
    # 手续费参数
    commission_rate: float = 0.0003   # 双边手续费率
    
    # 移动止损参数
    trailing_stop_enabled: bool = True  # 是否启用移动止损
    profit_threshold_ratio: float = 2.0  # 盈利阈值（手续费的倍数）
    
    def __post_init__(self):
        if self.timeframes is None:
            self.timeframes = [60, 900, 14400, 86400, 604800]  # 1m, 15m, 4h, 1d, 1w


@dataclass
class Position:
    """持仓信息"""
    direction: PositionDirection = PositionDirection.NONE
    entry_price: float = 0.0
    quantity: int = 0
    stop_loss_price: float = 0.0
    entry_time: datetime = None
    timeframe: int = 0
    profit_loss: float = 0.0
    is_trailing: bool = False


@dataclass
class MarketSignal:
    """市场信号"""
    timeframe: int
    signal_type: SignalType
    price: float
    timestamp: datetime
    ma_short: float
    ma_long: float
    ma_diff: float


class TqSDKDataManager:
    """TqSDK数据管理器"""
    
    def __init__(self, api: TqApi, symbol: str, config: StrategyConfig):
        self.api = api
        self.symbol = symbol
        self.config = config
        self.klines_cache: Dict[int, pd.DataFrame] = {}
        
        # 获取合约信息
        self.quote = self.api.get_quote(self.symbol)
        print(f"合约信息: {self.symbol}")
        print(f"合约名称: {self.quote.instrument_name}")
        print(f"当前价格: {self.quote.last_price}")
        
        # 预加载所有时间周期的数据
        self._preload_all_timeframes()
    
    def _calculate_data_length(self, timeframe: int) -> int:
        """根据时间对齐原则计算各周期需要的数据量"""
        base_timeframe = 60  # 1分钟
        base_length = self.config.base_data_length
        
        # 计算时间比例
        ratio = timeframe / base_timeframe
        
        # 计算对应的数据量，确保时间范围一致
        length = max(100, int(base_length / ratio))
        
        print(f"时间周期 {timeframe}秒 ({timeframe//60}分钟): 获取 {length} 条数据")
        return length
    
    def _preload_all_timeframes(self):
        """预加载所有时间周期的数据"""
        print("=" * 60)
        print("开始预加载多时间周期数据...")
        print("=" * 60)
        
        for timeframe in self.config.timeframes:
            try:
                length = self._calculate_data_length(timeframe)
                
                print(f"正在获取 {timeframe}秒 周期数据...")
                klines = self.api.get_kline_serial(self.symbol, timeframe, length)
                
                # 转换为DataFrame并缓存
                df = klines.copy()
                self.klines_cache[timeframe] = df
                
                # 显示数据信息
                if len(df) > 0:
                    start_time = time_to_str(df.datetime.iloc[0])
                    end_time = time_to_str(df.datetime.iloc[-1])
                    price_range = f"{df.close.min():.2f} - {df.close.max():.2f}"
                    
                    print(f"✓ {timeframe}秒周期: {len(df)} 条数据")
                    print(f"  时间范围: {start_time} 到 {end_time}")
                    print(f"  价格范围: {price_range}")
                else:
                    print(f"✗ {timeframe}秒周期: 无数据")
                
            except Exception as e:
                print(f"✗ 获取 {timeframe}秒 周期数据失败: {e}")
                # 创建空的DataFrame作为占位符
                self.klines_cache[timeframe] = pd.DataFrame()
        
        print("=" * 60)
        print("数据预加载完成")
        print("=" * 60)
    
    def get_klines(self, timeframe: int, length: int = None) -> pd.DataFrame:
        """获取指定时间周期的K线数据"""
        if timeframe not in self.klines_cache:
            print(f"警告: 时间周期 {timeframe} 未预加载")
            return pd.DataFrame()
        
        df = self.klines_cache[timeframe].copy()
        
        if length and len(df) > length:
            df = df.tail(length)
        
        return df
    
    def update_latest_data(self, timeframe: int):
        """更新最新数据（实时模式）"""
        try:
            # 获取最新的几根K线
            latest_klines = self.api.get_kline_serial(self.symbol, timeframe, 10)
            
            if len(latest_klines) > 0:
                # 更新缓存中的最新数据
                cached_df = self.klines_cache[timeframe]
                
                # 合并新数据（这里简化处理，实际应该更智能地合并）
                if len(cached_df) > 0:
                    # 保留大部分历史数据，只更新最新的几根
                    updated_df = pd.concat([cached_df[:-10], latest_klines], ignore_index=True)
                    self.klines_cache[timeframe] = updated_df.drop_duplicates(subset=['datetime']).reset_index(drop=True)
                
        except Exception as e:
            print(f"更新 {timeframe}秒 数据失败: {e}")


class MultiTimeFrameStrategy:
    """多时间周期策略主类"""
    
    def __init__(self, config: StrategyConfig, symbol: str = "<EMAIL>",
                 initial_capital: float = 100000, auth: str = None):
        if not TQSDK_AVAILABLE:
            raise ImportError("TqSDK未安装，无法运行策略")
        
        self.config = config
        self.symbol = symbol
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.auth = auth
        
        # 持仓管理
        self.positions: Dict[int, Position] = {}
        self.signals_history: List[MarketSignal] = []
        
        # 初始化各时间周期的持仓
        for tf in self.config.timeframes:
            self.positions[tf] = Position(timeframe=tf)
        
        # 初始化API和数据管理器
        self.api = None
        self.data_manager = None
        
        self._initialize_api()
    
    def _initialize_api(self):
        """初始化TqSDK API"""
        try:
            if self.auth:
                self.api = TqApi(TqKq(), auth=self.auth, disable_print=True)
            else:
                # 使用快期模拟账户
                self.api = TqApi(TqKq(), disable_print=True)
            
            print(f"TqSDK API初始化成功")
            
            # 初始化数据管理器
            self.data_manager = TqSDKDataManager(self.api, self.symbol, self.config)
            
        except Exception as e:
            print(f"TqSDK API初始化失败: {e}")
            raise
    
    def calculate_ma_signals(self, df: pd.DataFrame, timeframe: int) -> MarketSignal:
        """计算均线信号"""
        if len(df) < max(self.config.short_ma_period, self.config.long_ma_period):
            return MarketSignal(timeframe, SignalType.NONE, 0, datetime.now(), 0, 0, 0)
        
        # 计算均线
        df = df.copy()
        df['ma_short'] = df['close'].rolling(self.config.short_ma_period).mean()
        df['ma_long'] = df['close'].rolling(self.config.long_ma_period).mean()
        df['ma_diff'] = df['ma_short'] - df['ma_long']
        
        # 获取最新数据
        current = df.iloc[-1]
        previous = df.iloc[-2] if len(df) > 1 else current
        
        current_price = current['close']
        current_ma_short = current['ma_short']
        current_ma_long = current['ma_long']
        current_ma_diff = current['ma_diff']
        
        # 判断信号类型
        signal_type = SignalType.NONE
        
        # 金叉死叉信号
        if (previous['ma_short'] <= previous['ma_long'] and 
            current['ma_short'] > current['ma_long']):
            signal_type = SignalType.GOLDEN_CROSS
        elif (previous['ma_short'] >= previous['ma_long'] and 
              current['ma_short'] < current['ma_long']):
            signal_type = SignalType.DEATH_CROSS
        else:
            # 回归信号
            if len(df) >= 5:
                # 计算最近几个周期的ma_diff变化
                recent_diffs = df['ma_diff'].tail(5).values
                
                # 向上回归：从负最小值逐渐增大
                if (current_ma_diff < 0 and 
                    len(recent_diffs) >= 3 and
                    recent_diffs[-1] > recent_diffs[-2] > recent_diffs[-3]):
                    signal_type = SignalType.UPWARD_REGRESSION
                
                # 向下回归：从正最大值逐渐减小
                elif (current_ma_diff > 0 and 
                      len(recent_diffs) >= 3 and
                      recent_diffs[-1] < recent_diffs[-2] < recent_diffs[-3]):
                    signal_type = SignalType.DOWNWARD_REGRESSION
        
        # 转换时间戳
        if hasattr(current['datetime'], 'to_pydatetime'):
            timestamp = current['datetime'].to_pydatetime()
        else:
            timestamp = pd.to_datetime(current['datetime'])
        
        return MarketSignal(
            timeframe=timeframe,
            signal_type=signal_type,
            price=current_price,
            timestamp=timestamp,
            ma_short=current_ma_short,
            ma_long=current_ma_long,
            ma_diff=current_ma_diff
        )

    def get_higher_timeframe_signal(self, current_timeframe: int) -> Optional[MarketSignal]:
        """获取上一级大周期的信号"""
        current_index = self.config.timeframes.index(current_timeframe)
        if current_index >= len(self.config.timeframes) - 1:
            return None

        higher_timeframe = self.config.timeframes[current_index + 1]

        # 获取大周期数据和信号
        df = self.data_manager.get_klines(higher_timeframe)
        return self.calculate_ma_signals(df, higher_timeframe)

    def calculate_stop_loss(self, df: pd.DataFrame, direction: PositionDirection) -> float:
        """计算止损价格"""
        if len(df) < self.config.stop_loss_periods:
            periods = len(df)
        else:
            periods = self.config.stop_loss_periods

        recent_data = df.tail(periods)

        if direction == PositionDirection.LONG:
            # 多仓止损：最近N个周期的最低点
            return recent_data['low'].min()
        else:
            # 空仓止损：最近N个周期的最高点
            return recent_data['high'].max()

    def should_open_position(self, current_signal: MarketSignal, higher_signal: Optional[MarketSignal],
                           current_kline: pd.Series) -> Tuple[bool, PositionDirection]:
        """判断是否应该开仓"""
        if not higher_signal:
            return False, PositionDirection.NONE

        # 多仓条件：当前周期金叉 + 当前K线涨 + 大周期向上回归
        if (current_signal.signal_type == SignalType.GOLDEN_CROSS and
            current_kline['close'] > current_kline['open'] and
            higher_signal.signal_type == SignalType.UPWARD_REGRESSION):
            return True, PositionDirection.LONG

        # 空仓条件：当前周期死叉 + 当前K线跌 + 大周期向下回归
        elif (current_signal.signal_type == SignalType.DEATH_CROSS and
              current_kline['close'] < current_kline['open'] and
              higher_signal.signal_type == SignalType.DOWNWARD_REGRESSION):
            return True, PositionDirection.SHORT

        return False, PositionDirection.NONE

    def get_1min_entry_signal(self, target_timeframe: int, target_direction: PositionDirection) -> bool:
        """使用1分钟K线精确入场"""
        if 60 not in self.config.timeframes:
            return True  # 如果没有1分钟数据，直接返回True

        # 获取1分钟K线数据
        df_1m = self.data_manager.get_klines(60, 50)
        signal_1m = self.calculate_ma_signals(df_1m, 60)

        # 检查1分钟级别的信号是否与目标方向一致
        if target_direction == PositionDirection.LONG:
            return signal_1m.signal_type == SignalType.GOLDEN_CROSS
        elif target_direction == PositionDirection.SHORT:
            return signal_1m.signal_type == SignalType.DEATH_CROSS

        return False

    def check_position_limit(self, timeframe: int) -> bool:
        """检查是否可以开仓（风险控制）"""
        # 检查该时间周期是否已有持仓
        if self.positions[timeframe].direction != PositionDirection.NONE:
            return False

        # 检查总持仓比例
        total_position_value = sum(
            pos.quantity * pos.entry_price
            for pos in self.positions.values()
            if pos.direction != PositionDirection.NONE
        )

        position_ratio = total_position_value / self.current_capital
        if position_ratio >= self.config.max_position_ratio:
            return False

        return True

    def calculate_position_size(self, price: float) -> int:
        """计算开仓数量"""
        # 等权重分配资金
        available_capital = self.current_capital * self.config.max_position_ratio / len(self.config.timeframes)

        # 考虑杠杆
        leveraged_capital = available_capital * self.config.max_leverage

        # 计算数量（最小为1手）
        quantity = max(1, int(leveraged_capital / price))

        return quantity

    def open_position(self, timeframe: int, direction: PositionDirection,
                     price: float, stop_loss_price: float):
        """开仓"""
        if not self.check_position_limit(timeframe):
            print(f"时间周期{timeframe}秒无法开仓：已有持仓或超出风险限制")
            return False

        quantity = self.calculate_position_size(price)

        # 更新持仓信息
        position = self.positions[timeframe]
        position.direction = direction
        position.entry_price = price
        position.quantity = quantity
        position.stop_loss_price = stop_loss_price
        position.entry_time = datetime.now()
        position.is_trailing = False

        tf_name = f"{timeframe//60}分钟" if timeframe < 3600 else f"{timeframe//3600}小时" if timeframe < 86400 else f"{timeframe//86400}天"
        print(f"开仓 - {tf_name}: {direction.value}, 价格:{price:.2f}, 数量:{quantity}, 止损:{stop_loss_price:.2f}")

        return True

    def close_position(self, timeframe: int, price: float, reason: str = ""):
        """平仓"""
        position = self.positions[timeframe]
        if position.direction == PositionDirection.NONE:
            return False

        # 计算盈亏
        if position.direction == PositionDirection.LONG:
            pnl = (price - position.entry_price) * position.quantity
        else:
            pnl = (position.entry_price - price) * position.quantity

        # 扣除手续费
        commission = position.entry_price * position.quantity * self.config.commission_rate * 2
        net_pnl = pnl - commission

        # 更新资金
        self.current_capital += net_pnl

        tf_name = f"{timeframe//60}分钟" if timeframe < 3600 else f"{timeframe//3600}小时" if timeframe < 86400 else f"{timeframe//86400}天"
        print(f"平仓 - {tf_name}: {reason}, 价格:{price:.2f}, 盈亏:{net_pnl:.2f}")

        # 重置持仓信息
        position.direction = PositionDirection.NONE
        position.entry_price = 0.0
        position.quantity = 0
        position.stop_loss_price = 0.0
        position.is_trailing = False

        return True

    def update_trailing_stop(self, timeframe: int, current_price: float, ma_long: float):
        """更新移动止损"""
        position = self.positions[timeframe]
        if position.direction == PositionDirection.NONE:
            return

        # 计算当前盈亏
        if position.direction == PositionDirection.LONG:
            current_pnl = (current_price - position.entry_price) * position.quantity
        else:
            current_pnl = (position.entry_price - current_price) * position.quantity

        # 计算手续费阈值
        commission_threshold = (position.entry_price * position.quantity *
                              self.config.commission_rate * 2 *
                              self.config.profit_threshold_ratio)

        # 如果盈利超过阈值，启用移动止损
        if current_pnl > commission_threshold and not position.is_trailing:
            position.is_trailing = True
            position.stop_loss_price = current_price
            tf_name = f"{timeframe//60}分钟" if timeframe < 3600 else f"{timeframe//3600}小时" if timeframe < 86400 else f"{timeframe//86400}天"
            print(f"启用移动止损 - {tf_name}, 当前价格:{current_price:.2f}")

        # 如果已启用移动止损，更新止损点
        if position.is_trailing:
            # 计算新的止损点：当前价格与长周期均线的中间点
            new_stop_price = (current_price + ma_long) / 2

            if position.direction == PositionDirection.LONG:
                # 多仓：止损点只能向上移动
                if new_stop_price > position.stop_loss_price:
                    position.stop_loss_price = new_stop_price
            else:
                # 空仓：止损点只能向下移动
                if new_stop_price < position.stop_loss_price:
                    position.stop_loss_price = new_stop_price

    def check_stop_loss(self, timeframe: int, current_price: float) -> bool:
        """检查是否触发止损"""
        position = self.positions[timeframe]
        if position.direction == PositionDirection.NONE:
            return False

        if position.direction == PositionDirection.LONG:
            # 多仓：价格跌破止损点
            if current_price <= position.stop_loss_price:
                self.close_position(timeframe, current_price, "止损")
                return True
        else:
            # 空仓：价格涨破止损点
            if current_price >= position.stop_loss_price:
                self.close_position(timeframe, current_price, "止损")
                return True

        return False

    def process_timeframe(self, timeframe: int):
        """处理单个时间周期的策略逻辑"""
        # 获取K线数据
        df = self.data_manager.get_klines(timeframe)
        if len(df) < max(self.config.short_ma_period, self.config.long_ma_period):
            return

        # 计算当前信号
        current_signal = self.calculate_ma_signals(df, timeframe)
        current_kline = df.iloc[-1]

        # 获取大周期信号
        higher_signal = self.get_higher_timeframe_signal(timeframe)

        # 检查是否需要开仓
        should_open, direction = self.should_open_position(current_signal, higher_signal, current_kline)

        if should_open:
            # 使用1分钟K线精确入场
            if self.get_1min_entry_signal(timeframe, direction):
                # 计算止损价格
                stop_loss_price = self.calculate_stop_loss(df, direction)

                # 开仓
                self.open_position(timeframe, direction, current_signal.price, stop_loss_price)

        # 检查现有持仓
        position = self.positions[timeframe]
        if position.direction != PositionDirection.NONE:
            current_price = current_signal.price

            # 检查止损
            if self.check_stop_loss(timeframe, current_price):
                return  # 已平仓，无需继续处理

            # 更新移动止损
            if self.config.trailing_stop_enabled:
                self.update_trailing_stop(timeframe, current_price, current_signal.ma_long)

        # 记录信号历史
        self.signals_history.append(current_signal)

        # 保持历史记录在合理范围内
        if len(self.signals_history) > 1000:
            self.signals_history = self.signals_history[-500:]

    def print_status(self):
        """打印当前状态"""
        print(f"\n当前资金: {self.current_capital:.2f}")
        print(f"当前价格: {self.data_manager.quote.last_price:.2f}")

        active_positions = 0
        total_pnl = 0

        for timeframe, position in self.positions.items():
            if position.direction != PositionDirection.NONE:
                active_positions += 1

                # 计算当前盈亏
                current_price = self.data_manager.quote.last_price

                if position.direction == PositionDirection.LONG:
                    pnl = (current_price - position.entry_price) * position.quantity
                else:
                    pnl = (position.entry_price - current_price) * position.quantity

                total_pnl += pnl

                tf_name = f"{timeframe//60}分钟" if timeframe < 3600 else f"{timeframe//3600}小时" if timeframe < 86400 else f"{timeframe//86400}天"
                trailing_status = "移动" if position.is_trailing else "固定"
                print(f"  {tf_name}: {position.direction.value}, 入场:{position.entry_price:.2f}, "
                      f"当前:{current_price:.2f}, 盈亏:{pnl:.2f}, 止损:{position.stop_loss_price:.2f}({trailing_status})")

        if active_positions == 0:
            print("  当前无持仓")
        else:
            print(f"总浮动盈亏: {total_pnl:.2f}")

    def run_strategy(self, iterations: int = 100):
        """运行策略主循环"""
        print("=" * 80)
        print("启动基于TqSDK的多时间周期双边市场策略")
        print(f"交易合约: {self.symbol}")
        print(f"初始资金: {self.initial_capital}")
        print(f"时间周期: {[f'{tf//60}分钟' if tf < 3600 else f'{tf//3600}小时' if tf < 86400 else f'{tf//86400}天' for tf in self.config.timeframes]}")
        print("=" * 80)

        try:
            for iteration in range(iterations):
                print(f"\n--- 第 {iteration + 1} 次策略执行 ---")

                # 更新最新数据
                for timeframe in self.config.timeframes:
                    self.data_manager.update_latest_data(timeframe)

                # 处理每个时间周期
                for timeframe in self.config.timeframes:
                    try:
                        self.process_timeframe(timeframe)
                    except Exception as e:
                        print(f"处理时间周期 {timeframe} 秒时出错: {e}")

                # 显示当前状态
                self.print_status()

                # 等待API更新
                self.api.wait_update()

        except KeyboardInterrupt:
            print("策略被用户中断")
        except Exception as e:
            print(f"策略运行出错: {e}")
        finally:
            self.close_all_positions()
            self.print_final_report()
            if self.api:
                self.api.close()

    def close_all_positions(self):
        """平掉所有持仓"""
        print("\n平掉所有持仓...")
        current_price = self.data_manager.quote.last_price

        for timeframe in self.config.timeframes:
            position = self.positions[timeframe]
            if position.direction != PositionDirection.NONE:
                self.close_position(timeframe, current_price, "策略结束")

    def print_final_report(self):
        """打印最终报告"""
        print("\n" + "=" * 80)
        print("策略执行完成 - 最终报告")
        print("=" * 80)

        total_return = self.current_capital - self.initial_capital
        return_rate = (total_return / self.initial_capital) * 100

        print(f"初始资金: {self.initial_capital:.2f}")
        print(f"最终资金: {self.current_capital:.2f}")
        print(f"总收益: {total_return:.2f}")
        print(f"收益率: {return_rate:.2f}%")

        # 统计信号数量
        signal_counts = {}
        for signal in self.signals_history:
            tf_name = f"{signal.timeframe//60}分钟" if signal.timeframe < 3600 else f"{signal.timeframe//3600}小时" if signal.timeframe < 86400 else f"{signal.timeframe//86400}天"
            key = f"{tf_name}-{signal.signal_type.value}"
            signal_counts[key] = signal_counts.get(key, 0) + 1

        print("\n信号统计:")
        for signal_type, count in signal_counts.items():
            print(f"  {signal_type}: {count} 次")


def create_default_config() -> StrategyConfig:
    """创建默认策略配置"""
    return StrategyConfig(
        timeframes=[60, 900, 14400, 86400, 604800],  # 1m, 15m, 4h, 1d, 1w
        base_data_length=10000,  # 1分钟数据获取1万根
        short_ma_period=5,
        long_ma_period=20,
        stop_loss_periods=10,
        max_position_ratio=0.1,  # 10%
        max_leverage=3.0,
        commission_rate=0.0003,
        trailing_stop_enabled=True,
        profit_threshold_ratio=2.0
    )


def run_oi_index_strategy(auth: str = None, iterations: int = 50):
    """运行OI指数策略"""
    if not TQSDK_AVAILABLE:
        print("错误: TqSDK未安装，请先安装: pip install tqsdk")
        return

    # 创建策略配置
    config = create_default_config()

    # 创建策略实例
    strategy = MultiTimeFrameStrategy(
        config=config,
        symbol="<EMAIL>",  # OI指数合约
        initial_capital=100000,  # 10万初始资金
        auth=auth
    )

    # 运行策略
    strategy.run_strategy(iterations=iterations)


def run_custom_oi_strategy(auth: str = None, **kwargs):
    """运行自定义OI指数策略"""
    if not TQSDK_AVAILABLE:
        print("错误: TqSDK未安装，请先安装: pip install tqsdk")
        return

    # 创建配置
    config = create_default_config()

    # 应用自定义参数
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
            print(f"设置参数 {key} = {value}")

    # 创建策略
    strategy = MultiTimeFrameStrategy(
        config=config,
        symbol="<EMAIL>",
        initial_capital=kwargs.get('initial_capital', 100000),
        auth=auth
    )

    # 运行策略
    strategy.run_strategy(iterations=kwargs.get('iterations', 50))


def test_data_loading(auth: str = None):
    """测试数据加载功能"""
    if not TQSDK_AVAILABLE:
        print("错误: TqSDK未安装，请先安装: pip install tqsdk")
        return

    print("测试TqSDK数据加载功能...")

    config = create_default_config()

    try:
        # 初始化API
        if auth:
            api = TqApi(TqKq(), auth=auth, disable_print=True)
        else:
            api = TqApi(TqKq(), disable_print=True)

        # 创建数据管理器
        data_manager = TqSDKDataManager(api, "<EMAIL>", config)

        # 测试获取各个时间周期的数据
        print("\n测试数据获取:")
        for timeframe in config.timeframes:
            df = data_manager.get_klines(timeframe, 10)
            tf_name = f"{timeframe//60}分钟" if timeframe < 3600 else f"{timeframe//3600}小时" if timeframe < 86400 else f"{timeframe//86400}天"

            if len(df) > 0:
                latest_price = df.iloc[-1]['close']
                print(f"  {tf_name}: 最新价格 {latest_price:.2f}")
            else:
                print(f"  {tf_name}: 无数据")

        api.close()
        print("\n数据加载测试完成")

    except Exception as e:
        print(f"数据加载测试失败: {e}")


if __name__ == "__main__":
    import sys

    # 默认认证信息（请替换为您的认证信息）
    default_auth = "quant_ggh,Qiai1301"

    if len(sys.argv) > 1:
        command = sys.argv[1]

        if command == "test":
            # 测试数据加载
            auth = sys.argv[2] if len(sys.argv) > 2 else default_auth
            test_data_loading(auth)

        elif command == "run":
            # 运行默认策略
            auth = sys.argv[2] if len(sys.argv) > 2 else default_auth
            iterations = int(sys.argv[3]) if len(sys.argv) > 3 else 50
            run_oi_index_strategy(auth, iterations)

        elif command == "custom":
            # 运行自定义策略
            auth = sys.argv[2] if len(sys.argv) > 2 else default_auth

            # 自定义参数示例
            custom_params = {
                'short_ma_period': 3,
                'long_ma_period': 15,
                'max_position_ratio': 0.08,
                'max_leverage': 2.5,
                'stop_loss_periods': 8,
                'iterations': 30
            }

            run_custom_oi_strategy(auth, **custom_params)

        else:
            print("用法:")
            print("  python multi_timeframe_strategy_tqsdk.py test [auth]")
            print("  python multi_timeframe_strategy_tqsdk.py run [auth] [iterations]")
            print("  python multi_timeframe_strategy_tqsdk.py custom [auth]")
            print("\n说明:")
            print("  auth: TqSDK认证信息，格式为 'username,password'")
            print("  iterations: 策略运行次数，默认50次")
    else:
        # 默认运行测试
        print("默认运行数据加载测试...")
        print("如需运行完整策略，请提供认证信息:")
        print("  python multi_timeframe_strategy_tqsdk.py run 'username,password'")
        test_data_loading()
