"""
多时间周期双边市场策略配置文件
用户可以通过修改这个文件来调整策略参数
"""

from dataclasses import dataclass
from typing import List, Dict, Any
import json


@dataclass
class TradingConfig:
    """交易配置"""
    # 基本设置
    symbol: str = "rb"                    # 交易合约
    initial_capital: float = 100000       # 初始资金
    auth: str = ""                        # TqSDK认证信息
    
    # 时间周期设置（秒）
    timeframes: List[int] = None
    
    # 均线参数
    short_ma_period: int = 5              # 短均线周期
    long_ma_period: int = 20              # 长均线周期
    
    # 风险控制参数
    max_position_ratio: float = 0.1       # 最大持仓比例（10%）
    max_leverage: float = 3.0             # 最大杠杆倍数
    stop_loss_periods: int = 10           # 止损计算的K线周期数
    
    # 手续费设置
    commission_rate: float = 0.0003       # 双边手续费率（0.03%）
    
    # 移动止损设置
    trailing_stop_enabled: bool = True    # 是否启用移动止损
    profit_threshold_ratio: float = 2.0   # 盈利阈值（手续费的倍数）
    
    # 策略运行参数
    max_iterations: int = 100             # 最大运行次数
    
    def __post_init__(self):
        if self.timeframes is None:
            # 默认时间周期：1分钟，15分钟，4小时，日K线，周K线
            self.timeframes = [60, 900, 14400, 86400, 604800]


# 预设配置方案
PRESET_CONFIGS = {
    "conservative": {
        "short_ma_period": 8,
        "long_ma_period": 21,
        "max_position_ratio": 0.05,  # 5%
        "max_leverage": 2.0,
        "stop_loss_periods": 15,
        "profit_threshold_ratio": 3.0,
        "description": "保守型策略：较低风险，较长均线周期"
    },
    
    "aggressive": {
        "short_ma_period": 3,
        "long_ma_period": 12,
        "max_position_ratio": 0.15,  # 15%
        "max_leverage": 5.0,
        "stop_loss_periods": 5,
        "profit_threshold_ratio": 1.5,
        "description": "激进型策略：较高风险，较短均线周期"
    },
    
    "balanced": {
        "short_ma_period": 5,
        "long_ma_period": 20,
        "max_position_ratio": 0.1,   # 10%
        "max_leverage": 3.0,
        "stop_loss_periods": 10,
        "profit_threshold_ratio": 2.0,
        "description": "平衡型策略：中等风险，标准参数"
    },
    
    "scalping": {
        "short_ma_period": 2,
        "long_ma_period": 8,
        "max_position_ratio": 0.08,  # 8%
        "max_leverage": 4.0,
        "stop_loss_periods": 3,
        "profit_threshold_ratio": 1.2,
        "timeframes": [60, 300, 900],  # 只使用短周期
        "description": "剥头皮策略：快进快出，短周期"
    },
    
    "swing": {
        "short_ma_period": 10,
        "long_ma_period": 30,
        "max_position_ratio": 0.12,  # 12%
        "max_leverage": 2.5,
        "stop_loss_periods": 20,
        "profit_threshold_ratio": 4.0,
        "timeframes": [900, 14400, 86400, 604800],  # 只使用长周期
        "description": "波段策略：持仓时间较长，大周期"
    }
}

# 不同品种的推荐配置
SYMBOL_CONFIGS = {
    "rb": {  # 螺纹钢
        "commission_rate": 0.0001,
        "max_leverage": 3.0,
        "description": "螺纹钢：活跃品种，适中波动"
    },
    
    "ag": {  # 白银
        "commission_rate": 0.0005,
        "max_leverage": 2.5,
        "stop_loss_periods": 8,
        "description": "白银：贵金属，波动较大"
    },
    
    "cu": {  # 铜
        "commission_rate": 0.0002,
        "max_leverage": 3.5,
        "description": "铜：工业金属，趋势性强"
    },
    
    "au": {  # 黄金
        "commission_rate": 0.0002,
        "max_leverage": 2.0,
        "stop_loss_periods": 12,
        "description": "黄金：避险资产，相对稳定"
    },
    
    "ni": {  # 镍
        "commission_rate": 0.0003,
        "max_leverage": 2.5,
        "stop_loss_periods": 6,
        "description": "镍：波动较大，需要较紧止损"
    }
}


def load_config_from_preset(preset_name: str) -> TradingConfig:
    """从预设配置加载"""
    if preset_name not in PRESET_CONFIGS:
        raise ValueError(f"未知的预设配置: {preset_name}")
    
    preset = PRESET_CONFIGS[preset_name]
    config = TradingConfig()
    
    for key, value in preset.items():
        if key != "description" and hasattr(config, key):
            setattr(config, key, value)
    
    return config


def load_config_for_symbol(symbol: str, base_config: TradingConfig = None) -> TradingConfig:
    """为特定品种加载配置"""
    if base_config is None:
        base_config = TradingConfig()
    
    if symbol in SYMBOL_CONFIGS:
        symbol_settings = SYMBOL_CONFIGS[symbol]
        for key, value in symbol_settings.items():
            if key != "description" and hasattr(base_config, key):
                setattr(base_config, key, value)
    
    base_config.symbol = symbol
    return base_config


def save_config_to_file(config: TradingConfig, filename: str):
    """保存配置到文件"""
    config_dict = {
        "symbol": config.symbol,
        "initial_capital": config.initial_capital,
        "timeframes": config.timeframes,
        "short_ma_period": config.short_ma_period,
        "long_ma_period": config.long_ma_period,
        "max_position_ratio": config.max_position_ratio,
        "max_leverage": config.max_leverage,
        "stop_loss_periods": config.stop_loss_periods,
        "commission_rate": config.commission_rate,
        "trailing_stop_enabled": config.trailing_stop_enabled,
        "profit_threshold_ratio": config.profit_threshold_ratio,
        "max_iterations": config.max_iterations
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    print(f"配置已保存到: {filename}")


def load_config_from_file(filename: str) -> TradingConfig:
    """从文件加载配置"""
    with open(filename, 'r', encoding='utf-8') as f:
        config_dict = json.load(f)
    
    config = TradingConfig()
    for key, value in config_dict.items():
        if hasattr(config, key):
            setattr(config, key, value)
    
    return config


def print_config_info(config: TradingConfig):
    """打印配置信息"""
    print("=" * 50)
    print("策略配置信息")
    print("=" * 50)
    print(f"交易合约: {config.symbol}")
    print(f"初始资金: {config.initial_capital:,.0f}")
    print(f"时间周期: {[f'{tf//60}分钟' if tf < 3600 else f'{tf//3600}小时' if tf < 86400 else f'{tf//86400}天' for tf in config.timeframes]}")
    print(f"短均线周期: {config.short_ma_period}")
    print(f"长均线周期: {config.long_ma_period}")
    print(f"最大持仓比例: {config.max_position_ratio*100:.1f}%")
    print(f"最大杠杆: {config.max_leverage}倍")
    print(f"止损周期: {config.stop_loss_periods}")
    print(f"手续费率: {config.commission_rate*100:.3f}%")
    print(f"移动止损: {'启用' if config.trailing_stop_enabled else '禁用'}")
    print(f"盈利阈值: {config.profit_threshold_ratio}倍手续费")
    print("=" * 50)


def create_config_interactive():
    """交互式创建配置"""
    print("交互式策略配置创建")
    print("=" * 30)
    
    # 选择预设
    print("可用的预设配置:")
    for name, preset in PRESET_CONFIGS.items():
        print(f"  {name}: {preset['description']}")
    
    preset_choice = input("\n选择预设配置 (直接回车使用balanced): ").strip()
    if not preset_choice:
        preset_choice = "balanced"
    
    try:
        config = load_config_from_preset(preset_choice)
    except ValueError:
        print("无效的预设，使用默认配置")
        config = TradingConfig()
    
    # 选择品种
    print("\n可用的品种配置:")
    for symbol, info in SYMBOL_CONFIGS.items():
        print(f"  {symbol}: {info['description']}")
    
    symbol = input(f"\n输入交易品种 (默认: {config.symbol}): ").strip()
    if symbol:
        config = load_config_for_symbol(symbol, config)
    
    # 调整资金
    capital_input = input(f"\n输入初始资金 (默认: {config.initial_capital}): ").strip()
    if capital_input:
        try:
            config.initial_capital = float(capital_input)
        except ValueError:
            print("无效的资金数额，使用默认值")
    
    # 显示最终配置
    print_config_info(config)
    
    # 保存配置
    save_choice = input("\n是否保存配置到文件? (y/N): ").strip().lower()
    if save_choice in ['y', 'yes']:
        filename = input("输入文件名 (默认: my_strategy_config.json): ").strip()
        if not filename:
            filename = "my_strategy_config.json"
        save_config_to_file(config, filename)
    
    return config


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "interactive":
            create_config_interactive()
        elif command == "preset":
            preset_name = sys.argv[2] if len(sys.argv) > 2 else "balanced"
            config = load_config_from_preset(preset_name)
            print_config_info(config)
        elif command == "symbol":
            symbol = sys.argv[2] if len(sys.argv) > 2 else "rb"
            config = load_config_for_symbol(symbol)
            print_config_info(config)
        else:
            print("用法:")
            print("  python strategy_config.py interactive  # 交互式创建配置")
            print("  python strategy_config.py preset [name]  # 显示预设配置")
            print("  python strategy_config.py symbol [symbol]  # 显示品种配置")
    else:
        # 默认显示所有预设配置
        print("可用的预设配置:")
        for name, preset in PRESET_CONFIGS.items():
            print(f"\n{name}: {preset['description']}")
            config = load_config_from_preset(name)
            print(f"  短均线: {config.short_ma_period}, 长均线: {config.long_ma_period}")
            print(f"  持仓比例: {config.max_position_ratio*100:.1f}%, 杠杆: {config.max_leverage}倍")
