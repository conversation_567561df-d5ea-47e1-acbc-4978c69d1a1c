import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any

class TradingStrategy:
    def __init__(self, initial_capital: float = 100000, ma_window: int = 13):
        self.portfolio_value = initial_capital
        self.trades: List[Dict[str, Any]] = []
        self.signal_profits: List[float] = []
        self.contract_multiplier = 10
        self.ma_window = ma_window
        self.price_history = []
        
        # 新增：多头和空头位置管理
        self.long_positions: List[float] = []
        self.short_positions: List[float] = []
        self.long_profits: List[float] = []
        self.short_profits: List[float] = []

    def calculate_improved_ma(self, prices: List[float]) -> float:
        return sum(prices[-self.ma_window:]) / len(prices[-self.ma_window:])

    def on_bar(self, bar: pd.Series) -> None:
        # 更新价格历史
        self.price_history.append(bar['close'])
        
        # 当有足够的数据点时计算移动平均线
        if len(self.price_history) >= self.ma_window:
            current_ma = self.calculate_improved_ma(self.price_history)
            prev_ma = self.calculate_improved_ma(self.price_history[:-1])
            
            # 生成交易信号
            if bar['close'] > current_ma and self.price_history[-2] <= prev_ma:
                self.handle_long_signal(bar)
            elif bar['close'] < current_ma and self.price_history[-2] >= prev_ma:
                self.handle_short_signal(bar)

    def handle_long_signal(self, bar: pd.Series) -> None:
        # 开多单
        self.long_positions.append(bar['close'])
        self.execute_trade(bar, 1, '开多单')

    def handle_short_signal(self, bar: pd.Series) -> None:
        # 检查是否有多单可以平仓
        for long_price in self.long_positions[:]:
            if bar['close'] > long_price:
                profit = (bar['close'] - long_price) * self.contract_multiplier
                self.long_profits.append(profit)
                self.portfolio_value += profit
                self.long_positions.remove(long_price)
                self.execute_trade(bar, -1, '平多单', profit)

        # 开空单
        self.short_positions.append(bar['close'])
        self.execute_trade(bar, -1, '开空单')
        print(f"多单列表:{self.long_positions}")

        # 检查是否有空单可以平仓
        for short_price in self.short_positions[:]:
            if bar['close'] < short_price:
                profit = (short_price - bar['close']) * self.contract_multiplier
                self.short_profits.append(profit)
                self.portfolio_value += profit
                self.short_positions.remove(short_price)
                self.execute_trade(bar, 1, '平空单', profit)
        print(f"空单列表:{self.short_positions}")

    def execute_trade(self, bar: pd.Series, direction: int, action: str, profit: float = 0) -> None:
        trade = {
            'datetime': bar.name,
            'price': bar['close'],
            'action': action,
            'quantity': 1,  # 每次交易1手
            'symbol': bar['symbol'],
            'portfolio_value': self.portfolio_value
        }
        if profit != 0:
            trade['profit'] = profit
        self.trades.append(trade)

    def get_results(self) -> Dict[str, Any]:
        total_profit = sum(self.long_profits) + sum(self.short_profits)
        winning_trades = sum(1 for profit in self.long_profits + self.short_profits if profit > 0)
        total_trades = len(self.long_profits) + len(self.short_profits)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        final_portfolio_value = self.portfolio_value

        return {
            'trades': self.trades,
            'long_profits': self.long_profits,
            'short_profits': self.short_profits,
            'total_profit': total_profit,
            'win_rate': win_rate,
            'final_portfolio_value': final_portfolio_value
        }

class BacktestEngine:
    def __init__(self, strategy: TradingStrategy):
        self.strategy = strategy

    def run(self, data: pd.DataFrame) -> None:
        for _, bar in data.iterrows():
            self.strategy.on_bar(bar)

    def get_results(self) -> Dict[str, Any]:
        return self.strategy.get_results()

def print_results(results: Dict[str, Any]) -> None:
    for trade in results['trades']:
        print(f"时间: {trade['datetime']}, 动作: {trade['action']}, 价格: {trade['price']}, 数量: {trade['quantity']}, 交易品种: {trade['symbol']}")
        if 'profit' in trade:
            print(f"利润: {trade['profit']:.2f}")
        print(f"组合价值: {trade['portfolio_value']:.2f}")
        print("---")

    print(f"总盈亏: {results['total_profit']:.2f}")
    print(f"胜率: {results['win_rate']:.2%}")
    print(f"最终组合价值: {results['final_portfolio_value']:.2f}")

    print("多头交易盈亏:")
    for i, profit in enumerate(results['long_profits'], 1):
        print(f"交易 {i}: {profit:.2f}")

    print("空头交易盈亏:")
    for i, profit in enumerate(results['short_profits'], 1):
        print(f"交易 {i}: {profit:.2f}")

if __name__ == "__main__":
    # 读取CSV文件
    df = pd.read_csv('data.csv')

    # 将datetime列转换为datetime类型并设置为索引
    df['datetime'] = pd.to_datetime(df['datetime'], unit='ns')
    df.set_index('datetime', inplace=True)
    df.sort_index(inplace=True)

    # 创建策略实例和回测引擎
    strategy = TradingStrategy()
    engine = BacktestEngine(strategy)

    # 运行回测
    engine.run(df)

    # 获取并打印结果
    results = engine.get_results()
    print_results(results)
