from TimeRoseMA_cross_multi_interval_class_base_1 import timeRoseMultiInterval, pos_interval_info


if __name__ == '__main__':

    symbol = 'CZCE.OI501'
    account = 'wolfquant2023,Qiai1301'

    s15 = pos_interval_info(0, 10, 0, 0, 1)
    s60 = pos_interval_info(0, 30, 0, 0, 3)
    s180 = pos_interval_info(0, 50, 0, 0, 5)
    s300 = pos_interval_info(0, 80, 0, 0, 10)

    instance_map = {
        15: s15,
        60: s60,
        180: s180,
        300: s300
    }
    pos_interval_info_file = account.split(',')[0] + '_' + symbol.split('.')[1] + '_pos_interval_info.pkl'
    bot = timeRoseMultiInterval(account, symbol, bklimit=20, sklimit=20, singlevolume=1, interval_parameters=instance_map, pos_instance_file=pos_interval_info_file)

    bot.run()
