from tqsdk import TqApi, TqAuth, TqKq
import numpy as np

# 计算波动率的函数
def calculate_volatility(klines):
    # 获取收盘价序列
    close_values = [kline.close for kline in klines]
    # 计算收益率序列
    returns = np.diff(close_values) / close_values[:-1]
    # 计算收益率的标准差作为波动率
    volatility = np.std(returns)
    return volatility

# 假设已经正确初始化了 api 和 auth
api = TqApi(TqKq(), auth='bigwolf,ftp123')

# 获取所有主力合约信息
instrument_infos = api.instrument_info()

# 存储合约及其波动率的字典
contract_volatility_dict = {}

for instrument_info in instrument_infos:
    if instrument_info.is_active:  # 判断是否为主力合约
        # 获取合约的 K 线数据
        klines = api.get_kline_serial(instrument_info.instrument_id, 1, 'D')
        # 计算日波动率
        volatility = calculate_volatility(klines)
        contract_volatility_dict[instrument_info.instrument_id] = volatility

# 对波动率字典进行排序并获取前十个
sorted_contracts = sorted(contract_volatility_dict.items(), key=lambda x: x[1], reverse=True)[:10]

# 打印结果
for contract, volatility in sorted_contracts:
    print(f"合约: {contract}, 日波动率: {volatility}")

api.close()
