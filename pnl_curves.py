import json
import matplotlib.pyplot as plt
import os

# File path
file_path = 'simulatedaysummary_new.json'

# Read the JSON data from the file
with open(file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

# Extract dates and balances for each account
account_balances = {}
for date, accounts in data.items():
    for account in accounts:
        user_name = account['user_name']
        balance = account['balance']
        if user_name not in account_balances:
            account_balances[user_name] = {'dates': [], 'balances': []}
        account_balances[user_name]['dates'].append(date)
        account_balances[user_name]['balances'].append(balance)

# Plot the net value curve for each account
plt.figure(figsize=(12, 8))
for user_name, values in account_balances.items():
    plt.plot(values['dates'], values['balances'], label=user_name)

plt.xlabel('Date')
plt.ylabel('Balance')
plt.title('Net Value Curve for Each Account')
plt.legend()
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()