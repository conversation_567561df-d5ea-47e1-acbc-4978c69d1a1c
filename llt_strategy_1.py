import pandas as pd
from tqsdk import TqApi, TqAuth


# 1. 核心LLT计算函数
def cal_LLT(price: pd.Series, alpha: float):
    """
    计算低延迟趋势线 (LLT)
    """
    LLT = []
    price_value = price.values
    if len(price_value) < 2:
        return [float('nan')] * len(price_value)

    LLT.append(price_value[0])
    LLT.append(price_value[1])

    for i, e in enumerate(price_value):
        if i > 1:
            v = (alpha - alpha ** 2 / 4) * e + (alpha ** 2 / 2) * price_value[i - 1] - \
                (alpha - 3 * (alpha ** 2) / 4) * price_value[i - 2] + 2 * \
                (1 - alpha) * LLT[i - 1] - (1 - alpha) ** 2 * LLT[i - 2]
            LLT.append(v)
    return LLT


# --- 实盘交易参数 ---
# 合约代码
SYMBOL = "CZCE.OI509"
# K线周期（秒），1分钟 = 60
KLINE_PERIOD = 60
# LLT指标参数
D_VALUE = 60
ALPHA = 2 / (D_VALUE + 1)
# 每次下单手数
VOLUME = 1
# K线数据长度，需要足够长以计算初始信号
KLINE_DATA_LENGTH = 500

# --- 初始化 TqApi ---
# 连接到您的实盘账户
# 警告：直接在代码中写入密码是不安全的做法
api = TqApi(auth=TqAuth("smartmanp", "ftp123"))

print(f"实盘策略启动: 合约={SYMBOL}, 周期={KLINE_PERIOD}秒")

# 获取初始K线数据
klines = api.get_kline_serial(SYMBOL, duration_seconds=KLINE_PERIOD, data_length=KLINE_DATA_LENGTH)

# +++ 新增功能：计算并输出所有历史信号 +++
print("\n" + "=" * 25 + " 历史信号输出 " + "=" * 25)
initial_close_prices = pd.Series(klines.close)
initial_llt_series = cal_LLT(initial_close_prices, ALPHA)

# 从第三根K线开始，因为LLT需要两个前置值
for i in range(2, len(klines)):
    if initial_llt_series[i] > initial_llt_series[i - 1]:
        signal = 1  # 看多
    elif initial_llt_series[i] < initial_llt_series[i - 1]:
        signal = -1  # 看空
    else:
        signal = 0  # 观望

    dt = pd.to_datetime(klines.iloc[i]["datetime"])
    close_price = klines.iloc[i]["close"]
    llt_val = initial_llt_series[i]

    print(f"历史时间: {dt.strftime('%Y-%m-%d %H:%M')}, 收盘价: {close_price:.2f}, LLT: {llt_val:.2f}, 信号: {signal}")

print("=" * 25 + " 历史信号输出结束 " + "=" * 25 + "\n")
print("等待新的1分钟K线生成...\n")
# +++ 功能结束 +++

# --- 实盘交易主循环 ---
while True:
    api.wait_update()

    # 当有新的1分钟K线生成时，执行交易逻辑
    if api.is_changing(klines.iloc[-1], "datetime"):

        # --- 交易逻辑开始 ---
        close_prices = pd.Series(klines.close)
        llt_series = cal_LLT(close_prices, ALPHA)

        if len(llt_series) < 2:
            continue

        if llt_series[-1] > llt_series[-2]:
            signal = 1
        elif llt_series[-1] < llt_series[-2]:
            signal = -1
        else:
            signal = 0

        position = api.get_position(SYMBOL)

        # 打印当前状态
        dt_now = pd.to_datetime(klines.iloc[-1]["datetime"])
        print(f"时间: {dt_now.strftime('%Y-%m-%d %H:%M')}, "
              f"收盘价: {klines.close.iloc[-1]:.2f}, "
              f"LLT: {llt_series[-1]:.2f}, "
              f"信号: {signal}, "
              f"当前多仓: {position.pos_long}, "
              f"当前空仓: {position.pos_short}")

        if signal == 1:  # 看多信号
            if position.pos_short > 0:
                print(f"信号为多，平掉 {position.pos_short} 手空仓")
                api.close_position(SYMBOL, offset="CLOSE", volume=1)
            if position.pos_long == 0:
                print("信号为多，开多仓")
                api.insert_order(symbol=SYMBOL, direction="BUY", offset="OPEN", volume=VOLUME)

        elif signal == -1:  # 看空信号
            if position.pos_long > 0:
                print(f"信号为空，平掉 {position.pos_long} 手多仓")
                api.close_position(SYMBOL, offset="CLOSE", volume=position.pos_long)
            if position.pos_short == 0:
                print("信号为空，开空仓")
                api.insert_order(symbol=SYMBOL, direction="SELL", offset="OPEN", volume=VOLUME)

        print("-" * 60)

# 在实际循环策略中，此行通常不会执行
# api.close()