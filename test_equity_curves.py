"""
测试资金曲线绘制功能
"""

import pandas as pd
import numpy as np
from loguru import logger
from llt_multi_contract_analyzer import (
    ContractConfig, ContractAnalysisResult, 
    plot_equity_curves_from_results, create_analysis_config,
    MultiContractAnalyzer
)

def create_mock_results():
    """创建模拟分析结果"""
    contracts = [
        ContractConfig("CZCE.OI509", "菜籽油主力", "CZCE"),
        ContractConfig("SHFE.cu2510", "沪铜主力", "SHFE"),
        ContractConfig("DCE.m2509", "豆粕主力", "DCE"),
        ContractConfig("SHFE.au2510", "沪金主力", "SHFE"),
        ContractConfig("DCE.i2509", "铁矿石主力", "DCE"),
        ContractConfig("CZCE.TA509", "PTA主力", "CZCE"),
        ContractConfig("SHFE.al2510", "沪铝主力", "SHFE"),
    ]
    
    results = []
    
    # 模拟不同的分析结果
    mock_data = [
        (11.73, 100.0, 40, 14, 0.133333, 1173.0, 0.0, 29.325, 0.0, 2.5),
        (8.45, 85.2, 156, 35, 0.055556, 845.0, 156.78, 12.34, -8.76, 1.41),
        (6.78, 68.9, 142, 28, 0.066667, 678.0, 234.5, 15.2, -12.1, 1.26),
        (5.92, 65.4, 138, 42, 0.045455, 592.0, 189.3, 18.7, -14.3, 1.31),
        (4.44, 26.7, 576, 63, 0.03125, 444.0, 540.0, 42.8, -14.6, 2.94),
        (3.21, 45.8, 89, 55, 0.035714, 321.0, 298.7, 22.1, -18.9, 1.17),
        (2.15, 38.2, 67, 48, 0.040000, 215.0, 187.4, 19.8, -16.2, 1.22),
    ]
    
    for i, (return_rate, win_rate, total_trades, best_d_value, best_alpha, 
            total_pnl, max_drawdown, avg_win, avg_loss, profit_factor) in enumerate(mock_data):
        
        if i < len(contracts):
            contract = contracts[i]
            
            result = ContractAnalysisResult(
                contract=contract,
                best_d_value=best_d_value,
                best_alpha=best_alpha,
                total_trades=total_trades,
                total_pnl=total_pnl,
                win_rate=win_rate,
                return_rate=return_rate,
                winning_trades=int(total_trades * win_rate / 100),
                losing_trades=int(total_trades * (100 - win_rate) / 100),
                avg_win=avg_win,
                avg_loss=avg_loss,
                profit_factor=profit_factor,
                max_drawdown=max_drawdown,
                sharpe_ratio=0.85,
                analysis_period_days=365,
                data_quality_score=95.0
            )
            
            results.append(result)
    
    return results


def test_equity_curves_plotting():
    """测试资金曲线绘制"""
    print("=" * 60)
    print("测试资金曲线绘制功能")
    print("=" * 60)
    
    # 设置日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level='INFO', colorize=True)
    
    try:
        # 创建模拟结果
        results = create_mock_results()
        print(f"创建了 {len(results)} 个模拟分析结果")
        
        # 显示前5名
        print(f"\n📊 前5名合约:")
        for i, result in enumerate(results[:5], 1):
            print(f"  {i}. {result.contract.symbol}: {result.return_rate:+.2f}% "
                 f"(胜率:{result.win_rate:.1f}%, 交易:{result.total_trades}次)")
        
        # 绘制资金曲线
        print(f"\n📈 开始绘制资金曲线...")
        plot_equity_curves_from_results(results, "test_equity_curves", top_n=5)
        
        print(f"\n✅ 资金曲线绘制完成")
        print(f"📁 图片保存在: test_equity_curves/top5_equity_curves.png")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_matplotlib_import():
    """测试matplotlib导入"""
    print("=" * 60)
    print("测试matplotlib导入")
    print("=" * 60)
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.dates as mdates
        print("✅ matplotlib导入成功")
        
        # 测试基本绘图
        fig, ax = plt.subplots(figsize=(8, 6))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y)
        ax.set_title('测试图表')
        ax.set_xlabel('X轴')
        ax.set_ylabel('Y轴')
        
        # 保存测试图片
        plt.savefig('test_plot.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ 基本绘图功能正常")
        print("📁 测试图片保存为: test_plot.png")
        
        return True
        
    except ImportError as e:
        print(f"❌ matplotlib导入失败: {e}")
        print("💡 请安装matplotlib: pip install matplotlib")
        return False
    except Exception as e:
        print(f"❌ 绘图测试失败: {e}")
        return False


def test_chinese_font():
    """测试中文字体"""
    print("=" * 60)
    print("测试中文字体")
    print("=" * 60)
    
    try:
        import matplotlib.pyplot as plt
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(8, 6))
        x = [1, 2, 3, 4, 5]
        y = [10, 15, 13, 18, 16]
        
        ax.plot(x, y, marker='o')
        ax.set_title('中文字体测试 - LLT策略资金曲线')
        ax.set_xlabel('交易日')
        ax.set_ylabel('累计盈亏 (点)')
        ax.grid(True, alpha=0.3)
        
        # 添加中文标注
        ax.text(3, 15, '最高点', fontsize=12, ha='center',
               bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
        
        plt.savefig('test_chinese_font.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ 中文字体测试成功")
        print("📁 中文字体测试图片: test_chinese_font.png")
        
        return True
        
    except Exception as e:
        print(f"❌ 中文字体测试失败: {e}")
        print("💡 可能需要安装中文字体或使用英文标题")
        return False


def main():
    """主测试函数"""
    print("资金曲线绘制功能测试")
    print("=" * 80)
    
    tests = [
        ("matplotlib导入", test_matplotlib_import),
        ("中文字体", test_chinese_font),
        ("资金曲线绘制", test_equity_curves_plotting),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 资金曲线绘制功能测试成功！")
        print("\n📋 使用方法:")
        print("# 分析时自动绘制资金曲线")
        print("python llt_multi_contract_analyzer.py --mode quick --plot-curves")
        print("")
        print("# 完整分析并绘制资金曲线")
        print("python llt_multi_contract_analyzer.py --mode full --plot-curves")
    else:
        print("⚠️  部分测试失败，可能需要安装matplotlib")
        print("💡 安装命令: pip install matplotlib")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
