"""
测试参数更新
验证180秒周期和8964数据长度的修改
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_parameter_values():
    """测试参数默认值"""
    print("=" * 60)
    print("测试参数默认值")
    print("=" * 60)
    
    try:
        # 检查程序文件
        program_file = "波动交易GUI.py"
        if not os.path.exists(program_file):
            print(f"✗ 程序文件不存在: {program_file}")
            return False
        
        # 读取程序内容
        with open(program_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查数据长度默认值
        if 'self.data_length = 8964' in content:
            print("✓ 数据长度默认值已更新为 8964")
        else:
            print("✗ 数据长度默认值未更新")
            return False
        
        # 检查界面默认值
        if 'self.length_var = tk.StringVar(value="8964")' in content:
            print("✓ 界面数据长度默认值已更新为 8964")
        else:
            print("✗ 界面数据长度默认值未更新")
            return False
        
        # 检查周期选项
        if '"60", "180", "300", "900", "3600"' in content:
            print("✓ 周期选项已添加 180秒")
        else:
            print("✗ 周期选项未添加 180秒")
            return False
        
        print("✓ 所有参数更新验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 参数值测试失败: {e}")
        return False

def test_gui_parameter_interface():
    """测试GUI参数界面"""
    print("=" * 60)
    print("测试GUI参数界面")
    print("=" * 60)
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("参数界面测试")
        root.geometry("800x400")
        
        # 创建参数设置界面
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(main_frame, text="波动交易参数设置测试", 
                 font=("Arial", 16, "bold")).pack(pady=(0, 20))
        
        # 参数设置区域
        params_frame = ttk.LabelFrame(main_frame, text="参数设置", padding="15")
        params_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 合约代码
        row1 = ttk.Frame(params_frame)
        row1.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(row1, text="合约代码:", width=12).pack(side=tk.LEFT, padx=(0, 5))
        symbol_var = tk.StringVar(value="KQ.i@OI")
        symbol_entry = ttk.Entry(row1, textvariable=symbol_var, width=15)
        symbol_entry.pack(side=tk.LEFT, padx=(0, 20))
        
        # 数据周期
        ttk.Label(row1, text="数据周期(秒):", width=12).pack(side=tk.LEFT, padx=(0, 5))
        duration_var = tk.StringVar(value="60")
        duration_combo = ttk.Combobox(row1, textvariable=duration_var, 
                                     values=["60", "180", "300", "900", "3600"], 
                                     width=10, state="readonly")
        duration_combo.pack(side=tk.LEFT)
        
        # 数据长度
        row2 = ttk.Frame(params_frame)
        row2.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(row2, text="数据长度:", width=12).pack(side=tk.LEFT, padx=(0, 5))
        length_var = tk.StringVar(value="8964")
        length_entry = ttk.Entry(row2, textvariable=length_var, width=15)
        length_entry.pack(side=tk.LEFT, padx=(0, 20))
        
        # 更新间隔
        ttk.Label(row2, text="更新间隔(ms):", width=12).pack(side=tk.LEFT, padx=(0, 5))
        interval_var = tk.StringVar(value="1000")
        interval_combo = ttk.Combobox(row2, textvariable=interval_var,
                                     values=["500", "1000", "2000", "5000"], 
                                     width=10, state="readonly")
        interval_combo.pack(side=tk.LEFT)
        
        # 测试按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        def test_parameters():
            """测试参数获取"""
            symbol = symbol_var.get()
            duration = duration_var.get()
            length = length_var.get()
            interval = interval_var.get()
            
            result_text = f"""
参数测试结果:
合约代码: {symbol}
数据周期: {duration} 秒
数据长度: {length} 条
更新间隔: {interval} 毫秒

验证结果:
✓ 180秒周期选项: {'存在' if '180' in duration_combo['values'] else '不存在'}
✓ 8964默认长度: {'正确' if length == '8964' else '错误'}
✓ 参数获取功能: 正常
            """
            
            result_label.config(text=result_text)
            print("参数测试完成")
        
        ttk.Button(button_frame, text="测试参数", command=test_parameters, width=15).pack(side=tk.LEFT, padx=(0, 10))
        
        def reset_parameters():
            """重置参数"""
            symbol_var.set("KQ.i@OI")
            duration_var.set("180")  # 测试新增的180秒选项
            length_var.set("8964")
            interval_var.set("1000")
            result_label.config(text="参数已重置为默认值")
        
        ttk.Button(button_frame, text="重置参数", command=reset_parameters, width=15).pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="测试结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        result_label = ttk.Label(result_frame, text="点击'测试参数'按钮查看结果", 
                                justify=tk.LEFT, font=("Courier", 10))
        result_label.pack(fill=tk.BOTH, expand=True)
        
        # 验证信息
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        info_text = "✓ 新增180秒周期选项  ✓ 数据长度默认8964  ✓ 参数界面正常"
        ttk.Label(info_frame, text=info_text, foreground="green", 
                 font=("Arial", 10, "bold")).pack()
        
        print("✓ GUI参数界面创建成功")
        print("✓ 180秒周期选项已添加")
        print("✓ 8964数据长度默认值已设置")
        print("✓ 参数控件功能正常")
        
        # 自动测试一次
        reset_parameters()
        test_parameters()
        
        print("\n请测试参数设置功能，然后关闭窗口...")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI参数界面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_calculation():
    """测试数据计算影响"""
    print("=" * 60)
    print("测试数据计算影响")
    print("=" * 60)
    
    # 分析不同数据长度的影响
    data_lengths = [200, 1000, 5000, 8964]
    periods = [60, 180, 300, 900, 3600]
    
    print("数据长度对应的时间跨度分析:")
    print(f"{'数据长度':<8} {'60秒':<8} {'180秒':<8} {'300秒':<8} {'900秒':<8} {'3600秒'}")
    print("-" * 60)
    
    for length in data_lengths:
        row = f"{length:<8}"
        for period in periods:
            total_seconds = length * period
            if total_seconds < 3600:
                time_str = f"{total_seconds//60}分"
            elif total_seconds < 86400:
                time_str = f"{total_seconds//3600}小时"
            else:
                time_str = f"{total_seconds//86400}天"
            row += f" {time_str:<8}"
        print(row)
    
    print(f"\n8964条数据的时间跨度:")
    print(f"• 60秒周期: {8964*60//3600}小时{(8964*60%3600)//60}分")
    print(f"• 180秒周期: {8964*180//3600}小时{(8964*180%3600)//60}分")
    print(f"• 300秒周期: {8964*300//86400}天{(8964*300%86400)//3600}小时")
    print(f"• 900秒周期: {8964*900//86400}天{(8964*900%86400)//3600}小时")
    print(f"• 3600秒周期: {8964*3600//86400}天")
    
    print(f"\n180秒周期的特点:")
    print(f"• 3分钟一根K线")
    print(f"• 比60秒周期噪音更少")
    print(f"• 比300秒周期更敏感")
    print(f"• 适合中短期波动分析")
    print(f"• 8964条数据覆盖约{8964*180//86400}天的历史")
    
    return True

def show_update_summary():
    """显示更新总结"""
    print("=" * 60)
    print("参数更新总结")
    print("=" * 60)
    
    print("更新内容:")
    print("1. 数据周期选项:")
    print("   原来: [60, 300, 900, 3600] 秒")
    print("   现在: [60, 180, 300, 900, 3600] 秒")
    print("   新增: 180秒 (3分钟) 周期")
    print()
    
    print("2. 数据长度默认值:")
    print("   原来: 200 条")
    print("   现在: 8964 条")
    print("   增加: 44.8倍数据量")
    print()
    
    print("3. 影响分析:")
    print("   • 更多历史数据用于技术指标计算")
    print("   • 更准确的趋势判断")
    print("   • 更长的回测时间窗口")
    print("   • 180秒周期提供中等时间粒度")
    print()
    
    print("4. 使用建议:")
    print("   • 短期分析: 60秒周期")
    print("   • 中期分析: 180秒周期 (新增)")
    print("   • 长期分析: 300秒以上周期")
    print("   • 数据量大时注意网络和内存使用")

def main():
    """主测试函数"""
    print("波动交易GUI参数更新测试")
    print("=" * 80)
    
    tests = [
        ("参数默认值检查", test_parameter_values),
        ("数据计算影响分析", test_data_calculation),
        ("GUI参数界面测试", test_gui_parameter_interface),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示更新总结
    show_update_summary()
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 参数更新成功！")
        print("\n更新内容:")
        print("✓ 新增180秒数据周期选项")
        print("✓ 数据长度默认值改为8964")
        print("✓ 界面默认值同步更新")
        print("✓ 参数功能正常工作")
        print("\n现在程序支持:")
        print("• 5种数据周期: 60, 180, 300, 900, 3600秒")
        print("• 大数据量分析: 8964条历史数据")
        print("• 更精确的技术指标计算")
        print("• 更长的历史回测窗口")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
