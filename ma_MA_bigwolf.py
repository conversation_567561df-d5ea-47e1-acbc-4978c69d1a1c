import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str, ema
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog
from strategyTRMA import trmastrategy
from time import sleep
import time

mylog.add('ema.log', encoding='utf-8')


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    # symbol = 'SHFE.ag2103'
    # symbol = 'CZCE.SR101'
    # symbol = 'DCE.pp2105'
    symbol = 'CZCE.MA105'
    symbol = 'DCE.eg2105'
    symname = symbol.rsplit('.', 1)[1]
    interval = 15
    bklimit = 230
    sklimit = 230
    single_volume = 1

    config = {
        symbol: 'DCE.pp2105',
        interval: 15,
        bklimit: 3000000,
        sklimit: 3000,
        single_volume: 1000
    }

    # 交易账号设置
    api = TqApi(TqKq(), auth="bigwolf,ftp123")

    trmastrategy(api, symbol, interval, single_volume, bklimit, sklimit)
