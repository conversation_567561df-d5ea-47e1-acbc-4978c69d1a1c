import pandas as pd
from tqsdk import TqApi, TqAuth, TqKq


# 1. Core LLT calculation function (unchanged)
def cal_LLT(price: pd.Series, alpha: float):
    """
    Calculates the Low-Latency Trendline (LLT).
    """
    LLT = []
    price_value = price.values
    if len(price_value) < 2:
        return [float('nan')] * len(price_value)
    LLT.append(price_value[0])
    LLT.append(price_value[1])
    for i, e in enumerate(price_value):
        if i > 1:
            v = (alpha - alpha ** 2 / 4) * e + (alpha ** 2 / 2) * price_value[i - 1] - \
                (alpha - 3 * (alpha ** 2) / 4) * price_value[i - 2] + 2 * \
                (1 - alpha) * LLT[i - 1] - (1 - alpha) ** 2 * LLT[i - 2]
            LLT.append(v)
    return LLT


# --- 新的交易逻辑说明 ---
# 平仓条件修改：
# 1. 出现做多信号时，如果持有空单且盈利，则全部平掉空单
# 2. 出现做空信号时，如果持有多单且盈利，则全部平掉多单
# 3. 基于entry_price的平仓条件：根据entry_price计算盈亏，如果盈利则平掉最近一次开仓数量，如果亏损则不平仓
# 4. 如果发出反向信号需要平仓时，先检查当前持仓盈亏
# 5. 如果本次持仓盈利 > 0，则执行平仓并开反向仓
# 6. 如果持仓亏损，则不平仓，反向开仓数量 = 当前持仓 + 1
# 7. 这样可以在亏损时通过加仓来摊低成本，等待价格回归
# 8. 多头和空头持仓的最大数量限制为100，超过不再加仓

# --- Live Trading Parameters ---
SYMBOL = "CZCE.OI509"
KLINE_PERIOD_SECONDS = 60*5  # Correct parameter name
D_VALUE = 60  # 默认值，将在优化中测试
ALPHA = 2 / (D_VALUE + 1)
VOLUME = 1
KLINE_DATA_LENGTH = 8964
MAX_POSITION = 100  # 多头和空头持仓的最大数量限制

# --- Initialize TqApi ---
api = TqApi(TqKq(),auth=TqAuth("bigwolf", "ftp123"),disable_print=True)

print(f"Live strategy started: Symbol={SYMBOL}, Period={KLINE_PERIOD_SECONDS}s")
print(f"Requesting {KLINE_DATA_LENGTH} K-lines of historical data...")

# Using the correct parameter name 'duration_seconds'
klines = api.get_kline_serial(SYMBOL, duration_seconds=KLINE_PERIOD_SECONDS, data_length=KLINE_DATA_LENGTH)

# 等待数据加载完成
#api.wait_update()

print(f"实际获取到的K线数量: {len(klines)}")
if len(klines) > 0:
    first_time = pd.to_datetime(klines.datetime.iloc[0])
    last_time = pd.to_datetime(klines.datetime.iloc[-1])
    print(f"数据时间范围: {first_time.strftime('%Y-%m-%d %H:%M')} 到 {last_time.strftime('%Y-%m-%d %H:%M')}")

# --- Historical Signal and P&L Calculation ---
def calculate_historical_signals_and_pnl(klines_data, alpha):
    """
    计算历史信号和累计盈亏
    """
    close_prices = pd.Series(klines_data.close)
    llt_series = cal_LLT(close_prices, alpha)
    
    # 创建结果DataFrame
    results = pd.DataFrame({
        'datetime': klines_data.datetime,
        'close': klines_data.close,
        'llt': llt_series
    })
    
    # 计算信号
    signals = []
    for i in range(len(llt_series)):
        if i == 0:
            signals.append(0)
        else:
            if llt_series[i] > llt_series[i-1]:
                signals.append(1)  # 买入信号
            elif llt_series[i] < llt_series[i-1]:
                signals.append(-1)  # 卖出信号
            else:
                signals.append(0)  # 无信号
    
    results['signal'] = signals
    
    # 计算持仓和盈亏
    position = 0  # 0: 空仓, 1: 多头, -1: 空头
    entry_price = 0
    trades = []
    cumulative_pnl = 0
    pnl_series = []
    
    for i in range(len(results)):
        current_price = results.iloc[i]['close']
        current_signal = results.iloc[i]['signal']
        current_time = pd.to_datetime(results.iloc[i]['datetime'])
        
        # 处理交易信号 - 新的平仓逻辑
        # 新增平仓条件：信号出现时检查反向持仓是否盈利，盈利则全部平仓
        if current_signal == 1 and position == -1:  # 做多信号且持有空头
            floating_pnl = entry_price - current_price
            if floating_pnl > 0:  # 空头盈利，全部平仓
                cumulative_pnl += floating_pnl
                trades.append({
                    'exit_time': current_time,
                    'exit_price': current_price,
                    'position_type': 'SHORT_CLOSE_ALL',
                    'pnl': floating_pnl,
                    'cumulative_pnl': cumulative_pnl
                })
                position = 0  # 平仓后空仓
                entry_price = 0
        
        elif current_signal == -1 and position == 1:  # 做空信号且持有多头
            floating_pnl = current_price - entry_price
            if floating_pnl > 0:  # 多头盈利，全部平仓
                cumulative_pnl += floating_pnl
                trades.append({
                    'exit_time': current_time,
                    'exit_price': current_price,
                    'position_type': 'LONG_CLOSE_ALL',
                    'pnl': floating_pnl,
                    'cumulative_pnl': cumulative_pnl
                })
                position = 0  # 平仓后空仓
                entry_price = 0
        
        # 原有交易逻辑
        if current_signal == 1 and position != 1:  # 买入信号
            # 如果有空头持仓，检查盈亏决定是否平仓
            if position == -1:
                floating_pnl = entry_price - current_price
                if floating_pnl > 0:  # 盈利则平仓
                    cumulative_pnl += floating_pnl
                    trades.append({
                        'exit_time': current_time,
                        'exit_price': current_price,
                        'position_type': 'SHORT',
                        'pnl': floating_pnl,
                        'cumulative_pnl': cumulative_pnl
                    })
                    # 开多头
                    position = 1
                    entry_price = current_price
                else:  # 亏损则不平仓，继续持有空头，但记录为混合持仓
                    # 在回测中简化处理：如果亏损不平仓，则保持原持仓方向
                    pass
            else:
                # 没有空头持仓，直接开多头
                position = 1
                entry_price = current_price
            
        elif current_signal == -1 and position != -1:  # 卖出信号
            # 如果有多头持仓，检查盈亏决定是否平仓
            if position == 1:
                floating_pnl = current_price - entry_price
                if floating_pnl > 0:  # 盈利则平仓
                    cumulative_pnl += floating_pnl
                    trades.append({
                        'exit_time': current_time,
                        'exit_price': current_price,
                        'position_type': 'LONG',
                        'pnl': floating_pnl,
                        'cumulative_pnl': cumulative_pnl
                    })
                    # 开空头
                    position = -1
                    entry_price = current_price
                else:  # 亏损则不平仓，继续持有多头
                    # 在回测中简化处理：如果亏损不平仓，则保持原持仓方向
                    pass
            else:
                # 没有多头持仓，直接开空头
                position = -1
                entry_price = current_price
        
        # 计算当前浮动盈亏
        if position == 1:  # 多头
            floating_pnl = current_price - entry_price
        elif position == -1:  # 空头
            floating_pnl = entry_price - current_price
        else:  # 空仓
            floating_pnl = 0
        
        pnl_series.append(cumulative_pnl + floating_pnl)
    
    results['cumulative_pnl'] = pnl_series
    
    return results, trades

print("开始参数优化测试...\n")

# --- 参数优化功能 ---
def optimize_d_value(klines_data, d_value_range):
    """
    优化D_VALUE参数，返回收益率最高的前5个参数
    """
    optimization_results = []
    
    print("正在测试不同的D_VALUE参数...")
    print("D_VALUE | 交易次数 | 累计盈亏 | 胜率 | 收益率")
    print("-" * 50)
    
    for d_value in d_value_range:
        alpha = 2 / (d_value + 1)
        
        try:
            # 计算该参数下的回测结果
            results, trades = calculate_historical_signals_and_pnl(klines_data, alpha)
            
            if trades and len(trades) > 0:
                total_pnl = trades[-1]['cumulative_pnl']
                winning_trades = [t for t in trades if t['pnl'] > 0]
                win_rate = len(winning_trades) / len(trades) * 100
                
                # 计算收益率 (假设初始资金为10000)
                initial_capital = 10000
                return_rate = (total_pnl / initial_capital) * 100
                
                optimization_results.append({
                    'D_VALUE': d_value,
                    'ALPHA': alpha,
                    'total_trades': len(trades),
                    'total_pnl': total_pnl,
                    'win_rate': win_rate,
                    'return_rate': return_rate,
                    'winning_trades': len(winning_trades),
                    'losing_trades': len(trades) - len(winning_trades)
                })
                
                print(f"{d_value:7d} | {len(trades):8d} | {total_pnl:8.2f} | {win_rate:5.1f}% | {return_rate:6.2f}%")
            else:
                print(f"{d_value:7d} | {0:8d} | {0:8.2f} | {0:5.1f}% | {0:6.2f}%")
                
        except Exception as e:
            print(f"{d_value:7d} | 计算错误: {str(e)}")
            continue
    
    # 按收益率排序，返回前5名
    optimization_results.sort(key=lambda x: x['return_rate'], reverse=True)
    return optimization_results[:5]

# 执行参数优化
d_value_range = range(10, 101)  # 从10到100，步长为1
top_5_results = optimize_d_value(klines, d_value_range)

print("\n" + "="*60)
print("🏆 收益率前5名的D_VALUE参数:")
print("="*60)

for i, result in enumerate(top_5_results, 1):
    print(f"第{i}名:")
    print(f"  D_VALUE: {result['D_VALUE']}")
    print(f"  ALPHA: {result['ALPHA']:.6f}")
    print(f"  收益率: {result['return_rate']:.2f}%")
    print(f"  累计盈亏: {result['total_pnl']:.2f} 点")
    print(f"  交易次数: {result['total_trades']}")
    print(f"  胜率: {result['win_rate']:.1f}%")
    print(f"  盈利交易: {result['winning_trades']} 次")
    print(f"  亏损交易: {result['losing_trades']} 次")
    print("-" * 40)

# 使用最优参数进行详细回测
if top_5_results:
    best_d_value = top_5_results[0]['D_VALUE']
    best_alpha = top_5_results[0]['ALPHA']
    print(f"\n使用最优参数 D_VALUE={best_d_value} 进行详细回测...")
    
    # 更新全局参数
    D_VALUE = best_d_value
    ALPHA = best_alpha
else:
    print("\n未找到有效的优化结果，使用默认参数...")

# 保存优化结果到文件
if top_5_results:
    optimization_summary = []
    optimization_summary.append("LLT策略参数优化结果")
    optimization_summary.append("="*50)
    optimization_summary.append(f"测试合约: {SYMBOL}")
    optimization_summary.append(f"数据期间: {pd.to_datetime(klines.datetime.iloc[0]).strftime('%Y-%m-%d %H:%M')} 到 {pd.to_datetime(klines.datetime.iloc[-1]).strftime('%Y-%m-%d %H:%M')}")
    optimization_summary.append(f"K线数量: {len(klines)}")
    optimization_summary.append("")
    optimization_summary.append("收益率前5名参数:")
    optimization_summary.append("-" * 50)
    
    for i, result in enumerate(top_5_results, 1):
        optimization_summary.append(f"第{i}名: D_VALUE={result['D_VALUE']}, 收益率={result['return_rate']:.2f}%, 交易次数={result['total_trades']}, 胜率={result['win_rate']:.1f}%")
    
    # 写入文件
    with open('llt_optimization_results.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(optimization_summary))
    
    print(f"\n优化结果已保存到: llt_optimization_results.txt")

print("\n" + "="*60)

# 使用最优参数计算历史信号和盈亏
historical_results, historical_trades = calculate_historical_signals_and_pnl(klines, ALPHA)

# 打印统计信息
print("=== 最优参数历史回测统计 ===")
print(f"使用参数: D_VALUE={D_VALUE}, ALPHA={ALPHA:.6f}")
print(f"数据期间: {pd.to_datetime(klines.datetime.iloc[0]).strftime('%Y-%m-%d %H:%M')} 到 {pd.to_datetime(klines.datetime.iloc[-1]).strftime('%Y-%m-%d %H:%M')}")
print(f"总K线数量: {len(klines)}")
print(f"总交易次数: {len(historical_trades)}")

if historical_trades:
    total_pnl = historical_trades[-1]['cumulative_pnl']
    winning_trades = [t for t in historical_trades if t['pnl'] > 0]
    losing_trades = [t for t in historical_trades if t['pnl'] < 0]
    
    print(f"累计盈亏: {total_pnl:.2f} 点")
    print(f"盈利交易: {len(winning_trades)} 次")
    print(f"亏损交易: {len(losing_trades)} 次")
    
    if len(winning_trades) > 0:
        avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades)
        print(f"平均盈利: {avg_win:.2f} 点")
    
    if len(losing_trades) > 0:
        avg_loss = sum(t['pnl'] for t in losing_trades) / len(losing_trades)
        print(f"平均亏损: {avg_loss:.2f} 点")
    
    win_rate = len(winning_trades) / len(historical_trades) * 100 if historical_trades else 0
    print(f"胜率: {win_rate:.1f}%")
    
    # 打印最近5笔交易
    print("\n=== 最近5笔交易 ===")
    for trade in historical_trades[-5:]:
        print(f"{trade['exit_time'].strftime('%m-%d %H:%M')} | {trade['position_type']} | "
              f"盈亏: {trade['pnl']:+.2f} | 累计: {trade['cumulative_pnl']:.2f}")

print("\n" + "="*50)
print("开始实时交易监控...\n")

# Variable to store the entry price of the current position
entry_price = 0.0
# 实时交易累计盈亏跟踪
live_cumulative_pnl = historical_trades[-1]['cumulative_pnl'] if historical_trades else 0.0
live_trades = []
last_summary_time = pd.Timestamp.now()

# --- Live Trading Main Loop ---
while True:
    api.wait_update()

    if api.is_changing(klines.iloc[-1], "datetime"):
        # 1. Calculate signal
        close_prices = pd.Series(klines.close)
        llt_series = cal_LLT(close_prices, ALPHA)

        if len(llt_series) < 2:
            continue

        signal = 0
        if llt_series[-1] > llt_series[-2]:
            signal = 1
            print(f"Signal: Buy at {klines.close.iloc[-1]:.2f}")
        elif llt_series[-1] < llt_series[-2]:
            signal = -1
            print(f"Signal: Sell at {klines.close.iloc[-1]:.2f}")

        position = api.get_position(SYMBOL)
        current_price = klines.close.iloc[-1]
        dt_now = pd.to_datetime(klines.iloc[-1]["datetime"])

        # 2. Check and print floating PnL if in a position
        if position.pos_long > 0:
            long_open_price = position.open_price_long
            if long_open_price > 0:
                pnl_points = (current_price - long_open_price) * position.pos_long
                print(f"[{dt_now.strftime('%H:%M:%S')}] LONG {position.pos_long}手 | 开仓价: {long_open_price:.2f}, 当前价: {current_price:.2f}, 浮动盈亏: {pnl_points:+.2f} 点")
        elif position.pos_short > 0:
            short_open_price = position.open_price_short
            if short_open_price > 0:
                pnl_points = (short_open_price - current_price) * position.pos_short
                print(f"[{dt_now.strftime('%H:%M:%S')}] SHORT {position.pos_short}手 | 开仓价: {short_open_price:.2f}, 当前价: {current_price:.2f}, 浮动盈亏: {pnl_points:+.2f} 点")

        # 3. 新增平仓条件：信号出现时检查反向持仓是否盈利，盈利则全部平仓
        # 做多信号时，检查空单是否盈利
        if signal == 1 and position.pos_short > 0:
            # 使用position对象中的开仓价格计算盈亏
            short_open_price = position.open_price_short  # 空头开仓价格
            if short_open_price > 0:  # 确保有有效的开仓价格
                floating_pnl = (short_open_price - current_price) * position.pos_short  # 空单盈亏 = (开仓价 - 当前价) * 手数
                if floating_pnl > 0:  # 空单盈利，全部平仓
                    live_cumulative_pnl += floating_pnl
                    live_trades.append({
                        'time': dt_now,
                        'type': 'SHORT_CLOSE_ALL',
                        'price': current_price,
                        'pnl': floating_pnl,
                        'cumulative_pnl': live_cumulative_pnl
                    })
                    print(f"[{dt_now.strftime('%H:%M:%S')}] 做多信号-空单盈利全平 | 开仓价: {short_open_price:.2f} | 当前价: {current_price:.2f} | 盈亏: {floating_pnl:+.2f} | 累计: {live_cumulative_pnl:.2f}")
                    api.insert_order(symbol=SYMBOL, direction="BUY", offset="CLOSE", volume=position.pos_short)
                    entry_price = 0.0
        
        # 做空信号时，检查多单是否盈利
        elif signal == -1 and position.pos_long > 0:
            # 使用position对象中的开仓价格计算盈亏
            long_open_price = position.open_price_long  # 多头开仓价格
            if long_open_price > 0:  # 确保有有效的开仓价格
                floating_pnl = (current_price - long_open_price) * position.pos_long  # 多单盈亏 = (当前价 - 开仓价) * 手数
                if floating_pnl > 0:  # 多单盈利，全部平仓
                    live_cumulative_pnl += floating_pnl
                    live_trades.append({
                        'time': dt_now,
                        'type': 'LONG_CLOSE_ALL',
                        'price': current_price,
                        'pnl': floating_pnl,
                        'cumulative_pnl': live_cumulative_pnl
                    })
                    print(f"[{dt_now.strftime('%H:%M:%S')}] 做空信号-多单盈利全平 | 开仓价: {long_open_price:.2f} | 当前价: {current_price:.2f} | 盈亏: {floating_pnl:+.2f} | 累计: {live_cumulative_pnl:.2f}")
                    api.insert_order(symbol=SYMBOL, direction="SELL", offset="CLOSE", volume=position.pos_long)
                    entry_price = 0.0

        # 3.5. 基于entry_price的平仓条件：检查最近一次开仓是否盈利
        if entry_price != 0.0:
            # 检查多头持仓基于entry_price的盈亏
            if position.pos_long > 0:
                entry_pnl = current_price - entry_price
                if entry_pnl > 0:  # 基于entry_price盈利，平掉最近一次开仓数量
                    close_volume = VOLUME  # 平掉最近一次开仓的数量
                    actual_close_volume = min(close_volume, position.pos_long)  # 确保不超过实际持仓
                    
                    # 计算平仓盈亏
                    close_pnl = entry_pnl * actual_close_volume
                    live_cumulative_pnl += close_pnl
                    live_trades.append({
                        'time': dt_now,
                        'type': 'LONG_CLOSE_ENTRY',
                        'price': current_price,
                        'pnl': close_pnl,
                        'cumulative_pnl': live_cumulative_pnl
                    })
                    print(f"[{dt_now.strftime('%H:%M:%S')}] 多头entry_price盈利平仓 | entry_price: {entry_price:.2f} | 当前价: {current_price:.2f} | 平仓{actual_close_volume}手 | 盈亏: {close_pnl:+.2f} | 累计: {live_cumulative_pnl:.2f}")
                    api.insert_order(symbol=SYMBOL, direction="SELL", offset="CLOSE", volume=actual_close_volume)
                    
                    # 如果全部平仓，重置entry_price
                    if actual_close_volume == position.pos_long:
                        entry_price = 0.0
                else:
                    print(f"[{dt_now.strftime('%H:%M:%S')}] 多头entry_price亏损不平仓 | entry_price: {entry_price:.2f} | 当前价: {current_price:.2f} | 浮亏: {entry_pnl:+.2f}")
            
            # 检查空头持仓基于entry_price的盈亏
            elif position.pos_short > 0:
                entry_pnl = entry_price - current_price
                if entry_pnl > 0:  # 基于entry_price盈利，平掉最近一次开仓数量
                    close_volume = VOLUME  # 平掉最近一次开仓的数量
                    actual_close_volume = min(close_volume, position.pos_short)  # 确保不超过实际持仓
                    
                    # 计算平仓盈亏
                    close_pnl = entry_pnl * actual_close_volume
                    live_cumulative_pnl += close_pnl
                    live_trades.append({
                        'time': dt_now,
                        'type': 'SHORT_CLOSE_ENTRY',
                        'price': current_price,
                        'pnl': close_pnl,
                        'cumulative_pnl': live_cumulative_pnl
                    })
                    print(f"[{dt_now.strftime('%H:%M:%S')}] 空头entry_price盈利平仓 | entry_price: {entry_price:.2f} | 当前价: {current_price:.2f} | 平仓{actual_close_volume}手 | 盈亏: {close_pnl:+.2f} | 累计: {live_cumulative_pnl:.2f}")
                    api.insert_order(symbol=SYMBOL, direction="BUY", offset="CLOSE", volume=actual_close_volume)
                    
                    # 如果全部平仓，重置entry_price
                    if actual_close_volume == position.pos_short:
                        entry_price = 0.0
                else:
                    print(f"[{dt_now.strftime('%H:%M:%S')}] 空头entry_price亏损不平仓 | entry_price: {entry_price:.2f} | 当前价: {current_price:.2f} | 浮亏: {entry_pnl:+.2f}")

        # 4. Execute trading decisions with new closing conditions and position limits
        if signal == 1:  # Buy signal
            # 检查多头持仓是否已达到最大限制
            current_long_position = position.pos_long
            if current_long_position >= MAX_POSITION:
                print(f"[{dt_now.strftime('%H:%M:%S')}] 多头持仓已达到最大限制 {MAX_POSITION}，不再开仓")
            else:
                if position.pos_short > 0:
                    # 计算空头浮动盈亏
                    short_open_price = position.open_price_short
                    if short_open_price > 0:
                        floating_pnl = (short_open_price - current_price) * position.pos_short
                        
                        if floating_pnl > 0:  # 持仓盈利，执行平仓
                            live_cumulative_pnl += floating_pnl
                            live_trades.append({
                                'time': dt_now,
                                'type': 'SHORT_CLOSE',
                                'price': current_price,
                                'pnl': floating_pnl,
                                'cumulative_pnl': live_cumulative_pnl
                            })
                            print(f"[{dt_now.strftime('%H:%M:%S')}] SHORT盈利平仓 | 盈亏: {floating_pnl:+.2f} | 累计: {live_cumulative_pnl:.2f}")
                            
                            # 平仓空头
                            api.insert_order(symbol=SYMBOL, direction="BUY", offset="CLOSE", volume=position.pos_short)
                            entry_price = 0.0
                            
                            # 开多头
                            entry_price = current_price
                            print(f"[{dt_now.strftime('%H:%M:%S')}] Opening LONG position at: {entry_price:.2f}")
                            api.insert_order(symbol=SYMBOL, direction="BUY", offset="OPEN", volume=VOLUME)
                            
                        else:  # 持仓亏损，不平仓，加仓反向
                            print(f"[{dt_now.strftime('%H:%M:%S')}] SHORT亏损不平仓 | 开仓价: {short_open_price:.2f} | 当前价: {current_price:.2f} | 浮亏: {floating_pnl:+.2f} | 加仓LONG")
                            # 反向开仓数量 = 当前持仓 + 1，但不超过最大限制
                            reverse_volume = min(position.pos_short + VOLUME, MAX_POSITION - current_long_position)
                            if reverse_volume > 0:
                                entry_price = current_price  # 更新入场价格为当前价格
                                print(f"[{dt_now.strftime('%H:%M:%S')}] Adding LONG position {reverse_volume} lots at: {entry_price:.2f}")
                                api.insert_order(symbol=SYMBOL, direction="BUY", offset="OPEN", volume=reverse_volume)
                            else:
                                print(f"[{dt_now.strftime('%H:%M:%S')}] 多头持仓接近最大限制，无法加仓")
                    else:
                        # 没有入场价格记录，直接开多头
                        entry_price = current_price
                        print(f"[{dt_now.strftime('%H:%M:%S')}] Opening LONG position at: {entry_price:.2f}")
                        api.insert_order(symbol=SYMBOL, direction="BUY", offset="OPEN", volume=VOLUME)
                else:
                    # 没有空头持仓，直接开多头
                    entry_price = current_price
                    print(f"[{dt_now.strftime('%H:%M:%S')}] Opening LONG position at: {entry_price:.2f}")
                    api.insert_order(symbol=SYMBOL, direction="BUY", offset="OPEN", volume=VOLUME)

        elif signal == -1:  # Sell signal
            # 检查空头持仓是否已达到最大限制
            current_short_position = position.pos_short
            if current_short_position >= MAX_POSITION:
                print(f"[{dt_now.strftime('%H:%M:%S')}] 空头持仓已达到最大限制 {MAX_POSITION}，不再开仓")
            else:
                if position.pos_long > 0:
                    # 计算多头浮动盈亏
                    long_open_price = position.open_price_long
                    if long_open_price > 0:
                        floating_pnl = (current_price - long_open_price) * position.pos_long
                        
                        if floating_pnl > 0:  # 持仓盈利，执行平仓
                            live_cumulative_pnl += floating_pnl
                            live_trades.append({
                                'time': dt_now,
                                'type': 'LONG_CLOSE',
                                'price': current_price,
                                'pnl': floating_pnl,
                                'cumulative_pnl': live_cumulative_pnl
                            })
                            print(f"[{dt_now.strftime('%H:%M:%S')}] LONG盈利平仓 | 盈亏: {floating_pnl:+.2f} | 累计: {live_cumulative_pnl:.2f}")
                            
                            # 平仓多头
                            api.insert_order(symbol=SYMBOL, direction="SELL", offset="CLOSE", volume=position.pos_long)
                            entry_price = 0.0
                            
                            # 开空头
                            entry_price = current_price
                            print(f"[{dt_now.strftime('%H:%M:%S')}] Opening SHORT position at: {entry_price:.2f}")
                            api.insert_order(symbol=SYMBOL, direction="SELL", offset="OPEN", volume=VOLUME)
                            
                        else:  # 持仓亏损，不平仓，加仓反向
                            print(f"[{dt_now.strftime('%H:%M:%S')}] LONG亏损不平仓 | 开仓价: {long_open_price:.2f} | 当前价: {current_price:.2f} | 浮亏: {floating_pnl:+.2f} | 加仓SHORT")
                            # 反向开仓数量 = 当前持仓 + 1，但不超过最大限制
                            reverse_volume = min(position.pos_long + VOLUME, MAX_POSITION - current_short_position)
                            if reverse_volume > 0:
                                entry_price = current_price  # 更新入场价格为当前价格
                                print(f"[{dt_now.strftime('%H:%M:%S')}] Adding SHORT position {reverse_volume} lots at: {entry_price:.2f}")
                                api.insert_order(symbol=SYMBOL, direction="SELL", offset="OPEN", volume=reverse_volume)
                            else:
                                print(f"[{dt_now.strftime('%H:%M:%S')}] 空头持仓接近最大限制，无法加仓")
                    else:
                        # 没有入场价格记录，直接开空头
                        entry_price = current_price
                        print(f"[{dt_now.strftime('%H:%M:%S')}] Opening SHORT position at: {entry_price:.2f}")
                        api.insert_order(symbol=SYMBOL, direction="SELL", offset="OPEN", volume=VOLUME)
                else:
                    # 没有多头持仓，直接开空头
                    entry_price = current_price
                    print(f"[{dt_now.strftime('%H:%M:%S')}] Opening SHORT position at: {entry_price:.2f}")
                    api.insert_order(symbol=SYMBOL, direction="SELL", offset="OPEN", volume=VOLUME)
        
        # 5. 定期显示累计盈亏统计 (每10分钟)
        if (pd.Timestamp.now() - last_summary_time).total_seconds() > 600:  # 10分钟
            current_floating_pnl = 0
            if position.pos_long > 0:
                long_open_price = position.open_price_long
                if long_open_price > 0:
                    current_floating_pnl = (current_price - long_open_price) * position.pos_long
            elif position.pos_short > 0:
                short_open_price = position.open_price_short
                if short_open_price > 0:
                    current_floating_pnl = (short_open_price - current_price) * position.pos_short
            
            total_pnl = live_cumulative_pnl + current_floating_pnl
            print(f"\n=== 盈亏统计 [{dt_now.strftime('%H:%M:%S')}] ===")
            print(f"历史累计盈亏: {live_cumulative_pnl:.2f} 点")
            print(f"当前浮动盈亏: {current_floating_pnl:+.2f} 点")
            print(f"总盈亏: {total_pnl:.2f} 点")
            print(f"实时交易次数: {len(live_trades)}")
            print(f"当前持仓: 多头 {position.pos_long}/{MAX_POSITION}, 空头 {position.pos_short}/{MAX_POSITION}")
            print("=" * 40 + "\n")
            
            last_summary_time = pd.Timestamp.now()