import os
import getpass
from crontab import CronTab

basedir=os.path.dirname(__file__)
username = getpass.getuser()

cron = CronTab(user=username)
cron.remove_all()


#
# job = cron.new(command="python /home/<USER>/rsitrader/speak_test.py")
# job.minute.every(10)
# cron.write()
# #
# job = cron.new(command="python /home/<USER>/rsitrader/clock_alarm.py")
# job.minute.every(15)
# job.hour.on(20)
# # job.minute.on(30)
# # job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
# job.dow.during('mon', 'fri')
# # cron.remove_all()
# cron.write()

programs = ['ma_pp_smartmanp.py', 'ma_pp_thj.py', 'ma_pp_tyc.py','ma_ag_bigwolf.py', 'trma_pp_bigwolf.py', 'trma_rb_bigwolf.py', "trma_oi_bigwolf.py"]
directory = '/home/<USER>/dingdangNo6/'

for p in programs:
    prejob = "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = prejob + '; python ' + os.path.join(basedir, p)

    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.hour.on(20)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    # job.dow.on()
    # cron.remove_all()
    cron.write()

for p in programs:
    prejob =  "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = prejob + '; python ' + directory + p
    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.hour.on(8)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    # job.dow.on()
    # cron.remove_all()
    cron.write()


for p in ["strategySMA.py", "rsi_strategy.py", "trma_oi_bigwolf.py"]:
    # prejob =  "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = 'python ' + directory + p
    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.hour.on(8)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    # job.dow.on()
    # cron.remove_all()
    cron.write()


for p in ["strategySMA.py", "rsi_strategy.py", "trma_oi_bigwolf.py"]:
    # prejob =  "ps -ef | grep " + p + r" | grep -v grep | awk '{print $2}' | xargs kill -9"
    jobstr = 'python ' + directory + p
    job = cron.new(command=jobstr)
    # job.minute.every(15)
    job.hour.on(20)
    job.minute.on(45)
    job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
    # job.dow.on()
    # cron.remove_all()
    cron.write()


