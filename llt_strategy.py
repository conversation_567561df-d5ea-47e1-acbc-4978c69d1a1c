import pandas as pd
from tqsdk import TqApi, TqAuth, TqSim


# 1. 从Jupyter Notebook中提取的核心LLT计算函数
def cal_LLT(price: pd.Series, alpha: float):
    """
    计算低延迟趋势线 (LLT)
    """
    LLT = []
    price_value = price.values
    if len(price_value) < 2:
        return [float('nan')] * len(price_value)  # 数据不足时返回NaN

    LLT.append(price_value[0])
    LLT.append(price_value[1])

    for i, e in enumerate(price_value):
        if i > 1:
            v = (alpha - alpha ** 2 / 4) * e + (alpha ** 2 / 2) * price_value[i - 1] - \
                (alpha - 3 * (alpha ** 2) / 4) * price_value[i - 2] + 2 * \
                (1 - alpha) * LLT[i - 1] - (1 - alpha) ** 2 * LLT[i - 2]
            LLT.append(v)

    return LLT


# 2. TQSDK交易策略实现
# 定义策略参数
SYMBOL = "SHFE.rb2509"  # 交易合约代码，例如螺纹钢主连
PERIOD_DAY = 24 * 60 * 60  # 日线周期
PERIOD_MINUTE = 60
D_VALUE = 60  # LLT指标的参数d，对应alpha = 2/(d+1)
ALPHA = 2 / (D_VALUE + 1)
VOLUME = 1  # 每次下单手数

# 创建 TqApi 实例
# 使用TqSim()进行模拟交易，或替换为 TqAuth("快期账户", "账户密码") 进行实盘交易
api = TqApi(TqSim(), auth=TqAuth("walkquant", "ftp123"))

# 获取日线数据
klines = api.get_kline_serial(SYMBOL, duration_seconds=PERIOD_MINUTE, data_length=9999)
close_prices = pd.Series(klines.close)

# 计算LLT指标
llt_series = cal_LLT(close_prices, ALPHA)

# 计算LLT的差值（斜率）来判断趋势
# diff_llt > 0 表示上升趋势，信号为1 (做多)
# diff_llt < 0 表示下降趋势，信号为-1 (做空)
# 使用最后两个LLT值来判断当前信号
if llt_series[-1] > llt_series[-2]:
    signal = 1
elif llt_series[-1] < llt_series[-2]:
    signal = -1
else:
    signal = 0  # 趋势不明，保持不变
print(signal)

print("策略开始运行，等待K线生成...")

while True:
    api.wait_update()

    # 当K线发生变化时执行策略逻辑
    if api.is_changing(klines.iloc[-1], "datetime"):
        # 将收盘价序列转换为pandas Series
        close_prices = pd.Series(klines.close)

        # 计算LLT指标
        llt_series = cal_LLT(close_prices, ALPHA)

        # 计算LLT的差值（斜率）来判断趋势
        # diff_llt > 0 表示上升趋势，信号为1 (做多)
        # diff_llt < 0 表示下降趋势，信号为-1 (做空)
        # 使用最后两个LLT值来判断当前信号
        if llt_series[-1] > llt_series[-2]:
            signal = 1
        elif llt_series[-1] < llt_series[-2]:
            signal = -1
        else:
            signal = 0  # 趋势不明，保持不变

        # 获取当前持仓
        position = api.get_position(SYMBOL)

        print(f"最新K线时间: {klines.datetime.iloc[-1]}")
        print(f"最新收盘价: {klines.close.iloc[-1]:.2f}")
        print(f"LLT值: {llt_series[-1]:.2f}")
        print(f"交易信号: {signal}")
        print(f"当前多头持仓: {position.pos_long}, 空头持仓: {position.pos_short}")

        # 交易逻辑
        if signal == 1:  # 看多信号
            if position.pos_short > 0:  # 如果有空仓，先平仓
                print("平空仓")
                api.close_position(SYMBOL, offset="CLOSE", volume=position.pos_short)
            if position.pos_long == 0:  # 如果没有多仓，则开仓
                print("开多仓")
                api.insert_order(symbol=SYMBOL, direction="BUY", offset="OPEN", volume=VOLUME)

        elif signal == -1:  # 看空信号
            if position.pos_long > 0:  # 如果有多仓，先平仓
                print("平多仓")
                api.close_position(SYMBOL, offset="CLOSE", volume=position.pos_long)
            if position.pos_short == 0:  # 如果没有空仓，则开仓
                print("开空仓")
                api.insert_order(symbol=SYMBOL, direction="SELL", offset="OPEN", volume=VOLUME)

        print("--------------------")

# 关闭api连接（在实际循环策略中，此行通常不会执行）
# api.close()