import pandas as pd
import numpy as np
from datetime import datetime


class PriceActionStrategy:
    def __init__(self, lookback_period: int = 20, breakout_threshold: float = 0.02):
        self.lookback_period = lookback_period
        self.breakout_threshold = breakout_threshold

    def identify_support_resistance(self, data: pd.DataFrame) -> tuple[float, float]:
        period_low = data['low'].rolling(window=self.lookback_period).min()
        period_high = data['high'].rolling(window=self.lookback_period).max()

        support = period_low.iloc[-1]
        resistance = period_high.iloc[-1]

        return support, resistance

    def detect_pin_bar(self, data: pd.DataFrame) -> bool:
        row = data.iloc[-1]
        body = abs(row['close'] - row['open'])
        upper_wick = row['high'] - max(row['open'], row['close'])
        lower_wick = min(row['open'], row['close']) - row['low']
        total_length = row['high'] - row['low']

        if lower_wick > 2 * body and upper_wick < 0.2 * total_length:
            return True
        return False

    def detect_engulfing(self, data: pd.DataFrame) -> tuple[bool, bool]:
        current = data.iloc[-1]
        previous = data.iloc[-2]

        bullish_engulfing = (current['open'] < previous['close'] and
                             current['close'] > previous['open'] and
                             current['close'] > current['open'])

        bearish_engulfing = (current['open'] > previous['close'] and
                             current['close'] < previous['open'] and
                             current['close'] < current['open'])

        return bullish_engulfing, bearish_engulfing

    def generate_signals(self, data: pd.DataFrame) -> list[dict]:
        signals = []
        support, resistance = self.identify_support_resistance(data)

        current_price = data['close'].iloc[-1]
        current_time = data.index[-1]

        if current_price > resistance * (1 + self.breakout_threshold):
            signals.append({
                'datetime': current_time,
                'type': 'LONG',
                'reason': 'Resistance breakout',
                'price': current_price,
                'stop_loss': resistance
            })

        elif current_price < support * (1 - self.breakout_threshold):
            signals.append({
                'datetime': current_time,
                'type': 'SHORT',
                'reason': 'Support breakdown',
                'price': current_price,
                'stop_loss': support
            })

        if self.detect_pin_bar(data):
            if current_price < support * 1.1:
                signals.append({
                    'datetime': current_time,
                    'type': 'LONG',
                    'reason': 'Bullish pin bar near support',
                    'price': current_price,
                    'stop_loss': data['low'].iloc[-1]
                })

        bullish_engulfing, bearish_engulfing = self.detect_engulfing(data)
        if bullish_engulfing:
            signals.append({
                'datetime': current_time,
                'type': 'LONG',
                'reason': 'Bullish engulfing pattern',
                'price': current_price,
                'stop_loss': data['low'].iloc[-1]
            })
        elif bearish_engulfing:
            signals.append({
                'datetime': current_time,
                'type': 'SHORT',
                'reason': 'Bearish engulfing pattern',
                'price': current_price,
                'stop_loss': data['high'].iloc[-1]
            })

        return signals

    def backtest(self, data: pd.DataFrame, initial_capital: float = 100000.0) -> tuple[pd.DataFrame, dict]:
        """
        回测策略表现并返回详细的交易记录和统计数据
        """
        trades = []
        capital = initial_capital
        position = None

        for i in range(self.lookback_period, len(data)):
            window = data.iloc[i - self.lookback_period:i + 1]
            current_price = data['close'].iloc[i]
            current_time = data.index[i]

            # 检查止损
            if position is not None:
                if (position['type'] == 'LONG' and current_price < position['stop_loss']) or \
                        (position['type'] == 'SHORT' and current_price > position['stop_loss']):
                    # 计算收益
                    pnl = position['size'] * (current_price - position['price']) if position['type'] == 'LONG' \
                        else position['size'] * (position['price'] - current_price)

                    trades.append({
                        'entry_time': position['datetime'],
                        'exit_time': current_time,
                        'type': position['type'],
                        'entry_price': position['price'],
                        'exit_price': current_price,
                        'size': position['size'],
                        'pnl': pnl,
                        'reason': 'Stop Loss',
                        'capital': capital + pnl
                    })
                    capital += pnl
                    position = None

            # 生成新信号
            if position is None:  # 只在没有持仓时寻找新信号
                signals = self.generate_signals(window)
                if signals:
                    signal = signals[0]  # 取第一个信号
                    position_size = capital * 0.1  # 使用10%资金
                    position = {
                        'datetime': current_time,
                        'type': signal['type'],
                        'price': current_price,
                        'size': position_size,
                        'stop_loss': signal['stop_loss'],
                        'reason': signal['reason']
                    }

        # 转换交易记录为DataFrame
        trades_df = pd.DataFrame(trades)

        # 计算策略统计数据
        stats = self._calculate_statistics(trades_df, initial_capital)

        return trades_df, stats

    def _calculate_statistics(self, trades_df: pd.DataFrame, initial_capital: float) -> dict:
        """计算策略统计数据"""
        if len(trades_df) == 0:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0,
                'total_pnl': 0,
                'max_drawdown': 0,
                'sharpe_ratio': 0
            }

        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] <= 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        total_pnl = trades_df['pnl'].sum()

        # 计算最大回撤
        capital_series = trades_df['capital']
        rolling_max = capital_series.expanding().max()
        drawdowns = (capital_series - rolling_max) / rolling_max
        max_drawdown = drawdowns.min() if len(drawdowns) > 0 else 0

        # 计算夏普比率（简化版，假设无风险利率为0）
        if len(trades_df) > 1:
            returns = trades_df['pnl'] / initial_capital
            sharpe_ratio = np.sqrt(252) * (returns.mean() / returns.std()) if returns.std() != 0 else 0
        else:
            sharpe_ratio = 0

        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio
        }


# 读取和处理数据
def prepare_data(file_path: str) -> pd.DataFrame:
    # 读取CSV文件
    df = pd.read_csv(file_path)

    # 将datetime列转换为索引
    df['datetime'] = pd.to_datetime(df['datetime'].astype(float), unit='ns')
    df.set_index('datetime', inplace=True)

    return df


# 主函数
def main():
    # 读取数据
    data = prepare_data('data15.csv')

    # 创建策略实例
    strategy = PriceActionStrategy(lookback_period=20, breakout_threshold=0.02)

    # 运行回测
    trades_df, stats = strategy.backtest(data, initial_capital=100000.0)

    # 打印回测结果
    print("\n=== 交易统计 ===")
    print(f"总交易次数: {stats['total_trades']}")
    print(f"盈利交易: {stats['winning_trades']}")
    print(f"亏损交易: {stats['losing_trades']}")
    print(f"胜率: {stats['win_rate']:.2%}")
    print(f"总盈亏: {stats['total_pnl']:.2f}")
    print(f"最大回撤: {stats['max_drawdown']:.2%}")
    print(f"夏普比率: {stats['sharpe_ratio']:.2f}")

    if len(trades_df) > 0:
        print("\n=== 交易明细 ===")
        print(trades_df[['entry_time', 'exit_time', 'type', 'entry_price',
                         'exit_price', 'pnl', 'reason']].head())


if __name__ == "__main__":
    main()