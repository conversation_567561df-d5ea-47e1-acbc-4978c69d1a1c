import os
import getpass
from crontab import CronTab
directory=os.getcwd()+r'/'
username = getpass.getuser()

cron = CronTab(user=username)
cron.remove_all()
cron.write()


#
# job = cron.new(command="python /home/<USER>/rsitrader/speak_test.py")
# job.minute.every(10)
# cron.write()
# #
# job = cron.new(command="python /home/<USER>/rsitrader/clock_alarm.py")
# job.minute.every(15)
# job.hour.on(20)
# # job.minute.on(30)
# # job.dow.on('mon', 'tue', 'wed', 'thu', 'fri')
# job.dow.during('mon', 'fri')
# # cron.remove_all()

