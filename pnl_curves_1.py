import json
import matplotlib.pyplot as plt
import ipywidgets as widgets
from IPython.display import display

# File path
file_path = 'simulatedaysummary_new.json'

# Read the JSON data from the file
with open(file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

# Extract dates and balances for each account
account_balances = {}
for date, accounts in data.items():
    for account in accounts:
        user_name = account['user_name']
        balance = account['balance']
        if user_name not in account_balances:
            account_balances[user_name] = {'dates': [], 'balances': []}
        account_balances[user_name]['dates'].append(date)
        account_balances[user_name]['balances'].append(balance)

# Create a dropdown menu for account selection
account_dropdown = widgets.Dropdown(
    options=list(account_balances.keys()),
    description='Account:',
    disabled=False,
)

# Function to update the plot based on selected account
def update_plot(account_name):
    plt.figure(figsize=(12, 8))
    values = account_balances[account_name]
    plt.plot(values['dates'], values['balances'], label=account_name)
    plt.xlabel('Date')
    plt.ylabel('Balance')
    plt.title(f'Net Value Curve for {account_name}')
    plt.legend()
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

# Display the dropdown menu and plot
widgets.interactive(update_plot, account_name=account_dropdown)
display(account_dropdown)
update_plot(account_dropdown.value)