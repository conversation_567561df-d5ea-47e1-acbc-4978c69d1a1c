import requests
from bs4 import BeautifulSoup


def get_rape_oil_price():
    # 生意社菜籽油价格页面（需要确认实际URL）
    url = 'http://www.100ppi.com/mprice/detail-26.html'  # 示例URL，可能需要调整

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        # 方法1：使用string参数替代已弃用的text参数
        price_element = soup.find(string='菜籽油')  # 查找包含"菜籽油"文本的节点

        # 方法2：或者使用更精确的CSS选择器
        # price_element = soup.select_one('td:contains("菜籽油")')  # 如果使用lxml解析器

        if price_element:
            # 根据实际HTML结构找到价格所在的相邻元素
            # 这里需要根据实际页面结构调整
            price = price_element.find_parent('tr').find_all('td')[1].text.strip()
            return f"今日菜籽油价格: {price}"
        else:
            return "未找到菜籽油价格信息"
    except requests.exceptions.RequestException as e:
        return f"请求失败: {str(e)}"
    except Exception as e:
        return f"解析失败: {str(e)}"


if __name__ == '__main__':
    print(get_rape_oil_price())