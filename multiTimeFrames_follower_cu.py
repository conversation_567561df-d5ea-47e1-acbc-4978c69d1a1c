import time

# 主程序
from tqsdk import Tq<PERSON><PERSON>, TqAuth, TqKq
from multiTimeFrames_base import MultiTimeframeStrategy
from utils.utils import parse_time, get_time_period, is_trading_time

if __name__ == "__main__":
    account = "follower,ftp123"
    api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)

    try:
        # product_id = 'OI'
        # product_id = 'rb'
        # product_id = 'SA'
        product_id = 'cu'

        symbol = api.query_cont_quotes(product_id=product_id)[0]
        # symbol = 'CZCE.OI509'
        print(f"交易的合约是: {symbol}")
        time_periods = get_time_period(api, symbol)
        posfile_name = f"{account.split(',')[0]}.json"
        # 定义交易周期（以秒为单位）和每个周期的持仓限制
        # timeframes = [i * 60 for i in range(1, 16)]
        # max_positions = {k: {'long': 0, 'short': 3} for k in timeframes}
        max_positions = {
            15: {'long': 2, 'short': 2},
            60: {'long': 0, 'short': 0},
            180: {'long': 0, 'short': 0},
            300: {'long': 0, 'short': 0},
            800: {'long': 0, 'short': 0},
            900: {'long': 0, 'short': 0}
        }

        while True:

            if is_trading_time(time_periods):
                try:
                    api = TqApi(TqKq(), auth=account, disable_print=True, debug=None)
                    # symbol = api.query_cont_quotes(product_id=product_id)[0]
                    # symbol = 'CZCE.OI503'
                    strategy = MultiTimeframeStrategy(api, symbol, max_positions, positions_file=posfile_name)

                    while True:
                        api.wait_update()
                        strategy.update()

                except Exception as e:
                    print(e)
                    time.sleep(10)


            else:
                print('非交易时间:', time.asctime())
                time.sleep(1)
                # continue

    except KeyboardInterrupt:
        print("策略已停止")

    finally:
        api.close()
