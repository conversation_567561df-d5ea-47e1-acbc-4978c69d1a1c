'''
叮当6号solid 2017/06/07

//仓比:=30,NODRAW;
N:=23;
KNUM:=COUNT(C>0,0);
初始:REF(MONEYREAL/10000,KNUM-1),NODRAW;
当前..MONEYREAL/10000,COLORCYAN,NODRAW;
//最小..LV(当前,0);
//最大..HV(当前,0);

NN:=BARSLAST(DATE<>REF(DATE,1))+1;//当天开盘一共走了多少根K线
KH:=HHV(NN,0),NODRAW;//当前周期一天的K线数

SIGSUM:COUNTSIG(BPK,KH)+COUNTSIG(SP,KH)+COUNTSIG(SPK,KH)+COUNTSIG(BP,KH)+COUNTSIG(BK,KH)+COUNTSIG(SK,KH),NODRAW;
//最近一天内发出的信号数量

HSIG:HV(SIGSUM,0),NODRAW;
LSIG:=LV(SIGSUM,0),NODRAW;


N1:=BARSLAST(DATE<>REF(DATE,1))+1;
N2:=REF(N1,N1);
开盘:VALUEWHEN(N1=1,O),COLORWHITE,LINETHICK3,NODRAW;
CH:=HV(H,N),LINETHICK2;
CL:=LV(L,N),LINETHICK2;
MID:=(CH+CL)/2;

NC..(CH-CL)/((CH+CL)*0.5)*100,COLORYELLOW,BOLD, NODRAW;


SIGALL:=COUNT(CROSS(C,MID),0);
AVSIGP..KNUM/SIGALL,NODRAW;
SIGSUM2..COUNTSIG(BPK,AVSIGP)+COUNTSIG(SP,AVSIGP)+COUNTSIG(SPK,AVSIGP)+COUNTSIG(BP,AVSIGP)+COUNTSIG(BK,AVSIGP)+COUNTSIG(SK,AVSIGP),NODRAW;
SIGSUM3..COUNT(CROSS(C,MID),AVSIGP)+COUNT(CROSSDOWN(C,MID),AVSIGP),NODRAW;
SIGSUM4:=SIGSUM3*CC;
SIGSUM5..VALUEWHEN(CROSS(C,MID) OR CROSSDOWN(C,MID),SIGSUM4),NODRAW;

PP:MIN((SIGSUM5+1)*10,80),NODRAW;
SETDEALPERCENT(PP);


CHUP:=MID+2*(CH-MID)/3,COLORWHITE;
CHDN:=MID-2*(CH-MID)/3,COLORWHITE;

IF C>=开盘 THEN
 BEGIN
   BKVOL=0 && C>MID,BK;
   CROSS(C,MID),BPK;
	CROSSDOWN(C,MID),SP;
  END

ELSE
//IF C<开盘 THEN
   BEGIN
   SKVOL=0 &&C<MID,SK;

   CROSSDOWN(C,MID),SPK;
   CROSS(C,MID),BP;
   END

AUTOFILTER;


//振幅计算
PV:SUM(H-L,0)/COUNT(C>0,0),NODRAW;
PH:HHV(H-L,0),NODRAW;
PL:=LLV(H-L,0),NODRAW;

/*
//#IMPORT [DAY,1,INSSTATS] AS VAR;
#IMPORT[DAY,1,INSSTATS] AS VAR
DV:VAR.PVIB,COLORYELLOW,LINETHICK2,NODRAW;
DH:VAR.HVIB,NODRAW;
DL:VAR.LVIB,NODRAW;
*/
//缺口计算



HH2:=REF(HHV(H,N2),N2+N1);//前日高点
LL2:=REF(LLV(L,N2),N2+N1);//前日低点
前日:HH2-LL2,NODRAW;

HH1:=REF(HHV(H,N2),N1);//昨日高点
LL1:=REF(LLV(L,N2),N1);//昨日低点
昨振:HH1-LL1,NODRAW;

平均:(前日+昨振)/2,COLORGREEN,NODRAW;

今振:HHV(H,N1)-LLV(L,N1),COLORYELLOW,NODRAW;

今高:=HHV(H,N1),NODRAW;
今低:=LLV(L,N1),NODRAW;
BB:(C-今低)/(今高-今低),NODRAW;

昨收:=REF(C,N1),NODRAW;
ZO:=REF(O,N1);
ZH:=REF(H,N1);
ZL:=REF(L,N1);
//昨日振幅:ZH-ZL,NODRAW;

缺口:(开盘-昨收),NODRAW;
ZD:=缺口/昨收;
//QKQK: 1,高开，-1，低开，0，无缺口
QKQK:=IFELSE(ABS(缺口)>0,IFELSE(缺口>0,1,-1),0);
回补:IFELSE(QKQK=1,IFELSE(今低<=昨收,1,0),IFELSE(今高>=昨收,1,0)),NODRAW;
网格:平均*0.8/8,NODRAW;

ALL:COUNT(C>0,0),NODRAW;
YANG:COUNT(C>O,0),NODRAW;
YIN:COUNT(C<O,0),NODRAW;
HHL:HHV(H-L,0),NODRAW;
LHL:LLV(H-L,0),NODRAW;

AVHL:SUM(H-L,0)/ALL,NODRAW;
'''
import copy
from myfunction import *

from tqsdk.tafunc import hhv, llv, crossup, crossdown, count, time_to_str
from tradefuncs import *
from speaktext import speak_text
from loguru import logger as mylog

from time import sleep
import time

mylog.add('dingdangNo6.log', encoding='utf-8')


def dingdang(bars, period):
    strategy = '叮当六号'
    O = bars.open
    H = bars.high
    L = bars.low
    C = bars.close

    CH = HHV(H, period)
    CL = LLV(L, period)
    MID = (CH + CL) / 2

    signalList = cross(C, dingdangline)
    buysigdetail = CROSS(C, dingdangline)
    sellsigdetail = CROSS(dingdangline, C)

    # try:
    #     knum = len(H)
    #     signalall = buysigdetail.count(True) * 2
    #     avsigp = int(knum / signalall)
    #
    # if buysigdetail[-1]:
    #     print(SYMBOL, '发出买入信号。。。')
    #     speak_text(strategy + SYMBOL + '发出买入信号。')
    #     # playsound('2.wav')
    #     print(SYMBOL, '最近的买入信号:', avsigp, buysigdetail[(knum - avsigp):])
    #     # sleep(1)
    #
    #     sleep(2)
    # elif sellsigdetail[-1]:
    #     print(trading_symbol, '发出卖出信号。。。', str(quote.last_price))
    #     speak_text(strategy + trading_symbol + '发出卖出信号。')
    #     print(trading_symbol, '最近的卖出信号:', avsigp, sellsigdetail[(knum - avsigp):])
    #     # sleep(1)

    # except:
    #     pass

    # print(buysigdetail.count(True))
    # print(sellsigdetail.count(True))
    # print(dingdangline[(knum - avsigp):])
    # print(C[(knum - avsigp):])

    return dingdangline


def dingdangno6solid(api, symbol, interval=900):
    savebars = False
    quote = api.get_quote(symbol)

    if '@' in symbol:
        SYMBOL = quote.underlying_symbol
    else:
        SYMBOL = symbol

    trading_symbol = SYMBOL.split('.')[1]

    ticks = api.get_tick_serial(SYMBOL)
    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines

    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()
    hl = daybar.high - daybar.low
    ho = daybar.high - daybar.open
    ol = daybar.open - daybar.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    N = 23
    O = klines1.open
    H = klines1.high
    L = klines1.low
    C = klines1.close

    CH = hhv(H, N)
    CL = llv(L, N)
    MID = (CH + CL) / 2
    dingdangline = MID
    NC = (CH - CL) / ((CH + CL) * 0.5) * 100

    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    sigdetails = crossup(C, MID)
    sigall = count(sigdetails, 0)
    KNUM = len(klines1)
    SIGALL = sigall.iloc[-1]
    sigavperiod = int(KNUM / SIGALL)
    gapcover = False

    position = api.get_position(SYMBOL)
    acc = api.get_account()
    bkvol = position.pos_long
    bkvol_yd = position.pos_long_his
    bkvol_td = position.pos_long_today

    skvol = position.pos_short
    skvol_yd = position.pos_short_his
    skvol_td = position.pos_short_today

    skflag = True
    bkflag = True
    spflag = True
    bpflag = True
    speakyn = True
    dayopensp = True
    dayopenbp = True

    fundavailable = acc.available
    disp_day_info(quote, daymean, last3mean, hlmax)

    while True:

        api.wait_update()

        if api.is_changing(daybars.iloc[-1], "datetime"):
            savebars = True
            dayopensp = True
            dayopenbp = True

            hl = daybar.high - daybar.low
            ho = daybar.high - daybar.open
            ol = daybar.open - daybar.low
            daymean = int(hl.mean())
            homean = ho.mean()
            olmean = ol.mean()
            hlmax = max(hl)
            last3mean = int(hl.iloc[-4:-1].mean())
            dayopen = quote.open
            dayhigh = quote.highest
            daylow = quote.lowest
            preclose = quote.pre_close
            dayvib = dayhigh - daylow
            gap = dayopen - preclose
            gapcover = False
            print('日线数据更新完成。。。')
            mylog.info('日线数据更新完成。。。')
        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            # 恢复下单标志, 确保k线周期内如果有多个信号的话,执行一次.
            disp_day_info(quote, daymean, last3mean, hlmax)
            speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                klines1 = klines1.append(newk)

            O = klines1.open
            H = klines1.high
            L = klines1.low
            C = klines1.close

            CH = hhv(H, N)
            CL = llv(L, N)
            MID = (CH + CL) / 2
            dingdangline = (CH + CL) / 2
            NC = (CH - CL) / ((CH + CL) * 0.5) * 100

            # dingdangline = dingdang(klines1, N)
            print(SYMBOL, '叮当数值:', dingdangline.iloc[-1], '现价:', quote.last_price, '多单:', bkvol, '其中：昨仓:', bkvol_yd, '今仓:', bkvol_td, '空单:', skvol, '其中：昨仓:', skvol_yd, '今仓:', skvol_td, '净值:',
                  acc.balance)
            print(' ')
            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today

            # 交易部分
            ups = crossup(C, dingdangline)
            dns = crossdown(C, dingdangline)
            bkdist = be_apart_from(ups.tolist())
            skdist = be_apart_from(dns.tolist())
            if bkdist > skdist:
                signalnow = 'SK'
                distnow = skdist
            else:
                signalnow = 'BK'
                distnow = bkdist

            signalprice = C.iloc[-distnow]
            sigfloatprofit = quote.last_price - signalprice if signalnow == 'BK' else signalprice - quote.last_price
            print(SYMBOL, interval,  '当前信号:', signalnow, '持续周期:', distnow, '信号价格:', signalprice, '现价:', quote.last_price, '信号盈亏:', sigfloatprofit)
            print('upsignal:  ', ups.iloc[-40:].tolist())
            print('downsignal:', dns.iloc[-40:].tolist())
            if C.iloc[-2] >= dayopen:
                if skvol > 0 and dayopenbp and position.position_profit_short > 10:  # 观察看看有什么问题
                    if position.pos_short_today > 0:
                        BP(api, symbol=SYMBOL, order_price=quote.last_price, volume=1, today=True)
                        mylog.info(['bp', SYMBOL, quote.last_price, ])

                    else:
                        BP(api, symbol=SYMBOL, order_price=quote.last_price, volume=1, today=False)
                        mylog.info(['bp', SYMBOL, quote.last_price, ])
                    mylog.info(['价格大于开盘价，持仓空单大于0，平空单。', 'bp', SYMBOL, quote.last_price, 1])

                    dayopenbp = False

                if bkvol == 0 and C.iloc[-2] > dingdangline.iloc[-1] and bkflag and C.iloc[-2] - dayopen < 6:
                    BK(api, symbol=SYMBOL, order_price=quote.last_price, volume=1)
                    mylog.info(['bk', SYMBOL, quote.last_price, 1])
                    bkflag = False

                if ups.iloc[-1]:
                    if skvol > 0 :
                        BPK(api, symbol=SYMBOL, order_price=quote.last_price, volume=1)
                    else:
                        BK(api, symbol=SYMBOL, order_price=quote.last_price, volume=1)

                    mylog.info(['bpk', SYMBOL, quote.last_price, 1])

                if dns.iloc[-1]:
                    SP(api, symbol=SYMBOL, order_price=quote.last_price, volume=1)
                    mylog.info(['sp', SYMBOL, quote.last_price, 1])

            if C.iloc[-2] < dayopen:
                if bkvol > 0 and dayopensp and position.position_profit_long > 10:
                    if position.pos_long_today > 0:
                        SP(api, symbol=SYMBOL, order_price=quote.last_price, volume=1, today=True)

                    else:
                        SP(api, symbol=SYMBOL, order_price=quote.last_price, volume=1, today=False)
                        mylog.info(['sp', SYMBOL, quote.last_price, 1])

                    mylog.info(['价格小于开盘价，持仓多单大于0，平多单。', 'sp', SYMBOL, quote.last_price, 1])
                    dayopensp = False

                if skvol == 0 and C.iloc[-1] < dingdangline.iloc[-1] and dayopen - C.iloc[-1] < 6 and acc.available>30000:
                    SK(api, symbol=SYMBOL, order_price=quote.last_price, volume=1)
                    mylog.info(['sk', SYMBOL, quote.last_price, 1])

                if dns.iloc[-1] and acc.available >30000:
                    if bkvol > 0:
                        SPK(api, symbol=SYMBOL, order_price=quote.last_price, volume=1)
                    else:
                        SK(api, symbol=SYMBOL, order_price=quote.last_price, volume=1)

                    mylog.info(['spk', SYMBOL, quote.last_price, 1])

        if api.is_changing(quote):
            if gap > 0:
                if quote.lowest < quote.pre_close:
                    gapcover = True
            else:
                if quote.highest > quote.pre_close:
                    gapcover = True

            # print(quote.datetime, trading_symbol, '缺口：', quote.open-quote.pre_close, '回补:', gapcover, '前收盘:', quote.pre_close, '开盘:', quote.open, '现价:', quote.last_price, '日内价差：', daymean, '三日平均：', last3mean, '今日价差：',
            #       quote.highest - quote.lowest)
            # disp_day_info(quote, daymean, last3mean)

        if api.is_changing(position):
            bkvol = position.pos_long
            bkvol_yd = position.pos_long_his
            bkvol_td = position.pos_long_today

            skvol = position.pos_short
            skvol_yd = position.pos_short_his
            skvol_td = position.pos_short_today

        if time.localtime().tm_hour > 15 and time.localtime().tm_min > 15 and savebars:
            klines1.to_csv(SYMBOL + '.csv')
            savebars = False

    pass


if __name__ == "__main__":
    from tqsdk import TqApi, TqKq

    symbol = 'SHFE.ag2105'
    # symbol = 'CZCE.SR101'
    # symbol = 'DCE.pp2105'
    symname = symbol.rsplit('.', 1)[1]
    interval = 60
    bklimit = 50
    sklimit = 50
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth="smartmanp,ftp123")

    dingdangno6solid(api, symbol, interval)
