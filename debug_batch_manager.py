"""
调试批量管理器问题
"""

import subprocess
import sys
import time
import os


def test_direct_command():
    """直接测试命令"""
    print("=" * 50)
    print("直接测试策略命令")
    print("=" * 50)
    
    cmd = [
        sys.executable,
        "TimeRoseMA_cross_ag_MultiTimeFrames.py",
        "ag",
        "schedule"
    ]
    
    print(f"测试命令: {' '.join(cmd)}")
    
    try:
        # 直接运行命令，不重定向输出
        process = subprocess.Popen(cmd)
        
        print(f"进程启动，PID: {process.pid}")
        
        # 等待5秒
        time.sleep(5)
        
        # 检查进程状态
        if process.poll() is None:
            print("进程仍在运行")
            process.terminate()
            process.wait()
            print("进程已终止")
        else:
            print(f"进程已退出，返回码: {process.returncode}")
            
    except Exception as e:
        print(f"测试失败: {e}")


def test_with_output_capture():
    """测试输出捕获"""
    print("=" * 50)
    print("测试输出捕获")
    print("=" * 50)
    
    cmd = [
        sys.executable,
        "TimeRoseMA_cross_ag_MultiTimeFrames.py",
        "ag",
        "schedule"
    ]
    
    print(f"测试命令: {' '.join(cmd)}")
    
    try:
        # 捕获输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"进程启动，PID: {process.pid}")
        
        # 等待5秒
        time.sleep(5)
        
        # 检查进程状态
        if process.poll() is None:
            print("进程仍在运行")
            process.terminate()
            stdout, stderr = process.communicate(timeout=5)
        else:
            print(f"进程已退出，返回码: {process.returncode}")
            stdout, stderr = process.communicate()
        
        print("标准输出:")
        print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
        
        if stderr:
            print("错误输出:")
            print(stderr[:500] + "..." if len(stderr) > 500 else stderr)
            
    except Exception as e:
        print(f"测试失败: {e}")


def test_simple_strategy():
    """测试简单策略"""
    print("=" * 50)
    print("测试简单策略")
    print("=" * 50)
    
    cmd = [
        sys.executable,
        "TimeRoseMA_cross_ag_MultiTimeFrames.py",
        "ag",
        "optimized"
    ]
    
    print(f"测试命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"进程启动，PID: {process.pid}")
        
        # 等待3秒
        time.sleep(3)
        
        # 检查进程状态
        if process.poll() is None:
            print("进程仍在运行")
            process.terminate()
            stdout, stderr = process.communicate(timeout=5)
        else:
            print(f"进程已退出，返回码: {process.returncode}")
            stdout, stderr = process.communicate()
        
        print("输出:")
        print(stdout[:300] + "..." if len(stdout) > 300 else stdout)
        
        if stderr:
            print("错误:")
            print(stderr[:300] + "..." if len(stderr) > 300 else stderr)
            
    except Exception as e:
        print(f"测试失败: {e}")


def test_batch_manager_logic():
    """测试批量管理器逻辑"""
    print("=" * 50)
    print("测试批量管理器逻辑")
    print("=" * 50)
    
    # 模拟批量管理器的启动逻辑
    cmd = [
        sys.executable,
        "TimeRoseMA_cross_ag_MultiTimeFrames.py",
        "ag",
        "schedule",
        "quant_ggh,Qiai1301"
    ]
    
    print(f"批量管理器命令: {' '.join(cmd)}")
    
    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, "ag_debug.log")
    
    try:
        # 启动进程并重定向到日志文件
        with open(log_file, 'w', encoding='utf-8') as f:
            process = subprocess.Popen(
                cmd,
                stdout=f,
                stderr=subprocess.STDOUT,
                text=True
            )
        
        print(f"进程启动，PID: {process.pid}")
        print(f"日志文件: {log_file}")
        
        # 等待5秒
        time.sleep(5)
        
        # 检查进程状态
        if process.poll() is None:
            print("进程仍在运行")
            process.terminate()
            process.wait()
            print("进程已终止")
        else:
            print(f"进程已退出，返回码: {process.returncode}")
        
        # 读取日志文件
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            print("日志内容:")
            print(log_content[:500] + "..." if len(log_content) > 500 else log_content)
        else:
            print("日志文件不存在")
            
    except Exception as e:
        print(f"测试失败: {e}")


def main():
    """主函数"""
    print("批量管理器调试")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        
        if test_type == "direct":
            test_direct_command()
        elif test_type == "capture":
            test_with_output_capture()
        elif test_type == "simple":
            test_simple_strategy()
        elif test_type == "batch":
            test_batch_manager_logic()
        else:
            print("可用测试:")
            print("  python debug_batch_manager.py direct   # 直接测试")
            print("  python debug_batch_manager.py capture  # 输出捕获测试")
            print("  python debug_batch_manager.py simple   # 简单策略测试")
            print("  python debug_batch_manager.py batch    # 批量管理器逻辑测试")
    else:
        # 运行所有测试
        test_simple_strategy()
        test_batch_manager_logic()


if __name__ == "__main__":
    main()
