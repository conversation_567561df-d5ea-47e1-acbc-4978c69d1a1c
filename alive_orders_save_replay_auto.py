import schedule
import time
from tqsdk import TqApi, TqKq
import pickle

def save_alive_orders(api):
    orders = api.get_order()
    orderlist = []

    for order in orders.items():
        orderlist.append(order)

    orderlist1 = []
    for o in orderlist:
        orderlist1.append(o[1])

    aliveorderlist = []
    for o in orderlist1:
        if o.status == 'ALIVE':
            tmporder = [o.exchange_id, o.instrument_id, o.direction, o.offset, o.volume_orign, o.limit_price]
            if tmporder not in orderlist:
                aliveorderlist.append(tmporder)

    print('number of active orders:', len(aliveorderlist))

    if aliveorderlist:
        filename = api.get_account().user_id + '_alive_orders.pkl'
        with open(filename, 'wb') as f:
            pickle.dump(aliveorderlist, f)
        print('save alive orders done...')

def replay_alive_orders(api):
    userid = api.get_account().user_id
    filename = userid + '_alive_orders.pkl'
    with open(filename, 'rb') as file:
        orders = pickle.load(file)

    print('total orders:', len(orders))

    for o in orders:
        symbol = o[0] + '.' + o[1]
        direction = o[2]
        offset = o[3]
        volume = o[4]
        price = o[5]

        api.insert_order(symbol, direction=direction, offset=offset, limit_price=price, volume=volume)

# def run_tasks():
#     api = TqApi(TqKq(), auth="bigwolf,ftp123")
#
#     def task_save():
#         print("Executing save_alive_orders() at 15:03...")
#         save_alive_orders(api)
#
#     def task_replay():
#         print("Executing replay_alive_orders() at 20:55...")
#         replay_alive_orders(api)
#
#     # 定时任务
#     schedule.every().monday.to.friday.at("15:03").do(task_save)
#     schedule.every().monday.to.friday.at("20:55").do(task_replay)
#
#     print("Scheduler started. Waiting for tasks...")
#     while True:
#         schedule.run_pending()
#         time.sleep(1)

def run_tasks():
    api = TqApi(TqKq(), auth="bigwolf,ftp123")

    def task_save():
        print("Executing save_alive_orders() at 15:03...")
        save_alive_orders(api)

    def task_replay():
        print("Executing replay_alive_orders() at 20:55...")
        replay_alive_orders(api)

    # 定时任务
    schedule.every().monday.at("15:03").do(task_save)
    schedule.every().tuesday.at("15:03").do(task_save)
    schedule.every().wednesday.at("15:03").do(task_save)
    schedule.every().thursday.at("15:03").do(task_save)
    schedule.every().friday.at("15:03").do(task_save)

    schedule.every().monday.at("20:55").do(task_replay)
    schedule.every().tuesday.at("20:55").do(task_replay)
    schedule.every().wednesday.at("20:55").do(task_replay)
    schedule.every().thursday.at("20:55").do(task_replay)
    schedule.every().friday.at("20:55").do(task_replay)

    print("Scheduler started. Waiting for tasks...")
    while True:
        schedule.run_pending()
        time.sleep(1)
if __name__ == "__main__":
    run_tasks()