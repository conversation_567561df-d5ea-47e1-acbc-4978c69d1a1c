"""
多时间周期双边市场策略使用示例
展示不同的配置和使用方法
"""

from multi_timeframe_dual_market_strategy import MultiTimeFrameStrategy, StrategyConfig
from strategy_config import (
    TradingConfig, load_config_from_preset, load_config_for_symbol,
    print_config_info, PRESET_CONFIGS, SYMBOL_CONFIGS
)


def example_1_basic_usage():
    """示例1: 基本使用方法"""
    print("=" * 60)
    print("示例1: 基本使用方法")
    print("=" * 60)
    
    # 创建默认配置
    config = StrategyConfig()
    
    # 创建策略实例
    strategy = MultiTimeFrameStrategy(
        config=config,
        symbol='rb',  # 螺纹钢
        initial_capital=100000
    )
    
    # 运行策略（模拟模式）
    strategy.run_strategy(iterations=20)


def example_2_custom_parameters():
    """示例2: 自定义参数"""
    print("=" * 60)
    print("示例2: 自定义参数")
    print("=" * 60)
    
    # 创建自定义配置
    config = StrategyConfig(
        timeframes=[60, 900, 3600],  # 1分钟、15分钟、1小时
        short_ma_period=3,           # 短均线3周期
        long_ma_period=15,           # 长均线15周期
        max_position_ratio=0.08,     # 最大持仓8%
        max_leverage=2.5,            # 杠杆2.5倍
        stop_loss_periods=8,         # 止损8个周期
        commission_rate=0.0002,      # 手续费0.02%
        trailing_stop_enabled=True,  # 启用移动止损
        profit_threshold_ratio=1.5   # 盈利阈值1.5倍手续费
    )
    
    strategy = MultiTimeFrameStrategy(config, 'ag', 200000)  # 白银，20万资金
    strategy.run_strategy(iterations=15)


def example_3_preset_configs():
    """示例3: 使用预设配置"""
    print("=" * 60)
    print("示例3: 使用预设配置")
    print("=" * 60)
    
    # 保守型策略
    print("运行保守型策略...")
    conservative_config = load_config_from_preset("conservative")
    print_config_info(conservative_config)
    
    # 转换为StrategyConfig
    strategy_config = StrategyConfig(
        timeframes=conservative_config.timeframes,
        short_ma_period=conservative_config.short_ma_period,
        long_ma_period=conservative_config.long_ma_period,
        max_position_ratio=conservative_config.max_position_ratio,
        max_leverage=conservative_config.max_leverage,
        stop_loss_periods=conservative_config.stop_loss_periods,
        commission_rate=conservative_config.commission_rate,
        trailing_stop_enabled=conservative_config.trailing_stop_enabled,
        profit_threshold_ratio=conservative_config.profit_threshold_ratio
    )
    
    strategy = MultiTimeFrameStrategy(strategy_config, 'cu', 150000)  # 铜
    strategy.run_strategy(iterations=10)


def example_4_multiple_symbols():
    """示例4: 多品种对比"""
    print("=" * 60)
    print("示例4: 多品种对比")
    print("=" * 60)
    
    symbols = ['rb', 'ag', 'cu']
    results = {}
    
    for symbol in symbols:
        print(f"\n测试品种: {symbol}")
        
        # 为每个品种加载专门的配置
        trading_config = load_config_for_symbol(symbol)
        
        # 转换为策略配置
        strategy_config = StrategyConfig(
            timeframes=trading_config.timeframes,
            short_ma_period=trading_config.short_ma_period,
            long_ma_period=trading_config.long_ma_period,
            max_position_ratio=trading_config.max_position_ratio,
            max_leverage=trading_config.max_leverage,
            stop_loss_periods=trading_config.stop_loss_periods,
            commission_rate=trading_config.commission_rate,
            trailing_stop_enabled=trading_config.trailing_stop_enabled,
            profit_threshold_ratio=trading_config.profit_threshold_ratio
        )
        
        strategy = MultiTimeFrameStrategy(strategy_config, symbol, 100000)
        
        # 记录初始资金
        initial_capital = strategy.current_capital
        
        # 运行策略
        strategy.run_strategy(iterations=10)
        
        # 记录结果
        final_capital = strategy.current_capital
        return_rate = (final_capital - initial_capital) / initial_capital * 100
        results[symbol] = {
            'initial': initial_capital,
            'final': final_capital,
            'return': return_rate
        }
    
    # 显示对比结果
    print("\n" + "=" * 60)
    print("多品种对比结果")
    print("=" * 60)
    for symbol, result in results.items():
        print(f"{symbol}: 初始资金 {result['initial']:,.0f}, "
              f"最终资金 {result['final']:,.0f}, "
              f"收益率 {result['return']:+.2f}%")


def example_5_different_timeframes():
    """示例5: 不同时间周期组合"""
    print("=" * 60)
    print("示例5: 不同时间周期组合")
    print("=" * 60)
    
    timeframe_combinations = [
        {
            'name': '短周期组合',
            'timeframes': [60, 300, 900],  # 1分钟、5分钟、15分钟
            'description': '适合日内交易'
        },
        {
            'name': '中周期组合',
            'timeframes': [900, 3600, 14400],  # 15分钟、1小时、4小时
            'description': '适合短期波段'
        },
        {
            'name': '长周期组合',
            'timeframes': [14400, 86400, 604800],  # 4小时、日线、周线
            'description': '适合长期持有'
        }
    ]
    
    for combo in timeframe_combinations:
        print(f"\n测试 {combo['name']} - {combo['description']}")
        
        config = StrategyConfig(
            timeframes=combo['timeframes'],
            short_ma_period=5,
            long_ma_period=20,
            max_position_ratio=0.1,
            max_leverage=3.0
        )
        
        strategy = MultiTimeFrameStrategy(config, 'rb', 100000)
        initial_capital = strategy.current_capital
        
        strategy.run_strategy(iterations=8)
        
        final_capital = strategy.current_capital
        return_rate = (final_capital - initial_capital) / initial_capital * 100
        
        print(f"结果: 收益率 {return_rate:+.2f}%")


def example_6_risk_management():
    """示例6: 风险管理测试"""
    print("=" * 60)
    print("示例6: 风险管理测试")
    print("=" * 60)
    
    risk_levels = [
        {
            'name': '低风险',
            'max_position_ratio': 0.05,  # 5%
            'max_leverage': 2.0,
            'stop_loss_periods': 15
        },
        {
            'name': '中风险',
            'max_position_ratio': 0.1,   # 10%
            'max_leverage': 3.0,
            'stop_loss_periods': 10
        },
        {
            'name': '高风险',
            'max_position_ratio': 0.2,   # 20%
            'max_leverage': 5.0,
            'stop_loss_periods': 5
        }
    ]
    
    for risk in risk_levels:
        print(f"\n测试 {risk['name']} 策略")
        
        config = StrategyConfig(
            max_position_ratio=risk['max_position_ratio'],
            max_leverage=risk['max_leverage'],
            stop_loss_periods=risk['stop_loss_periods'],
            short_ma_period=5,
            long_ma_period=20
        )
        
        strategy = MultiTimeFrameStrategy(config, 'rb', 100000)
        initial_capital = strategy.current_capital
        
        strategy.run_strategy(iterations=12)
        
        final_capital = strategy.current_capital
        return_rate = (final_capital - initial_capital) / initial_capital * 100
        
        print(f"风险参数: 持仓比例{risk['max_position_ratio']*100:.0f}%, "
              f"杠杆{risk['max_leverage']}倍, 止损{risk['stop_loss_periods']}周期")
        print(f"结果: 收益率 {return_rate:+.2f}%")


def example_7_ma_parameters():
    """示例7: 均线参数优化"""
    print("=" * 60)
    print("示例7: 均线参数优化")
    print("=" * 60)
    
    ma_combinations = [
        (3, 10),   # 快速
        (5, 20),   # 标准
        (8, 21),   # 经典
        (10, 30),  # 稳健
        (13, 34),  # 斐波那契
    ]
    
    results = []
    
    for short_ma, long_ma in ma_combinations:
        print(f"\n测试均线组合: MA{short_ma} / MA{long_ma}")
        
        config = StrategyConfig(
            short_ma_period=short_ma,
            long_ma_period=long_ma,
            max_position_ratio=0.1,
            max_leverage=3.0
        )
        
        strategy = MultiTimeFrameStrategy(config, 'rb', 100000)
        initial_capital = strategy.current_capital
        
        strategy.run_strategy(iterations=10)
        
        final_capital = strategy.current_capital
        return_rate = (final_capital - initial_capital) / initial_capital * 100
        
        results.append({
            'ma_combo': f"MA{short_ma}/MA{long_ma}",
            'return': return_rate
        })
        
        print(f"收益率: {return_rate:+.2f}%")
    
    # 排序显示最佳组合
    results.sort(key=lambda x: x['return'], reverse=True)
    
    print("\n均线组合排名:")
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['ma_combo']}: {result['return']:+.2f}%")


def run_all_examples():
    """运行所有示例"""
    examples = [
        example_1_basic_usage,
        example_2_custom_parameters,
        example_3_preset_configs,
        example_4_multiple_symbols,
        example_5_different_timeframes,
        example_6_risk_management,
        example_7_ma_parameters
    ]
    
    for i, example_func in enumerate(examples, 1):
        print(f"\n{'='*80}")
        print(f"运行示例 {i}")
        print(f"{'='*80}")
        
        try:
            example_func()
        except KeyboardInterrupt:
            print("用户中断示例运行")
            break
        except Exception as e:
            print(f"示例运行出错: {e}")
        
        input("\n按回车键继续下一个示例...")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        example_num = sys.argv[1]
        
        examples_map = {
            "1": example_1_basic_usage,
            "2": example_2_custom_parameters,
            "3": example_3_preset_configs,
            "4": example_4_multiple_symbols,
            "5": example_5_different_timeframes,
            "6": example_6_risk_management,
            "7": example_7_ma_parameters,
            "all": run_all_examples
        }
        
        if example_num in examples_map:
            examples_map[example_num]()
        else:
            print("可用示例:")
            print("  1 - 基本使用方法")
            print("  2 - 自定义参数")
            print("  3 - 预设配置")
            print("  4 - 多品种对比")
            print("  5 - 不同时间周期")
            print("  6 - 风险管理")
            print("  7 - 均线参数优化")
            print("  all - 运行所有示例")
    else:
        print("多时间周期双边市场策略示例")
        print("使用方法: python strategy_examples.py [1-7|all]")
        print("\n运行基本示例...")
        example_1_basic_usage()
