"""
测试界面改进效果
验证按钮模式和窗口居中功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_window_centering():
    """测试窗口居中功能"""
    print("=" * 60)
    print("测试窗口居中功能")
    print("=" * 60)
    
    try:
        # 创建测试窗口
        test_root = tk.Tk()
        test_root.title("窗口居中测试")
        
        # 模拟居中逻辑
        window_width = 1400
        window_height = 900
        
        screen_width = test_root.winfo_screenwidth()
        screen_height = test_root.winfo_screenheight()
        
        center_x = int(screen_width / 2 - window_width / 2)
        center_y = int(screen_height / 2 - window_height / 2)
        
        test_root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')
        
        print(f"✓ 屏幕尺寸: {screen_width} x {screen_height}")
        print(f"✓ 窗口尺寸: {window_width} x {window_height}")
        print(f"✓ 居中位置: ({center_x}, {center_y})")
        print(f"✓ 窗口几何: {window_width}x{window_height}+{center_x}+{center_y}")
        
        # 验证位置是否合理
        if 0 <= center_x <= screen_width - window_width and 0 <= center_y <= screen_height - window_height:
            print("✓ 窗口位置计算正确")
            result = True
        else:
            print("✗ 窗口位置计算可能有问题")
            result = False
        
        test_root.destroy()
        return result
        
    except Exception as e:
        print(f"✗ 窗口居中测试失败: {e}")
        return False

def test_button_mode_interface():
    """测试按钮模式界面"""
    print("=" * 60)
    print("测试按钮模式界面")
    print("=" * 60)
    
    try:
        # 创建测试界面
        test_root = tk.Tk()
        test_root.title("按钮模式测试")
        test_root.geometry("800x600")
        
        # 创建控制面板
        control_frame = ttk.LabelFrame(test_root, text="分析控制", padding="10")
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 模式按钮区域
        mode_frame = ttk.Frame(control_frame)
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(mode_frame, text="分析模式:", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=(0, 10))
        
        # 模拟按钮状态变量
        current_mode = tk.StringVar(value="总体分析")
        
        def set_total_mode():
            current_mode.set("总体分析")
            total_btn.configure(state="disabled")
            account_btn.configure(state="normal")
            mode_indicator.config(text="当前模式: 总体分析", foreground="blue")
            account_frame.pack_forget()
            print("切换到总体分析模式")
        
        def set_account_mode():
            current_mode.set("分账户分析")
            total_btn.configure(state="normal")
            account_btn.configure(state="disabled")
            mode_indicator.config(text="当前模式: 分账户分析", foreground="green")
            account_frame.pack(fill=tk.X, pady=(10, 0))
            print("切换到分账户分析模式")
        
        # 总体分析按钮
        total_btn = ttk.Button(mode_frame, text="总体分析", command=set_total_mode, width=12)
        total_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 分账户分析按钮
        account_btn = ttk.Button(mode_frame, text="分账户分析", command=set_account_mode, width=12)
        account_btn.pack(side=tk.LEFT, padx=(0, 15))
        
        # 模式指示器
        mode_indicator = ttk.Label(mode_frame, text="当前模式: 总体分析", 
                                  foreground="blue", font=("Arial", 9))
        mode_indicator.pack(side=tk.LEFT, padx=(10, 0))
        
        # 账户选择区域
        account_frame = ttk.Frame(control_frame)
        
        ttk.Label(account_frame, text="选择账户:", font=("Arial", 10)).pack(side=tk.LEFT, padx=(0, 5))
        
        account_var = tk.StringVar()
        account_combo = ttk.Combobox(account_frame, textvariable=account_var,
                                    values=["账户1", "账户2", "账户3"], state="readonly", width=25)
        account_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 快速切换按钮
        ttk.Button(account_frame, text="◀ 上一个", width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(account_frame, text="下一个 ▶", width=8).pack(side=tk.LEFT, padx=(2, 0))
        
        # 操作按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="🔍 分析数据", width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="💾 导出图表", width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🗑️ 清除图表", width=12).pack(side=tk.LEFT, padx=(0, 5))
        
        # 统计信息
        stats_label = ttk.Label(button_frame, text="统计信息将显示在这里", 
                               foreground="blue", font=("Arial", 9))
        stats_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 设置初始状态
        set_total_mode()
        
        # 添加测试说明
        info_frame = ttk.LabelFrame(test_root, text="测试说明", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        info_text = """
界面改进测试:
1. 点击"总体分析"按钮 - 该按钮变为禁用状态，账户选择区域隐藏
2. 点击"分账户分析"按钮 - 该按钮变为禁用状态，账户选择区域显示
3. 模式指示器会显示当前选择的模式
4. 按钮包含图标，界面更加直观
5. 窗口会自动居中显示
        """
        
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).pack()
        
        print("✓ 按钮模式界面创建成功")
        print("✓ 包含总体分析和分账户分析按钮")
        print("✓ 包含模式指示器")
        print("✓ 包含账户快速切换功能")
        print("✓ 包含图标按钮")
        
        # 显示窗口进行手动测试
        print("\n请手动测试界面功能，然后关闭窗口...")
        test_root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ 按钮模式界面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_program_structure():
    """测试程序结构"""
    print("=" * 60)
    print("测试程序结构")
    print("=" * 60)
    
    try:
        # 检查程序文件是否存在
        program_file = "模拟盘资金曲线分析器.py"
        if not os.path.exists(program_file):
            print(f"✗ 程序文件不存在: {program_file}")
            return False
        
        print(f"✓ 程序文件存在: {program_file}")
        
        # 读取程序内容检查关键功能
        with open(program_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键方法
        required_methods = [
            'setup_window_center',
            'set_total_mode',
            'set_account_mode',
            'prev_account',
            'next_account'
        ]
        
        for method in required_methods:
            if f"def {method}" in content:
                print(f"✓ 方法存在: {method}")
            else:
                print(f"✗ 方法缺失: {method}")
                return False
        
        # 检查界面元素
        ui_elements = [
            'total_analysis_btn',
            'account_analysis_btn',
            'mode_indicator',
            'prev_account_btn',
            'next_account_btn'
        ]
        
        for element in ui_elements:
            if element in content:
                print(f"✓ 界面元素存在: {element}")
            else:
                print(f"✗ 界面元素缺失: {element}")
                return False
        
        # 检查按钮文本
        button_texts = [
            '"总体分析"',
            '"分账户分析"',
            '"🔍 分析数据"',
            '"💾 导出图表"',
            '"🗑️ 清除图表"'
        ]
        
        for text in button_texts:
            if text in content:
                print(f"✓ 按钮文本存在: {text}")
            else:
                print(f"✗ 按钮文本缺失: {text}")
        
        print("✓ 程序结构检查完成")
        return True
        
    except Exception as e:
        print(f"✗ 程序结构测试失败: {e}")
        return False

def show_improvement_summary():
    """显示改进总结"""
    print("=" * 60)
    print("界面改进总结")
    print("=" * 60)
    
    improvements = [
        {
            "改进项": "分析模式选择",
            "原来": "下拉框选择",
            "现在": "两个独立按钮",
            "优势": "操作更直观，状态更清晰"
        },
        {
            "改进项": "窗口位置",
            "原来": "默认位置",
            "现在": "自动居中显示",
            "优势": "用户体验更好"
        },
        {
            "改进项": "模式指示",
            "原来": "无明确指示",
            "现在": "彩色模式指示器",
            "优势": "当前状态一目了然"
        },
        {
            "改进项": "账户切换",
            "原来": "只有下拉选择",
            "现在": "下拉选择+快速切换按钮",
            "优势": "切换更便捷"
        },
        {
            "改进项": "按钮样式",
            "原来": "纯文本按钮",
            "现在": "图标+文本按钮",
            "优势": "界面更美观直观"
        },
        {
            "改进项": "布局优化",
            "原来": "简单排列",
            "现在": "分组布局，层次清晰",
            "优势": "界面更专业"
        }
    ]
    
    for item in improvements:
        print(f"\n{item['改进项']}:")
        print(f"  原来: {item['原来']}")
        print(f"  现在: {item['现在']}")
        print(f"  优势: {item['优势']}")
    
    print(f"\n新增功能:")
    print(f"  • 窗口自动居中")
    print(f"  • 按钮状态管理")
    print(f"  • 模式指示器")
    print(f"  • 账户快速切换")
    print(f"  • 图标按钮")
    print(f"  • 最小窗口大小限制")

def main():
    """主测试函数"""
    print("界面改进测试")
    print("=" * 80)
    
    tests = [
        ("窗口居中功能", test_window_centering),
        ("程序结构检查", test_program_structure),
        ("按钮模式界面", test_button_mode_interface),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示改进总结
    show_improvement_summary()
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:15}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 界面改进成功！")
        print("\n主要改进:")
        print("✓ 分析模式改为两个按钮")
        print("✓ 窗口自动居中显示")
        print("✓ 添加模式指示器")
        print("✓ 添加账户快速切换")
        print("✓ 美化按钮样式")
        print("✓ 优化布局结构")
        print("\n现在可以运行:")
        print("python 模拟盘资金曲线分析器.py")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
