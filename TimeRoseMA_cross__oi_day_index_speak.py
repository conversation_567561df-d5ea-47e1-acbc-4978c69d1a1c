from strategies.TimeRoseMA_cross_speak import ma_cross


def runstrategy():
    from tqsdk import TqA<PERSON>, TqAccount, TqKq
    from datetime import date

    interval = 60 * 60 * 24
    bklimit = 250
    sklimit = 250
    single_volume = 15

    product_id = 'OI'
    product_index_prefix = 'KQ.i'
    product_main_prifix = 'KQ.m'

    # 交易账号设置
    # api = TqApi(TqAccount(acct.name, acct.investorid, acct.password), auth=acct.tqacc)
    # symbol = api.query_cont_quotes(product_id=product)[0]
    api = TqApi(TqKq(), auth="quant_ggh,Qiai1301")
    symbol = api.query_cont_quotes(product_id=product_id)[0]
    exchang_id = symbol.split('.')[0]
    symbol_index = product_index_prefix + '@' + exchang_id + '.' + product_id
    symbol_main = product_main_prifix + '@' + exchang_id + '.' + product_id
    symbol = symbol_index

    ma_cross(api, symbol, interval, single_volume, bklimit, sklimit)


runstrategy()
