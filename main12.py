from tqsdk import Tq<PERSON><PERSON>, TqAuth, TargetPosTask,TqKq
from tqsdk.ta import MA
import time

class MultiTimeframeStrategy:
    def __init__(self, api, symbol):
        self.api = api
        self.symbol = symbol
        self.timeframes = [3, 5, 15]  # 3分钟, 5分钟, 15分钟
        self.positions = {tf: {'long': 0, 'short': 0} for tf in self.timeframes}
        self.max_position = 1  # 每个周期最多持仓1手
        self.klines = {}
        self.target_pos_tasks = {}

        for tf in self.timeframes:
            self.klines[tf] = self.api.get_kline_serial(self.symbol, tf * 60)
            self.target_pos_tasks[tf] = TargetPosTask(self.api, self.symbol)

    def check_crossover(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] > ma.iloc[-1] and close.iloc[-2] <= ma.iloc[-2]

    def check_crossunder(self, close, ma):
        if len(close) < 2 or len(ma) < 2:
            return False
        return close.iloc[-1] < ma.iloc[-1] and close.iloc[-2] >= ma.iloc[-2]

    def get_position_profit(self, timeframe):
        position = self.api.get_position(self.symbol)
        return position.float_profit if position.volume_long > 0 else -position.float_profit

    def execute_trade(self, timeframe, action, position_type):
        if action == 'open':
            if self.positions[timeframe][position_type] < self.max_position:
                self.positions[timeframe][position_type] += 1
                target_pos = self.max_position if position_type == 'long' else -self.max_position
                self.target_pos_tasks[timeframe].set_target_volume(target_pos)
                print(f"{timeframe}分钟周期: 开{position_type}单")
        elif action == 'close':
            if self.positions[timeframe][position_type] > 0:
                profit = self.get_position_profit(timeframe)
                if profit >= 0:
                    self.positions[timeframe][position_type] -= 1
                    self.target_pos_tasks[timeframe].set_target_volume(0)
                    print(f"{timeframe}分钟周期: 平{position_type}单")
                else:
                    print(f"{timeframe}分钟周期: {position_type}单亏损,不平仓")

    def run(self, timeframe):
        kline = self.klines[timeframe]
        # ma13 = MA(kline, 13)
        # ma13 = MA(kline, 13).series
        ma13 = kline.close.rolling(13).mean()

        if self.check_crossover(kline.close, ma13):
            self.execute_trade(timeframe, 'open', 'long')
            self.execute_trade(timeframe, 'close', 'short')
        elif self.check_crossunder(kline.close, ma13):
            self.execute_trade(timeframe, 'open', 'short')
            self.execute_trade(timeframe, 'close', 'long')

    def update(self):
        for timeframe in self.timeframes:
            self.run(timeframe)

# 主程序
api = TqApi(TqKq(), auth="wolfquant,ftp123", disable_print=True, debug=None)
# api = TqApi(auth=TqAuth("YOUR_USERNAME", "YOUR_PASSWORD"))
try:
    product_id = 'OI'
    symbol = api.query_cont_quotes(product_id=product_id)[0]
    print(f"交易的合约是: {symbol}")
    strategy = MultiTimeframeStrategy(api, symbol)

    while True:
        api.wait_update()
        strategy.update()

except KeyboardInterrupt:
    print("策略已停止")
finally:
    api.close()