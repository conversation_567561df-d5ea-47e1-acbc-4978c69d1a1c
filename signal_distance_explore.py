import time
import copy
import pandas as pd
from MyTT import *


def get_symbol_bars(symbol, interval, datalength=8964):
    from tqsdk import TqApi, TqKq
    api = TqApi(TqKq(), auth="smartmanp,ftp123", disable_print=True, debug=False)
    bars = api.get_kline_serial(symbol, duration_seconds=interval, data_length=datalength).dropna()

    api.close()
    return bars


def get_Signals_Ma(bars, period=13):
    CLOSE = bars.close.values
    OPEN = bars.open.values
    HIGH = bars.high.values
    LOW = bars.low.values
    N = period

    ups = list(CROSS(CLOSE, MA(CLOSE, N)))
    dns = list(CROSS(MA(CLOSE, N), CLOSE))

    # dnsn = [-1 if x == 1 else x for x in dns]
    signals = []

    for i in range(len(ups)):
        if ups[i] == 0 and dns[i] == 0:
            signals.append(0)

        else:
            if ups[i] == 1:
                signals.append(1)
            if dns[i] == 1:
                signals.append(-1)

    # bsigs = ups.count(True)
    # ssigs = dns.count(True)
    # avgdistance = len(ups) / (bsigs + ssigs)
    return ups, dns, signals


def get_Signals_zjtj(bars, period=13):
    CLOSE = bars.close
    # SYMBOL = bars.iloc[0].symbol
    # interval = bars.iloc[0].duration

    VAR1 = EMA(EMA(CLOSE, period), period)
    trendline = (VAR1 - REF(VAR1, 1)) / REF(VAR1, 1) * 1000
    # A10=CROSS(trendline,0)

    ups = list(CROSS(trendline, 0))

    dns = list(CROSS(0, trendline))
    signals = []

    for i in range(len(ups)):
        if ups[i] == 0 and dns[i] == 0:
            signals.append(0)

        else:
            if ups[i] == 1:
                signals.append(1)
            if dns[i] == 1:
                signals.append(-1)

    # bsigs = ups.count(True)
    # ssigs = dns.count(True)
    # avgdistance = len(ups) / (bsigs + ssigs)
    return ups, dns, signals


def cal_distlist(siglist):
    distlist = []
    count = 0
    for i in range(len(siglist)):
        if not siglist[i]:
            count += 1
        elif siglist[i]:
            distlist.append(count)
            count = 0

    return distlist


def cal_signaldist(signals):
    updistance = []
    dndistance = []
    upcount = 0
    dncount = 0
    upinitialized = False
    dninitialized = False

    for i in range(len(signals)):
        if signals[i] == 1:
            upinitialized = True
            upcount = 0

        if signals[i] == -1:
            upcount += 1
            updistance.append(upcount)
            upinitialized = False

        if signals[i] == 0 and upinitialized:
            upcount += 1

        else:
            continue

    for i in range(len(signals)):
        if signals[i] == -1:
            dninitialized = True
            dncount = 0

        if signals[i] == 1:
            dncount += 1
            dndistance.append(dncount)
            dninitialized = False

        if signals[i] == 0 and dninitialized:
            dncount += 1

        else:
            continue

    return updistance, dndistance


def profit_calculate(ups, dns, bars):
    buy_price_sum = 0
    sell_price_sum = 0
    for s in range(len(bars)):
        if ups[s] == 1:
            buy_price_sum += bars.close.iloc[s]
        if dns[s] == 1:
            sell_price_sum += bars.close.iloc[s]
    print('profits:', sell_price_sum - buy_price_sum)


def signals_ma(api, SYMBOL, interval):
    klines = api.get_kline_serial(SYMBOL, duration_seconds=interval, data_length=8964)
    klines_tmp = api.get_kline_serial(SYMBOL, interval, 10)
    klines1 = copy.deepcopy(klines)
    del klines

    # soundmsg = ''.join([productid, minutes])
    # 设置语音提醒信息
    # openlong_sound = soundmsg + '分钟' + '发出做多信号'
    # openshort_sound = soundmsg + '分钟' + '发出做空信号'
    # closelong_sound = soundmsg + '分钟' + '发出平多单信号'
    # closeshort_sound = soundmsg + '分钟' + '发出平空单信号'

    # 获得日线数据的相关信息
    daybars = api.get_kline_serial(SYMBOL, duration_seconds=60 * 60 * 24, data_length=365)
    daybar = daybars.dropna()
    hl = daybar.high - daybar.low
    ho = daybar.high - daybar.open
    ol = daybar.open - daybar.low
    hlmax = max(hl)
    daymean = int(hl.mean())
    homean = ho.mean()
    olmean = ol.mean()
    last3mean = int(hl.iloc[-4:-1].mean())

    quote = api.get_quote(SYMBOL)
    dayopen = quote.open
    dayhigh = quote.highest
    daylow = quote.lowest
    preclose = quote.pre_close
    dayvib = dayhigh - daylow
    gap = dayopen - preclose
    KNUM = len(klines1)
    gapcover = False
    ups, dns, signals = get_Signals_Ma(klines1, period=13)
    print(ups[-40:])
    print(dns[-40:])
    print(signals[-40:])
    updistance, dndistance = cal_signaldist(signals)
    print(updistance[-50:])
    print(dndistance[-50:])

    while True:
        api.wait_update()
        if api.is_changing(daybars.iloc[-1], "datetime"):
            hl = daybar.high - daybar.low
            ho = daybar.high - daybar.open
            ol = daybar.open - daybar.low
            daymean = int(hl.mean())
            homean = ho.mean()
            olmean = ol.mean()
            hlmax = max(hl)
            last3mean = int(hl.iloc[-4:-1].mean())
            dayopen = quote.open
            dayhigh = quote.highest
            daylow = quote.lowest
            preclose = quote.pre_close
            dayvib = dayhigh - daylow
            gap = dayopen - preclose
            gapcover = False
            print('日线数据更新完成。。。')
            # mylog.info('日线数据更新完成。。。')

        if api.is_changing(klines_tmp.iloc[-1], "datetime"):
            # disp_day_info(daymean, last3mean, hlmax, homean, olmean)
            # disp_0Day_info(quote)
            # print('balance:', acct.balance, 'poslong:', position.pos_long, 'posshort:', position.pos_short,
            #       'poslong float profit:', position.float_profit_long, 'posshort float profit:',
            #       position.float_profit_short)
            #
            # speakyn = True

            newk = klines_tmp.iloc[-2]
            bdt = klines1.datetime.tolist()
            tt = newk.datetime

            if tt in bdt:
                continue
                # mylog.info('发现重复数据, 跳过...', time_to_str(tt))
            else:
                newk = newk.to_frame()
                newk = newk.T
                klines1 = pd.concat([klines1, newk], ignore_index=True)

                # klines1 = klines1.append(newk)

            ups, dns, signals = get_Signals_Ma(klines1)
            print(ups[-40:])
            print(dns[-40:])
            print(signals[-40:])
            updistance, dndistance = cal_signaldist(signals)
            print(updistance[-50:])
            print(dndistance[-50:])


def runstrategy():
    from tqsdk import TqApi, TqKq, TqBacktest, TqAuth
    from datetime import date
    product = 'OI'
    interval = 60
    bklimit = 300
    sklimit = 300
    single_volume = 1

    # 交易账号设置
    api = TqApi(TqKq(), auth=("bigwolf,ftp123"), disable_print=True)
    symbol = api.query_cont_quotes(product_id=product)[0]

    signals_ma(api, symbol, interval)


if __name__ == '__main__':
    runstrategy()
