"""
OI指数多时间周期策略测试脚本
"""

import sys
from multi_timeframe_strategy_tqsdk import (
    MultiTimeFrameStrategy, 
    StrategyConfig, 
    create_default_config,
    test_data_loading,
    run_oi_index_strategy,
    TQSDK_AVAILABLE
)


def test_strategy_config():
    """测试策略配置"""
    print("=" * 50)
    print("测试策略配置")
    print("=" * 50)
    
    config = create_default_config()
    
    print(f"时间周期: {config.timeframes}")
    print(f"1分钟数据量: {config.base_data_length}")
    print(f"短均线周期: {config.short_ma_period}")
    print(f"长均线周期: {config.long_ma_period}")
    print(f"最大持仓比例: {config.max_position_ratio*100:.1f}%")
    print(f"最大杠杆: {config.max_leverage}倍")
    print(f"手续费率: {config.commission_rate*100:.3f}%")
    
    # 计算各时间周期的数据量
    print("\n各时间周期预计数据量:")
    base_length = config.base_data_length
    for tf in config.timeframes:
        ratio = tf / 60  # 相对于1分钟的比例
        length = max(100, int(base_length / ratio))
        tf_name = f"{tf//60}分钟" if tf < 3600 else f"{tf//3600}小时" if tf < 86400 else f"{tf//86400}天"
        print(f"  {tf_name}: {length} 条")


def test_without_api():
    """不使用API的基本测试"""
    print("=" * 50)
    print("基本功能测试（无API）")
    print("=" * 50)
    
    if not TQSDK_AVAILABLE:
        print("TqSDK未安装，跳过API相关测试")
        return
    
    try:
        config = create_default_config()
        print("✓ 配置创建成功")
        
        # 测试配置参数
        assert len(config.timeframes) == 5
        assert config.base_data_length == 10000
        assert config.short_ma_period < config.long_ma_period
        print("✓ 配置参数验证通过")
        
    except Exception as e:
        print(f"✗ 基本测试失败: {e}")


def test_with_mock_auth():
    """使用模拟认证的测试"""
    print("=" * 50)
    print("模拟认证测试")
    print("=" * 50)
    
    if not TQSDK_AVAILABLE:
        print("TqSDK未安装，跳过此测试")
        return
    
    # 使用快期模拟账户（不需要真实认证）
    try:
        print("尝试连接快期模拟账户...")
        test_data_loading(auth=None)  # 使用模拟账户
        
    except Exception as e:
        print(f"模拟账户连接失败: {e}")
        print("这是正常的，可能需要网络连接或真实认证")


def run_demo_strategy():
    """运行演示策略"""
    print("=" * 50)
    print("演示策略运行")
    print("=" * 50)
    
    if not TQSDK_AVAILABLE:
        print("TqSDK未安装，无法运行演示")
        return
    
    # 创建一个短时间运行的演示配置
    config = StrategyConfig(
        timeframes=[60, 900, 3600],  # 只使用3个时间周期
        base_data_length=1000,       # 减少数据量
        short_ma_period=3,
        long_ma_period=10,
        max_position_ratio=0.05,     # 降低风险
        max_leverage=2.0,
        trailing_stop_enabled=True
    )
    
    print("配置信息:")
    print(f"  时间周期: {[f'{tf//60}分钟' for tf in config.timeframes]}")
    print(f"  数据量: {config.base_data_length}")
    print(f"  均线: MA{config.short_ma_period}/MA{config.long_ma_period}")
    print(f"  风险控制: {config.max_position_ratio*100:.1f}% 持仓, {config.max_leverage}倍杠杆")
    
    try:
        # 尝试运行演示（使用模拟账户）
        strategy = MultiTimeFrameStrategy(
            config=config,
            symbol="<EMAIL>",
            initial_capital=50000,  # 5万演示资金
            auth=None  # 使用模拟账户
        )
        
        print("\n开始运行演示策略（5次迭代）...")
        strategy.run_strategy(iterations=5)
        
    except Exception as e:
        print(f"演示策略运行失败: {e}")
        print("可能需要网络连接或有效的TqSDK认证")


def show_usage():
    """显示使用说明"""
    print("=" * 60)
    print("OI指数多时间周期策略使用说明")
    print("=" * 60)
    
    print("1. 基本测试:")
    print("   python test_oi_strategy.py")
    
    print("\n2. 数据加载测试:")
    print("   python multi_timeframe_strategy_tqsdk.py test")
    print("   python multi_timeframe_strategy_tqsdk.py test 'username,password'")
    
    print("\n3. 运行策略:")
    print("   python multi_timeframe_strategy_tqsdk.py run 'username,password'")
    print("   python multi_timeframe_strategy_tqsdk.py run 'username,password' 100")
    
    print("\n4. 自定义策略:")
    print("   python multi_timeframe_strategy_tqsdk.py custom 'username,password'")
    
    print("\n5. 策略特点:")
    print("   - 使用************指数合约")
    print("   - 5个时间周期: 1分钟、15分钟、4小时、日线、周线")
    print("   - 1分钟数据获取10,000根，其他周期按时间对齐")
    print("   - 双均线金叉死叉 + 回归信号")
    print("   - 1分钟级别精确入场")
    print("   - 动态止损和移动止损")
    print("   - 等权重资金分配")
    
    print("\n6. 风险提示:")
    print("   - 本策略仅供学习研究使用")
    print("   - 实盘交易前请充分测试")
    print("   - 注意控制风险和资金管理")
    
    print("\n7. 技术要求:")
    print("   - 安装TqSDK: pip install tqsdk")
    print("   - 稳定的网络连接")
    print("   - 有效的TqSDK认证信息（可选，有模拟账户）")


def main():
    """主测试函数"""
    print("OI指数多时间周期策略测试")
    print("=" * 60)
    
    # 检查TqSDK安装
    if not TQSDK_AVAILABLE:
        print("❌ TqSDK未安装")
        print("请先安装: pip install tqsdk")
        return
    else:
        print("✅ TqSDK已安装")
    
    # 运行测试
    tests = [
        ("配置测试", test_strategy_config),
        ("基本功能测试", test_without_api),
        ("模拟认证测试", test_with_mock_auth),
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            test_func()
            print(f"✅ {test_name} 完成")
        except Exception as e:
            print(f"❌ {test_name} 失败: {e}")
    
    # 询问是否运行演示
    print(f"\n{'='*60}")
    choice = input("是否运行演示策略? (需要网络连接) [y/N]: ").strip().lower()
    if choice in ['y', 'yes']:
        run_demo_strategy()
    
    # 显示使用说明
    print(f"\n{'='*60}")
    show_usage()


if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "help":
            show_usage()
        elif command == "demo":
            run_demo_strategy()
        elif command == "config":
            test_strategy_config()
        else:
            print(f"未知命令: {command}")
            print("可用命令: help, demo, config")
    else:
        main()
