import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
import tqsdk
from tqsdk import TqKq

class FutureWidget(QWidget):
    def init(self, contract_code):
        super().init()
        self.contract_code = 'CZCE.OI305'
        self.initUI()

    def initUI(self):
        self.setWindowTitle('期货行情 - %s' % self.contract_code)
        self.resize(600, 400)
        self.setMinimumSize(400, 300)

        # 创建布局
        layout = QVBoxLayout()
        self.setLayout(layout)

        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(['指标', '值'])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.table)


        # 创建按钮
        self.refresh_button = QPushButton('刷新')
        self.refresh_button.clicked.connect(self.refresh)
        layout.addWidget(self.refresh_button)

    def refresh(self):
        # 创建 tqsdk 客户端对象
        client = tqsdk.TqApi(TqKq(), auth='bigwolf,ftp123')

        # 获取指定合约的行情数据
        quote = client.get_quote(self.contract_code)

        # 设置表格的行数
        self.table.setRowCount(7)

        # 设置表格的每一行的值
        self.table.setItem(0, 0, QTableWidgetItem('合约代码'))
        self.table.setItem(0, 1, QTableWidgetItem(quote.code))
        self.table.setItem(1, 0, QTableWidgetItem('合约名称'))
        self.table.setItem(1, 1, QTableWidgetItem(quote.name))
        self.table.setItem(2, 0, QTableWidgetItem('最新价'))
        self.table.setItem(2, 1, QTableWidgetItem('%.2f' % quote.last_price))
        self.table.setItem(3, 0, QTableWidgetItem('开盘价'))
        self.table.setItem(3, 1, QTableWidgetItem('%.2f' % quote.open))
        self.table.setItem(4, 0, QTableWidgetItem('收盘价'))
        self.table.setItem(4, 1, QTableWidgetItem('%.2f' % quote.close))
        self.table.setItem(5, 0, QTableWidgetItem('最高价'))
        self.table.setItem(5, 1, QTableWidgetItem('%.2f' % quote.high))
        self.table.setItem(6, 0, QTableWidgetItem('最低价'))
        self.table.setItem(6, 1, QTableWidgetItem('%.2f' % quote.low))

# if name == '__main__':
app = QApplication(sys.argv)
widget = FutureWidget()
widget.show()
sys.exit(app.exec_())


# 获取期货行情数据，并更新表格

