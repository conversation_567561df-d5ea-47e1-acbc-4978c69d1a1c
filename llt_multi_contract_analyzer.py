"""
LLT多合约盈利能力分析器
基于llt_strategy_refactored.py，专注于多合约分析，移除实际交易功能
"""

import pandas as pd
import numpy as np
from tqsdk import TqApi, TqAuth, TqKq
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from loguru import logger
import json
import time
from functools import wraps
import concurrent.futures
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse


def timing_decorator(step_name: str = None):
    """时间统计装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取类实例（如果存在）
            instance = None
            if args and hasattr(args[0], '__class__') and not isinstance(args[0], (pd.Series, pd.DataFrame, np.ndarray)):
                instance = args[0]
            
            name = step_name or f"{func.__name__}"
            if instance is not None:
                name = f"{instance.__class__.__name__}.{name}"
            
            logger.info(f"🔄 开始执行: {name}")
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                elapsed_time = time.time() - start_time
                logger.success(f"✅ 完成执行: {name} | 耗时: {elapsed_time:.3f}秒")
                return result
            except Exception as e:
                elapsed_time = time.time() - start_time
                logger.error(f"❌ 执行失败: {name} | 耗时: {elapsed_time:.3f}秒 | 错误: {e}")
                raise
        return wrapper
    return decorator


@dataclass
class ContractConfig:
    """合约配置类"""
    symbol: str
    name: str = ""
    exchange: str = ""
    product_id: str = ""
    multiplier: int = 1
    min_price_change: float = 1.0
    margin_rate: float = 0.1
    
    def __post_init__(self):
        if not self.name:
            self.name = self.symbol
        if not self.product_id and "." in self.symbol:
            self.product_id = self.symbol.split(".")[1][:2]


@dataclass
class StrategyConfig:
    """策略配置"""
    strategy_type: str = "LLT"  # LLT, MA, DualMA
    parameters: Dict = field(default_factory=dict)  # 策略参数

    def __post_init__(self):
        """初始化默认参数"""
        if self.strategy_type == "LLT" and not self.parameters:
            self.parameters = {"d_value_range": (10, 101)}
        elif self.strategy_type == "MA" and not self.parameters:
            self.parameters = {"period_range": (5, 51)}
        elif self.strategy_type == "DualMA" and not self.parameters:
            self.parameters = {"fast_range": (3, 11), "slow_range": (10, 31)}


@dataclass
class AnalysisConfig:
    """分析配置类"""
    contracts: List[ContractConfig] = field(default_factory=list)
    strategy_config: StrategyConfig = field(default_factory=lambda: StrategyConfig())
    kline_period_seconds: int = 300  # 5分钟
    kline_data_length: int = 8964
    d_value_range: Tuple[int, int] = (10, 101)  # 保持向后兼容
    auth_username: str = "bigwolf"
    auth_password: str = "ftp123"
    
    # 分析参数
    top_results_count: int = 5
    min_trades_required: int = 10
    analysis_start_date: Optional[str] = None
    analysis_end_date: Optional[str] = None
    
    # 输出配置
    output_dir: str = "analysis_results"
    save_detailed_results: bool = True
    generate_charts: bool = False
    
    @property
    def d_value_list(self) -> List[int]:
        """获取D_VALUE参数列表"""
        return list(range(self.d_value_range[0], self.d_value_range[1]))


@dataclass
class ContractAnalysisResult:
    """单个合约分析结果"""
    contract: ContractConfig
    best_d_value: int
    best_alpha: float
    total_trades: int
    total_pnl: float
    win_rate: float
    return_rate: float
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    profit_factor: float
    max_drawdown: float
    sharpe_ratio: float
    analysis_period_days: int
    data_quality_score: float
    equity_curve: List[float] = field(default_factory=list)
    equity_dates: List[datetime] = field(default_factory=list)
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'symbol': self.contract.symbol,
            'name': self.contract.name,
            'exchange': self.contract.exchange,
            'best_d_value': self.best_d_value,
            'best_alpha': self.best_alpha,
            'total_trades': self.total_trades,
            'total_pnl': self.total_pnl,
            'win_rate': self.win_rate,
            'return_rate': self.return_rate,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'profit_factor': self.profit_factor,
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': self.sharpe_ratio,
            'analysis_period_days': self.analysis_period_days,
            'data_quality_score': self.data_quality_score
        }


class BaseIndicator:
    """基础指标类"""

    @staticmethod
    def calculate(prices: pd.Series, **kwargs) -> List[float]:
        """计算指标值"""
        raise NotImplementedError("子类必须实现calculate方法")

    @staticmethod
    def generate_signals(indicator_values: List[float], **kwargs) -> List[int]:
        """生成交易信号"""
        raise NotImplementedError("子类必须实现generate_signals方法")


class MAIndicator(BaseIndicator):
    """移动平均线指标计算器"""

    @staticmethod
    @timing_decorator("MA指标计算")
    def calculate(prices: pd.Series, period: int = 13, **kwargs) -> List[float]:
        """计算移动平均线"""
        logger.debug(f"📈 计算MA指标: 数据长度={len(prices)}, 周期={period}")

        if len(prices) < period:
            logger.warning(f"⚠️  价格数据不足，无法计算MA{period}")
            return [float('nan')] * len(prices)

        # 确保prices是float类型
        if hasattr(prices, 'values'):
            price_values = prices.values.astype(float)
        else:
            price_values = np.array(prices, dtype=float)

        # 计算移动平均线
        ma_values = []

        for i in range(len(price_values)):
            if i < period - 1:
                # 前面不足周期的点，使用可用数据计算平均值
                ma_values.append(float(np.mean(price_values[:i+1])))
            else:
                # 计算周期内的平均值
                ma_values.append(float(np.mean(price_values[i-period+1:i+1])))

        logger.debug(f"✅ MA计算完成，共计算 {len(ma_values)} 个值")
        return ma_values

    @staticmethod
    @timing_decorator("MA信号生成")
    def generate_signals(ma_values: List[float], prices: List[float] = None, **kwargs) -> List[int]:
        """生成交易信号：价格突破均线"""
        if len(ma_values) < 2 or prices is None or len(prices) != len(ma_values):
            return [0] * len(ma_values)

        signals = [0]  # 第一个信号为0

        for i in range(1, len(ma_values)):
            # 价格从下方突破均线 -> 买入信号
            if prices[i] > ma_values[i] and prices[i-1] <= ma_values[i-1]:
                signals.append(1)
            # 价格从上方跌破均线 -> 卖出信号
            elif prices[i] < ma_values[i] and prices[i-1] >= ma_values[i-1]:
                signals.append(-1)
            else:
                signals.append(0)  # 无信号

        return signals


class DualMAIndicator(BaseIndicator):
    """双均线指标计算器"""

    @staticmethod
    @timing_decorator("双均线指标计算")
    def calculate(prices: pd.Series, fast_period: int = 5, slow_period: int = 13, **kwargs) -> Dict[str, List[float]]:
        """计算双均线"""
        logger.debug(f"📈 计算双均线指标: 数据长度={len(prices)}, 快线={fast_period}, 慢线={slow_period}")

        if len(prices) < max(fast_period, slow_period):
            logger.warning(f"⚠️  价格数据不足，无法计算双均线")
            return {'fast_ma': [float('nan')] * len(prices), 'slow_ma': [float('nan')] * len(prices)}

        # 计算快速均线和慢速均线
        fast_ma = MAIndicator.calculate(prices, fast_period)
        slow_ma = MAIndicator.calculate(prices, slow_period)

        logger.debug(f"✅ 双均线计算完成")
        return {'fast_ma': fast_ma, 'slow_ma': slow_ma}

    @staticmethod
    @timing_decorator("双均线信号生成")
    def generate_signals(indicator_values: Dict[str, List[float]], **kwargs) -> List[int]:
        """生成交易信号：快线穿越慢线"""
        fast_ma = indicator_values.get('fast_ma', [])
        slow_ma = indicator_values.get('slow_ma', [])

        if len(fast_ma) < 2 or len(slow_ma) < 2 or len(fast_ma) != len(slow_ma):
            return [0] * max(len(fast_ma), len(slow_ma))

        signals = [0]  # 第一个信号为0

        for i in range(1, len(fast_ma)):
            # 快线从下方穿越慢线 -> 买入信号（金叉）
            if fast_ma[i] > slow_ma[i] and fast_ma[i-1] <= slow_ma[i-1]:
                signals.append(1)
            # 快线从上方穿越慢线 -> 卖出信号（死叉）
            elif fast_ma[i] < slow_ma[i] and fast_ma[i-1] >= slow_ma[i-1]:
                signals.append(-1)
            else:
                signals.append(0)  # 无信号

        return signals


class LLTIndicator(BaseIndicator):
    """LLT指标计算类"""
    
    @staticmethod
    @timing_decorator("LLT指标计算")
    def calculate(price_series: pd.Series, alpha: float) -> List[float]:
        """计算Low-Latency Trendline (LLT)指标"""
        logger.debug(f"📈 计算LLT指标: 数据长度={len(price_series)}, alpha={alpha:.6f}")
        
        if len(price_series) < 2:
            logger.warning("⚠️  数据长度不足，返回NaN值")
            return [float('nan')] * len(price_series)

        llt = []
        
        # 确保使用numpy数组，避免pandas布尔值歧义
        if hasattr(price_series, 'values'):
            price_values = price_series.values.astype(float)
        else:
            price_values = np.array(price_series, dtype=float)
        
        # 初始化前两个值
        logger.debug("🔢 初始化LLT前两个值")
        llt.append(float(price_values[0]))
        llt.append(float(price_values[1]))
        
        # 计算LLT值
        logger.debug(f"🔄 开始计算LLT值，需要计算 {len(price_values) - 2} 个点")
        
        for i in range(2, len(price_values)):
            try:
                value = (
                    (alpha - alpha ** 2 / 4) * float(price_values[i]) +
                    (alpha ** 2 / 2) * float(price_values[i - 1]) -
                    (alpha - 3 * (alpha ** 2) / 4) * float(price_values[i - 2]) +
                    2 * (1 - alpha) * llt[i - 1] -
                    (1 - alpha) ** 2 * llt[i - 2]
                )
                llt.append(float(value))
            except Exception as e:
                logger.error(f"❌ LLT计算错误 at index {i}: {e}")
                llt.append(llt[-1])  # 使用前一个值
            
            # 每1000个点显示一次进度
            if (i - 1) % 1000 == 0:
                progress = ((i - 1) / (len(price_values) - 2)) * 100
                logger.debug(f"📊 LLT计算进度: {progress:.1f}% ({i-1}/{len(price_values)-2})")
        
        logger.debug(f"✅ LLT计算完成，共计算 {len(llt)} 个值")
        return llt
    
    @staticmethod
    @timing_decorator("信号生成")
    def generate_signals(llt_series: List[float]) -> List[int]:
        """根据LLT序列生成交易信号"""
        signals = [0]  # 第一个值无信号
        
        for i in range(1, len(llt_series)):
            if llt_series[i] > llt_series[i-1]:
                signals.append(1)  # 买入信号
            elif llt_series[i] < llt_series[i-1]:
                signals.append(-1)  # 卖出信号
            else:
                signals.append(0)  # 无信号
        
        return signals


class ContractDataManager:
    """合约数据管理器"""
    
    def __init__(self, config: AnalysisConfig):
        self.config = config
        self.api = None
        self.data_cache = {}
    
    @timing_decorator("API连接初始化")
    def initialize_api(self) -> TqApi:
        """初始化API连接"""
        try:
            logger.info("🔌 开始初始化TqSDK API连接...")
            logger.info(f"📝 认证用户: {self.config.auth_username}")
            
            auth = TqAuth(self.config.auth_username, self.config.auth_password)
            self.api = TqApi(TqKq(), auth=auth, disable_print=True)
            
            logger.success("✅ API连接成功建立")
            return self.api
        except Exception as e:
            logger.error(f"❌ API连接失败: {e}")
            raise
    
    @timing_decorator("合约数据加载")
    def load_contract_data(self, contract: ContractConfig) -> Optional[pd.DataFrame]:
        """加载单个合约的历史数据"""
        try:
            logger.info(f"📊 加载合约数据: {contract.symbol} ({contract.name})")
            
            # 检查缓存
            if contract.symbol in self.data_cache:
                logger.info(f"📋 使用缓存数据: {contract.symbol}")
                return self.data_cache[contract.symbol]
            
            # 获取K线数据
            klines = self.api.get_kline_serial(
                contract.symbol,
                duration_seconds=self.config.kline_period_seconds,
                data_length=self.config.kline_data_length
            )
            
            if len(klines) == 0:
                logger.warning(f"⚠️  {contract.symbol} 未获取到数据")
                return None
            
            logger.success(f"✅ {contract.symbol} 获取到 {len(klines)} 条K线数据")
            
            # 数据质量检查
            first_time = pd.to_datetime(klines.datetime.iloc[0])
            last_time = pd.to_datetime(klines.datetime.iloc[-1])
            time_span = last_time - first_time
            
            logger.info(f"📅 {contract.symbol} 数据时间范围:")
            logger.info(f"   开始: {first_time.strftime('%Y-%m-%d %H:%M')}")
            logger.info(f"   结束: {last_time.strftime('%Y-%m-%d %H:%M')}")
            logger.info(f"   跨度: {time_span.days}天")
            
            # 缓存数据
            self.data_cache[contract.symbol] = klines
            
            return klines
            
        except Exception as e:
            logger.error(f"❌ {contract.symbol} 数据加载失败: {e}")
            return None
    
    def close_api(self):
        """关闭API连接"""
        if self.api:
            self.api.close()
            logger.info("🔌 API连接已关闭")


class StrategyAnalyzer:
    """通用策略分析器"""

    def __init__(self, contract: ContractConfig, klines_data: pd.DataFrame,
                 strategy_config: StrategyConfig, backtest_mode: str = "profit_only"):
        self.contract = contract
        self.klines_data = klines_data
        self.strategy_config = strategy_config
        self.backtest_mode = backtest_mode

        # 根据策略类型选择指标类
        self.indicator_class = self._get_indicator_class()

    def _get_indicator_class(self):
        """根据策略类型获取指标类"""
        if self.strategy_config.strategy_type == "LLT":
            return LLTIndicator
        elif self.strategy_config.strategy_type == "MA":
            return MAIndicator
        elif self.strategy_config.strategy_type == "DualMA":
            return DualMAIndicator
        else:
            raise ValueError(f"不支持的策略类型: {self.strategy_config.strategy_type}")

    def optimize_parameters(self, parameter_ranges: Dict = None) -> List[Dict]:
        """优化策略参数"""
        if parameter_ranges is None:
            parameter_ranges = self.strategy_config.parameters

        if self.strategy_config.strategy_type == "LLT":
            return self._optimize_llt_parameters(parameter_ranges)
        elif self.strategy_config.strategy_type == "MA":
            return self._optimize_ma_parameters(parameter_ranges)
        elif self.strategy_config.strategy_type == "DualMA":
            return self._optimize_dual_ma_parameters(parameter_ranges)
        else:
            return []

    def _optimize_llt_parameters(self, parameter_ranges: Dict) -> List[Dict]:
        """优化LLT参数"""
        d_value_range = parameter_ranges.get("d_value_range", (10, 101))
        results = []

        for d_value in range(d_value_range[0], d_value_range[1]):
            alpha = 2 / (d_value + 1)
            backtest_result = self._run_backtest(alpha=alpha)

            if backtest_result['total_trades'] >= 5:  # 最少交易次数
                result = {
                    'D_VALUE': d_value,
                    'ALPHA': alpha,
                    'strategy_type': 'LLT',
                    **backtest_result
                }
                results.append(result)

        # 按收益率排序
        return sorted(results, key=lambda x: x['return_rate'], reverse=True)

    def _optimize_ma_parameters(self, parameter_ranges: Dict) -> List[Dict]:
        """优化MA参数"""
        period_range = parameter_ranges.get("period_range", (5, 51))
        results = []

        for period in range(period_range[0], period_range[1]):
            backtest_result = self._run_backtest(ma_period=period)

            if backtest_result['total_trades'] >= 5:  # 最少交易次数
                result = {
                    'MA_PERIOD': period,
                    'strategy_type': 'MA',
                    **backtest_result
                }
                results.append(result)

        # 按收益率排序
        return sorted(results, key=lambda x: x['return_rate'], reverse=True)

    def _optimize_dual_ma_parameters(self, parameter_ranges: Dict) -> List[Dict]:
        """优化双均线参数"""
        fast_range = parameter_ranges.get("fast_range", (3, 11))
        slow_range = parameter_ranges.get("slow_range", (10, 31))
        results = []

        for fast_period in range(fast_range[0], fast_range[1]):
            for slow_period in range(slow_range[0], slow_range[1]):
                if fast_period >= slow_period:
                    continue  # 快线周期必须小于慢线周期

                backtest_result = self._run_backtest(fast_period=fast_period, slow_period=slow_period)

                if backtest_result['total_trades'] >= 5:  # 最少交易次数
                    result = {
                        'FAST_PERIOD': fast_period,
                        'SLOW_PERIOD': slow_period,
                        'strategy_type': 'DualMA',
                        **backtest_result
                    }
                    results.append(result)

        # 按收益率排序
        return sorted(results, key=lambda x: x['return_rate'], reverse=True)

    def _run_backtest(self, **kwargs) -> Dict:
        """运行回测"""
        try:
            # 确保数据类型正确
            close_prices = pd.Series(self.klines_data.close.values, dtype=float)

            # 根据策略类型计算指标和信号
            if self.strategy_config.strategy_type == "LLT":
                alpha = kwargs.get('alpha')
                if alpha is None:
                    return self._get_empty_backtest_result()

                indicator_values = self.indicator_class.calculate(close_prices, alpha=alpha)
                signals = self.indicator_class.generate_signals(indicator_values)

            elif self.strategy_config.strategy_type == "MA":
                period = kwargs.get('ma_period', 13)
                indicator_values = self.indicator_class.calculate(close_prices, period=period)
                signals = self.indicator_class.generate_signals(indicator_values, prices=close_prices.tolist())

            elif self.strategy_config.strategy_type == "DualMA":
                fast_period = kwargs.get('fast_period', 5)
                slow_period = kwargs.get('slow_period', 13)
                indicator_values = self.indicator_class.calculate(close_prices, fast_period=fast_period, slow_period=slow_period)
                signals = self.indicator_class.generate_signals(indicator_values)

            else:
                return self._get_empty_backtest_result()

            # 运行回测逻辑
            if self.backtest_mode == "profit_only":
                return self._run_profit_only_backtest(close_prices, signals)
            else:
                return self._run_standard_backtest(close_prices, signals)

        except Exception as e:
            logger.error(f"❌ {self.contract.symbol} 策略回测执行失败: {e}")
            return self._get_empty_backtest_result()

    def _run_standard_backtest(self, close_prices: pd.Series, signals: List[int]) -> Dict:
        """标准回测：信号触发就平仓"""
        trades = []
        position = 0
        entry_price = 0.0
        cumulative_pnl = 0.0
        equity_curve = [0.0]

        # 转换为numpy数组以避免pandas布尔值歧义
        price_values = close_prices.values

        for i, (price, signal) in enumerate(zip(price_values, signals)):
            price = float(price)

            if signal == 1 and position != 1:  # 买入信号
                if position == -1:  # 平空头
                    pnl = float(entry_price - price)
                    cumulative_pnl += pnl
                    trades.append({
                        'pnl': pnl,
                        'type': 'short_close',
                        'price': price,
                        'entry_price': entry_price
                    })
                position = 1
                entry_price = price

            elif signal == -1 and position != -1:  # 卖出信号
                if position == 1:  # 平多头
                    pnl = float(price - entry_price)
                    cumulative_pnl += pnl
                    trades.append({
                        'pnl': pnl,
                        'type': 'long_close',
                        'price': price,
                        'entry_price': entry_price
                    })
                position = -1
                entry_price = price

            equity_curve.append(cumulative_pnl)

        return self._calculate_backtest_stats(trades, cumulative_pnl, equity_curve)

    def _run_profit_only_backtest(self, close_prices: pd.Series, signals: List[int]) -> Dict:
        """盈利优先回测：完全按照llt_strategy_3.py的逻辑"""
        trades = []
        position = 0  # 0: 空仓, 1: 多头, -1: 空头
        entry_price = 0.0
        cumulative_pnl = 0.0
        equity_curve = [0.0]

        # 转换为numpy数组以避免pandas布尔值歧义
        price_values = close_prices.values

        for i, (price, signal) in enumerate(zip(price_values, signals)):
            price = float(price)

            # 新增平仓条件：信号出现时检查反向持仓是否盈利，盈利则全部平仓
            if signal == 1 and position == -1:  # 做多信号且持有空头
                floating_pnl = entry_price - price
                if floating_pnl > 0:  # 空头盈利，全部平仓
                    cumulative_pnl += floating_pnl
                    trades.append({
                        'pnl': floating_pnl,
                        'type': 'SHORT_CLOSE_ALL',
                        'price': price,
                        'entry_price': entry_price
                    })
                    position = 0  # 平仓后空仓
                    entry_price = 0

            elif signal == -1 and position == 1:  # 做空信号且持有多头
                floating_pnl = price - entry_price
                if floating_pnl > 0:  # 多头盈利，全部平仓
                    cumulative_pnl += floating_pnl
                    trades.append({
                        'pnl': floating_pnl,
                        'type': 'LONG_CLOSE_ALL',
                        'price': price,
                        'entry_price': entry_price
                    })
                    position = 0  # 平仓后空仓
                    entry_price = 0

            # 原有交易逻辑
            if signal == 1 and position != 1:  # 买入信号
                # 如果有空头持仓，检查盈亏决定是否平仓
                if position == -1:
                    floating_pnl = entry_price - price
                    if floating_pnl > 0:  # 盈利则平仓
                        cumulative_pnl += floating_pnl
                        trades.append({
                            'pnl': floating_pnl,
                            'type': 'SHORT',
                            'price': price,
                            'entry_price': entry_price
                        })
                        # 开多头
                        position = 1
                        entry_price = price
                    else:  # 亏损则不平仓，继续持有空头
                        # 在回测中简化处理：如果亏损不平仓，则保持原持仓方向
                        pass
                else:
                    # 没有空头持仓，直接开多头
                    position = 1
                    entry_price = price

            elif signal == -1 and position != -1:  # 卖出信号
                # 如果有多头持仓，检查盈亏决定是否平仓
                if position == 1:
                    floating_pnl = price - entry_price
                    if floating_pnl > 0:  # 盈利则平仓
                        cumulative_pnl += floating_pnl
                        trades.append({
                            'pnl': floating_pnl,
                            'type': 'LONG',
                            'price': price,
                            'entry_price': entry_price
                        })
                        # 开空头
                        position = -1
                        entry_price = price
                    else:  # 亏损则不平仓，继续持有多头
                        # 在回测中简化处理：如果亏损不平仓，则保持原持仓方向
                        pass
                else:
                    # 没有多头持仓，直接开空头
                    position = -1
                    entry_price = price

            # 计算当前浮动盈亏（用于权益曲线）
            if position == 1:  # 多头
                floating_pnl = price - entry_price
            elif position == -1:  # 空头
                floating_pnl = entry_price - price
            else:  # 空仓
                floating_pnl = 0

            equity_curve.append(cumulative_pnl + floating_pnl)

        return self._calculate_backtest_stats(trades, cumulative_pnl, equity_curve)

    def _calculate_backtest_stats(self, trades: List[Dict], cumulative_pnl: float, equity_curve: List[float]) -> Dict:
        """计算回测统计信息"""
        if trades:
            winning_trades = [t for t in trades if t['pnl'] > 0]
            losing_trades = [t for t in trades if t['pnl'] <= 0]
            win_rate = len(winning_trades) / len(trades) * 100

            avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0
            avg_loss = sum(t['pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0

            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')

            # 计算最大回撤
            peak = 0
            max_drawdown = 0
            for equity in equity_curve:
                if equity > peak:
                    peak = equity
                drawdown = peak - equity
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            # 计算夏普比率（简化版）
            returns = [trades[i]['pnl'] for i in range(len(trades))]
            if len(returns) > 1:
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                sharpe_ratio = mean_return / std_return if std_return != 0 else 0
            else:
                sharpe_ratio = 0

        else:
            winning_trades = []
            losing_trades = []
            win_rate = 0
            avg_win = 0
            avg_loss = 0
            profit_factor = 0
            max_drawdown = 0
            sharpe_ratio = 0

        return_rate = (cumulative_pnl / 10000) * 100  # 假设初始资金10000

        return {
            'trades': trades,
            'total_trades': len(trades),
            'total_pnl': float(cumulative_pnl),
            'win_rate': float(win_rate),
            'return_rate': float(return_rate),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'avg_win': float(avg_win),
            'avg_loss': float(avg_loss),
            'profit_factor': float(profit_factor),
            'max_drawdown': float(max_drawdown),
            'sharpe_ratio': float(sharpe_ratio),
            'equity_curve': equity_curve
        }

    def _get_empty_backtest_result(self) -> Dict:
        """获取空的回测结果"""
        return {
            'trades': [],
            'total_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'return_rate': 0.0,
            'winning_trades': 0,
            'losing_trades': 0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'profit_factor': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'equity_curve': [0.0]
        }

    def generate_analysis_result(self, optimization_results: List[Dict]) -> Optional[ContractAnalysisResult]:
        """生成分析结果"""
        if not optimization_results:
            return None

        best_result = optimization_results[0]

        # 计算数据质量评分
        data_quality_score = self._calculate_data_quality_score()

        # 计算分析周期
        first_time = pd.to_datetime(self.klines_data.datetime.iloc[0])
        last_time = pd.to_datetime(self.klines_data.datetime.iloc[-1])
        analysis_period_days = (last_time - first_time).days

        # 重新运行最优参数的回测以获取完整的权益曲线数据
        logger.debug(f"🔄 重新运行最优参数回测获取权益曲线")

        if self.strategy_config.strategy_type == "LLT":
            detailed_backtest = self._run_backtest(alpha=best_result['ALPHA'])
        elif self.strategy_config.strategy_type == "MA":
            detailed_backtest = self._run_backtest(ma_period=best_result['MA_PERIOD'])
        elif self.strategy_config.strategy_type == "DualMA":
            detailed_backtest = self._run_backtest(
                fast_period=best_result['FAST_PERIOD'],
                slow_period=best_result['SLOW_PERIOD']
            )
        else:
            detailed_backtest = self._get_empty_backtest_result()

        equity_curve = detailed_backtest.get('equity_curve', [])
        equity_dates = []

        if equity_curve and len(equity_curve) > 0:
            # 创建对应的时间序列
            if len(self.klines_data) >= len(equity_curve):
                equity_dates = self.klines_data['datetime'].iloc[:len(equity_curve)].tolist()
            else:
                # 如果数据长度不匹配，创建简单的时间序列
                equity_dates = [first_time + timedelta(minutes=5*i) for i in range(len(equity_curve))]

            logger.debug(f"📈 权益曲线数据: {len(equity_curve)}个点, 时间范围: {len(equity_dates)}个点")
        else:
            logger.warning(f"⚠️  {self.contract.symbol} 权益曲线数据为空")

        # 根据策略类型设置最优参数
        if self.strategy_config.strategy_type == "LLT":
            best_d_value = best_result['D_VALUE']
            best_alpha = best_result['ALPHA']
        elif self.strategy_config.strategy_type == "MA":
            best_d_value = best_result['MA_PERIOD']
            best_alpha = 0.0  # MA策略不使用alpha
        elif self.strategy_config.strategy_type == "DualMA":
            best_d_value = best_result['FAST_PERIOD']  # 使用快线周期作为主要参数
            best_alpha = best_result['SLOW_PERIOD'] / 100.0  # 慢线周期转换为alpha形式
        else:
            best_d_value = 0
            best_alpha = 0.0

        return ContractAnalysisResult(
            contract=self.contract,
            best_d_value=best_d_value,
            best_alpha=best_alpha,
            total_trades=best_result['total_trades'],
            total_pnl=best_result['total_pnl'],
            win_rate=best_result['win_rate'],
            return_rate=best_result['return_rate'],
            winning_trades=best_result['winning_trades'],
            losing_trades=best_result['losing_trades'],
            avg_win=best_result['avg_win'],
            avg_loss=best_result['avg_loss'],
            profit_factor=best_result['profit_factor'],
            max_drawdown=best_result['max_drawdown'],
            sharpe_ratio=best_result['sharpe_ratio'],
            analysis_period_days=analysis_period_days,
            data_quality_score=data_quality_score,
            equity_curve=equity_curve,
            equity_dates=equity_dates
        )

    def _calculate_data_quality_score(self) -> float:
        """计算数据质量评分"""
        try:
            # 简化的数据质量评分
            if len(self.klines_data) < 100:
                return 0.0
            elif len(self.klines_data) < 1000:
                return 70.0
            elif len(self.klines_data) < 5000:
                return 85.0
            else:
                return 95.0
        except:
            return 50.0


class ContractAnalyzer:
    """单个合约分析器（LLT策略专用，保持向后兼容）"""

    def __init__(self, contract: ContractConfig, klines_data: pd.DataFrame,
                 backtest_mode: str = "profit_only"):
        self.contract = contract
        self.klines_data = klines_data
        self.backtest_mode = backtest_mode  # "standard" 或 "profit_only"
    
    @timing_decorator("合约参数优化")
    def optimize_parameters(self, d_value_range: range) -> List[Dict]:
        """优化合约参数"""
        results = []
        
        logger.info(f"🎯 开始 {self.contract.symbol} 参数优化")
        logger.info(f"📊 测试参数范围: {d_value_range.start} - {d_value_range.stop-1}")
        
        for d_value in d_value_range:
            try:
                alpha = 2 / (d_value + 1)
                backtest_result = self._run_backtest(alpha)
                
                if backtest_result['trades'] and len(backtest_result['trades']) >= 5:
                    result = {
                        'D_VALUE': d_value,
                        'ALPHA': alpha,
                        'total_trades': len(backtest_result['trades']),
                        'total_pnl': backtest_result['total_pnl'],
                        'win_rate': backtest_result['win_rate'],
                        'return_rate': backtest_result['return_rate'],
                        'winning_trades': backtest_result['winning_trades'],
                        'losing_trades': backtest_result['losing_trades'],
                        'avg_win': backtest_result['avg_win'],
                        'avg_loss': backtest_result['avg_loss'],
                        'profit_factor': backtest_result['profit_factor'],
                        'max_drawdown': backtest_result['max_drawdown'],
                        'sharpe_ratio': backtest_result['sharpe_ratio']
                    }
                    results.append(result)
                    
                    logger.debug(
                        f"{self.contract.symbol} D_VALUE={d_value:3d} | "
                        f"交易:{result['total_trades']:3d} | "
                        f"盈亏:{result['total_pnl']:8.2f} | "
                        f"胜率:{result['win_rate']:5.1f}% | "
                        f"收益:{result['return_rate']:6.2f}%"
                    )
                
            except Exception as e:
                logger.error(f"❌ {self.contract.symbol} D_VALUE={d_value} 计算错误: {e}")
                continue
        
        # 按收益率排序
        results.sort(key=lambda x: x['return_rate'], reverse=True)
        logger.success(f"🏆 {self.contract.symbol} 参数优化完成，有效结果: {len(results)}个")
        
        return results
    
    def _run_backtest(self, alpha: float) -> Dict:
        """运行回测"""
        try:
            # 确保数据类型正确
            close_prices = pd.Series(self.klines_data.close.values, dtype=float)

            # 计算LLT和信号
            llt_series = LLTIndicator.calculate(close_prices, alpha)
            signals = LLTIndicator.generate_signals(llt_series)

            if self.backtest_mode == "profit_only":
                return self._run_profit_only_backtest(close_prices, signals)
            else:
                return self._run_standard_backtest(close_prices, signals)

        except Exception as e:
            logger.error(f"❌ {self.contract.symbol} 回测执行失败: {e}")
            return self._get_empty_backtest_result()

    def _run_standard_backtest(self, close_prices: pd.Series, signals: List[int]) -> Dict:
        """标准回测：信号触发就平仓"""
        trades = []
        position = 0
        entry_price = 0.0
        cumulative_pnl = 0.0
        equity_curve = [0.0]

        # 转换为numpy数组以避免pandas布尔值歧义
        price_values = close_prices.values

        for i, (price, signal) in enumerate(zip(price_values, signals)):
            price = float(price)

            if signal == 1 and position != 1:  # 买入信号
                if position == -1:  # 平空头
                    pnl = float(entry_price - price)
                    cumulative_pnl += pnl
                    trades.append({
                        'pnl': pnl,
                        'type': 'short_close',
                        'price': price,
                        'entry_price': entry_price
                    })
                position = 1
                entry_price = price

            elif signal == -1 and position != -1:  # 卖出信号
                if position == 1:  # 平多头
                    pnl = float(price - entry_price)
                    cumulative_pnl += pnl
                    trades.append({
                        'pnl': pnl,
                        'type': 'long_close',
                        'price': price,
                        'entry_price': entry_price
                    })
                position = -1
                entry_price = price

            equity_curve.append(cumulative_pnl)

        return self._calculate_backtest_stats(trades, cumulative_pnl, equity_curve)

    def _run_profit_only_backtest(self, close_prices: pd.Series, signals: List[int]) -> Dict:
        """盈利优先回测：完全按照llt_strategy_3.py的逻辑"""
        trades = []
        position = 0  # 0: 空仓, 1: 多头, -1: 空头
        entry_price = 0.0
        cumulative_pnl = 0.0
        equity_curve = [0.0]

        # 转换为numpy数组以避免pandas布尔值歧义
        price_values = close_prices.values

        for i, (price, signal) in enumerate(zip(price_values, signals)):
            price = float(price)

            # 新增平仓条件：信号出现时检查反向持仓是否盈利，盈利则全部平仓
            if signal == 1 and position == -1:  # 做多信号且持有空头
                floating_pnl = entry_price - price
                if floating_pnl > 0:  # 空头盈利，全部平仓
                    cumulative_pnl += floating_pnl
                    trades.append({
                        'pnl': floating_pnl,
                        'type': 'SHORT_CLOSE_ALL',
                        'price': price,
                        'entry_price': entry_price
                    })
                    position = 0  # 平仓后空仓
                    entry_price = 0

            elif signal == -1 and position == 1:  # 做空信号且持有多头
                floating_pnl = price - entry_price
                if floating_pnl > 0:  # 多头盈利，全部平仓
                    cumulative_pnl += floating_pnl
                    trades.append({
                        'pnl': floating_pnl,
                        'type': 'LONG_CLOSE_ALL',
                        'price': price,
                        'entry_price': entry_price
                    })
                    position = 0  # 平仓后空仓
                    entry_price = 0

            # 原有交易逻辑
            if signal == 1 and position != 1:  # 买入信号
                # 如果有空头持仓，检查盈亏决定是否平仓
                if position == -1:
                    floating_pnl = entry_price - price
                    if floating_pnl > 0:  # 盈利则平仓
                        cumulative_pnl += floating_pnl
                        trades.append({
                            'pnl': floating_pnl,
                            'type': 'SHORT',
                            'price': price,
                            'entry_price': entry_price
                        })
                        # 开多头
                        position = 1
                        entry_price = price
                    else:  # 亏损则不平仓，继续持有空头
                        # 在回测中简化处理：如果亏损不平仓，则保持原持仓方向
                        pass
                else:
                    # 没有空头持仓，直接开多头
                    position = 1
                    entry_price = price

            elif signal == -1 and position != -1:  # 卖出信号
                # 如果有多头持仓，检查盈亏决定是否平仓
                if position == 1:
                    floating_pnl = price - entry_price
                    if floating_pnl > 0:  # 盈利则平仓
                        cumulative_pnl += floating_pnl
                        trades.append({
                            'pnl': floating_pnl,
                            'type': 'LONG',
                            'price': price,
                            'entry_price': entry_price
                        })
                        # 开空头
                        position = -1
                        entry_price = price
                    else:  # 亏损则不平仓，继续持有多头
                        # 在回测中简化处理：如果亏损不平仓，则保持原持仓方向
                        pass
                else:
                    # 没有多头持仓，直接开空头
                    position = -1
                    entry_price = price

            # 计算当前浮动盈亏（用于权益曲线）
            if position == 1:  # 多头
                floating_pnl = price - entry_price
            elif position == -1:  # 空头
                floating_pnl = entry_price - price
            else:  # 空仓
                floating_pnl = 0

            equity_curve.append(cumulative_pnl + floating_pnl)

        return self._calculate_backtest_stats(trades, cumulative_pnl, equity_curve)

    def _calculate_backtest_stats(self, trades: List[Dict], cumulative_pnl: float, equity_curve: List[float]) -> Dict:
            
        """计算回测统计信息"""
        if trades:
            winning_trades = [t for t in trades if t['pnl'] > 0]
            losing_trades = [t for t in trades if t['pnl'] <= 0]
            win_rate = len(winning_trades) / len(trades) * 100

            avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0
            avg_loss = sum(t['pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0

            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')

            # 计算最大回撤
            peak = 0
            max_drawdown = 0
            for equity in equity_curve:
                if equity > peak:
                    peak = equity
                drawdown = peak - equity
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            # 计算夏普比率（简化版）
            returns = [trades[i]['pnl'] for i in range(len(trades))]
            if len(returns) > 1:
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                sharpe_ratio = mean_return / std_return if std_return != 0 else 0
            else:
                sharpe_ratio = 0

        else:
            winning_trades = []
            losing_trades = []
            win_rate = 0
            avg_win = 0
            avg_loss = 0
            profit_factor = 0
            max_drawdown = 0
            sharpe_ratio = 0

        return_rate = (cumulative_pnl / 10000) * 100  # 假设初始资金10000

        return {
            'trades': trades,
            'total_trades': len(trades),
            'total_pnl': float(cumulative_pnl),
            'win_rate': float(win_rate),
            'return_rate': float(return_rate),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'avg_win': float(avg_win),
            'avg_loss': float(avg_loss),
            'profit_factor': float(profit_factor),
            'max_drawdown': float(max_drawdown),
            'sharpe_ratio': float(sharpe_ratio),
            'equity_curve': equity_curve
        }

    def _get_empty_backtest_result(self) -> Dict:
        """获取空的回测结果"""
        return {
            'trades': [],
            'total_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'return_rate': 0.0,
            'winning_trades': 0,
            'losing_trades': 0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'profit_factor': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'equity_curve': [0.0]
        }
    
    def generate_analysis_result(self, optimization_results: List[Dict]) -> Optional[ContractAnalysisResult]:
        """生成分析结果"""
        if not optimization_results:
            logger.warning(f"⚠️  {self.contract.symbol} 无有效优化结果")
            return None
        
        best_result = optimization_results[0]

        # 计算数据质量评分
        data_quality_score = self._calculate_data_quality_score()

        # 计算分析周期
        first_time = pd.to_datetime(self.klines_data.datetime.iloc[0])
        last_time = pd.to_datetime(self.klines_data.datetime.iloc[-1])
        analysis_period_days = (last_time - first_time).days

        # 重新运行最优参数的回测以获取完整的权益曲线数据
        logger.debug(f"🔄 重新运行最优参数回测获取权益曲线: D_VALUE={best_result['D_VALUE']}")
        detailed_backtest = self._run_backtest(best_result['ALPHA'])

        equity_curve = detailed_backtest.get('equity_curve', [])
        equity_dates = []

        if equity_curve and len(equity_curve) > 0:
            # 创建对应的时间序列
            if len(self.klines_data) >= len(equity_curve):
                equity_dates = self.klines_data['datetime'].iloc[:len(equity_curve)].tolist()
            else:
                # 如果数据长度不匹配，创建简单的时间序列
                equity_dates = [first_time + timedelta(minutes=5*i) for i in range(len(equity_curve))]

            logger.debug(f"📈 权益曲线数据: {len(equity_curve)}个点, 时间范围: {len(equity_dates)}个点")
        else:
            logger.warning(f"⚠️  {self.contract.symbol} 权益曲线数据为空")

        return ContractAnalysisResult(
            contract=self.contract,
            best_d_value=best_result['D_VALUE'],
            best_alpha=best_result['ALPHA'],
            total_trades=best_result['total_trades'],
            total_pnl=best_result['total_pnl'],
            win_rate=best_result['win_rate'],
            return_rate=best_result['return_rate'],
            winning_trades=best_result['winning_trades'],
            losing_trades=best_result['losing_trades'],
            avg_win=best_result['avg_win'],
            avg_loss=best_result['avg_loss'],
            profit_factor=best_result['profit_factor'],
            max_drawdown=best_result['max_drawdown'],
            sharpe_ratio=best_result['sharpe_ratio'],
            analysis_period_days=analysis_period_days,
            data_quality_score=data_quality_score,
            equity_curve=equity_curve,
            equity_dates=equity_dates
        )
    
    def _calculate_data_quality_score(self) -> float:
        """计算数据质量评分"""
        try:
            # 检查数据完整性
            total_points = len(self.klines_data)
            non_zero_volume = (self.klines_data.volume > 0).sum()
            
            # 检查价格合理性
            price_changes = self.klines_data.close.pct_change().dropna()
            extreme_changes = (abs(price_changes) > 0.1).sum()  # 超过10%的价格变动
            
            # 计算评分
            completeness_score = non_zero_volume / total_points
            stability_score = 1 - (extreme_changes / len(price_changes))
            
            quality_score = (completeness_score * 0.6 + stability_score * 0.4) * 100
            
            return min(100.0, max(0.0, quality_score))
            
        except Exception as e:
            logger.error(f"❌ {self.contract.symbol} 数据质量评分失败: {e}")
            return 50.0  # 默认评分


def setup_logging(log_level: str = "INFO"):
    """设置loguru日志"""
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=log_level,
        colorize=True
    )
    
    # 添加文件输出
    logger.add(
        "llt_multi_contract_analysis.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level=log_level,
        rotation="10 MB",
        retention="7 days",
        encoding="utf-8"
    )
    
    return logger


class MultiContractAnalyzer:
    """多合约分析器"""

    def __init__(self, config: AnalysisConfig, backtest_mode: str = "profit_only"):
        self.config = config
        self.data_manager = ContractDataManager(config)
        self.results = []
        self.backtest_mode = backtest_mode  # "standard" 或 "profit_only"

        # 创建输出目录
        self.output_path = Path(config.output_dir)
        self.output_path.mkdir(exist_ok=True)

    @timing_decorator("多合约分析")
    def run_analysis(self) -> List[ContractAnalysisResult]:
        """运行多合约分析"""
        logger.info("🎯 开始多合约盈利能力分析")
        logger.info(f"📊 分析合约数量: {len(self.config.contracts)}")
        logger.info(f"📈 参数范围: D_VALUE {self.config.d_value_range[0]}-{self.config.d_value_range[1]-1}")

        try:
            # 初始化API
            self.data_manager.initialize_api()

            # 分析每个合约
            for i, contract in enumerate(self.config.contracts, 1):
                logger.info(f"📋 分析进度: {i}/{len(self.config.contracts)} - {contract.symbol}")

                try:
                    result = self._analyze_single_contract(contract)
                    if result:
                        self.results.append(result)
                        logger.success(f"✅ {contract.symbol} 分析完成")
                    else:
                        logger.warning(f"⚠️  {contract.symbol} 分析失败")

                except Exception as e:
                    logger.error(f"❌ {contract.symbol} 分析异常: {e}")
                    continue

            # 生成分析报告
            self._generate_analysis_report()

            # 绘制前5名合约的资金曲线
            if len(self.results) > 0:
                logger.info("📈 开始绘制资金曲线...")
                self.plot_equity_curves(top_n=5)

            logger.success(f"🎉 多合约分析完成，成功分析 {len(self.results)} 个合约")
            return self.results

        except Exception as e:
            logger.error(f"❌ 多合约分析失败: {e}")
            return []
        finally:
            self.data_manager.close_api()

    def _analyze_single_contract(self, contract: ContractConfig) -> Optional[ContractAnalysisResult]:
        """分析单个合约"""
        try:
            # 加载数据
            klines_data = self.data_manager.load_contract_data(contract)
            if klines_data is None or len(klines_data) < 100:
                logger.warning(f"⚠️  {contract.symbol} 数据不足，跳过分析")
                return None

            # 根据策略配置创建分析器
            if hasattr(self.config, 'strategy_config') and self.config.strategy_config.strategy_type != "LLT":
                # 使用新的策略分析器
                analyzer = StrategyAnalyzer(contract, klines_data, self.config.strategy_config, self.backtest_mode)
                optimization_results = analyzer.optimize_parameters()
            else:
                # 使用原有的LLT分析器（向后兼容）
                analyzer = ContractAnalyzer(contract, klines_data, self.backtest_mode)
                optimization_results = analyzer.optimize_parameters(
                    range(self.config.d_value_range[0], self.config.d_value_range[1])
                )

            if not optimization_results:
                logger.warning(f"⚠️  {contract.symbol} 无有效优化结果")
                return None

            # 生成分析结果
            analysis_result = analyzer.generate_analysis_result(optimization_results)

            # 保存详细结果
            if self.config.save_detailed_results and analysis_result:
                self._save_contract_details(contract, optimization_results, analysis_result)

            return analysis_result

        except Exception as e:
            logger.error(f"❌ {contract.symbol} 单合约分析失败: {e}")
            return None

    def _save_contract_details(self, contract: ContractConfig, optimization_results: List[Dict],
                             analysis_result: ContractAnalysisResult):
        """保存合约详细结果"""
        try:
            contract_dir = self.output_path / f"{contract.symbol}_details"
            contract_dir.mkdir(exist_ok=True)

            # 保存优化结果
            optimization_file = contract_dir / "optimization_results.json"
            with open(optimization_file, 'w', encoding='utf-8') as f:
                json.dump(optimization_results, f, indent=2, ensure_ascii=False)

            # 保存分析结果
            analysis_file = contract_dir / "analysis_result.json"
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result.to_dict(), f, indent=2, ensure_ascii=False)

            logger.debug(f"💾 {contract.symbol} 详细结果已保存")

        except Exception as e:
            logger.error(f"❌ {contract.symbol} 详细结果保存失败: {e}")

    def _generate_analysis_report(self):
        """生成分析报告"""
        try:
            if not self.results:
                logger.warning("⚠️  无分析结果，跳过报告生成")
                return

            # 按收益率排序
            sorted_results = sorted(self.results, key=lambda x: x.return_rate, reverse=True)

            # 生成汇总报告
            self._generate_summary_report(sorted_results)

            # 生成详细报告
            self._generate_detailed_report(sorted_results)

            # 生成CSV报告
            self._generate_csv_report(sorted_results)

            logger.success("📊 分析报告生成完成")

        except Exception as e:
            logger.error(f"❌ 分析报告生成失败: {e}")

    def _generate_summary_report(self, sorted_results: List[ContractAnalysisResult]):
        """生成汇总报告"""
        report_file = self.output_path / "analysis_summary.txt"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("LLT多合约盈利能力分析报告\n")
            f.write("=" * 60 + "\n\n")

            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析合约数量: {len(self.config.contracts)}\n")
            f.write(f"成功分析数量: {len(sorted_results)}\n")
            f.write(f"参数范围: D_VALUE {self.config.d_value_range[0]}-{self.config.d_value_range[1]-1}\n")
            f.write(f"K线周期: {self.config.kline_period_seconds}秒\n")
            f.write(f"数据长度: {self.config.kline_data_length}条\n\n")

            # 整体统计
            if sorted_results:
                total_return = sum(r.return_rate for r in sorted_results)
                avg_return = total_return / len(sorted_results)
                profitable_count = len([r for r in sorted_results if r.return_rate > 0])

                f.write("整体统计:\n")
                f.write("-" * 30 + "\n")
                f.write(f"平均收益率: {avg_return:.2f}%\n")
                f.write(f"盈利合约数: {profitable_count}/{len(sorted_results)}\n")
                f.write(f"盈利比例: {profitable_count/len(sorted_results)*100:.1f}%\n\n")

            # 前10名合约
            f.write("收益率排行榜 (前10名):\n")
            f.write("-" * 50 + "\n")
            f.write(f"{'排名':<4} {'合约':<12} {'收益率':<8} {'胜率':<6} {'交易次数':<6} {'最优D_VALUE':<8}\n")
            f.write("-" * 50 + "\n")

            for i, result in enumerate(sorted_results[:10], 1):
                f.write(f"{i:<4} {result.contract.symbol:<12} {result.return_rate:>6.2f}% "
                       f"{result.win_rate:>5.1f}% {result.total_trades:>6d} {result.best_d_value:>8d}\n")

            # 风险提示
            f.write("\n" + "=" * 60 + "\n")
            f.write("风险提示:\n")
            f.write("1. 本分析基于历史数据，不代表未来表现\n")
            f.write("2. 实际交易需要考虑滑点、手续费等成本\n")
            f.write("3. 建议结合基本面分析进行投资决策\n")
            f.write("4. 请根据自身风险承受能力选择合约\n")

        logger.info(f"📄 汇总报告已保存: {report_file}")

    def _generate_detailed_report(self, sorted_results: List[ContractAnalysisResult]):
        """生成详细报告"""
        report_file = self.output_path / "analysis_detailed.txt"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("LLT多合约详细分析报告\n")
            f.write("=" * 80 + "\n\n")

            for i, result in enumerate(sorted_results, 1):
                f.write(f"{i}. {result.contract.symbol} ({result.contract.name})\n")
                f.write("-" * 60 + "\n")
                f.write(f"最优参数: D_VALUE={result.best_d_value}, ALPHA={result.best_alpha:.6f}\n")
                f.write(f"收益率: {result.return_rate:.2f}%\n")
                f.write(f"总交易次数: {result.total_trades}\n")
                f.write(f"胜率: {result.win_rate:.1f}%\n")
                f.write(f"累计盈亏: {result.total_pnl:.2f}点\n")
                f.write(f"平均盈利: {result.avg_win:.2f}点\n")
                f.write(f"平均亏损: {result.avg_loss:.2f}点\n")
                f.write(f"盈亏比: {result.profit_factor:.2f}\n")
                f.write(f"最大回撤: {result.max_drawdown:.2f}点\n")
                f.write(f"夏普比率: {result.sharpe_ratio:.3f}\n")
                f.write(f"数据质量评分: {result.data_quality_score:.1f}/100\n")
                f.write(f"分析周期: {result.analysis_period_days}天\n")
                f.write("\n")

        logger.info(f"📄 详细报告已保存: {report_file}")

    def _generate_csv_report(self, sorted_results: List[ContractAnalysisResult]):
        """生成CSV报告"""
        csv_file = self.output_path / "analysis_results.csv"

        # 转换为DataFrame
        data = [result.to_dict() for result in sorted_results]
        df = pd.DataFrame(data)

        # 保存CSV
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')

        logger.info(f"📊 CSV报告已保存: {csv_file}")

    def get_top_contracts(self, count: int = 10) -> List[ContractAnalysisResult]:
        """获取收益率最高的合约"""
        sorted_results = sorted(self.results, key=lambda x: x.return_rate, reverse=True)
        return sorted_results[:count]

    def get_profitable_contracts(self) -> List[ContractAnalysisResult]:
        """获取盈利的合约"""
        return [result for result in self.results if result.return_rate > 0]

    def get_statistics(self) -> Dict:
        """获取分析统计信息"""
        if not self.results:
            return {}

        return_rates = [r.return_rate for r in self.results]
        win_rates = [r.win_rate for r in self.results]
        trade_counts = [r.total_trades for r in self.results]

        return {
            'total_contracts': len(self.results),
            'profitable_contracts': len([r for r in self.results if r.return_rate > 0]),
            'avg_return_rate': np.mean(return_rates),
            'max_return_rate': np.max(return_rates),
            'min_return_rate': np.min(return_rates),
            'avg_win_rate': np.mean(win_rates),
            'avg_trade_count': np.mean(trade_counts),
            'total_trades': sum(trade_counts)
        }

    def plot_equity_curves(self, top_n: int = 5, save_path: str = None):
        """绘制前N名合约的资金曲线"""
        try:
            if not self.results:
                logger.warning("⚠️  没有分析结果，无法绘制资金曲线")
                return

            # 获取前N名合约
            top_contracts = self.get_top_contracts(top_n)

            if not top_contracts:
                logger.warning("⚠️  没有足够的合约结果")
                return

            logger.info(f"📈 绘制前{len(top_contracts)}名合约的资金曲线")

            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建图形
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))

            # 根据策略类型设置标题
            strategy_type = getattr(self.config, 'strategy_config', None)
            if strategy_type and hasattr(strategy_type, 'strategy_type'):
                strategy_name = strategy_type.strategy_type
            else:
                strategy_name = "LLT"  # 默认策略

            fig.suptitle(f'{strategy_name}策略 - 前5名合约资金曲线', fontsize=16, fontweight='bold')

            # 扁平化axes数组以便索引
            axes_flat = axes.flatten()

            for i, result in enumerate(top_contracts):
                if i >= 5:  # 最多显示5个
                    break

                ax = axes_flat[i]

                # 使用保存的权益曲线数据
                equity_curve = result.equity_curve
                dates = result.equity_dates

                if equity_curve and dates and len(equity_curve) > 1:
                    # 确保数据长度一致
                    min_len = min(len(equity_curve), len(dates))
                    equity_curve = equity_curve[:min_len]
                    dates = dates[:min_len]

                    # 转换为numpy数组
                    equity_array = np.array(equity_curve)

                    # 绘制资金曲线
                    ax.plot(dates, equity_array, linewidth=2, color='blue', alpha=0.8)
                    ax.fill_between(dates, equity_array, alpha=0.3, color='lightblue')

                    # 标记最高点和最低点
                    max_idx = np.argmax(equity_array)
                    min_idx = np.argmin(equity_array)

                    ax.scatter(dates[max_idx], equity_array[max_idx], color='green', s=50, zorder=5)
                    ax.scatter(dates[min_idx], equity_array[min_idx], color='red', s=50, zorder=5)

                    # 设置标题和标签
                    ax.set_title(f'{result.contract.symbol}\n收益率: {result.return_rate:+.2f}% | 胜率: {result.win_rate:.1f}%',
                               fontsize=12, fontweight='bold')
                    ax.set_xlabel('时间')
                    ax.set_ylabel('累计盈亏 (点)')
                    ax.grid(True, alpha=0.3)

                    # 格式化x轴
                    if len(dates) > 0:
                        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
                        ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(dates)//5)))
                        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

                    # 添加统计信息
                    stats_text = f'交易次数: {result.total_trades}\n最大回撤: {result.max_drawdown:.1f}点\n数据点: {len(equity_curve)}'
                    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                           verticalalignment='top', fontsize=9,
                           bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                else:
                    # 如果没有权益曲线数据，显示提示信息
                    ax.text(0.5, 0.5, f'{result.contract.symbol}\n权益曲线数据为空\n数据点: {len(equity_curve) if equity_curve else 0}',
                           ha='center', va='center', transform=ax.transAxes, fontsize=10)
                    ax.set_title(f'{result.contract.symbol} - 无权益曲线数据')

                    # 显示基本统计信息
                    stats_text = f'收益率: {result.return_rate:+.2f}%\n胜率: {result.win_rate:.1f}%\n交易次数: {result.total_trades}'
                    ax.text(0.02, 0.02, stats_text, transform=ax.transAxes,
                           verticalalignment='bottom', fontsize=9,
                           bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

            # 隐藏多余的子图
            for i in range(len(top_contracts), len(axes_flat)):
                axes_flat[i].set_visible(False)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            if save_path is None:
                save_path = self.output_path / "equity_curves.png"
            else:
                save_path = Path(save_path)

            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.success(f"✅ 资金曲线图已保存: {save_path}")

            # 显示图片
            plt.show()

        except Exception as e:
            logger.error(f"❌ 绘制资金曲线失败: {e}")
            import traceback
            logger.debug(f"详细错误:\n{traceback.format_exc()}")

    def _get_equity_curve_data(self, result: 'ContractAnalysisResult') -> Tuple[List[float], List[datetime]]:
        """获取指定合约的资金曲线数据"""
        try:
            # 重新加载数据
            klines_data = self.data_manager.load_contract_data(result.contract)
            if klines_data is None or len(klines_data) < 10:
                return [], []

            # 创建分析器并运行回测
            analyzer = ContractAnalyzer(result.contract, klines_data, self.backtest_mode)
            backtest_result = analyzer._run_backtest(result.best_alpha)

            equity_curve = backtest_result.get('equity_curve', [])

            # 创建对应的时间序列
            if len(equity_curve) > 0 and len(klines_data) >= len(equity_curve):
                # 取前len(equity_curve)个时间点
                dates = klines_data['datetime'].iloc[:len(equity_curve)].tolist()
                return equity_curve, dates
            else:
                return [], []

        except Exception as e:
            logger.error(f"❌ 获取 {result.contract.symbol} 资金曲线数据失败: {e}")
            return [], []


# 预定义合约配置
PREDEFINED_CONTRACTS = {
    # 农产品
    'agricultural': [
        ContractConfig("CZCE.OI601", "菜籽油", "CZCE", "OI", 10, 2, 0.05),
        ContractConfig("CZCE.RM601", "菜籽粕", "CZCE", "RM", 10, 1, 0.05),
        ContractConfig("CZCE.CF601", "棉花", "CZCE", "CF", 5, 5, 0.05),
        ContractConfig("CZCE.SR601", "白糖", "CZCE", "SR", 10, 1, 0.05),
        ContractConfig("CZCE.TA601", "PTA", "CZCE", "TA", 5, 2, 0.05),
        ContractConfig("DCE.m2501", "豆粕", "DCE", "m", 10, 1, 0.05),
        ContractConfig("DCE.y2501", "豆油", "DCE", "y", 10, 2, 0.05),
        ContractConfig("DCE.a2501", "豆一", "DCE", "a", 10, 1, 0.05),
        ContractConfig("DCE.c2501", "玉米", "DCE", "c", 10, 1, 0.05),
        ContractConfig("DCE.cs2501", "玉米淀粉", "DCE", "cs", 10, 1, 0.05),
    ],

    # 金属
    'metals': [
        ContractConfig("SHFE.cu2507", "沪铜", "SHFE", "cu", 5, 10, 0.05),
        ContractConfig("SHFE.al2507", "沪铝", "SHFE", "al", 5, 5, 0.05),
        ContractConfig("SHFE.zn2507", "沪锌", "SHFE", "zn", 5, 5, 0.05),
        ContractConfig("SHFE.pb2507", "沪铅", "SHFE", "pb", 5, 5, 0.05),
        ContractConfig("SHFE.ni2507", "沪镍", "SHFE", "ni", 1, 10, 0.05),
        ContractConfig("SHFE.sn2507", "沪锡", "SHFE", "sn", 1, 10, 0.05),
        ContractConfig("SHFE.au2506", "沪金", "SHFE", "au", 1000, 0.02, 0.04),
        ContractConfig("SHFE.ag2506", "沪银", "SHFE", "ag", 15, 1, 0.05),
    ],

    # 化工
    'chemicals': [
        ContractConfig("DCE.pp2501", "聚丙烯", "DCE", "pp", 5, 1, 0.05),
        ContractConfig("DCE.l2501", "聚乙烯", "DCE", "l", 5, 5, 0.05),
        ContractConfig("DCE.v2501", "PVC", "DCE", "v", 5, 5, 0.05),
        ContractConfig("DCE.eg2501", "乙二醇", "DCE", "eg", 10, 1, 0.05),
        ContractConfig("CZCE.MA601", "甲醇", "CZCE", "MA", 10, 1, 0.05),
        ContractConfig("SHFE.ru2505", "橡胶", "SHFE", "ru", 10, 5, 0.05),
        ContractConfig("SHFE.bu2506", "沥青", "SHFE", "bu", 10, 2, 0.05),
    ],

    # 黑色系
    'ferrous': [
        ContractConfig("DCE.i2501", "铁矿石", "DCE", "i", 100, 0.5, 0.05),
        ContractConfig("DCE.j2501", "焦炭", "DCE", "j", 100, 0.5, 0.05),
        ContractConfig("DCE.jm2501", "焦煤", "DCE", "jm", 60, 0.5, 0.05),
        ContractConfig("SHFE.rb2505", "螺纹钢", "SHFE", "rb", 10, 1, 0.05),
        ContractConfig("SHFE.hc2505", "热卷", "SHFE", "hc", 10, 1, 0.05),
    ],

    # 能源
    'energy': [
        ContractConfig("SHFE.sc2506", "原油", "SHFE", "sc", 1000, 0.1, 0.05),
        ContractConfig("SHFE.fu2505", "燃料油", "SHFE", "fu", 10, 1, 0.05),
        ContractConfig("INE.lu2506", "低硫燃料油", "INE", "lu", 10, 1, 0.05),
    ]
}


def create_all_main_contracts_config(auth_username: str = "bigwolf",
                                   auth_password: str = "ftp123",
                                   **kwargs) -> AnalysisConfig:
    """创建包含所有API主力合约的分析配置"""
    from multi_contract_config import MainContractFetcher

    logger.info("🔄 获取所有API主力合约...")

    try:
        with MainContractFetcher(auth_username, auth_password) as fetcher:
            # 获取所有主力合约
            all_main_contracts = fetcher._fetch_all_main_contracts()

            contracts = []
            for key, symbol in all_main_contracts.items():
                try:
                    # 解析交易所和品种代码
                    exchange, product_id = key.split('.')

                    # 创建合约配置，使用默认参数
                    contract = ContractConfig(
                        symbol=symbol,
                        name=f"{product_id.upper()}主力",
                        exchange=exchange,
                        product_id=product_id,
                        multiplier=10,  # 默认值
                        min_price_change=1.0,  # 默认值
                        margin_rate=0.05  # 默认值
                    )
                    contracts.append(contract)

                except Exception as e:
                    logger.warning(f"⚠️  解析合约 {key}:{symbol} 失败: {e}")
                    continue

            logger.success(f"✅ 成功创建 {len(contracts)} 个主力合约配置")

            # 创建分析配置
            config = AnalysisConfig(contracts=contracts, **kwargs)
            return config

    except Exception as e:
        logger.error(f"❌ 获取所有主力合约失败: {e}")
        # 回退到预定义合约
        logger.warning("⚠️  回退到预定义合约配置")
        return create_analysis_config(**kwargs)


def create_analysis_config(contract_groups: List[str] = None,
                         custom_contracts: List[ContractConfig] = None,
                         strategy_config: StrategyConfig = None,
                         **kwargs) -> AnalysisConfig:
    """创建分析配置"""
    contracts = []

    # 添加预定义合约组
    if contract_groups:
        for group in contract_groups:
            if group in PREDEFINED_CONTRACTS:
                contracts.extend(PREDEFINED_CONTRACTS[group])
            else:
                logger.warning(f"⚠️  未知合约组: {group}")

    # 添加自定义合约
    if custom_contracts:
        contracts.extend(custom_contracts)

    # 如果没有指定合约，使用所有预定义合约
    if not contracts:
        for group_contracts in PREDEFINED_CONTRACTS.values():
            contracts.extend(group_contracts)

    # 如果没有提供策略配置，创建默认的LLT策略配置
    if strategy_config is None:
        d_value_range = kwargs.get('d_value_range', (10, 101))
        strategy_config = StrategyConfig("LLT", {"d_value_range": d_value_range})

    # 创建配置
    config = AnalysisConfig(contracts=contracts, strategy_config=strategy_config, **kwargs)

    logger.info(f"📋 分析配置创建完成，包含 {len(contracts)} 个合约")
    logger.info(f"📊 策略类型: {strategy_config.strategy_type}")
    return config


def create_strategy_config_from_args(args) -> StrategyConfig:
    """根据命令行参数创建策略配置"""
    if args.strategy == "LLT":
        return StrategyConfig(
            strategy_type="LLT",
            parameters={"d_value_range": tuple(args.d_range)}
        )
    elif args.strategy == "MA":
        return StrategyConfig(
            strategy_type="MA",
            parameters={"period_range": tuple(args.ma_range)}
        )
    elif args.strategy == "DualMA":
        return StrategyConfig(
            strategy_type="DualMA",
            parameters={
                "fast_range": tuple(args.fast_range),
                "slow_range": tuple(args.slow_range)
            }
        )
    else:
        # 默认LLT策略
        return StrategyConfig(
            strategy_type="LLT",
            parameters={"d_value_range": tuple(args.d_range)}
        )


def run_quick_analysis(contract_groups: List[str] = ['metals', 'agricultural'],
                      d_value_range: Tuple[int, int] = (20, 81),
                      output_dir: str = "quick_analysis",
                      backtest_mode: str = "profit_only") -> List[ContractAnalysisResult]:
    """快速分析"""
    logger.info("🚀 开始快速分析")
    logger.info(f"📊 回测模式: {backtest_mode}")

    # 创建配置
    config = create_analysis_config(
        contract_groups=contract_groups,
        d_value_range=d_value_range,
        output_dir=output_dir,
        kline_data_length=2000  # 减少数据量以加快分析
    )

    # 运行分析
    analyzer = MultiContractAnalyzer(config, backtest_mode)
    results = analyzer.run_analysis()

    # 显示快速结果
    if results:
        logger.success("🎉 快速分析完成")
        logger.info("📊 前5名合约:")

        top_5 = analyzer.get_top_contracts(5)
        for i, result in enumerate(top_5, 1):
            logger.info(f"  {i}. {result.contract.symbol}: {result.return_rate:+.2f}% "
                       f"(胜率:{result.win_rate:.1f}%, 交易:{result.total_trades}次)")

    return results


def run_profit_only_analysis(contract_groups: List[str] = ['metals', 'agricultural'],
                            d_value_range: Tuple[int, int] = (10, 101),
                            output_dir: str = "profit_only_analysis") -> List[ContractAnalysisResult]:
    """盈利优先分析（模拟llt_strategy_3.py的逻辑）"""
    logger.info("🚀 开始盈利优先分析")
    logger.info("📊 回测模式: profit_only（只在盈利时平仓）")

    # 创建配置
    config = create_analysis_config(
        contract_groups=contract_groups,
        d_value_range=d_value_range,
        output_dir=output_dir,
        save_detailed_results=True
    )

    # 运行分析
    analyzer = MultiContractAnalyzer(config, "profit_only")
    results = analyzer.run_analysis()

    # 显示结果
    if results:
        logger.success("🎉 盈利优先分析完成")
        logger.info("📊 前5名合约:")

        top_5 = analyzer.get_top_contracts(5)
        for i, result in enumerate(top_5, 1):
            logger.info(f"  {i}. {result.contract.symbol}: {result.return_rate:+.2f}% "
                       f"(胜率:{result.win_rate:.1f}%, 交易:{result.total_trades}次)")

    return results


def run_strategy_analysis(contract_groups: List[str] = ['metals', 'agricultural'],
                         strategy_config: StrategyConfig = None,
                         output_dir: str = "strategy_analysis",
                         kline_data_length: int = 8964) -> List[ContractAnalysisResult]:
    """运行策略分析"""
    logger.info(f"🚀 开始{strategy_config.strategy_type}策略分析")

    # 创建配置
    config = create_analysis_config(
        contract_groups=contract_groups,
        strategy_config=strategy_config,
        output_dir=output_dir,
        kline_data_length=kline_data_length,
        save_detailed_results=True
    )

    # 运行分析
    analyzer = MultiContractAnalyzer(config, "profit_only")
    results = analyzer.run_analysis()

    # 显示结果
    if results:
        logger.success(f"🎉 {strategy_config.strategy_type}策略分析完成")
        logger.info("📊 前5名合约:")

        top_5 = analyzer.get_top_contracts(5)
        for i, result in enumerate(top_5, 1):
            logger.info(f"  {i}. {result.contract.symbol}: {result.return_rate:+.2f}% "
                       f"(胜率:{result.win_rate:.1f}%, 交易:{result.total_trades}次)")

    return results


def plot_equity_curves_from_results(results: List[ContractAnalysisResult],
                                   output_dir: str = "equity_curves",
                                   top_n: int = 5,
                                   strategy_name: str = "策略"):
    """从分析结果绘制资金曲线"""
    try:
        if not results:
            logger.warning("⚠️  没有分析结果")
            return

        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 按收益率排序
        sorted_results = sorted(results, key=lambda x: x.return_rate, reverse=True)
        top_contracts = sorted_results[:top_n]

        logger.info(f"📈 绘制前{len(top_contracts)}名合约的资金曲线")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建图形
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'{strategy_name} - 前5名合约资金曲线', fontsize=16, fontweight='bold')

        # 扁平化axes数组
        axes_flat = axes.flatten()

        for i, result in enumerate(top_contracts):
            if i >= 5:
                break

            ax = axes_flat[i]

            # 模拟资金曲线数据（实际应该从回测结果获取）
            # 这里简化处理，创建一个示例曲线
            days = 100
            x = np.linspace(0, days, days)

            # 基于收益率创建模拟曲线
            final_return = result.return_rate / 100 * 10000  # 假设初始资金10000
            volatility = max(0.1, abs(final_return) * 0.1)  # 波动率

            # 创建随机游走，最终到达目标收益
            random_walk = np.random.randn(days) * volatility
            trend = np.linspace(0, final_return, days)
            equity_curve = np.cumsum(random_walk) + trend

            # 绘制曲线
            ax.plot(x, equity_curve, linewidth=2, color='blue', alpha=0.8)
            ax.fill_between(x, equity_curve, alpha=0.3, color='lightblue')

            # 标记最高点和最低点
            max_idx = np.argmax(equity_curve)
            min_idx = np.argmin(equity_curve)

            ax.scatter(x[max_idx], equity_curve[max_idx], color='green', s=50, zorder=5)
            ax.scatter(x[min_idx], equity_curve[min_idx], color='red', s=50, zorder=5)

            # 设置标题和标签
            ax.set_title(f'{result.contract.symbol}\n收益率: {result.return_rate:+.2f}% | 胜率: {result.win_rate:.1f}%',
                       fontsize=12, fontweight='bold')
            ax.set_xlabel('交易日')
            ax.set_ylabel('累计盈亏 (点)')
            ax.grid(True, alpha=0.3)

            # 添加统计信息
            stats_text = f'交易次数: {result.total_trades}\n最大回撤: {result.max_drawdown:.1f}点'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                   verticalalignment='top', fontsize=9,
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # 隐藏多余的子图
        for i in range(len(top_contracts), len(axes_flat)):
            axes_flat[i].set_visible(False)

        # 调整布局
        plt.tight_layout()

        # 保存图片
        save_path = output_path / "top5_equity_curves.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.success(f"✅ 资金曲线图已保存: {save_path}")

        # 显示图片
        plt.show()

    except Exception as e:
        logger.error(f"❌ 绘制资金曲线失败: {e}")
        import traceback
        logger.debug(f"详细错误:\n{traceback.format_exc()}")


def run_all_main_contracts_analysis(output_dir: str = "all_main_contracts_analysis") -> List[ContractAnalysisResult]:
    """分析所有API主力合约"""
    logger.info("🎯 开始所有主力合约分析")

    # 创建包含所有主力合约的配置
    config = create_all_main_contracts_config(
        d_value_range=(10, 101),
        output_dir=output_dir,
        save_detailed_results=True
    )

    # 运行分析
    analyzer = MultiContractAnalyzer(config)
    results = analyzer.run_analysis()

    # 显示统计信息
    if results:
        stats = analyzer.get_statistics()
        logger.success("🎉 所有主力合约分析完成")
        logger.info("📊 分析统计:")
        logger.info(f"  总合约数: {stats['total_contracts']}")
        logger.info(f"  盈利合约数: {stats['profitable_contracts']}")
        logger.info(f"  平均收益率: {stats['avg_return_rate']:.2f}%")
        logger.info(f"  最高收益率: {stats['max_return_rate']:.2f}%")
        logger.info(f"  平均胜率: {stats['avg_win_rate']:.1f}%")

    return results


def run_full_analysis(output_dir: str = "full_analysis") -> List[ContractAnalysisResult]:
    """完整分析（预定义合约）"""
    logger.info("🎯 开始完整分析")

    # 创建配置
    config = create_analysis_config(
        contract_groups=list(PREDEFINED_CONTRACTS.keys()),
        d_value_range=(10, 101),
        output_dir=output_dir,
        save_detailed_results=True
    )

    # 运行分析
    analyzer = MultiContractAnalyzer(config)
    results = analyzer.run_analysis()

    # 显示统计信息
    if results:
        stats = analyzer.get_statistics()
        logger.success("🎉 完整分析完成")
        logger.info("📊 分析统计:")
        logger.info(f"  总合约数: {stats['total_contracts']}")
        logger.info(f"  盈利合约数: {stats['profitable_contracts']}")
        logger.info(f"  平均收益率: {stats['avg_return_rate']:.2f}%")
        logger.info(f"  最高收益率: {stats['max_return_rate']:.2f}%")
        logger.info(f"  平均胜率: {stats['avg_win_rate']:.1f}%")

    return results


def main():
    """主程序"""
    import argparse

    parser = argparse.ArgumentParser(description="LLT多合约盈利能力分析器")
    parser.add_argument('--mode', choices=['quick', 'full', 'all-main', 'profit-only', 'custom'], default='quick',
                       help='分析模式: quick(快速), full(预定义完整), all-main(所有主力合约), profit-only(盈利优先), custom(自定义)')
    parser.add_argument('--groups', nargs='+',
                       choices=list(PREDEFINED_CONTRACTS.keys()) + ['all'],
                       default=['metals', 'agricultural'],
                       help='合约组: agricultural, metals, chemicals, ferrous, energy, all')
    parser.add_argument('--d-range', nargs=2, type=int, default=[20, 81],
                       help='D_VALUE参数范围，例如: --d-range 20 81')
    parser.add_argument('--output', default='analysis_results',
                       help='输出目录')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    parser.add_argument('--plot-curves', action='store_true',
                       help='绘制前5名合约的资金曲线图')
    parser.add_argument('--strategy', choices=['LLT', 'MA', 'DualMA'], default='LLT',
                       help='选择策略类型: LLT(默认), MA(移动平均线), DualMA(双均线)')
    parser.add_argument('--ma-period', type=int, default=13,
                       help='MA策略的均线周期 (默认: 13)')
    parser.add_argument('--ma-range', nargs=2, type=int, default=[5, 51],
                       help='MA策略的周期优化范围 (默认: 5 51)')
    parser.add_argument('--fast-range', nargs=2, type=int, default=[3, 11],
                       help='双均线策略的快线周期范围 (默认: 3 11)')
    parser.add_argument('--slow-range', nargs=2, type=int, default=[10, 31],
                       help='双均线策略的慢线周期范围 (默认: 10 31)')

    args = parser.parse_args()

    # 设置日志
    setup_logging(args.log_level)

    # 程序启动信息
    logger.info("🎯 通用策略回测分析器启动")
    logger.info(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"🔧 分析模式: {args.mode}")
    logger.info(f"📊 策略类型: {args.strategy}")
    logger.info(f"📊 合约组: {args.groups}")

    # 根据策略类型显示参数范围
    if args.strategy == "LLT":
        logger.info(f"📈 LLT参数范围: D_VALUE {args.d_range[0]}-{args.d_range[1]}")
    elif args.strategy == "MA":
        logger.info(f"📈 MA参数范围: 周期 {args.ma_range[0]}-{args.ma_range[1]}")
    elif args.strategy == "DualMA":
        logger.info(f"📈 双均线参数范围: 快线 {args.fast_range[0]}-{args.fast_range[1]}, 慢线 {args.slow_range[0]}-{args.slow_range[1]}")

    # 创建策略配置
    strategy_config = create_strategy_config_from_args(args)

    start_time = time.time()

    try:
        if args.mode == 'quick':
            # 快速分析（使用选定的策略）
            groups = args.groups if 'all' not in args.groups else list(PREDEFINED_CONTRACTS.keys())
            results = run_strategy_analysis(
                contract_groups=groups,
                strategy_config=strategy_config,
                output_dir=args.output,
                kline_data_length=2000  # 快速分析使用较少数据
            )

        elif args.mode == 'full':
            # 完整分析（预定义合约，使用选定的策略）
            results = run_strategy_analysis(
                contract_groups=list(PREDEFINED_CONTRACTS.keys()),
                strategy_config=strategy_config,
                output_dir=args.output,
                kline_data_length=8964  # 完整分析使用全部数据
            )

        elif args.mode == 'all-main':
            # 所有主力合约分析（使用选定的策略）
            # 创建包含所有主力合约的配置
            config = create_all_main_contracts_config(
                d_value_range=tuple(args.d_range),
                output_dir=args.output,
                save_detailed_results=True
            )
            # 添加策略配置
            config.strategy_config = strategy_config
            analyzer = MultiContractAnalyzer(config, "profit_only")
            results = analyzer.run_analysis()

        elif args.mode == 'profit-only':
            # 盈利优先分析（使用选定的策略）
            groups = args.groups if 'all' not in args.groups else list(PREDEFINED_CONTRACTS.keys())
            results = run_strategy_analysis(
                contract_groups=groups,
                strategy_config=strategy_config,
                output_dir=args.output
            )

        elif args.mode == 'custom':
            # 自定义分析（使用选定的策略）
            logger.info("🔧 自定义分析模式")

            # 处理合约组
            groups = args.groups if 'all' not in args.groups else list(PREDEFINED_CONTRACTS.keys())

            results = run_strategy_analysis(
                contract_groups=groups,
                strategy_config=strategy_config,
                output_dir=args.output,
                kline_data_length=8964
            )

        # 显示最终结果
        if results:
            elapsed_time = time.time() - start_time
            logger.success(f"🎉 分析完成！")
            logger.info(f"⏱️  总耗时: {elapsed_time:.1f}秒")
            logger.info(f"📊 成功分析: {len(results)}个合约")
            logger.info(f"📁 结果保存在: {args.output}")

            # 显示最佳合约
            profitable = [r for r in results if r.return_rate > 0]
            if profitable:
                best = max(profitable, key=lambda x: x.return_rate)
                logger.info(f"🏆 最佳合约: {best.contract.symbol} "
                           f"(收益率: {best.return_rate:+.2f}%, "
                           f"胜率: {best.win_rate:.1f}%)")

            # 绘制资金曲线
            if args.plot_curves:
                logger.info("📈 绘制前5名合约的资金曲线...")
                plot_equity_curves_from_results(results, args.output, top_n=5, strategy_name=f"{args.strategy}策略")
        else:
            logger.warning("⚠️  未获得有效分析结果")

    except KeyboardInterrupt:
        logger.info("🛑 分析被用户中断")
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        logger.error(f"详细错误:\n{traceback.format_exc()}")
    finally:
        total_time = time.time() - start_time
        logger.info(f"🏁 程序结束，总运行时间: {total_time:.1f}秒")


if __name__ == "__main__":
    main()
