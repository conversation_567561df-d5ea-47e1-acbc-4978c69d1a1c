"""
多合约分析器测试脚本
验证核心功能是否正常工作
"""

import sys
import time
import pandas as pd
import numpy as np
from loguru import logger
from llt_multi_contract_analyzer import *
from multi_contract_config import *


def test_contract_config():
    """测试合约配置"""
    print("=" * 60)
    print("测试合约配置")
    print("=" * 60)
    
    try:
        # 测试基本合约配置
        contract = ContractConfig("SHFE.cu2507", "沪铜07", "SHFE")
        print(f"✅ 合约配置创建成功: {contract.symbol} - {contract.name}")
        
        # 测试自动推断
        contract2 = ContractConfig("DCE.m2501")
        print(f"✅ 自动推断成功: {contract2.symbol} - {contract2.name}")
        
        return True
    except Exception as e:
        print(f"❌ 合约配置测试失败: {e}")
        return False


def test_analysis_config():
    """测试分析配置"""
    print("=" * 60)
    print("测试分析配置")
    print("=" * 60)
    
    try:
        # 创建测试合约
        contracts = [
            ContractConfig("SHFE.cu2507", "沪铜测试", "SHFE"),
            ContractConfig("DCE.m2501", "豆粕测试", "DCE"),
        ]
        
        # 创建分析配置
        config = AnalysisConfig(
            contracts=contracts,
            d_value_range=(20, 31),  # 小范围测试
            kline_data_length=100,   # 少量数据
            output_dir="test_output"
        )
        
        print(f"✅ 分析配置创建成功")
        print(f"   合约数量: {len(config.contracts)}")
        print(f"   参数范围: {config.d_value_range}")
        print(f"   D_VALUE列表: {config.d_value_list}")
        
        return True
    except Exception as e:
        print(f"❌ 分析配置测试失败: {e}")
        return False


def test_llt_indicator():
    """测试LLT指标计算"""
    print("=" * 60)
    print("测试LLT指标计算")
    print("=" * 60)
    
    try:
        # 创建测试数据
        np.random.seed(42)
        prices = np.random.randn(50).cumsum() + 100
        price_series = pd.Series(prices)
        
        print(f"测试数据: {len(price_series)}个价格点")
        
        # 测试LLT计算
        alpha = 0.1
        llt_values = LLTIndicator.calculate(price_series, alpha)
        
        print(f"✅ LLT计算成功: 输入{len(price_series)}个点，输出{len(llt_values)}个点")
        
        # 测试信号生成
        signals = LLTIndicator.generate_signals(llt_values)
        
        buy_signals = signals.count(1)
        sell_signals = signals.count(-1)
        no_signals = signals.count(0)
        
        print(f"✅ 信号生成成功:")
        print(f"   买入信号: {buy_signals}个")
        print(f"   卖出信号: {sell_signals}个")
        print(f"   无信号: {no_signals}个")
        
        return True
    except Exception as e:
        print(f"❌ LLT指标测试失败: {e}")
        return False


def test_contract_analyzer():
    """测试单合约分析器"""
    print("=" * 60)
    print("测试单合约分析器")
    print("=" * 60)
    
    try:
        # 创建模拟K线数据
        np.random.seed(42)
        n_points = 200
        
        dates = pd.date_range('2024-01-01', periods=n_points, freq='5min')
        prices = np.random.randn(n_points).cumsum() + 100
        volumes = np.random.randint(100, 1000, n_points)
        
        klines_data = pd.DataFrame({
            'datetime': dates,
            'open': prices + np.random.randn(n_points) * 0.1,
            'high': prices + np.abs(np.random.randn(n_points)) * 0.5,
            'low': prices - np.abs(np.random.randn(n_points)) * 0.5,
            'close': prices,
            'volume': volumes
        })
        
        print(f"模拟数据: {len(klines_data)}条K线")
        
        # 创建合约和分析器
        contract = ContractConfig("TEST.contract", "测试合约", "TEST")
        analyzer = ContractAnalyzer(contract, klines_data)
        
        # 测试参数优化（小范围）
        print("开始参数优化测试...")
        optimization_results = analyzer.optimize_parameters(range(20, 26))  # 只测试5个参数
        
        print(f"✅ 参数优化成功: {len(optimization_results)}个有效结果")
        
        if optimization_results:
            best = optimization_results[0]
            print(f"   最佳D_VALUE: {best['D_VALUE']}")
            print(f"   收益率: {best['return_rate']:.2f}%")
            print(f"   胜率: {best['win_rate']:.1f}%")
            print(f"   交易次数: {best['total_trades']}")
            
            # 测试生成分析结果
            analysis_result = analyzer.generate_analysis_result(optimization_results)
            if analysis_result:
                print(f"✅ 分析结果生成成功")
                print(f"   数据质量评分: {analysis_result.data_quality_score:.1f}")
                print(f"   分析周期: {analysis_result.analysis_period_days}天")
        
        return True
    except Exception as e:
        print(f"❌ 单合约分析器测试失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False


def test_preset_configs():
    """测试预设配置"""
    print("=" * 60)
    print("测试预设配置")
    print("=" * 60)
    
    try:
        # 测试列出预设配置
        print("可用预设配置:")
        for name in PRESET_CONFIGS.keys():
            config = PRESET_CONFIGS[name]
            print(f"  {name}: {config.description}")
        
        # 测试获取预设配置
        test_config = get_preset_config('quick_test')
        print(f"✅ 预设配置获取成功: {len(test_config.contracts)}个合约")
        
        # 测试自定义合约列表
        custom_symbols = ["SHFE.cu2507", "DCE.m2501"]
        custom_contracts = create_custom_contract_list(custom_symbols)
        print(f"✅ 自定义合约列表创建成功: {len(custom_contracts)}个合约")
        
        return True
    except Exception as e:
        print(f"❌ 预设配置测试失败: {e}")
        return False


def test_data_structures():
    """测试数据结构"""
    print("=" * 60)
    print("测试数据结构")
    print("=" * 60)
    
    try:
        # 测试ContractAnalysisResult
        contract = ContractConfig("TEST.symbol", "测试", "TEST")
        result = ContractAnalysisResult(
            contract=contract,
            best_d_value=30,
            best_alpha=0.06,
            total_trades=100,
            total_pnl=500.0,
            win_rate=65.0,
            return_rate=5.0,
            winning_trades=65,
            losing_trades=35,
            avg_win=12.5,
            avg_loss=-8.2,
            profit_factor=1.52,
            max_drawdown=150.0,
            sharpe_ratio=0.85,
            analysis_period_days=365,
            data_quality_score=95.0
        )
        
        # 测试转换为字典
        result_dict = result.to_dict()
        print(f"✅ 分析结果数据结构测试成功")
        print(f"   字典键数量: {len(result_dict)}")
        print(f"   合约: {result_dict['symbol']}")
        print(f"   收益率: {result_dict['return_rate']}%")
        
        return True
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        return False


def test_logging_setup():
    """测试日志设置"""
    print("=" * 60)
    print("测试日志设置")
    print("=" * 60)
    
    try:
        # 测试日志设置
        setup_logging("INFO")
        
        # 测试不同级别的日志
        logger.debug("这是DEBUG日志")
        logger.info("这是INFO日志")
        logger.success("这是SUCCESS日志")
        logger.warning("这是WARNING日志")
        logger.error("这是ERROR日志")
        
        print("✅ 日志设置测试成功")
        return True
    except Exception as e:
        print(f"❌ 日志设置测试失败: {e}")
        return False


def run_mini_analysis():
    """运行迷你分析测试"""
    print("=" * 60)
    print("运行迷你分析测试")
    print("=" * 60)
    
    try:
        # 设置日志
        setup_logging("INFO")
        
        # 创建最小配置
        test_contracts = [
            ContractConfig("TEST.contract1", "测试合约1", "TEST"),
            ContractConfig("TEST.contract2", "测试合约2", "TEST"),
        ]
        
        config = AnalysisConfig(
            contracts=test_contracts,
            d_value_range=(25, 31),  # 只测试6个参数
            kline_data_length=100,   # 少量数据
            output_dir="mini_test_output"
        )
        
        print(f"迷你分析配置:")
        print(f"  合约数量: {len(config.contracts)}")
        print(f"  参数范围: {config.d_value_range}")
        print(f"  数据长度: {config.kline_data_length}")
        
        # 注意：这里不能真正运行MultiContractAnalyzer，因为需要真实的API连接
        # 但我们可以测试配置是否正确
        
        print("✅ 迷你分析配置测试成功")
        print("💡 注意：完整分析需要TqSDK API连接")
        
        return True
    except Exception as e:
        print(f"❌ 迷你分析测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("LLT多合约分析器功能测试")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("合约配置", test_contract_config),
        ("分析配置", test_analysis_config),
        ("LLT指标计算", test_llt_indicator),
        ("单合约分析器", test_contract_analyzer),
        ("预设配置", test_preset_configs),
        ("数据结构", test_data_structures),
        ("日志设置", test_logging_setup),
        ("迷你分析", run_mini_analysis),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！多合约分析器功能正常")
        print("\n📋 下一步:")
        print("1. 配置TqSDK API认证信息")
        print("2. 运行实际的多合约分析")
        print("3. 查看生成的分析报告")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
