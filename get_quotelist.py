from tqsdk import TqApi, TqKq
symbollist=[
    # 'DCE.pp2301',
    # 'CZCE.OI301',
    'CZCE.OI305',
    # 'CZCE.OI307'
]
# 交易账号设置

api = TqApi(TqKq(), auth="wolfquant2023,Qiai1301", debug=False, disable_print=True)
quotelist = api.get_quote_list(symbollist)

while True:
    api.wait_update()
    if api.is_changing(quotelist):
        for q in quotelist:
            print(q.datetime, q.instrument_id, q.last_price,'ask1:', q.ask_price1,'bid1:', q.bid_price1)

