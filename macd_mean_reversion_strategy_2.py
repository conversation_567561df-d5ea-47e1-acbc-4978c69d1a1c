import pandas as pd
import numpy as np
from dataclasses import dataclass
from typing import Tuple, List, Dict
from datetime import datetime
import math


@dataclass
class TradeStats:
    total_trades: int
    profitable_trades: int
    losing_trades: int
    win_rate: float
    profit_loss_ratio: float
    max_profit: float
    max_loss: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    total_returns: float
    sharpe_ratio: float
    max_drawdown: float
    avg_profit_per_trade: float
    avg_loss_per_trade: float


class TradingStrategy:
    def __init__(self, lookback_period: int = 20):
        self.lookback_period = lookback_period
        self.positions = []
        self.trades = []

    def calculate_macd(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        exp12 = data['close'].ewm(span=12, adjust=False).mean()
        exp26 = data['close'].ewm(span=26, adjust=False).mean()

        data['diff'] = exp12 - exp26
        data['dea'] = data['diff'].ewm(span=9, adjust=False).mean()
        data['macd'] = 2 * (data['diff'] - data['dea'])

        return data

    def calculate_mean_reversion(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算均值回归信号"""
        data['ma'] = data['close'].rolling(window=self.lookback_period).mean()
        data['std'] = data['close'].rolling(window=self.lookback_period).std()

        data['upper_band'] = data['ma'] + 2 * data['std']
        data['lower_band'] = data['ma'] - 2 * data['std']

        data['mean_reversion_long'] = (data['close'] < data['lower_band']).astype(int)
        data['mean_reversion_short'] = (data['close'] > data['upper_band']).astype(int)

        return data

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        data = self.calculate_macd(data)
        data = self.calculate_mean_reversion(data)

        data['signal'] = 0

        data['macd_cross_up'] = (data['macd'] > 0) & (data['macd'].shift(1) <= 0)
        data['macd_cross_down'] = (data['macd'] < 0) & (data['macd'].shift(1) >= 0)

        data['diff_cross_up'] = (data['diff'] > data['dea']) & (data['diff'].shift(1) <= data['dea'].shift(1))
        data['diff_cross_down'] = (data['diff'] < data['dea']) & (data['diff'].shift(1) >= data['dea'].shift(1))

        for i in range(1, len(data)):
            if data.iloc[i]['macd_cross_up']:
                data.iloc[i, data.columns.get_loc('signal')] = 1
            elif data.iloc[i]['macd_cross_down']:
                data.iloc[i, data.columns.get_loc('signal')] = -1
            elif (data.iloc[i]['mean_reversion_short'] or data.iloc[i]['diff_cross_down']) and \
                    data.iloc[i - 1]['signal'] == 1:
                data.iloc[i, data.columns.get_loc('signal')] = 0
            elif (data.iloc[i]['mean_reversion_long'] or data.iloc[i]['diff_cross_up']) and \
                    data.iloc[i - 1]['signal'] == -1:
                data.iloc[i, data.columns.get_loc('signal')] = 0
            else:
                data.iloc[i, data.columns.get_loc('signal')] = data.iloc[i - 1]['signal']

        return data

@dataclass
class BacktestStats:
    """回测统计结果数据类"""
    # 基础信息
    strategy_name: str
    start_date: datetime
    end_date: datetime
    test_days: int
    test_periods: int
    signal_count: int
    executed_signals: int
    contract_unit: int

    # 资金统计
    initial_capital: float
    final_equity: float
    max_equity: float
    min_equity: float
    avg_capital_usage: float
    max_capital_usage: float
    max_capital_usage_time: datetime
    margin_ratio: float
    leverage_ratio: float

    # 收益统计
    total_returns: float
    returns_long: float
    returns_short: float
    returns_rate: float
    returns_rate_long: float
    returns_rate_short: float
    annual_returns: float
    monthly_returns: float
    annual_compound_returns: float
    monthly_compound_returns: float
    avg_margin_returns: float
    max_drawdown: float
    max_drawdown_time: datetime
    max_drawdown_rate: float
    max_drawdown_rate_time: datetime
    longest_drawdown_periods: int
    longest_drawdown_time: Tuple[datetime, datetime]

    # 交易统计
    total_trades: int
    long_trades: int
    short_trades: int
    win_rate: float
    win_rate_long: float
    win_rate_short: float
    profit_factor: float
    profit_factor_long: float
    profit_factor_short: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    r_squared: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float

    # 盈亏统计
    total_profit: float
    total_loss: float
    max_single_profit: float
    max_single_profit_time: datetime
    max_single_loss: float
    max_single_loss_time: datetime
    avg_profit: float
    avg_loss: float
    avg_profit_per_trade: float

    # 持仓统计
    avg_holding_periods: float
    max_holding_periods: int
    avg_profit_holding_periods: float
    avg_loss_holding_periods: float
    avg_position_size: float
    max_position_size: int

    # 交易成本
    commission_total: float
    commission_per_return: float
    slippage_total: float
    slippage_per_return: float
    total_turnover: float


class EnhancedBacktester:
    def __init__(self,
                 initial_capital: float = 500000,
                 margin_ratio: float = 0.12,
                 commission_rate: float = 0.0103,
                 slippage: float = 0,
                 contract_unit: int = 10,
                 lookback_period: int = 20):
        self.initial_capital = initial_capital
        self.margin_ratio = margin_ratio
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.contract_unit = contract_unit
        self.trades = []
        self.equity_curve = []

        # def __init__(self, ):
        self.lookback_period = lookback_period
        self.positions = []
        self.trades = []
    def calculate_macd(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        exp12 = data['close'].ewm(span=12, adjust=False).mean()
        exp26 = data['close'].ewm(span=26, adjust=False).mean()

        data['diff'] = exp12 - exp26
        data['dea'] = data['diff'].ewm(span=9, adjust=False).mean()
        data['macd'] = 2 * (data['diff'] - data['dea'])

        return data

    def calculate_mean_reversion(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算均值回归信号"""
        data['ma'] = data['close'].rolling(window=self.lookback_period).mean()
        data['std'] = data['close'].rolling(window=self.lookback_period).std()

        data['upper_band'] = data['ma'] + 2 * data['std']
        data['lower_band'] = data['ma'] - 2 * data['std']

        data['mean_reversion_long'] = (data['close'] < data['lower_band']).astype(int)
        data['mean_reversion_short'] = (data['close'] > data['upper_band']).astype(int)

        return data

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        data = self.calculate_macd(data)
        data = self.calculate_mean_reversion(data)

        data['signal'] = 0

        data['macd_cross_up'] = (data['macd'] > 0) & (data['macd'].shift(1) <= 0)
        data['macd_cross_down'] = (data['macd'] < 0) & (data['macd'].shift(1) >= 0)

        data['diff_cross_up'] = (data['diff'] > data['dea']) & (data['diff'].shift(1) <= data['dea'].shift(1))
        data['diff_cross_down'] = (data['diff'] < data['dea']) & (data['diff'].shift(1) >= data['dea'].shift(1))

        for i in range(1, len(data)):
            if data.iloc[i]['macd_cross_up']:
                data.iloc[i, data.columns.get_loc('signal')] = 1
            elif data.iloc[i]['macd_cross_down']:
                data.iloc[i, data.columns.get_loc('signal')] = -1
            elif (data.iloc[i]['mean_reversion_short'] or data.iloc[i]['diff_cross_down']) and \
                    data.iloc[i - 1]['signal'] == 1:
                data.iloc[i, data.columns.get_loc('signal')] = 0
            elif (data.iloc[i]['mean_reversion_long'] or data.iloc[i]['diff_cross_up']) and \
                    data.iloc[i - 1]['signal'] == -1:
                data.iloc[i, data.columns.get_loc('signal')] = 0
            else:
                data.iloc[i, data.columns.get_loc('signal')] = data.iloc[i - 1]['signal']

        return data

    def calculate_returns(self, trade: Dict) -> Dict:
        """计算单笔交易的收益"""
        # 提取交易信息
        position = trade['position']  # 1表示多头，-1表示空头
        entry_price = trade['entry_price']
        exit_price = trade['exit_price']
        position_size = trade['position_size']

        # 计算毛收益
        # 多头：(卖出价 - 买入价) * 合约数量 * 合约单位
        # 空头：(买入价 - 卖出价) * 合约数量 * 合约单位
        gross_profit = (exit_price - entry_price) * position * position_size * self.contract_unit

        # 计算交易成本
        # 交易额 = (开仓价 + 平仓价) * 合约数量 * 合约单位
        turnover = (entry_price + exit_price) * abs(position_size) * self.contract_unit
        # 手续费 = 交易额 * 手续费率
        commission = turnover * self.commission_rate
        # 滑点成本 = 滑点 * 合约数量 * 2（开仓和平仓）
        slippage_cost = self.slippage * abs(position_size) * 2

        # 计算净收益
        net_profit = gross_profit - commission - slippage_cost

        # 更新交易记录
        trade.update({
            'gross_profit': gross_profit,
            'commission': commission,
            'slippage': slippage_cost,
            'net_profit': net_profit,
            'turnover': turnover,
            'return_rate': net_profit / (entry_price * abs(position_size) * self.contract_unit * self.margin_ratio)
        })

        return trade
    def calculate_sharpe_ratio(self, equity_curve: np.ndarray) -> float:
        """计算夏普比率"""
        returns = np.diff(equity_curve) / equity_curve[:-1]
        excess_returns = returns - 0.04 / 252  # 假设无风险利率为4%
        if len(excess_returns) < 2:
            return 0
        return np.sqrt(252) * np.mean(excess_returns) / np.std(excess_returns)

    def calculate_sortino_ratio(self, equity_curve: np.ndarray) -> float:
        """计算索提诺比率"""
        returns = np.diff(equity_curve) / equity_curve[:-1]
        excess_returns = returns - 0.04 / 252
        downside_returns = excess_returns[excess_returns < 0]
        if len(downside_returns) < 2:
            return 0
        return np.sqrt(252) * np.mean(excess_returns) / np.std(downside_returns)

    def get_max_consecutive(self, trades: List[Dict], is_win: bool) -> int:
        """计算最大连续盈利/亏损次数"""
        max_consecutive = 0
        current_consecutive = 0

        for trade in trades:
            if (trade['net_profit'] > 0) == is_win:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def analyze_trades(self, trades: List[Dict]) -> TradeStats:
        """分析交易统计指标"""
        profits = []
        consecutive_wins = 0
        consecutive_losses = 0
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        current_streak = 0

        # 计算每笔交易的盈亏
        for trade in trades:
            if 'profit' in trade:
                profits.append(trade['profit'])

                # 更新连续盈亏计数
                if trade['profit'] > 0:
                    if current_streak >= 0:
                        current_streak += 1
                    else:
                        current_streak = 1
                    max_consecutive_wins = max(max_consecutive_wins, current_streak)
                elif trade['profit'] < 0:
                    if current_streak <= 0:
                        current_streak -= 1
                    else:
                        current_streak = -1
                    max_consecutive_losses = max(max_consecutive_losses, abs(current_streak))

        # 统计盈亏交易
        profitable_trades = len([p for p in profits if p > 0])
        losing_trades = len([p for p in profits if p < 0])
        total_trades = len(profits)

        # 计算盈亏统计
        if total_trades > 0:
            win_rate = profitable_trades / total_trades

            winning_profits = [p for p in profits if p > 0]
            losing_profits = [abs(p) for p in profits if p < 0]

            avg_profit = np.mean(winning_profits) if winning_profits else 0
            avg_loss = np.mean(losing_profits) if losing_profits else 0

            profit_loss_ratio = avg_profit / avg_loss if avg_loss != 0 else float('inf')
        else:
            win_rate = 0
            profit_loss_ratio = 0
            avg_profit = 0
            avg_loss = 0

        return TradeStats(
            total_trades=total_trades,
            profitable_trades=profitable_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            profit_loss_ratio=profit_loss_ratio,
            max_profit=max(profits) if profits else 0,
            max_loss=min(profits) if profits else 0,
            max_consecutive_wins=max_consecutive_wins,
            max_consecutive_losses=max_consecutive_losses,
            total_returns=sum(profits),
            sharpe_ratio=np.sqrt(252) * np.mean(profits) / np.std(profits) if profits else 0,
            max_drawdown=min(np.minimum.accumulate(np.cumsum(profits)) - np.cumsum(profits)) if profits else 0,
            avg_profit_per_trade=avg_profit,
            avg_loss_per_trade=avg_loss
        )

    def backtest(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict], BacktestStats]:
        """执行回测"""
        data = self.generate_signals(data)
        self.equity_curve = [self.initial_capital]
        trades = []
        position = 0
        entry_price = 0
        entry_time = None
        entry_index = 0
        current_equity = self.initial_capital

        for i in range(1, len(data)):
            current_row = data.iloc[i]
            previous_row = data.iloc[i - 1]

            # 平仓信号
            if position != 0 and current_row['signal'] != np.sign(position):
                # 计算持仓周期
                holding_periods = i - entry_index

                # 记录交易
                trade = {
                    'entry_time': entry_time,
                    'exit_time': current_row.name,
                    'position': np.sign(position),
                    'position_size': abs(position),
                    'entry_price': entry_price,
                    'exit_price': current_row['close'],
                    'holding_periods': holding_periods,
                    'margin_occupied': abs(position) * entry_price * self.contract_unit * self.margin_ratio
                }

                # 计算收益
                trade = self.calculate_returns(trade)
                trades.append(trade)

                # 更新权益
                current_equity += trade['net_profit']
                self.equity_curve.append(current_equity)

                # 重置仓位
                position = 0

            # 开仓信号
            if position == 0 and current_row['signal'] != 0:
                # 计算可用资金和最大仓位
                available_capital = current_equity
                max_contracts = math.floor(available_capital * 0.95 /
                                           (current_row['close'] * self.contract_unit * self.margin_ratio))

                position = max_contracts * current_row['signal']
                entry_price = current_row['close']
                entry_time = current_row.name
                entry_index = i

            # 记录当前权益
            if position == 0:
                self.equity_curve.append(current_equity)
            else:
                # 计算持仓盈亏
                unrealized_pnl = (current_row['close'] - entry_price) * position * self.contract_unit
                self.equity_curve.append(current_equity + unrealized_pnl)

        # 强制平最后的持仓
        if position != 0:
            holding_periods = len(data) - entry_index
            trade = {
                'entry_time': entry_time,
                'exit_time': data.index[-1],
                'position': np.sign(position),
                'position_size': abs(position),
                'entry_price': entry_price,
                'exit_price': data.iloc[-1]['close'],
                'holding_periods': holding_periods,
                'margin_occupied': abs(position) * entry_price * self.contract_unit * self.margin_ratio
            }
            trade = self.calculate_returns(trade)
            trades.append(trade)

            current_equity += trade['net_profit']
            self.equity_curve.append(current_equity)

        # 计算统计指标
        stats = self.analyze_trades(trades)

        return data, trades, stats

    def run(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict], BacktestStats]:
        """运行回测"""
        # 数据预处理
        data = data.copy()
        if not isinstance(data.index, pd.DatetimeIndex):
            data.index = pd.to_datetime(data.index)

        # 执行回测
        data, trades, stats = self.backtest(data)

        return data, trades, stats


# 使用示例
if __name__ == "__main__":
    # 假设数据格式为: datetime, open, high, low, close, volume
    # 读取数据
    data = pd.read_csv('data.csv')
    data['datetime'] = pd.to_datetime(data['datetime'].astype(float), unit='ns')
    # 创建回测实例
    backtester = EnhancedBacktester(
        initial_capital=500000,
        margin_ratio=0.12,
        commission_rate=0.0103,
        slippage=0,
        contract_unit=10
    )

    # 运行回测
    data, trades, stats = backtester.run(data)

    # 打印回测结果
    print(f"总收益率: {stats.returns_rate:.2%}")
    print(f"年化收益率: {stats.annual_returns:.2%}")
    print(f"夏普比率: {stats.sharpe_ratio:.2f}")
    print(f"最大回撤: {stats.max_drawdown_rate:.2%}")
    print(f"胜率: {stats.win_rate:.2%}")