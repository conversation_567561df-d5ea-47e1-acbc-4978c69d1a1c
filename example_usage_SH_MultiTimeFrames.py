"""
SH多时间周期策略使用示例
展示如何使用统一的API运行多个时间周期
"""

from TimeRoseMA_cross_SH_MultiTimeFrames import (
    TimeFrameConfig,
    SimpleMultiTimeFrameStrategy,
    OptimizedMultiTimeFrameStrategy,
    MultiTimeFrameStrategy,
    run_sh_multi_timeframe_strategy,
    run_single_timeframe
)


def example_1_run_all_timeframes():
    """示例1: 运行所有时间周期（独立API模式）"""
    print("=== 示例1: 运行所有SH时间周期 ===")
    run_sh_multi_timeframe_strategy("independent")


def example_2_run_single_timeframe():
    """示例2: 运行单个时间周期"""
    print("=== 示例2: 运行单个时间周期 ===")
    
    # 运行1分钟策略
    run_single_timeframe("1m")


def example_3_custom_config():
    """示例3: 自定义配置运行策略"""
    print("=== 示例3: 自定义配置 ===")
    
    # 自定义时间周期配置
    custom_configs = [
        TimeFrameConfig(interval=60, bklimit=2, sklimit=2, single_volume=1),    # 1分钟，更大持仓限制
        TimeFrameConfig(interval=300, bklimit=3, sklimit=3, single_volume=2),   # 5分钟，更大交易量
    ]
    
    # 创建策略
    strategy = SimpleMultiTimeFrameStrategy(
        symbol='SH',
        timeframe_configs=custom_configs,
        auth="quant_ggh,Qiai1301"
    )
    
    try:
        strategy.run_strategy()
    except KeyboardInterrupt:
        print("策略已停止")


def example_4_different_products():
    """示例4: 运行不同产品的多时间周期策略"""
    print("=== 示例4: 不同产品 ===")
    
    products = ['SH', 'OI', 'ag']  # 可以根据需要修改
    
    for product in products:
        print(f"\n--- 配置 {product} 策略 ---")
        
        configs = [
            TimeFrameConfig(interval=180, bklimit=1, sklimit=1, single_volume=1),   # 3分钟
            TimeFrameConfig(interval=900, bklimit=1, sklimit=1, single_volume=1),   # 15分钟
        ]
        
        strategy = SimpleMultiTimeFrameStrategy(
            symbol=product,
            timeframe_configs=configs,
            auth="quant_ggh,Qiai1301"
        )
        
        print(f"{product} 策略已配置，包含时间周期: {[c.name for c in configs]}")
        # 注意：这里只是展示配置，实际运行时每个产品需要独立的进程


def example_5_threaded_mode():
    """示例5: 多线程模式（每个时间周期独立API）"""
    print("=== 示例5: 多线程模式 ===")
    run_sh_multi_timeframe_strategy("threaded")


def show_usage_help():
    """显示使用帮助"""
    print("""
=== SH多时间周期策略使用指南 ===

1. 运行所有时间周期（推荐）:
   python TimeRoseMA_cross_SH_MultiTimeFrames.py independent

2. 运行单个时间周期:
   python TimeRoseMA_cross_SH_MultiTimeFrames.py 1m
   python TimeRoseMA_cross_SH_MultiTimeFrames.py 3m
   python TimeRoseMA_cross_SH_MultiTimeFrames.py 5m
   python TimeRoseMA_cross_SH_MultiTimeFrames.py 15m

3. 不同运行模式:
   - independent: 独立API模式（最稳定，推荐）
   - threaded: 多线程模式（每个时间周期独立API）
   - optimized: 优化模式（需要修改ma_cross函数）

4. 在代码中使用:
   from TimeRoseMA_cross_SH_MultiTimeFrames import run_sh_multi_timeframe_strategy
   run_sh_multi_timeframe_strategy("independent")

5. 自定义配置:
   - 修改TimeFrameConfig参数调整持仓限制和交易量
   - 修改auth参数使用不同的交易账户
   - 添加或删除时间周期

注意事项:
- 独立API模式最稳定，每个时间周期使用独立的进程
- 多线程模式可能存在API冲突，但资源占用较少
- 确保网络连接稳定，避免API连接中断
- 建议先在模拟环境测试
""")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        example_name = sys.argv[1]
        
        if example_name == "help":
            show_usage_help()
        elif example_name == "1":
            example_1_run_all_timeframes()
        elif example_name == "2":
            example_2_run_single_timeframe()
        elif example_name == "3":
            example_3_custom_config()
        elif example_name == "4":
            example_4_different_products()
        elif example_name == "5":
            example_5_threaded_mode()
        else:
            print("可用示例: 1, 2, 3, 4, 5, help")
    else:
        print("使用方法: python example_usage_SH_MultiTimeFrames.py [1|2|3|4|5|help]")
        print("运行 'python example_usage_SH_MultiTimeFrames.py help' 查看详细说明")
