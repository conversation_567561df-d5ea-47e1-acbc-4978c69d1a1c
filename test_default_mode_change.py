"""
测试默认模式修改为共享API模式
"""

import subprocess
import sys
import time


def test_default_mode():
    """测试默认模式"""
    print("=" * 60)
    print("测试默认模式（应该是共享API模式）")
    print("=" * 60)
    
    test_commands = [
        # 只提供合约代码，应该使用默认的共享API模式
        ["python", "TimeRoseMA_cross_ag_MultiTimeFrames.py", "ag"],
        ["python", "TimeRoseMA_cross_ag_MultiTimeFrames.py", "rb"],
    ]
    
    for cmd in test_commands:
        print(f"测试命令: {' '.join(cmd)}")
        try:
            # 启动进程
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 等待3秒让程序启动并输出信息
            time.sleep(3)
            
            # 终止进程
            process.terminate()
            
            # 获取输出
            stdout, stderr = process.communicate(timeout=5)
            
            print("输出:")
            print(stdout[:400] + "..." if len(stdout) > 400 else stdout)
            
            # 检查是否显示了正确的默认模式
            if "默认共享API模式" in stdout:
                print("✓ 默认模式正确：共享API模式")
            elif "默认独立API模式" in stdout:
                print("✗ 默认模式错误：仍然是独立API模式")
            else:
                print("? 无法确定默认模式")
            
            print("-" * 40)
            
        except Exception as e:
            print(f"测试失败: {e}")
            if 'process' in locals():
                process.kill()


def test_explicit_modes():
    """测试显式指定模式"""
    print("=" * 60)
    print("测试显式指定模式")
    print("=" * 60)
    
    test_cases = [
        (["python", "TimeRoseMA_cross_ag_MultiTimeFrames.py", "ag", "optimized"], "共享API模式"),
        (["python", "TimeRoseMA_cross_ag_MultiTimeFrames.py", "ag", "independent"], "独立API模式"),
        (["python", "TimeRoseMA_cross_ag_MultiTimeFrames.py", "ag", "threaded"], "多线程模式"),
    ]
    
    for cmd, expected_mode in test_cases:
        print(f"测试命令: {' '.join(cmd)}")
        print(f"期望模式: {expected_mode}")
        
        try:
            # 启动进程
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 等待3秒
            time.sleep(3)
            
            # 终止进程
            process.terminate()
            
            # 获取输出
            stdout, stderr = process.communicate(timeout=5)
            
            print("输出:")
            print(stdout[:300] + "..." if len(stdout) > 300 else stdout)
            
            # 检查模式是否正确
            if "optimized" in cmd and ("OptimizedMultiTimeFrameStrategy" in stdout or "共享API" in stdout):
                print("✓ 共享API模式正确")
            elif "independent" in cmd and ("SimpleMultiTimeFrameStrategy" in stdout or "独立API" in stdout):
                print("✓ 独立API模式正确")
            elif "threaded" in cmd and ("MultiTimeFrameStrategy" in stdout or "多线程" in stdout):
                print("✓ 多线程模式正确")
            else:
                print("? 模式检查结果不明确")
            
            print("-" * 40)
            
        except Exception as e:
            print(f"测试失败: {e}")
            if 'process' in locals():
                process.kill()


def test_help_message():
    """测试帮助信息"""
    print("=" * 60)
    print("测试帮助信息")
    print("=" * 60)
    
    try:
        # 不提供参数，应该显示帮助信息
        result = subprocess.run([
            sys.executable, 
            "TimeRoseMA_cross_ag_MultiTimeFrames.py"
        ], capture_output=True, text=True, timeout=10)
        
        print("帮助信息输出:")
        print(result.stdout)
        
        # 检查帮助信息是否正确更新
        if "optimized: 共享API模式（默认" in result.stdout:
            print("✓ 帮助信息正确：显示共享API为默认模式")
        elif "independent: 独立API模式（推荐" in result.stdout:
            print("✗ 帮助信息错误：仍显示独立API为推荐模式")
        else:
            print("? 帮助信息检查结果不明确")
            
    except subprocess.TimeoutExpired:
        print("帮助信息获取超时")
    except Exception as e:
        print(f"测试帮助信息失败: {e}")


def test_resource_usage():
    """测试资源使用情况"""
    print("=" * 60)
    print("测试资源使用情况对比")
    print("=" * 60)
    
    modes = [
        ("optimized", "共享API模式"),
        ("independent", "独立API模式")
    ]
    
    for mode, description in modes:
        print(f"测试 {description} ({mode}):")
        
        try:
            cmd = ["python", "TimeRoseMA_cross_ag_MultiTimeFrames.py", "ag", mode]
            
            # 启动进程
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 等待5秒让策略完全启动
            time.sleep(5)
            
            # 检查进程数量（简化版本）
            import psutil
            try:
                parent = psutil.Process(process.pid)
                children = parent.children(recursive=True)
                total_processes = len(children) + 1  # 包括父进程
                
                print(f"  进程数量: {total_processes}")
                
                if mode == "optimized":
                    print(f"  预期: 较少的进程数量（共享API）")
                elif mode == "independent":
                    print(f"  预期: 较多的进程数量（每个周期独立API）")
                    
            except ImportError:
                print("  无法检查进程数量（需要psutil库）")
            except Exception as e:
                print(f"  进程检查失败: {e}")
            
            # 终止进程
            process.terminate()
            process.wait()
            
        except Exception as e:
            print(f"  测试失败: {e}")
            if 'process' in locals():
                process.kill()
        
        print()


def show_mode_comparison():
    """显示模式对比"""
    print("=" * 60)
    print("模式对比说明")
    print("=" * 60)
    
    comparison = [
        ("特性", "共享API模式(optimized)", "独立API模式(independent)"),
        ("默认", "✓ 是", "否"),
        ("资源消耗", "低（1个API连接）", "高（4个API连接）"),
        ("稳定性", "中等", "高"),
        ("启动速度", "快", "慢"),
        ("内存使用", "低", "高"),
        ("网络连接", "1个", "4个"),
        ("适用场景", "一般使用", "高稳定性要求"),
    ]
    
    # 打印表格
    for row in comparison:
        print(f"{row[0]:<12} | {row[1]:<20} | {row[2]:<20}")
        if row[0] == "特性":
            print("-" * 60)
    
    print("\n推荐使用:")
    print("- 日常使用: optimized 模式（默认）")
    print("- 生产环境: independent 模式（高稳定性）")
    print("- 资源受限: optimized 模式")


def main():
    """主测试函数"""
    print("默认模式修改测试")
    print("=" * 80)
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        
        if test_type == "default":
            test_default_mode()
        elif test_type == "explicit":
            test_explicit_modes()
        elif test_type == "help":
            test_help_message()
        elif test_type == "resource":
            test_resource_usage()
        elif test_type == "compare":
            show_mode_comparison()
        else:
            print("可用测试:")
            print("  python test_default_mode_change.py default    # 测试默认模式")
            print("  python test_default_mode_change.py explicit   # 测试显式模式")
            print("  python test_default_mode_change.py help       # 测试帮助信息")
            print("  python test_default_mode_change.py resource   # 测试资源使用")
            print("  python test_default_mode_change.py compare    # 模式对比")
    else:
        # 运行主要测试
        show_mode_comparison()
        test_help_message()
        test_default_mode()
        
        choice = input("\n是否进行详细测试? (需要TqSDK认证) [y/N]: ").strip().lower()
        if choice in ['y', 'yes']:
            test_explicit_modes()


if __name__ == "__main__":
    main()
