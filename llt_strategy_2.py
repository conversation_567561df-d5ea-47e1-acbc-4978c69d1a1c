import pandas as pd
from tqsdk import TqApi, TqAuth


# 1. 核心LLT计算函数 (与之前相同)
def cal_LLT(price: pd.Series, alpha: float):
    """
    计算低延迟趋势线 (LLT)
    """
    LLT = []
    price_value = price.values
    if len(price_value) < 2:
        return [float('nan')] * len(price_value)
    LLT.append(price_value[0])
    LLT.append(price_value[1])
    for i, e in enumerate(price_value):
        if i > 1:
            v = (alpha - alpha ** 2 / 4) * e + (alpha ** 2 / 2) * price_value[i - 1] - \
                (alpha - 3 * (alpha ** 2) / 4) * price_value[i - 2] + 2 * \
                (1 - alpha) * LLT[i - 1] - (1 - alpha) ** 2 * LLT[i - 2]
            LLT.append(v)
    return LLT


# --- 实盘交易参数 ---
SYMBOL = "CZCE.OI509"
KLINE_PERIOD = 60
D_VALUE = 60
D_VALUE =30
ALPHA = 2 / (D_VALUE + 1)
VOLUME = 1
KLINE_DATA_LENGTH = 500

# --- 初始化 TqApi ---
api = TqApi(auth=TqAuth("smartmanp", "ftp123"))

print(f"实盘策略启动: 合约={SYMBOL}, 周期={KLINE_PERIOD}秒")
klines = api.get_kline_serial(SYMBOL, duration_seconds=KLINE_PERIOD, data_length=KLINE_DATA_LENGTH)
print("历史数据加载完成，等待新的1分钟K线生成...\n")

# +++ 新增：用于记录开仓价格的变量 +++
entry_price = 0.0

# --- 实盘交易主循环 ---
while True:
    api.wait_update()

    if api.is_changing(klines.iloc[-1], "datetime"):
        # 1. 计算信号
        close_prices = pd.Series(klines.close)
        llt_series = cal_LLT(close_prices, ALPHA)

        if len(llt_series) < 2:
            continue

        signal = 0
        if llt_series[-1] > llt_series[-2]:
            signal = 1
        elif llt_series[-1] < llt_series[-2]:
            signal = -1

        position = api.get_position(SYMBOL)
        current_price = klines.close.iloc[-1]
        dt_now = pd.to_datetime(klines.iloc[-1]["datetime"])

        # 2. 检查并打印持仓盈亏 (如果持仓)
        if entry_price != 0.0:
            pnl_points = 0
            if position.pos_long > 0:  # 持有多单
                pnl_points = current_price - entry_price
                print(
                    f"[{dt_now.strftime('%H:%M:%S')}] 持有多仓 | 开仓价: {entry_price:.2f}, 当前价: {current_price:.2f}, 浮动盈亏: {pnl_points:+.2f} 点")
            elif position.pos_short > 0:  # 持有空单
                pnl_points = entry_price - current_price
                print(
                    f"[{dt_now.strftime('%H:%M:%S')}] 持有空仓 | 开仓价: {entry_price:.2f}, 当前价: {current_price:.2f}, 浮动盈亏: {pnl_points:+.2f} 点")

        # 3. 执行交易决策
        # 收到做多信号
        if signal == 1:
            if position.pos_short > 0:  # 先平空仓
                print(f"[{dt_now.strftime('%H:%M:%S')}] 信号转多，平空仓 {position.pos_short} 手")
                api.close_position(SYMBOL, offset="CLOSE", volume=position.pos_short)
                entry_price = 0.0  # 平仓后重置开仓价
            if position.pos_long == 0:  # 再开多仓
                entry_price = current_price  # **记录开仓价格**
                print(f"[{dt_now.strftime('%H:%M:%S')}] 开多仓, 价格: {entry_price:.2f}")
                api.insert_order(symbol=SYMBOL, direction="BUY", offset="OPEN", volume=VOLUME)
        # 收到做空信号
        elif signal == -1:
            if position.pos_long > 0:  # 先平多仓
                print(f"[{dt_now.strftime('%H:%M:%S')}] 信号转空，平多仓 {position.pos_long} 手")
                api.close_position(SYMBOL, offset="CLOSE", volume=position.pos_long)
                entry_price = 0.0  # 平仓后重置开仓价
            if position.pos_short == 0:  # 再开空仓
                entry_price = current_price  # **记录开仓价格**
                print(f"[{dt_now.strftime('%H:%M:%S')}] 开空仓, 价格: {entry_price:.2f}")
                api.insert_order(symbol=SYMBOL, direction="SELL", offset="OPEN", volume=VOLUME)

# api.close()